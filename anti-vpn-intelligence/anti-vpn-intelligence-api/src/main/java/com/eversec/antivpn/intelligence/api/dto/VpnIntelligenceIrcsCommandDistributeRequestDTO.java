package com.eversec.antivpn.intelligence.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 跨境通信情报库指令下发载体。解密前
 */
@Getter
@Setter
@Schema(name = "VpnIntelligenceIrcsCommandDistributeRequestDTO", description = "云信安跨境通信情报库指令下发对象")
@Builder
public class VpnIntelligenceIrcsCommandDistributeRequestDTO implements Serializable {

	private static final long serialVersionUID = 1L;

	private String ircsId;
	private String randVal;
	private String pwdHash;
	private String command;
	private String commandHash;
	private Integer commandType;
	private Integer encryptAlgorithm;
	private Integer hashAlgorithm;
	private Integer compressionFormat;
	private String commandVersion;
	private Long commandSequence;
}
