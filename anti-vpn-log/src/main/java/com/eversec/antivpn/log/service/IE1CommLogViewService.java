package com.eversec.antivpn.log.service;

import com.eversec.antivpn.emnus.ScreenTypeEnum;
import com.eversec.antivpn.log.api.dto.base.AggCountDTO;
import com.eversec.antivpn.log.entity.E1CommLogView;
import com.baomidou.mybatisplus.extension.service.IService;
import com.eversec.antivpn.log.mapper.vo.TypeCountVO;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-17
 */
public interface IE1CommLogViewService extends IService<E1CommLogView> {

    AggCountDTO timeRangeAndTodayCount(ScreenTypeEnum screenTypeEnum);

    List<TypeCountVO> vpnNameAggregate(ScreenTypeEnum screenTypeEnum);

}
