// const UglifyJsPlugin = require("uglifyjs-webpack-plugin");
const dynamicProxy = require('./vue-config/dynamic.proxy')
const path = require("path")
let resolve = function (dir) {
  return path.resolve(__dirname, './', dir);
};
module.exports = {
  publicPath: '',
  // lintOnSave: process.env.NODE_ENV !== "production",
  lintOnSave: false,
  productionSourceMap: false,
  // filenameHashing: false,
  devServer: {
    host: '0.0.0.0',
    port: 3335,
    proxy: {
      '/service': {
        target: '这里配置无效', // 为了支持代理地址热切换（无需重启），已转到 vue_config/dynamic.proxy.list.json 中配置，并通过 active 激活代理（保持只有一个处于 active 状态）
        changeOrigin: true,
        ws: true,
        pathRewrite: {
          '^/service': '' // rewrite path
        },
        router: function (req) {
          // 代理地址热切换相关
          return dynamicProxy()
        }
      }
    }
    // eslint 警告和错误将直接出现在页面上
    /* overlay: {
      warnings: true,
      errors: true
    } */
  },
  configureWebpack: config => {
    // 支持 VSCode 中调试
    config.devtool = config.mode === 'production' ? false : 'source-map'
    // 入口文件
    config.entry.app = ['./src/main.js']
    config.externals = {
      MapGeo: 'MapGeo',
    };

    config.optimization = {
      splitChunks: {
        maxAsyncRequests: 30,
        maxInitialRequests: 10,
        name: false
      }
    }

    config.resolve.alias = {
      ...config.resolve.alias,
    }

    /* if (process.env.NODE_ENV === "production") {
      // 生产环境去掉 console
      config.plugins = [
        ...config.plugins,
        new UglifyJsPlugin({
          parallel: true,
          uglifyOptions: {
            compress: {
              drop_console: true
            }
          }
        })
      ];
    } */
  },

  chainWebpack: config => {
    config.plugins.delete('preload')
    config.plugins.delete('prefetch')
    // 从 public 复制到 dist 的时候，忽略掉版本控制相关文件夹
    config.plugin('copy').tap(args => {
      const newIgnore = ['**/.svn/**', '**/.git/**']
      args[0][0].ignore = args[0][0].ignore.concat(newIgnore)
      return args
    })

    config.module
      .rule('url')
      .test(/\.svg$/)
      .exclude.add(resolve('src/icons')) // 处理svg目录
      .end()
      .use('url-loader')
      .loader('url-loader')
      .options({
        limit: 10000,
        name: 'static/img/[name].[hash:7].[ext]'
      })
      .end();
    const svgRule = config.module.rule('svg');

    svgRule.uses.clear();
    // 添加要替换的 loader
    svgRule
      .test(/\.(svg)(\?.*)?$/)
      .include.add([resolve('src/icons')])
      .end()
      .use('svg-sprite-loader')
      .loader('svg-sprite-loader')
      .options({
        symbolId: 'icon-[name]'
      })
      .end();
  }
}
