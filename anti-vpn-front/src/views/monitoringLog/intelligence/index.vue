<template>
  <div class="pagebox">
    <!-- 情报监测日志 -->
    <div class="searchBox">
      <!-- :createdRun="false" -->
      <Search
        :data="searchData"
        @on-search="handleSearch"
        @on-clear="handleClear"
        @custom-search="customSearch"
        :hasCustomSearch="true"
        :createdRun="false"
      ></Search>
    </div>

    <custom-table
      :table-data="dataList"
      :table-header="tableHeader"
      :page.sync="page"
      @query="updateList()"
      has-index
      v-loading="tableLoading"
    >
      <el-table-column fixed="right" label="操作" align="center" width="120">
        <template slot-scope="scope">
          <el-button @click="deleteFn(scope.row)" type="text">
            删除
          </el-button>
        </template>
      </el-table-column>
    </custom-table>

    <CustomSearch ref="customRef" @updateSearchD="updateSearchD"></CustomSearch>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import monitorApi from "./monitorApi.js";
import searchD from "./searchData.js";
import CustomSearch from "./CustomSearch.vue";
export default {
  components: { CustomSearch },
  data() {
    return {
      searchAll: searchD,
      tableLoading: false,
      searchData: [],
      tableHeader: [
        {
          key: "typeId",
          label: "VPN类型",
          overflowTip: true,
          minWidth: 100
        },
        {
          key: "vpnName",
          label: "VPN名称",
          overflowTip: true,
          minWidth: 100
        },
        // {
        //   key: "version",
        //   label: "版本号",
        //   overflowTip: true,
        //   minWidth: 100,
        // },
        {
          key: "comCode",
          label: "运营商",
          overflowTip: true,
          minWidth: 100
        },
        {
          key: "networkBusinessId",
          label: "网络类型",
          overflowTip: true,
          minWidth: 100
        },
        {
          key: "logId",
          label: "日志记录ID",
          overflowTip: true,
          minWidth: 100
        },
        {
          key: "houseId",
          label: "机房ID",
          overflowTip: true,
          minWidth: 100
        },
        {
          key: "houseName",
          label: "机房名称",
          overflowTip: true,
          minWidth: 100
        },
        {
          key: "logProvinceId",
          label: "日志监测地理信息",
          overflowTip: true,
          minWidth: 130,
          formatter: function(v) {
            return `${v.logProvinceId}-${v.logCityId}-${v.logCountyId}`;
          }
        },
        // {
        //   key: "logProvinceId",
        //   label: "日志监测所在省/直辖市",
        //   overflowTip: true,
        //   minWidth: 100,
        // },
        // {
        //   key: "logCityId",
        //   label: "日志监测所在市/区(县)",
        //   overflowTip: true,
        //   minWidth: 100,
        // },
        // {
        //   key: "logCountyId",
        //   label: "日志监测所在县",
        //   overflowTip: true,
        //   minWidth: 100,
        // },
        {
          key: "srcIp",
          label: "源IP",
          overflowTip: true,
          minWidth: 100
        },
        {
          key: "srcPort",
          label: "源端口",
          overflowTip: true,
          minWidth: 100
        },
        {
          key: "srcCountry",
          label: "源IP所属国家",
          overflowTip: true,
          minWidth: 100
        },
        {
          key: "srcProvinceId",
          label: "源IP所属省份",
          overflowTip: true,
          minWidth: 100
        },
        {
          key: "srcInfo",
          label: "源IP主体信息",
          overflowTip: true,
          minWidth: 100
        },
        {
          key: "destIp",
          label: "目的IP",
          overflowTip: true,
          minWidth: 100
        },
        {
          key: "destPort",
          label: "目的端口",
          overflowTip: true,
          minWidth: 100
        },
        {
          key: "dstCountry",
          label: "目的IP所属国家",
          overflowTip: true,
          minWidth: 110
        },
        {
          key: "dstProvinceId",
          label: "目的IP所属省份",
          overflowTip: true,
          minWidth: 110
        },
        {
          key: "destInfo",
          label: "目的IP主体信息",
          overflowTip: true,
          minWidth: 110
        },
        {
          key: "trafficType",
          label: "流量类型",
          overflowTip: true,
          minWidth: 100
        },
        {
          key: "protocolType",
          label: "传输层协议",
          overflowTip: true,
          minWidth: 100
        },
        {
          key: "applicationProtocol",
          label: "应用层协议",
          overflowTip: true,
          minWidth: 100
        },
        {
          key: "url",
          label: "URL地址",
          overflowTip: true,
          minWidth: 100
        },
        {
          key: "contentType",
          label: "内容类型",
          overflowTip: true,
          minWidth: 100
        },
        {
          key: "vpnId",
          label: "情报库ID",
          overflowTip: true,
          minWidth: 100
        },
        {
          key: "vpnContent",
          label: "匹配内容",
          overflowTip: true,
          minWidth: 100
        },
        {
          key: "userAgent",
          label: "用户代理信息",
          overflowTip: true,
          minWidth: 100
        },
        {
          key: "maskMsisdn",
          label: "用户信息",
          overflowTip: true,
          minWidth: 100,
          formatter: function(v) {
            return `手机号码:${v.maskMsisdn}<br/>
            手机号码MD5值：${v.msisdnMd5}<br/>
            手机号码归属地：${v.phoneCityId}<br/>
            IMSI：${v.IMSI}<br/>
            终端类型：${v.imei}<br/>
            接入点名称：${v.apn}<br/>
            基站编号：${v.lac}<br/>
            小区编号：${v.ci}<br/>`;
          }
        },
        // {
        //   key: "maskMsisdn",
        //   label: "手机号",
        //   overflowTip: true,
        //   minWidth: 100,
        // },
        // {
        //   key: "msisdnMd5",
        //   label: "手机号MD5值",
        //   overflowTip: true,
        //   minWidth: 150,
        // },
        // {
        //   key: "phoneCityId",
        //   label: "手机号归属市",
        //   overflowTip: true,
        //   minWidth: 100,
        // },
        // {
        //   key: "IMSI",
        //   label: "用户IMSI",
        //   overflowTip: true,
        //   minWidth: 100,
        // },
        // {
        //   key: "imei",
        //   label: "用户终端型号",
        //   overflowTip: true,
        //   minWidth: 100,
        // },
        // {
        //   key: "apn",
        //   label: "接入点名称",
        //   overflowTip: true,
        //   minWidth: 100,
        // },
        // {
        //   key: "lac",
        //   label: "基站编号",
        //   overflowTip: true,
        //   minWidth: 100,
        // },
        // {
        //   key: "ci",
        //   label: "小区编号",
        //   overflowTip: true,
        //   minWidth: 100,
        // },

        {
          key: "certInfoVersion",
          label: "证书信息",
          overflowTip: true,
          minWidth: 100,
          formatter: function(v) {
            return `版本号: ${v.certInfoVersion}<br/>
            序列号: ${v.certInfoSerialNumber}<br/>
            签名算法: ${v.certInfoAlgorithm}<br/>
            签发者信息: ${v.certInfoIssuer}<br/>
            有效期: ${v.certInfoValidity}<br/>
            主体信息: ${v.certInfoSubject}<br/>`;
          }
        },

        // {
        //   key: "certInfoVersion",
        //   label: "证书版本号",
        //   overflowTip: true,
        //   minWidth: 100,
        // },
        // {
        //   key: "certInfoSerialNumber",
        //   label: "证书序列号",
        //   overflowTip: true,
        //   minWidth: 100,
        // },
        // {
        //   key: "certInfoAlgorithm",
        //   label: "证书签名算法",
        //   overflowTip: true,
        //   minWidth: 100,
        // },
        // {
        //   key: "certInfoIssuer",
        //   label: "证书签发者信息",
        //   overflowTip: true,
        //   minWidth: 110,
        // },
        // {
        //   key: "certInfoValidity",
        //   label: "证书有效期",
        //   overflowTip: true,
        //   minWidth: 100,
        // },
        // {
        //   key: "certInfoSubject",
        //   label: "证书主体信息",
        //   overflowTip: true,
        //   minWidth: 100,
        // },
        {
          key: "accessTime",
          label: "访问时间",
          overflowTip: true,
          minWidth: 100
        },
        {
          key: "timeStamp",
          label: "生成时间",
          overflowTip: true,
          minWidth: 100
        }
      ],
      dataList: [],
      page: {
        paging: 1,
        pageSizes: [10, 20, 50, 200],
        limit: 10,
        total: 0
      }
    };
  },
  computed: {
    ...mapGetters("sys-dict", ["dictionaryData"])
  },
  watch: {
    dictionaryData: {
      handler(v) {
        this.searchAll.forEach((el, i) => {
          let enumKey = el.enumKey;
          let dic = v && v[enumKey];
          if (dic) this.$set(this.searchAll[i], "options", dic);
        });
        console.log("watch-dictionaryData", this.searchAll);
        this.updateSearchD();
      }
    }
  },
  mounted() {
    this.updateSearchD();

    let p = {
      current: this.page.paging,
      size: this.page.limit
      // offset,
    };
    // monitorApi.page().then(res => {
    //   console.log("接口", res);
    // });
  },
  methods: {
    async updateList() {
      // this.tableLoading = true;
    },
    async deleteFn(item) {
      console.log("删除", item);
    },
    
    updateSearchD(checkedD) {
      console.log("更新搜索字段", checkedD);
      if (checkedD) {
        this.searchData = this.searchAll.filter(el => {
          return checkedD.includes(el.prop);
        })
        localStorage.searchData = JSON.stringify(this.searchData);
      } else {
        let d = localStorage.searchData && JSON.parse(localStorage.searchData);
        if (d) {
          this.searchData = d;
        } else {
          this.searchData = this.searchAll.slice(0, 3);
        }
      }
      
      console.log("更新搜索字段2", this.searchData);
    },
    // 自定义查询
    customSearch() {
      console.log("自定义查询");
      this.$refs.customRef.init(this.searchData);
    },
    // 清空查询方法
    handleClear() {},
    // 查询方法--列表数据
    handleSearch(filters) {
      // let params = cloneDeep(filters);
      let params = {};
      for (let key in filters) {
        if (filters[key]) params[key] = filters[key];
      }
      console.log("search-参数", filters, params);
      this.searchParams = params;
      this.updateList();
    }
  }
};
</script>

<style lang="scss" scoped></style>
