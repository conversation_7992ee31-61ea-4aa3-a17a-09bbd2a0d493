package com.eversec.antivpn.log.mapper;


import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.eversec.antivpn.log.api.dto.CrossBorderCommLogRequest;
import com.eversec.antivpn.log.api.dto.TypeCountDTO;
import com.eversec.antivpn.log.entity.E1CommLog;
import com.eversec.antivpn.log.entity.E1CommLogPOExt;
import com.eversec.antivpn.log.mapper.vo.TypeCountVO;
import com.eversec.antivpn.log.mapper.vo.UserLogPageVO;
import com.eversec.antivpn.log.mapper.vo.VpnAggVO;
import com.eversec.antivpn.log.mapper.vo.VpnHistogramAggVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-12
 */
@Mapper
@DS("clickhouse")
public interface E1CommLogMapper extends BaseMapper<E1CommLog> {

    List<TypeCountVO> getDateHistogram(@Param("startTimePartition") Long startTimePartition, @Param("endTimePartition") Long endTimePartition);

    List<TypeCountVO> getVpnTypeAggregate(@Param("startTimePartition") Long startTimePartition, @Param("endTimePartition") Long endTimePartition);

    List<TypeCountVO> getVpnType5ContentTypeAggregate(@Param("startTimePartition") Long startTimePartition, @Param("endTimePartition") Long endTimePartition);


    List<TypeCountVO> getApplicationProtocolTypeAggregate(@Param("startTimePartition") Long startTimePartition, @Param("endTimePartition") Long endTimePartition);


    List<TypeCountVO> getProtocolTypeAggregate(@Param("startTimePartition") Long startTimePartition, @Param("endTimePartition") Long endTimePartition);


    List<TypeCountVO> getNetworkBusinessTypeAggregate(@Param("startTimePartition") Long startTimePartition, @Param("endTimePartition") Long endTimePartition);

    List<TypeCountVO> getProvinceAggregate(@Param("startTimePartition") Long startTimePartition, @Param("endTimePartition") Long endTimePartition);


    List<TypeCountVO> getTodayProvinceAggregate(@Param("timePartition") Integer timePartition);

    List<TypeCountVO> getAllLogProvinceAggregate(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    List<E1CommLogPOExt> getLogStatusPage();

    List<E1CommLogPOExt> getCommLogStatusPage(CrossBorderCommLogRequest crossBorderCommLogDTO);


    void deleteByLogId(@Param("logId") Long logId);

    List<Long> getDistinctVpnId(@Param("startTimePartition") Long startTimePartition, @Param("endTimePartition") Long endTimePartition);


    List<VpnAggVO> getUsedResourceNodeAggregate(@Param("startTimePartition") Long startTimePartition, @Param("endTimePartition") Long endTimePartition);

    List<UserLogPageVO> getUserLogPageList(@Param("startTimePartition") Long startTimePartition, @Param("endTimePartition") Long endTimePartition);
}
