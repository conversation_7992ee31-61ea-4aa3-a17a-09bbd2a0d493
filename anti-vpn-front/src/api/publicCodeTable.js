import {
  getDic
} from "@/utils/dic";

//获取字典类型列表
export const dicList = async function (code = "") {
  return new Promise((resolve, reject) => {
    getDic([code], (d) => {
      //   console.log("字典接口-" + code, d[code]);
      resolve(d[code]);
    });
  });
}

// 从字典查找转化 itemValue-->itemName
export const disChange = function (itemValue, disArr=[]) {
  if (itemValue === '' || itemValue === null || (disArr && disArr.length == 0)) return '';
  let finded = disArr.find(el => el.itemValue == itemValue);
  return finded && finded.itemName || itemValue; // 匹配不到仍返回当前值
}

export const allIndustryList = [{
    label: '电信',
    value: '1',
  }, {
    label: '广播电视',
    value: '2',
  }, {
    label: '能源',
    value: '3',
  }, {
    label: '金融',
    value: '4',
  },
  {
    label: '公路水路运输',
    value: '5',
  }, {
    label: '铁路',
    value: '6',
  }, {
    label: '民航',
    value: '7',
  }, {
    label: '邮政',
    value: '8',
  },
  {
    label: '水利',
    value: '9',
  }, {
    label: '应急管理',
    value: '10',
  }, {
    label: '卫生健康',
    value: '11',
  }, {
    label: '社会保障',
    value: '12',
  },
  {
    label: '国防科工',
    value: '13',
  }, {
    label: '政府部门',
    value: '14',
  }, {
    label: '教育科研',
    value: '15',
  }, {
    label: '工业制造',
    value: '16',
  },
  {
    label: '互联网',
    value: '17',
  }, {
    label: '新闻',
    value: '18',
  }, {
    label: '环境保护',
    value: '19',
  }, {
    label: '公共事业',
    value: '20',
  }, {
    label: '食品药品',
    value: '21',
  }, {
    label: '化工',
    value: '22',
  }, {
    label: '其他',
    value: '23',
  },
];



export const companyTypeList = [{
  label: '党政机关',
  value: '1',
}, {
  label: '事业单位',
  value: '2',
}, {
  label: '国有企业',
  value: '3',
}, {
  label: '社会团体',
  value: '4',
}, {
  label: '民营企业',
  value: '5',
}, {
  label: '其他',
  value: '6',
}, ];


