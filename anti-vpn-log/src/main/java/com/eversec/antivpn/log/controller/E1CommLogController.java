package com.eversec.antivpn.log.controller;


import cn.hutool.core.bean.BeanUtil;
import com.eversec.antivpn.emnus.ScreenTypeEnum;
import com.eversec.antivpn.log.aop.RedisCache;
import com.eversec.antivpn.log.api.dto.TypeCountDTO;
import com.eversec.antivpn.log.api.dto.base.AggCountDTO;
import com.eversec.antivpn.log.mapper.vo.TypeCountVO;
import com.eversec.antivpn.log.service.IE1CommLogService;
import com.eversec.antivpn.log.service.IE1CommLogViewService;
import com.eversec.antivpn.log.service.ScreenRedisService;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import lombok.AllArgsConstructor;

import java.util.ArrayList;
import java.util.List;

import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-12
 */
@RestController
@RequestMapping(com.eversec.antivpn.log.api.E1CommLogApi.PATH)
@AllArgsConstructor
@Slf4j
public class E1CommLogController implements com.eversec.antivpn.log.api.E1CommLogApi {

    private final IE1CommLogService service;

    private final IE1CommLogViewService iE1CommLogViewService;

    private ScreenRedisService screenRedisService;


    @RedisCache
    @Override
    public List<TypeCountDTO> vpnType5ContentTypeAggregate(ScreenTypeEnum screenTypeEnum) {
        List<TypeCountDTO> result = new ArrayList<>();
        List<TypeCountVO> typeCountVOList = iE1CommLogViewService.vpnNameAggregate(screenTypeEnum);
        typeCountVOList.forEach(typeCountVO -> result.add(BeanUtil.toBean(typeCountVO, TypeCountDTO.class)));
        return result;
    }

    @Override
    @RedisCache
    public List<TypeCountDTO> applicationProtocolTypeAggregate(ScreenTypeEnum screenTypeEnum) {
        List<TypeCountDTO> result = new ArrayList<>();
        List<TypeCountVO> longTypeCountVOList = service.getApplicationProtocolTypeAggregate(screenTypeEnum);
        longTypeCountVOList.forEach(longTypeCountVO -> {
            result.add(BeanUtil.toBean(longTypeCountVO, TypeCountDTO.class));
        });
        return result;
    }


    @Override
    public String deleteByLogId(Long logId) {
//        String a = null;
//        System.out.println(a.equals("hello"));
       return "删除成功！";
    }

    @RedisCache
    @Override
    public AggCountDTO timeRangeAndTodayCount(ScreenTypeEnum screenTypeEnum) {
        AggCountDTO result = iE1CommLogViewService.timeRangeAndTodayCount(screenTypeEnum);
        return result;
    }


}
