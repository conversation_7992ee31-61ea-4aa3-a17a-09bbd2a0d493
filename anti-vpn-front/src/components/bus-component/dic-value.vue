<template>
  <span>
    {{ getValue }}
  </span>
</template>

<script>
import { mapGetters } from 'vuex'
export default {
  props: {
    // 设置默认值，如果当前的value不存在就显示默认值
    defaultValue: {
      type: [String, Number, Array, Boolean]
    },
    enumKey: {
      type: String
    },
    dicKey: {
      type: [String, Number, Array, Boolean]
    },
    multiple: {
      type: Boolean,
      default: () => false
    }, // 是否多选
    isString: {
      type: Boolean,
      default: () => false
    }, // 是否按照字符串过滤
    dictionaryKey: {
      type: Array,
      default: () => []
    } // 如果频繁调用使用的值
  },
  data() {
    return {}
  },
  beforeDestroy() {},
  computed: {
    ...mapGetters('sys-dict', ['dictionaryData']),
    getValue: {
      cache: false,
      get() {
        let value = null
        if (this.dictionaryKey && this.dictionaryKey.length > 0) {
          // 如果是多选
          if (this.multiple) {
            if (!this.dicKey) {
              return '-'
            }
            value = this.dictionaryKey
              .filter(e => this.dicKey.map(v => v + '').includes(e.enumKey))
              .map(e => e.enumVal)
              .join(',')
            if (value.length === 0) value = '-'
            // 单选
          } else {
            this.dictionaryKey.some(item => {
              if (
                item.enumKey ===
                  (this.isString ? this.dicKey + '' : this.dicKey) ||
                item.enumKey.trim() ===
                  (this.isString ? this.dicKey + '' : this.dicKey)
              ) {
                value = item.enumVal
                return true
              }
            })
          }
        } else if (
          this.dictionaryData &&
          Object.keys(this.dictionaryData).length > 0 &&
          this.dictionaryData[this.enumKey]
        ) {
          // 如果是多选
          if (this.multiple) {
            if (!this.dicKey) {
              return '-'
            }
            value = this.dictionaryData[this.enumKey]
              .filter(e => this.dicKey.map(v => v + '').includes(e.enumKey))
              .map(e => e.enumVal)
              .join(',')
            if (value.length === 0) value = '-'
            // 单选
          } else {
            this.dictionaryData[this.enumKey].some(item => {
              if (
                item.enumKey ===
                  (this.isString ? this.dicKey + '' : this.dicKey) ||
                item.enumKey.trim() ===
                  (this.isString ? this.dicKey + '' : this.dicKey)
              ) {
                value = item.enumVal
                return true
              }
            })
          }
        }
        return value || this.defaultValue || '-'
      }
    }
  },
  mounted() {},
  methods: {}
}
</script>

<style lang="scss">
.icon-select-popover {
  max-width: 300px;
}
.icon-select-list {
  max-height: 300px;
  padding: 0;
  margin: -8px 0 0 -8px;
  overflow: auto;
  > .el-button {
    padding: 8px;
    margin: 8px 0 0 8px;
    > span {
      display: inline-block;
      vertical-align: middle;
      width: 18px;
      height: 18px;
      font-size: 18px;
    }
  }
}
</style>
