.color_main {
  color: var(--primary-bg);
}

.searchBox {
  background: #fff;
  padding: 20px 20px 0;
  border-radius: 0 0 var(--main-content-border-radius) var(--main-content-border-radius);

  &.p_no {
    padding: 0;
  }
}

.mycard-wrap {
  background: #fff;
  padding: 0 20px;
}

.table-wrap {
  background: #fff;
  margin-top: 20px;
  padding: 12px 20px;
  border-radius: var(--main-content-border-radius);

  .table_top {
    margin-bottom: 10px;
    overflow: hidden;
  }
}

::v-deep .myTabs {
  .el-tabs__item {
    font-size: 16px;

    &.is-active {
      font-weight: bold;
    }
  }

  .el-tabs__header {
    margin: 0;
    padding: 0 20px;
    background: #fff;
    border-radius: var(--main-content-border-radius) var(--main-content-border-radius) 0 0;
  }

  .el-tabs__content {
    border-radius: 0 0 var(--main-content-border-radius) var(--main-content-border-radius);
  }

  .el-tabs__nav-wrap {
    height: 59px;
    line-height: 58px;

    &::after {
      border-color: #E1E3ED !important;
      height: 1px;
    }
  }

  &.p0 {
    .el-tabs__header {
      padding: 0;
    }

    .mycard-wrap {
      padding: 0
    }
  }
}

// 图表3三列布局
.chartFlexBox {
  display: flex;
  padding: 0;
  gap: 20px;

  .item_card {
    flex: 1;
    height: 270px;
    width: 100%;
    border: 1px solid #e1e3ed;
    border-radius: 10px;

    &:last-of-type {
      margin-right: 0;
    }

    padding: 20px;
    .frbtn {
      margin-top: -8px;
      float: right;
    }
    .title {
      font-weight: bold;
      font-size: 14px;
      color: #3F405D;
      height: 20px;
    }
    
    .frbtn {
      margin-top: -8px;
      float: right;
    }
  }
}

// 模块-flex自由布局, 默认3列
.chartFlexBox2 {
  min-height: 100px;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: flex-start;
  gap: 1.6%;

  .item_card {
    margin-bottom: 20px;
    height: 270px;
    border: 1px solid #e1e3ed;
    border-radius: 10px;
    width: 32.2%;
    padding: 20px;
    transition: all linear 150ms;

    &:hover {
      border-color: rgba($color: #4074eb, $alpha: 0.6);
      box-shadow: 0px 0px 10px rgba($color: #4074eb, $alpha: 0.5);
    }

    .title {
      font-weight: bold;
      font-size: 14px;
      color: #3F405D;
      min-height: 22px;
      line-height: 1;
    }
    .frbtn {
      margin-top: -8px;
      float: right;
    }
    ::v-deep .chart_box{
      height: calc((100% - 14px));
    }
    // /deep/ .echarts-panel{
    //   height: calc((100% - 14px));
    // }
  }
}


.detailTit {
  display: flex;
  padding: 15px 20px 8px;
  border-bottom: 1px solid #e1e3ed;
  align-items: center;
  font-size: 16px;
  font-weight: bold;
  line-height: 36px;
  color: #3F405D;

  &::before {
    content: '';
    width: 3px;
    height: 16px;
    background: #436BD9;
    display: inline-block;
    margin-right: 8px;
  }
}

.myRadioGroup {
  ::v-deep .el-radio-button__inner {
    border: 1px solid #d7d6e3;
    color: #808191;
    background: #ffffff;
    font-size: 14px;
    // width: 108px;
    min-width: 90px;
    padding-left: 13px;
    padding-right: 13px;

    &:hover {
      color: #345bc4;
    }
  }

  ::v-deep .el-radio-button:first-child .el-radio-button__inner {
    background: #ffffff;
    border: 1px solid #d7d6e3;
    color: #808191;
  }

  ::v-deep .el-radio-button__orig-radio:checked+.el-radio-button__inner {
    background: #edf2ff;
    color: var(--primary-bg);
    border: 1px solid var(--primary-border);
  }
}
