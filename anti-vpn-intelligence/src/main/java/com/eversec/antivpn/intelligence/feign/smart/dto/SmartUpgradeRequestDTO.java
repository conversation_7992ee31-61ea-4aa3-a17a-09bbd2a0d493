package com.eversec.antivpn.intelligence.feign.smart.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 调用smart下发规则通知请求参数
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023-08-08 12:04
 */
@Data
public class SmartUpgradeRequestDTO {

	@Schema(description = "特征库的id、唯一标识，该标识主要用于特征库下发，相同的文件会返回相同的fileId")
	private String fileId;

	@Schema(description = "要下发的设备id，可通过“5.5 处置设备查询”接口获取所有的设备信息。.多个使用逗号隔开，如:\"1,2,3\"\n")
	private String devIds;

	@Schema(description = "设备类型：支持多值，用英文“,”号隔开，如：\"5,6,8\"\n" + "具体参考（附录二：设备类型说明）\n")
	private String devType;

	@Schema(description = "CPU指令集，如ARM,X86\n")
	private String cpuArchitecture;

	@Schema(description = "省份标识. 多个用因为逗号隔开，如:\"110000,120000,130000\"\n" + "具体参考（附录一：省份标识说明）")
	private String provinceIds;

	@Schema(description = "运营商标识：\n" + "联通:10010\n" + "电信:10000  \n" + "移动:10086\n" + "管局:11111\n" + "广电:10099 \n"
			+ "多个用英文逗号隔开，如：\"10010,11111\"\n")
	private String opFlag;

	@Schema(description = "本次特征库下发的批次号，用于后期核对数据及排查问题时使用。12—16位的不重复的数字英文字符串，uuid、雪花、其他。\n")
	private String bathNo;

	@Schema(description = "预留字段。升级任务描述")
	private String remarks;

	@Schema(description = "同步方式，1、全量同步，2、增量新增，3、增量删除\n")
	private String sycType;

}
