.bigScreen {
  position: relative;
  height: 100%;
  width: 100%;
  // min-width: 1910px;
  // min-height: 1080px;
  background-size: 100% 100%;
  // overflow: auto;

  /* 滚动条 */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  /*滚动条里面小方块*/
  ::-webkit-scrollbar-thumb {
    /* box-shadow: inset 0 0 4px #ccc; */
    border-radius: 4px;
    background: #223144;
    /* background: rgba(128, 129, 145, .5); */
    height: 8px;
    width: 8px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: rgba(67, 107, 217, .8);
    /* background: rgba(128, 129, 145, .8); */
    /* box-shadow: inset 0 0 4px #aaa; */
  }

  /*滚动条里面轨道*/
  ::-webkit-scrollbar-track {
    /* background: #F7F8FC; */
    border: 1px solid rgba(67, 107, 217, 0);
    background: rgba(67, 107, 217, .2);
    border-radius: 4px;
  }

  /* ::-webkit-scrollbar-track:hover {
  background: rgba(150, 150, 150, 0.2);
  box-shadow: inset 0 0 1px #ccc;
} */
  ::-webkit-scrollbar-corner {
    background: transparent;
  }

}
