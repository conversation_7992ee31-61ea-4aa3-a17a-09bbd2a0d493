import PageTitle from '@/components/page-title'
import DicValue from '@/components/bus-component/dic-value'
import DicSelect from '@/components/bus-component/dic-select'
import UploadFile from '@/components/bus-component/upload-file'
import publicForm from '@/components/bus-component/public-form'
import publicTable from '@/components/bus-component/public-table'
import CustomTable from '@/components/bus-component/CustomTable.vue'
import Search from '@/components/bus-component/Search'
import ECharts from '@/components/echarts/index.vue'
import empty from '@/components/empty.vue'
import btnList from '@/components/bus-component/btn-list'
import dicCascader from '@/components/bus-component/public-form/dic-cascader'
const Common = {
  install: function(Vue) {
    Vue.component('PageTitle', PageTitle)
    Vue.component('DicValue', DicValue)
    Vue.component('DicSelect', DicSelect)
    Vue.component('UploadFile', UploadFile)
    Vue.component('publicForm', publicForm)
    Vue.component('publicTable', publicTable)
    Vue.component('CustomTable', CustomTable)
    Vue.component('Search', Search)
    Vue.component('ECharts', ECharts)
    Vue.component('empty', empty)
    Vue.component('btnList', btnList)
    Vue.component('dicCascader', dicCascader)
  }
}
export default Common
