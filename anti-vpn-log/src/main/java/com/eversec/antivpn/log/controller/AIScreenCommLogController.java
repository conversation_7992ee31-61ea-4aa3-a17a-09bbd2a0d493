package com.eversec.antivpn.log.controller;

import cn.hutool.core.util.ObjectUtil;
import com.eversec.antivpn.config.AntiVpnProperties;
import com.eversec.antivpn.emnus.ScreenTypeEnum;
import com.eversec.antivpn.log.aop.RedisCache;
import com.eversec.antivpn.log.api.AIScreenCommLogApi;
import com.eversec.antivpn.log.api.dto.TypeCountDTO;
import com.eversec.antivpn.log.api.dto.base.AggCountDTO;
import com.eversec.antivpn.log.api.dto.base.DateHistogramDTO;
import com.eversec.antivpn.log.mapper.AIVpnMachineLearningCodeDictMapper;
import com.eversec.antivpn.log.service.IE2MachineLearningLogViewService;
import com.eversec.antivpn.log.service.ScreenRedisService;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import com.eversec.antivpn.log.util.MapDataCleanUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 *  Ai态势大屏
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-08
 */
@RestController
@RequestMapping(AIScreenCommLogApi.PATH)
@AllArgsConstructor
@Slf4j
public class AIScreenCommLogController implements AIScreenCommLogApi {

    private final IE2MachineLearningLogViewService service;

    private final AntiVpnProperties antiVpnProperties;

    private ScreenRedisService screenRedisService;

    private MapDataCleanUtil mapDataCleanUtil;

    private AIVpnMachineLearningCodeDictMapper aiVpnMachineLearningCodeDictMapper;

    @Override
    @RedisCache
    public AggCountDTO dateMonitoring(ScreenTypeEnum screenTypeEnum) {
        AggCountDTO aggCountDTO = service.countNum(screenTypeEnum);
        return aggCountDTO;
    }

    @Override
    public Integer dateRecognition(ScreenTypeEnum screenTypeEnum) {
        return antiVpnProperties.getAiModelAccuracy();
    }

    @Override
    public Integer dateAISum(ScreenTypeEnum screenTypeEnum) {
        return aiVpnMachineLearningCodeDictMapper.count();
    }

    @Override
    @RedisCache
    public AggCountDTO dateVisitorArea(ScreenTypeEnum screenTypeEnum) {
        AggCountDTO aggCountDTO = service.dateVisitorArea(screenTypeEnum);
        return aggCountDTO;
    }

    @Override
    @RedisCache
    public AggCountDTO dateDestinationArea(ScreenTypeEnum screenTypeEnum) {
        AggCountDTO aggCountDTO = service.dateDestinationArea(screenTypeEnum);
        return aggCountDTO;
    }

    @Override
    @RedisCache
    public List<TypeCountDTO> datedDiscernTopo10(ScreenTypeEnum screenTypeEnum) {
        List<TypeCountDTO> result = service.getDatedDiscernTopo10(screenTypeEnum);
        return result;
    }

    @Override
    public List<TypeCountDTO> protocolTypeAggregate(ScreenTypeEnum screenTypeEnum) {
        String methodAndTimePartition = "protocolTypeAggregate"+screenTypeEnum;

        if(ObjectUtil.isNotEmpty(screenRedisService.getScreenResult(methodAndTimePartition))){

            return (List<TypeCountDTO>) screenRedisService.getScreenResult(methodAndTimePartition);
        }

        List<TypeCountDTO> result = service.getLogListByProtocolType(screenTypeEnum);
        //设置缓存
        screenRedisService.setScreenResult(methodAndTimePartition,result);
        return result;
    }

    @Override
    @RedisCache
    public Map<String, Object> tendencyChart(ScreenTypeEnum screenTypeEnum) {
//        AI模型监测跨境通信日志趋势图  查询条件：今天-> 按小时、近一周-> 按天、月-> 按周、至今-> 按月
        // 查询条件：今天-> 按小时
        //查询条件：近一周-> 按天
        //查询条件：月-> 按周
        //查询条件：至今-> 按月
        List<TypeCountDTO> result1 = service.getAILogByYearORmonthORDayORWeek(screenTypeEnum);
        Map<String, Object> resulta = result1.stream().collect(Collectors.toMap(TypeCountDTO::getKey, Function.identity()));
        return resulta;
    }

    /**
     * AI模型识别通信日志置信度分布
     * @param screenTypeEnum
     * @return
     */
    @Override
    @RedisCache
    public List<DateHistogramDTO> rateChart(ScreenTypeEnum screenTypeEnum) {
        List<DateHistogramDTO> result = service.getLogNumByRateList(screenTypeEnum);
        return result;
    }

    /**
     * AI模型识别跨境通信日志置信度占比(饼图)
     * @param screenTypeEnum
     * @return
     */
    @Override
    @RedisCache
    public List<DateHistogramDTO> ratePie(ScreenTypeEnum screenTypeEnum) {
        List<DateHistogramDTO> result = service.getLogNumByRateDistribute(screenTypeEnum);
        return result;
    }

    @Override
    @RedisCache
    public List<TypeCountDTO> datedProvinceTopo10(ScreenTypeEnum screenTypeEnum) {
        List<TypeCountDTO> result = service.getLogNumByProvinceId(screenTypeEnum,false);
        return result;
    }

    @Override
    @RedisCache
    public List<TypeCountDTO> chinaMap(ScreenTypeEnum screenTypeEnum) {
        List<TypeCountDTO> result = service.getChinaMapByProvinceId(screenTypeEnum,antiVpnProperties.getStartProvinceSearch().getOpenStatus());
        mapDataCleanUtil.cleanCityOutOfTheProvinceTypeCountDTO(result);
        return result;
    }

    @Override
    public List<TypeCountDTO> systemRatioMap(ScreenTypeEnum screenTypeEnum) {

        String methodAndTimePartition = "systemRatioMap"+screenTypeEnum;

        if(ObjectUtil.isNotEmpty(screenRedisService.getScreenResult(methodAndTimePartition))){

            return (List<TypeCountDTO>) screenRedisService.getScreenResult(methodAndTimePartition);
        }

        List<TypeCountDTO> result = service.getSystemLogNum(screenTypeEnum);
        //设置缓存
        screenRedisService.setScreenResult(methodAndTimePartition,result);

        return result;
    }

    @Override
    public List<TypeCountDTO> modelTwiter(ScreenTypeEnum screenTypeEnum) {
        List<TypeCountDTO> result = service.modelTwiter(screenTypeEnum);
        return result;
    }

}
