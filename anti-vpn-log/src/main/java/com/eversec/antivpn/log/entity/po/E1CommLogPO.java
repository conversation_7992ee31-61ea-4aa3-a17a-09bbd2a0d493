package com.eversec.antivpn.log.entity.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.eversec.stark.generic.common.infra.mybatis.plus.BaseDO;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 * <p>
 * 持久化对象，此对象与数据库属性一一对应，被Entity对象继承，扩展属性写在Entity对象中
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-25
 */
@Getter
@Setter
@TableName("e1_comm_log")
@Schema(name = "E1CommLogPO", description = "")
public class E1CommLogPO {

    private static final long serialVersionUID = 1L;

    private String version;

    private String comCode;

    private Integer networkBusinessId;

    private Long logId;

    private Long houseId;

    private String houseName;

    private Long logProvinceId;

    private Long logCityId;

    private Long logCountyId;

    private String srcIp;

    private Long srcPort;

    private String srcCountry;

    private String srcProvinceId;

    private String srcInfo;

    private String destIp;

    private Long destPort;

    private String dstCountry;

    private String dstProvinceId;

    private String destInfo;

    private Long trafficType;

    private Long protocolType;

    private Long applicationProtocol;

    private String url;

    private String contentType;

    private Long vpnId;

    private String vpnName;

    private Integer typeId;

    private Integer vpnContentType;

    private String vpnContent;

    private String userAgent;

    private String msisdnMd5;

    private String maskMsisdn;

    private String phoneProvinceId;

    private String phoneCityId;

    private String imsi;

    private String imei;

    private String apn;

    private String lac;

    private String ci;

    private String certInfoVersion;

    private String certInfoSerialNumber;

    private String certInfoAlgorithm;

    private String certInfoIssuer;

    private String certInfoAlidity;

    private String certInfoSubject;

    private LocalDateTime accessTime;

    private LocalDateTime createTime;

    private LocalDateTime timestamp;

    private Long day;

    private Integer hour;
}