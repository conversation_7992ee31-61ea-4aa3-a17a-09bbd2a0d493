package com.eversec.antivpn.intelligence.api;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;

/**
 * 云信安
 */
@WebService(name = "VpnIntelligenceIrcsCommandWebService", targetNamespace = "http://impl.service.intelligence.antivpn.eversec.com/")
public interface VpnIntelligenceIrcsCommandWebService {


    /**
     *
     * @param ircsId 用systemCode来存储
     * @param randVal
     * @param pwdHash
     * @param command
     * @param commandHash
     * @param commandType
     * @param commandSequence
     * @param encryptAlgorithm
     * @param hashAlgorithm
     * @param compressionFormat
     * @param commandVersion
     * @return
     */
    @WebMethod(operationName = "ircs_command")
    @WebResult
    String ircs_command(@WebParam(name = "ircsId") String ircsId, @WebParam(name = "randVal") String randVal,
                               @WebParam(name = "pwdHash") String pwdHash, @WebParam(name = "command") String command,
                               @WebParam(name = "commandHash") String commandHash, @WebParam(name = "commandType") Integer commandType,
                               @WebParam(name = "commandSequence") Long commandSequence,
                               @WebParam(name = "encryptAlgorithm") Integer encryptAlgorithm, @WebParam(name = "hashAlgorithm") Integer hashAlgorithm,
                               @WebParam(name = "compressionFormat") Integer compressionFormat,
                               @WebParam(name = "commandVersion") String commandVersion);

}
