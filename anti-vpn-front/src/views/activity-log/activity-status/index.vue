<template>
  <div class="activity-status" v-loading="loading">
    <el-tabs v-model="active" @tab-click="handleClick">
      <el-tab-pane
        v-for="(item, index) in tabs"
        :key="index"
        :label="item.label"
        :name="item.key"
      />
    </el-tabs>
    <!-- form -->
    <publicForm
      ref="publicForm"
      :formDataLabel="formDataLabel"
      :labelWidth="'0'"
      :form-data="formData"
      @throwEvent="changeItem"
    />
    <section class="is-mb-10 is-overflow-hidden" v-if="active === 'new'">
      <div class="activity-status-title-box is-float-left is-mr-20" />
      <div
        v-for="(items, index) in describe[active].labels"
        :key="index"
        class="is-float-left"
        :class="items.msgClass"
      >
        {{ items.frontMsg }}
        <span :class="items.keyClass">{{ keyData[items.numKey] || 0 }}</span>
        {{ items.backMsg }}
      </div>
    </section>
    <!-- table -->
    <publicTable
      :tableLabel="tableLabel"
      :tableData="dataList"
      ref="table"
      height="100%"
      :options="{
        highlightCurrentRow: false,
        isString: true,
        border: false
      }"
    >
      <template v-slot:dateHandle="{ data }">
        <div
          class="is-float-left orange-box is-mt-5 is-mr-20"
          v-if="active === 'new' && timeConversion(data.slotItem)"
        />
        <div class="is-float-left">
          {{ formatTime(data.slotItem) }}
        </div>
      </template>
    </publicTable>
    <!-- 分页 -->
    <el-pagination
      v-if="active !== 'new'"
      class="is-pt-10 is-float-right"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="pagingInfo.current"
      :page-sizes="pagingInfo.pageSizes"
      :page-size="pagingInfo.size"
      :layout="pagingInfo.layout"
      :total="pagingInfo.total"
      :key="pagingInfo.total"
    />
  </div>
</template>

<script>
import { formatTime } from "@/utils/time";
import cloneDeep from "lodash/cloneDeep";
import { pagingInfo } from "@/config/base";
import debounce from "lodash/debounce";
import antiVpnService from "@/api/antiVpnService";

export default {
  name: "activity-status",
  components: {},
  data() {
    return {
      loading: false,
      formatTime,
      formData: {
        createDatetime: []
      },
      pagingInfo: cloneDeep(pagingInfo()), // 分页信息
      tableLabel: [
        {
          label: "运营商编码",
          val: "comCode",
          type: "dic-val",
          enumKey: "ISP_CODE"
        },
        {
          label: "省级区域编号",
          val: "provinceId",
          type: "dic-val",
          enumKey: "PROVINCE_CODE"
        },
        {
          label: "网络类型",
          val: "networkBusinessIdList",
          type: "dic-val",
          multiple: true,
          enumKey: "NETWORK_TYPE"
        },
        {
          label: "当前状态",
          val: "currentState",
          type: "status",
          enumKey: "PROVINCE_PLATFORM_CURRENT_STATE",
        },
        {
          label: "接口状态",
          val: "interfaceState",
          type: "dic-val",
          enumKey: "PROVINCE_PLATFORM_CURRENT_STATE"
        },
        {
          label: "系统标识",
          val: "systemCode",
          type: "dic-val",
          enumKey: "SYSTEM_CODE"
        },
        {
          label: "上报时间",
          val: "timeStamp",
          type: "slot",
          slotName: "dateHandle",
          width: 200
        }
      ],
      dataList: [],
      tabs: [
        {
          label: "最近一次",
          key: "new"
        },
        {
          label: "历史",
          key: "old"
        }
      ],
      active: "new",
      describe: {
        new: {
          labels: [
            {
              frontMsg: "正常状态",
              backMsg: "省",
              numKey: "normal",
              keyClass: "is-mx-5 is-color-blue is-text-weight-700",
              msgClass: "is-mr-20"
            },
            {
              frontMsg: "异常状态",
              backMsg: "省",
              numKey: "abnormal",
              keyClass: "is-mx-5 is-color-orange is-text-weight-700",
              msgClass: "is-mr-20"
            }
          ]
        },
        old: {
          // labels: [
          //   {
          //     frontMsg: '最近',
          //     backMsg: '天数据',
          //     numKey: 'key1',
          //     keyClass: 'is-mx-5 is-color-blue is-text-weight-700',
          //     msgClass: 'is-mr-20'
          //   },
          //   {
          //     frontMsg: '异常数据',
          //     backMsg: '条',
          //     numKey: 'key2',
          //     keyClass: 'is-mx-5 is-color-orange is-text-weight-700',
          //     msgClass: 'is-mr-20'
          //   },
          //   {
          //     frontMsg: '未覆盖',
          //     backMsg: '条',
          //     numKey: 'key3',
          //     keyClass: 'is-mx-5 is-color-red is-text-weight-700',
          //     msgClass: 'is-mr-20'
          //   }
          // ]
        }
      },
      keyData: {
        normal: "0",
        abnormal: "0"
      }
    };
  },
  computed: {
    formDataLabel() {
      if (this.active === "new") {
        return [
          [
            {
              val: "comCode",
              type: "dic-select",
              enumKey: "ISP_CODE",
              funName: "changeItem",
              className: "is-one-fifth is-pr-10",
              placeholder: "请选择运营商信息"
            },
            {
              val: "networkBusinessId",
              type: "dic-select",
              enumKey: "NETWORK_TYPE",
              funName: "changeItem",
              className: "is-one-fifth is-pr-10",
              placeholder: "请选择网络类型"
            },
            {
              val: "provinceId",
              type: "dic-select",
              enumKey: "PROVINCE_CODE",
              funName: "changeItem",
              className: "is-one-fifth is-pr-10",
              placeholder: "请选择省级区域编码"
            },
            {
              val: "currentState",
              type: "dic-select",
              enumKey: "PROVINCE_PLATFORM_CURRENT_STATE",
              funName: "changeItem",
              className: "is-one-fifth is-pr-10",
              placeholder: "请选择当前状态"
            },
            {
              val: "systemCode",
              type: "dic-select",
              enumKey: "SYSTEM_CODE",
              funName: "changeItem",
              className: "is-one-fifth",
              placeholder: "请选择系统标识"
            }
          ]
        ];
      } else {
        return [
          [
            {
              val: "comCode",
              type: "dic-select",
              enumKey: "ISP_CODE",
              funName: "changeItem",
              className: "is-one-fifth is-pr-10",
              placeholder: "请选择运营商信息"
            },
            {
              val: "networkBusinessId",
              type: "dic-select",
              enumKey: "NETWORK_TYPE",
              funName: "changeItem",
              className: "is-one-fifth is-pr-10",
              placeholder: "请选择网络类型"
            },
            {
              val: "provinceId",
              type: "dic-select",
              enumKey: "PROVINCE_CODE",
              funName: "changeItem",
              className: "is-one-fifth is-pr-10",
              placeholder: "请选择省级区域编码"
            },
            {
              val: "currentState",
              type: "dic-select",
              enumKey: "PROVINCE_PLATFORM_CURRENT_STATE",
              funName: "changeItem",
              className: "is-one-fifth is-pr-10",
              placeholder: "请选择当前状态"
            },
            {
              val: "systemCode",
              type: "dic-select",
              enumKey: "SYSTEM_CODE",
              funName: "changeItem",
              className: "is-one-fifth",
              placeholder: "请选择系统标识"
            }
          ],
          [
            {
              val: "createDatetime",
              type: "date-time-range",
              funName: "changeItem",
              className: "is-4",
              startP: "请选择开始上报时间",
              endP: "请选择结束上报时间"
            }
          ]
        ];
      }
    },
    queryKey() {
      if (this.active === "new") {
        return [
          "comCode",
          "networkBusinessId",
          "provinceId",
          "currentState",
          "systemCode"
        ];
      } else {
        return [
          "comCode",
          "networkBusinessId",
          "provinceId",
          "currentState",
          "systemCode",
          "createDatetime"
        ];
      }
    }
  },
  watch: {},
  mounted() {
    this.formData = {
      createDatetime: []
    };
    for (let name in this.$route.query) {
      if (name === "size") {
        this.pagingInfo.size = Number(this.$route.query[name]);
      } else if (name === "current") {
        this.pagingInfo.current = Number(this.$route.query[name]);
      } else if (name === "active") {
        this.active = this.$route.query[name];
      } else {
        this.$set(this.formData, name, this.$route.query[name]);
        this.formData[name] = this.$route.query[name];
      }
    }
    this.getDataList();
  },
  methods: {
    // 表单监听回调
    changeItem(val) {
      if (val) {
        this.pagingInfo.current = 1;
      }
      this.debounce(this);
    },
    // 防抖查询
    debounce: debounce(vm => {
      let tmpData = {
        size: vm.pagingInfo.size,
        current: vm.pagingInfo.current,
        active: vm.active || "new"
      };
      vm.queryKey.map(e => {
        if (vm.formData[e]) vm.$set(tmpData, e, vm.formData[e]);
      });
      vm.$router.push({
        path: vm.$route.path,
        query: tmpData
      });
      vm.getDataList();
    }, 300),
    // 列表查询
    getDataList() {
      this.loading = true;
      if (this.active === "new") {
        let param = {};
        this.queryKey.map(e => {
          if (this.formData[e] && this.formData[e].length > 0)
            this.$set(param, e, this.formData[e]);
        });
        antiVpnService
          .getListAllLatestReportProvincePlatform(param)
          .then(res => {
            this.dataList = res || [];
            this.$set(
              this.keyData,
              "normal",
              this.dataList.filter(e => e.currentState === 0).length
            );
            this.$set(
              this.keyData,
              "abnormal",
              this.dataList.filter(e => e.currentState === 1).length
            );
            this.dataList.map(item => {
              this.$set(item, "interfaceState", "正常");
            });
            this.loading = false;
          })
          .catch(() => {
            this.loading = false;
          });
      } else {
        let param = {
          current: this.pagingInfo.current, //当前页
          size: this.pagingInfo.size //每页展示数量
        };
        this.queryKey.map(e => {
          if (this.formData[e]) {
            if (e === "createDatetime" && this.formData[e].length > 0) {
              this.$set(param, "createDatetimeStart", this.formData[e][0]);
              this.$set(param, "createDatetimeEnd", this.formData[e][1]);
            } else {
              this.$set(param, e, this.formData[e]);
            }
          }
        });
        antiVpnService
          .getProvincePlatformStatus(param)
          .then(res => {
            this.dataList = res.records || [];
            this.pagingInfo.total = res.total || 0;
            this.loading = false;
            this.dataList.map(item => {
              this.$set(item, "interfaceState", "正常");
            });
          })
          .catch(() => {
            this.loading = false;
          });
      }
    },
    // 每页数
    handleSizeChange(val) {
      this.pagingInfo.size = val;
      this.pagingInfo.current = 1;
      this.changeItem();
    },
    // 当前页
    handleCurrentChange(val) {
      this.pagingInfo.current = val;
      this.changeItem();
    },
    // 切换tabs
    handleClick() {
      this.formData = {
        createDatetime: []
      };
      this.pagingInfo.current = 1;
      this.$router.push({
        path: this.$route.path,
        query: { active: this.active }
      });
      this.getDataList();
    },
    // 时间转换
    timeConversion(date) {
      let dataTime = new Date(date).getTime();
      let systemTime = new Date().getTime();
      let finalTime = systemTime - dataTime;
      // 异常
      if (finalTime > 1000 * 60 * 15) return true;
      else return false;
    }
  }
};
</script>

<style lang="scss" scoped>
.activity-status {
  height: 100%;
  .activity-status-title-box {
    width: 5px;
    height: 22px;
    background: var(--primary-bg);
  }
  ::v-deep .public-table {
    height: calc(100% - 190px);
  }
  .orange-box {
    width: 14px;
    height: 14px;
    border-radius: 50%;
    background: #f47822;
  }
}
</style>
