package com.eversec.antivpn.intelligence.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.codec.Base64;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.XmlUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.eversec.antivpn.config.AntiVpnProperties;
import com.eversec.antivpn.config.enums.AntiVpnDeployPlaceEnum;
import com.eversec.antivpn.config.enums.CollectTypeEnum;
import com.eversec.antivpn.config.enums.ResponseCodeEnum;
import com.eversec.antivpn.intelligence.api.VpnIntelligenceCommandWebService;
import com.eversec.antivpn.intelligence.api.dto.VpnIntelligenceCommandDistributeDTO;
import com.eversec.antivpn.intelligence.api.dto.VpnIntelligenceCommandDistributeRequestDTO;
import com.eversec.antivpn.intelligence.api.dto.VpnIntelligenceCommandDistributeResponseDTO;
import com.eversec.antivpn.intelligence.api.enums.IntelligenceCommandOperationTypeEnum;
import com.eversec.antivpn.intelligence.config.WebServiceConfig;
import com.eversec.antivpn.intelligence.entity.VpnIntelligence;
import com.eversec.antivpn.intelligence.service.IVpnIntelligenceCommandService;
import com.eversec.antivpn.intelligence.service.IVpnIntelligenceService;
import com.eversec.antivpn.intelligence.service.impl.encrypt.CompressionFormatEnum;
import com.eversec.antivpn.intelligence.service.impl.encrypt.EncryptAlgorithmEnum;
import com.eversec.antivpn.intelligence.util.EncryptUtil;
import com.eversec.antivpn.intelligence.service.impl.encrypt.HashAlgorithmEnum;
import com.eversec.antivpn.support.config.api.PlatformInterfaceInfoApi;
import com.eversec.antivpn.support.config.api.dto.PlatformInterfaceInfoDTO;
import com.eversec.framework.webboot.common.exception.BusinessException;
import com.eversec.framework.webboot.result.HttpResultHandler;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.xml.XMLInputFactoryUtils;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@Service
@AllArgsConstructor
@Slf4j
public class VpnIntelligenceCommandWebServiceImpl implements VpnIntelligenceCommandWebService {

	private final IVpnIntelligenceCommandService vpnIntelligenceCommandService;

	private final IVpnIntelligenceService vpnIntelligenceService;

	private final AntiVpnProperties antiVpnProperties;

	private final PlatformInterfaceInfoApi platformInterfaceInfoApi;

	private final HttpServletRequest request;


	/**
	 * 事务管理
	 * @return
	 */
	private VpnIntelligenceCommandWebServiceImpl getThisService() {
		VpnIntelligenceCommandWebServiceImpl bean = SpringUtil.getBean(VpnIntelligenceCommandWebServiceImpl.class);
		return bean;
	}

	@SneakyThrows
	@Override
	public String idcCommandProvince(String comCode, Long provinceId, String randVal, String pwdHash, String command,
									 String commandHash, Integer commandType, Integer encryptAlgorithm, Integer hashAlgorithm, Integer compressionFormat, String commandVersion) {

		return doIdcCommandProvince(comCode, provinceId, randVal, pwdHash, command,
				commandHash, commandType, encryptAlgorithm, hashAlgorithm, compressionFormat, commandVersion, null, null);

	}

	@Override
	public String reDistributeIdcCommandProvince(String comCode, Long provinceId, String randVal, String pwdHash, String command, String commandHash, Integer commandType, Integer encryptAlgorithm, Integer hashAlgorithm, Integer compressionFormat, String commandVersion, String systemCode, Long reDistributeByCommandId) {
		return doIdcCommandProvince(comCode, provinceId, randVal, pwdHash, command,
				commandHash, commandType, encryptAlgorithm, hashAlgorithm, compressionFormat, commandVersion, systemCode, reDistributeByCommandId);
	}

	/**
	 * 一：集团部署
	 * 1. 透传到省端
	 * 2. 多线程解析入库
	 * <p>
	 * 二：省端部署
	 * 1. 解析、入库
	 * 2. 转为smart规则
	 * 3.调用smart
	 */
	@SneakyThrows
	private String doIdcCommandProvince(String comCode, Long provinceId, String randVal, String pwdHash, String command,
			String commandHash, Integer commandType, Integer encryptAlgorithm, Integer hashAlgorithm, Integer compressionFormat, String commandVersion, String systemCode, Long reDistributeByCommandId) {
		VpnIntelligenceCommandDistributeResponseDTO resultDto;
		log.info("接收情报库指令下发，当前平台部署模式为: {}", antiVpnProperties.getDeployPlace());
		if (systemCode == null) {
			systemCode = getSystemCode(request);
		}
		Exception responseException = null;
		Long commandEntityId = null;
		try {
			VpnIntelligenceCommandDistributeRequestDTO requestDto = VpnIntelligenceCommandDistributeRequestDTO.builder().comCode(comCode).provinceId(provinceId)
					.randVal(randVal).pwdHash(pwdHash).command(command).commandHash(commandHash).commandType(commandType).encryptAlgorithm(encryptAlgorithm)
					.hashAlgorithm(hashAlgorithm).compressionFormat(compressionFormat).commandVersion(commandVersion).build();


			// 1、新增command记录，记录请求参数和状态
			commandEntityId = vpnIntelligenceCommandService.saveDistributeCommand(requestDto, systemCode);


			// 1. 解析command
			VpnIntelligenceCommandDistributeDTO vpnIntelligenceCommandDistributeDTO = parseCommand(requestDto, systemCode);

			// 部署在集团，透传
			// 解析command、入库、复制文件到南向接口目录；调用smart
			vpnIntelligenceCommandService.commandDistribute(commandEntityId, vpnIntelligenceCommandDistributeDTO, systemCode, reDistributeByCommandId);

			// 调用南向接口
			VpnIntelligenceCommandDistributeResponseDTO responseDTO = vpnIntelligenceCommandService.callSouth(requestDto, systemCode);

			// 整合结果
			if (responseDTO != null && !responseDTO.getResultCode().equals( ResponseCodeEnum.处理完成.getCode())) {
				// 调用省平台失败
				resultDto = responseDTO;
			} else {
				resultDto = VpnIntelligenceCommandDistributeResponseDTO.buildSuccess();
			}

		} catch (BusinessException e) {
			responseException = e;
			log.error("处理指令失败", e);
			// 异常返回
			int errorCode = Integer.parseInt(HttpResultHandler.getResultConfig().convertErrorCode(e.getErrorCode()).toString());
			ResponseCodeEnum by = EnumUtil.getBy(ResponseCodeEnum::getCode, errorCode);
			if (by == null) {
				errorCode = ResponseCodeEnum.其他异常.getCode();
			}
			resultDto = VpnIntelligenceCommandDistributeResponseDTO.buildError(errorCode, ResponseCodeEnum.其他异常.getMessage() + "-" +  e.getErrorCode() + "-" + e.getMessage());
		} catch (Exception e) {
			responseException = e;
			log.error("处理指令失败", e);
			resultDto = VpnIntelligenceCommandDistributeResponseDTO.buildError(ResponseCodeEnum.其他异常.getCode(), ResponseCodeEnum.其他异常.getMessage());
		}

		vpnIntelligenceCommandService.updateCommandProcessStatus(commandEntityId, resultDto, responseException);
		return XmlUtil.mapToXmlStr(BeanUtil.beanToMap(resultDto), "return");
	}



	public static void main(String[] args) {
		VpnIntelligenceCommandDistributeResponseDTO resultDto = new VpnIntelligenceCommandDistributeResponseDTO();
		resultDto.setResultCode(0);
		resultDto.setMsg("成功");
		resultDto.setVersion("1.0");
		System.out.println(XmlUtil.mapToXmlStr(BeanUtil.beanToMap(resultDto), "return"));
	}


	/**
	 * 根据webservice请求参数获取systemCode
	 * @param request
	 * @return
	 */
	private String getSystemCode(HttpServletRequest request) {
		String systemCode = request.getParameter("systemCode");
		if (StrUtil.isBlank(systemCode)) {
			// 参数中没有，从url中获取

			// /services/IDCWebService/idcCommand/xxx
			// /services/IDCWebService/idcCommand
			String requestURI = request.getRequestURI();
			systemCode = StrUtil.subAfter(requestURI, WebServiceConfig.IDC_COMMAND_PATH, false);
			if (systemCode.startsWith("/")) {
				systemCode = StrUtil.subAfter(systemCode, "/", false);
			}
			systemCode = systemCode.split("/")[0];
		}
		if (StrUtil.isBlank(systemCode)) {
			systemCode = "";
		}
		return systemCode;
	}

	/**
	 * 企业侧系统收到指令文件后，首先对command进行base64反解码，
	 * 然后采用参数encryptAlgorithm指定的加密算法对解码后的数据进行解密处理，得到data；
	 * 针对data串接消息认证密钥后，使用hashAlgorithm指定的哈希算法计算哈希值，将得到的哈希值与收到的commandHash进行比较，如果一致，则对指令文件的完整性校验通过。
	 * 进一步按照compressionFormat指定的压缩格式对data进行解压后即得到指令信息。
	 * <p>
	 * 解析command
	 * command 中不包含情报集合，避免全量内容太大导致内存溢出
	 */
	private VpnIntelligenceCommandDistributeDTO parseCommand(VpnIntelligenceCommandDistributeRequestDTO requestDto, String systemCode) {

		log.info("开始解析指令，systemCode：{}", systemCode);
		PlatformInterfaceInfoDTO platformInterfaceInfoDTO;
		if (antiVpnProperties.getDeployPlace() == AntiVpnDeployPlaceEnum.GROUP) {
			platformInterfaceInfoDTO = platformInterfaceInfoApi.getByComCodeAndProvinceAndSystemCode(requestDto.getComCode(), requestDto.getProvinceId(), systemCode);
		} else {
			platformInterfaceInfoDTO = platformInterfaceInfoApi.getCurrentPlatform();
		}

		log.info("对应指令下发平台配置: {}", JSONUtil.toJsonPrettyStr(platformInterfaceInfoDTO));

		PlatformInterfaceInfoDTO.SecretKeyDTO secretKeyDTO = platformInterfaceInfoDTO.toSecretKeyDTO(platformInterfaceInfoDTO.getNorthIntelligenceSecretKey());

		HashAlgorithmEnum hashAlgorithmEnum = EnumUtil.getBy(HashAlgorithmEnum::getCode, requestDto.getHashAlgorithm());
		// 1. pwdHash
		// TODO: 2023/10/30 校验不通过
//		EncryptUtil.checkPwdHash(requestDto.getPwdHash(), secretKeyDTO.getMessageKey(), requestDto.getRandVal(), hashAlgorithmEnum);

		// 1. base64反解码
		log.info("对指令进行base64解码，{}", requestDto.getCommand());
		byte[] commandEncrypt = Base64.decode(requestDto.getCommand());
		// 2. 然后采用参数encryptAlgorithm指定的加密算法对解码后的数据进行解密处理
		byte[] commandDecrypt = EncryptUtil.decryptCommand(EnumUtil.getBy(EncryptAlgorithmEnum::getCode, requestDto.getEncryptAlgorithm()), commandEncrypt, secretKeyDTO.getAesKey(), secretKeyDTO.getAesOffsets());

		// 3. hash校验
		// TODO: 2023/10/30  校验不通过
//		EncryptUtil.checkCommandHash(hashAlgorithmEnum, commandDecrypt, requestDto.getCommandHash());

		// 4. 解压
		String commandXml = EncryptUtil.decompressCommand(EnumUtil.getBy(CompressionFormatEnum::getCode, requestDto.getCompressionFormat()), commandDecrypt);

		// 5. 指令xml转为指令对象集合
		VpnIntelligenceCommandDistributeDTO vpnIntelligenceCommandDistributeDTO = XmlUtil.xmlToBean(XmlUtil.readXML(commandXml).getDocumentElement(), VpnIntelligenceCommandDistributeDTO.class);
		log.info("下发指令对象：{}", JSONUtil.toJsonPrettyStr(vpnIntelligenceCommandDistributeDTO));
		return vpnIntelligenceCommandDistributeDTO;
	}

}
