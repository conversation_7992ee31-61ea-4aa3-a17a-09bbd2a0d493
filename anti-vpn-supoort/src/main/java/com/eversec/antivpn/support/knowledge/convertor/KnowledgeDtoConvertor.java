package com.eversec.antivpn.support.knowledge.convertor;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.eversec.antivpn.config.enums.AntiVpnDataDictTypeEnum;
import com.eversec.antivpn.support.knowledge.api.dto.KnowledgeAirportDTO;
import com.eversec.antivpn.support.knowledge.api.dto.KnowledgeBaseDTO;
import com.eversec.antivpn.support.knowledge.api.dto.KnowledgeProtocolDTO;
import com.eversec.antivpn.support.knowledge.api.dto.KnowledgeSoftwareDTO;
import com.eversec.stark.generic.sys.dto.SysDataDictDto;
import java.text.DateFormat;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 实体类和DTO转换类
 */
@Slf4j
@Component
@AllArgsConstructor
public class KnowledgeDtoConvertor {

	/**
	 * 基类转机场信息DTO
	 * @param domain
	 * @return
	 */
	@SneakyThrows
	public KnowledgeAirportDTO toAirportDto(KnowledgeBaseDTO domain) {

		KnowledgeAirportDTO dto = new KnowledgeAirportDTO();
		Map<String, Object> enumValExtend = domain.getEnumValExtend();
		BeanUtils.populate(dto, enumValExtend);
		BeanUtil.copyProperties(domain,dto);
		return dto;
	}

	/**
	 * 机场信息DTO转基类
	 * @param airportDTO
	 * @return
	 */
	public KnowledgeBaseDTO airporToDomain(KnowledgeAirportDTO airportDTO) {

		Map<String, Object> map = JSONObject.parseObject(JSONObject.toJSONString(airportDTO));
		KnowledgeBaseDTO domain = KnowledgeBaseDTO
			                    .builder()
			                    .id(airportDTO.getId())
			                    .enumKey(airportDTO.enumKey)
								.enumKey2(airportDTO.enumKey2)
								.enumVal(airportDTO.enumVal)
								.seq(airportDTO.seq)
								.createUser(airportDTO.createUser)
								.updateUser(airportDTO.updateUser)
								.typeKey(AntiVpnDataDictTypeEnum.AIRPORT_INFORMATION.name())
								.enumValExtend(map).build();
		return domain;
	}

	/**
	 * 软件信息DTO转基类
	 * @param dto
	 * @return
	 */
	public KnowledgeBaseDTO softwareToDomain(KnowledgeSoftwareDTO dto) {
		Map<String, Object> map = JSONObject.parseObject(JSONObject.toJSONString(dto));
		KnowledgeBaseDTO domain = KnowledgeBaseDTO
			.builder()
			.id(dto.getId())
			.enumKey(dto.enumKey)
			.enumKey2(dto.enumKey2)
			.enumVal(dto.enumVal)
			.seq(dto.seq)
			.createUser(dto.createUser)
			.updateUser(dto.updateUser)
			.typeKey(AntiVpnDataDictTypeEnum.SOFTWARE_INFORMATION.name())
			.enumValExtend(map).build();
		return domain;
	}


	/**
	 * 协议信息DTO转基类
	 * @param dto
	 * @return
	 */
	public KnowledgeBaseDTO protocolToDomain(KnowledgeProtocolDTO dto) {
		Map<String, Object> map = JSONObject.parseObject(JSONObject.toJSONString(dto));
		KnowledgeBaseDTO domain = KnowledgeBaseDTO
			.builder()
			.id(dto.getId())
			.enumKey(dto.enumKey)
			.enumKey2(dto.enumKey2)
			.enumVal(dto.enumVal)
			.seq(dto.seq)
			.createUser(dto.createUser)
			.updateUser(dto.updateUser)
			.typeKey(AntiVpnDataDictTypeEnum.PROTOCOL_INFORMATION.name())
			.enumValExtend(map).build();
		return domain;
	}

	/**
	 * 字典服务转换成知识库通用类
	 * @param dictDto
	 * @return
	 */
	public KnowledgeBaseDTO toBaseDTO(SysDataDictDto dictDto) {
		KnowledgeBaseDTO baseDTO =  KnowledgeBaseDTO.builder().build();
		BeanUtil.copyProperties(dictDto,baseDTO);
		// 格式化日期时间
		if(ObjectUtil.isNotEmpty(baseDTO.getCreateDatetime())){
			String dateTimeStr = DateUtil.format(baseDTO.getCreateDatetime(), "yyyy-MM-dd HH:mm:ss");
			baseDTO.setCreateDatetimeStr(dateTimeStr);
		}
		return baseDTO;
	}

	/**
	 * 字典服务转换成知识库机场类
	 * @param dictDto
	 * @return
	 */
	public KnowledgeAirportDTO dictToAirportDTO(SysDataDictDto dictDto) {
		KnowledgeBaseDTO baseDTO =  KnowledgeBaseDTO.builder().build();
		BeanUtil.copyProperties(dictDto,baseDTO);
		return toAirportDto(baseDTO);
	}

	/**
	 * 基类转软件信息DTO
	 * @param domain
	 * @return
	 */
	@SneakyThrows
	public KnowledgeSoftwareDTO toSoftwareDto(KnowledgeBaseDTO domain) {
		KnowledgeSoftwareDTO dto = new KnowledgeSoftwareDTO();
		Map<String, Object> enumValExtend = domain.getEnumValExtend();
		BeanUtils.populate(dto, enumValExtend);
		BeanUtil.copyProperties(domain,dto);
		return dto;
	}
	/**
	 * 基类转协议信息DTO
	 * @param domain
	 * @return
	 */
	@SneakyThrows
	public KnowledgeProtocolDTO toProtocolDto(KnowledgeBaseDTO domain) {
		KnowledgeProtocolDTO dto = new KnowledgeProtocolDTO();
		Map<String, Object> enumValExtend = domain.getEnumValExtend();
		BeanUtils.populate(dto, enumValExtend);
		BeanUtil.copyProperties(domain,dto);
		return dto;
	}


}
