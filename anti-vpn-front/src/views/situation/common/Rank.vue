<template>
  <div class="rankList mt20">
    <ul class="rankUl" v-if="listD.length">
      <li v-for="(item, i) in listD" :key="'rank' + item.name + i">
        <div class="idx">{{ i + 1 }}</div>
        <div class="name">
          {{ item.name }}
        </div>
        <div class="num">
          <div class="linebox">
            <div class="line" :class="i>1?'line2':''" :style="{ width: item.percent }"></div>
            <div class="linemask" :style="{ width: item.percent }"></div>
          </div>
        </div>
        <div class="percent">{{ item.percent }}</div>
      </li>
    </ul>
    <empty color="#fff" v-else />
  </div>
</template>
<script>
export default {
  props: {
    lists: {
      type: Array,
      default: () => []
    }
  },
  watch: {
    lists: {
      handler(val) {
        console.log('watch--', val)
        this.listD = val;
        this.init();
      },
      immediate: true
    },
  },
  data() {
    return {
      listD: [
        // { name: "IDC", num: 2000 },
        // { name: "4G", num: 1000 },
        // { name: "5G", num: 1000 },
        // { name: "IPS", num: 1000 },
        // { name: "DNS", num: 1000 },
        // { name: "DNS", num: 800 }
      ]
    };
  },
  methods: {
    init() {
      let total = this.listD.reduce((n, cur) => (n += cur.num), 0);
      // let max = Math.max.apply(null,this.listD.map(el => el.num));
      this.listD = this.listD.map(el => {
        return {
          ...el,
          percent: ((el.num * 100) / total).toFixed(0) + '%',
          // linePercent: ((el.num * 100) / max).toFixed(2) + "%"
        };
      });

      // this.listD = [
      //   { name: "IDC", num: 2000 },
      //   { name: "4G", num: 1000 },
      //   { name: "5G", num: 1000 },
      //   { name: "IPS", num: 1000 },
      //   { name: "DNS", num: 1000 },
      //   { name: "DNS", num: 800 },
      //   { name: "DNS", num: 800 },
      // ]
    }
  }
};
</script>

<style scoped lang="scss">
.rankList {
  // height: 310px;
  height: 250px;

  color: #fff;
  overflow-y: auto;
  .rankUl {
    > li {
      margin-bottom: 7px;
      > div {
        text-align: center;
        background: rgba(205, 146, 80, 0.07);
        &:not(:last-child) {
          margin-right: 4px;
        }
      }
      display: flex;
      height: 37px;
      line-height: 37px;
      .idx {
        width: 37px;
      }
      .name {
        width: 85px;
        text-align: left;
        padding: 0 10px;
        position: relative;
      }
      .num {
        width: 185px;
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;
      }
      .linebox {
        width: 180px;
        height: 11px;
        background: url(./img/lineBg.png) no-repeat;
        padding-top: 1px;
        position: relative;
        // position: absolute;

        .line {
          height: 9px;
          background: url(./img/line1.png) no-repeat top right;
        }
        .line2 {
          background: url(./img/line2.png) no-repeat top right;
        }
        .linemask {
          height: 9px;
          background: linear-gradient(to right, rgba(0,0,0,.9), rgba(0,0,0,.05));
          position: absolute;
          left: 0;
          top: 0;
          z-index: 3;
          // background: ;
        }
      }
      .percent {
        width: 115px;
      }
      &:nth-of-type(2n-1) {
        > div {
          background: rgba(76, 218, 254, 0.07);
        }
      }
      &:nth-of-type(1),
      &:nth-of-type(2) {
        > div {
          // background: rgba(76, 218, 254, 0.07);
        }
        .percent {
          color: #ce914e;
        }
        .name {
          .linebox .line {
            background: #cd9250;
          }
        }
      }
    }
  }
}
</style>
