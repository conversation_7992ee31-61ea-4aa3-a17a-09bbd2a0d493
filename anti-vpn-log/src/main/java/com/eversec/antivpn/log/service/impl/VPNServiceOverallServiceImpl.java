package com.eversec.antivpn.log.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.eversec.antivpn.config.enums.AntiVpnDataDictTypeEnum;
import com.eversec.antivpn.emnus.ScreenTypeEnum;
import com.eversec.antivpn.log.api.dto.base.AggCountWeekDTO;
import com.eversec.antivpn.log.entity.po.GenericSysDataDict;
import com.eversec.antivpn.log.mapper.*;
import com.eversec.antivpn.log.mapper.vo.*;
import com.eversec.antivpn.log.service.GenericSysDataDictService;
import com.eversec.antivpn.log.service.VPNServiceOverallService;
import com.eversec.antivpn.log.util.AggregateDataUtil;
import com.eversec.antivpn.log.util.CalculateUtil;
import com.eversec.antivpn.log.util.SortUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/8/15 17:28
 **/
@Service
public class VPNServiceOverallServiceImpl implements VPNServiceOverallService {
    @Resource
    private LogVpnIntelligenceMapper logVpnIntelligenceMapper;

    @Resource
    private E2MachineLearningLogViewMapper e2MachineLearningLogViewMapper;

    @Resource
    private E1CommLogUserViewMapper e1CommLogUserViewMapper;

    @Resource
    private GenericSysDataDictService genericSysDataDictService;

    @Override
    public Long getVPNServiceCount() {
        return logVpnIntelligenceMapper.getVpnServiceCount();
    }

    @Override
    public Long getResourceNodeCount() {
        Long vpnIntelligenceCount = logVpnIntelligenceMapper.selectVpnIntelligenceCount();
        BaseSearchVO baseSearchVO = new BaseSearchVO();
        Long resourceNodeCount = e2MachineLearningLogViewMapper.getResourceNodeCount(baseSearchVO);
        return vpnIntelligenceCount + resourceNodeCount;
    }

    @Override
    public AggCountWeekDTO getLastSevenDayActiveVPNServiceNum() {
        AggCountWeekDTO aggCountWeekDTO = new AggCountWeekDTO();
        BaseSearchVO baseSearchVO = new BaseSearchVO();
        Long thisWeekTimeCKPartition = ScreenTypeEnum.getTimeCKPartition(ScreenTypeEnum.THISWEEK);
        baseSearchVO.setStartTimePartition(thisWeekTimeCKPartition);
        Long nowWeekNum = e1CommLogUserViewMapper.getVpnAirportCodeCount(baseSearchVO);
        baseSearchVO.clear();
        baseSearchVO.setStartTimePartition(thisWeekTimeCKPartition - 7);
        baseSearchVO.setEndTimePartition(thisWeekTimeCKPartition - 1);
        Long lastWeekNum = e1CommLogUserViewMapper.getVpnAirportCodeCount(baseSearchVO);
        aggCountWeekDTO.setNowWeek(nowWeekNum);
        aggCountWeekDTO.setLastWeek(lastWeekNum);
        aggCountWeekDTO.setIncreasePercent(CalculateUtil.cpuCyclePercent(nowWeekNum, lastWeekNum));
        return aggCountWeekDTO;
    }


    @Override
    public AggCountWeekDTO getLastSevenDayActiveResourceNodeNum() {
        AggCountWeekDTO aggCountWeekDTO = new AggCountWeekDTO();
        BaseSearchVO baseSearchVO = new BaseSearchVO();
        Long thisWeekPartition = ScreenTypeEnum.getTimeCKPartition(ScreenTypeEnum.THISWEEK);
        Long beforeWeekPartition = ScreenTypeEnum.getTimeCKPartition(ScreenTypeEnum.THISWEEK_BEFORE);
        baseSearchVO.setStartTimePartition(thisWeekPartition);
        Long vpnIdNum = e1CommLogUserViewMapper.getDistinctVpnIdNum(baseSearchVO);
        Long nowWeekNum = e2MachineLearningLogViewMapper.getResourceNodeCount(baseSearchVO);
        Long nowWeekResourceNodeNum = vpnIdNum + nowWeekNum;
        baseSearchVO.clear();
        baseSearchVO.setStartTimePartition(beforeWeekPartition);
        baseSearchVO.setEndTimePartition(thisWeekPartition - 1);
        Long lastWeekVpnIdNum = e1CommLogUserViewMapper.getDistinctVpnIdNum(baseSearchVO);
        Long lastWeekNum = e2MachineLearningLogViewMapper.getResourceNodeCount(baseSearchVO);
        Long lastWeekResourceNodeNum = lastWeekVpnIdNum + lastWeekNum;
        aggCountWeekDTO.setNowWeek(nowWeekResourceNodeNum);
        aggCountWeekDTO.setLastWeek(lastWeekResourceNodeNum);
        aggCountWeekDTO.setIncreasePercent(CalculateUtil.cpuCyclePercent(nowWeekResourceNodeNum, lastWeekResourceNodeNum));
        return aggCountWeekDTO;
    }

    @Override
    public List<VpnAggVO> getUsedResourceNodeTop10(ScreenTypeEnum screenTypeEnum) {
        List<VpnAggVO> result = new ArrayList<>();
        BaseSearchVO baseSearchVO = new BaseSearchVO();
        baseSearchVO.setStartTimePartition(ScreenTypeEnum.getTimeCKPartition(screenTypeEnum));
        List<VpnResourceNodeAggVO> e1VpnResourceNodeAggVO = e1CommLogUserViewMapper.getResourceNodeUserNum(baseSearchVO);
        removeUseLessData(e1VpnResourceNodeAggVO);
        List<VpnAggVO> e1Data = dealVpnResourceNodeAggVO(e1VpnResourceNodeAggVO);
        List<VpnAggVO> e2Data = e2MachineLearningLogViewMapper.getResourceNodeUserNum(baseSearchVO);
        e1Data.addAll(e2Data);
        SortUtil.sortVpnAggVOPersonDesc(e1Data);
        for (int i = 0; i < e1Data.size() && i < 10; i++) {
            result.add(e1Data.get(i));
        }
        return result;
    }

    private void removeUseLessData(List<VpnResourceNodeAggVO> list) {
        if (list == null || list.isEmpty()) return;
        Iterator<VpnResourceNodeAggVO> iterator = list.iterator();
        while (iterator.hasNext()) {
            VpnResourceNodeAggVO vo = iterator.next();
            if (vo.getVpnId() == null || "".equals(vo.getVpnId())) iterator.remove();
        }
    }

    private List<VpnAggVO> dealVpnResourceNodeAggVO(List<VpnResourceNodeAggVO> list) {
        List<VpnAggVO> result = new ArrayList<>();
        if (list == null || list.isEmpty()) return result;
        setVpnIP(list);
        setVpnServiceName(list);
        list.forEach(vo -> {
            VpnAggVO vpnAggVO = new VpnAggVO();
            vpnAggVO.setVpnServiceName(vo.getVpnServiceName());
            vpnAggVO.setResourceNodeName(vo.getResourceNodeName());
            vpnAggVO.setPersonNum(vo.getNum());
            result.add(vpnAggVO);
        });
        return result;
    }

    private void setVpnIP(List<VpnResourceNodeAggVO> list) {
        if (list.isEmpty()) return;
        List<Long> vpnIds = new ArrayList<>();
        list.forEach(vo -> {
            if (StringUtils.isBlank(vo.getVpnId())) return;
            Long vpnId = Long.valueOf(vo.getVpnId());
            vpnIds.add(vpnId);
        });
        List<VpnBaseVO> vpnBaseVOList = logVpnIntelligenceMapper.getVpnIPByVpnId(vpnIds);
        Map<Long, VpnBaseVO> map = new HashMap<>();
        vpnBaseVOList.forEach(vpnBaseVO -> {
            map.put(vpnBaseVO.getVpnId(), vpnBaseVO);
        });
        list.forEach(vo -> {
            if (vo.getVpnId() == null || "".equals(vo.getVpnId())) return;
            VpnBaseVO vpnBaseVO = map.get(Long.valueOf(vo.getVpnId()));
            if (vpnBaseVO != null) vo.setResourceNodeName(vpnBaseVO.getVpnIp());
        });
    }

    private void setVpnServiceName(List<VpnResourceNodeAggVO> list) {
        if (list.isEmpty()) return;
        List<String> vpnServiceCodes = new ArrayList<>();
        list.forEach(vo -> {
            if (!StringUtils.isBlank(vo.getVpnAirportCode())) {
                vpnServiceCodes.add(vo.getVpnAirportCode());
            }
        });
        if (vpnServiceCodes.isEmpty()) return;
        List<DictVO> dictVOList = logVpnIntelligenceMapper.getVpnAirportCodeAndName(vpnServiceCodes);
        Map<String, DictVO> dictMap = new HashMap<>();
        dictVOList.forEach(dictVO -> {
            dictMap.put(dictVO.getVpnAirportCode(), dictVO);
        });
        list.forEach(vo -> {
            if (dictMap.get(vo.getVpnAirportCode()) != null)
                vo.setVpnServiceName(dictMap.get(vo.getVpnAirportCode()).getVpnAirportName());
        });
    }

    @Override
    public List<VpnHistogramAggVO> getVpnServiceAndResourceNodeDateHistogram(ScreenTypeEnum screenTypeEnum) {
        BaseSearchVO baseSearchVO = new BaseSearchVO();
        baseSearchVO.setScreenTypeEnumCode(ScreenTypeEnum.THISWEEK.getCode());
        baseSearchVO.setStartTimePartition(ScreenTypeEnum.getTimeCKPartition(ScreenTypeEnum.THISWEEK));
        baseSearchVO.setEndTimePartition(ScreenTypeEnum.getTimeCKPartition(ScreenTypeEnum.TODAY));
        List<VpnHistogramAggVO> e1Data = e1CommLogUserViewMapper.getVpnServiceAndResourceNodeDateHistogram(baseSearchVO);
        List<VpnHistogramAggVO> e2Data = e2MachineLearningLogViewMapper.getResourceNodeDateHistogram(baseSearchVO);
        return AggregateDataUtil.mergeVpnHistogramAggVO(e1Data, e2Data);
    }


    @Override
    public List<VpnMapAggVO> map(ScreenTypeEnum screenTypeEnum1) {
        QueryWrapper<GenericSysDataDict> queryWrapper = new QueryWrapper();
        queryWrapper.select("json_extract( enum_val_extend, '$.registered' ) as key_type , count(*)  num");
        List<VpnMapAggVO> vpnMapAggVOList = getDictJSonQueryWrapper(queryWrapper, false);
        List<VpnMapAggVO> resourceNodeAggList = logVpnIntelligenceMapper.getCountryResourceNodeAgg();
        delaIllLegalSymbolKey(vpnMapAggVOList);
        delaIllLegalSymbolKey(resourceNodeAggList);
        List<VpnMapAggVO> result = mergeVpnServiceAndResourceNodeNum(vpnMapAggVOList, resourceNodeAggList);
        return result;
    }

    private List<VpnMapAggVO> mergeVpnServiceAndResourceNodeNum(List<VpnMapAggVO> vpnMapAggVOList, List<VpnMapAggVO> resourceNodeAggList) {
        List<VpnMapAggVO> result = new ArrayList<>();
        Map<String, VpnMapAggVO> map = new HashMap<>();
        vpnMapAggVOList.forEach(vpnMapAggVO -> {
            map.put(vpnMapAggVO.getKey(), vpnMapAggVO);
        });
        result.addAll(vpnMapAggVOList);
        resourceNodeAggList.forEach(vpnMapAggVO -> {
            if (!map.containsKey(vpnMapAggVO.getKey())) {
                result.add(vpnMapAggVO);
            } else {
                map.get(vpnMapAggVO.getKey()).setResourceNodeNum(vpnMapAggVO.getResourceNodeNum());
            }
        });
        return result;
    }

    private void delaIllLegalSymbolKey(List<VpnMapAggVO> dataList) {
        Iterator<VpnMapAggVO> iterator = dataList.iterator();
        while (iterator.hasNext()) {
            VpnMapAggVO vpnMapAggVO = iterator.next();
            if (StringUtils.isBlank(vpnMapAggVO.getKey()) || "".equals(vpnMapAggVO.getKey())) {
                iterator.remove();
                continue;
            }
            String dealKey = vpnMapAggVO.getKey().replace("\"", "");
            vpnMapAggVO.setKey(dealKey);
        }
    }

    @Override
    public List<TypeCountVO> getCountryResourceNodeNumTop10(ScreenTypeEnum screenTypeEnum) {
        BaseSearchVO baseSearchVO = new BaseSearchVO();
        baseSearchVO.setStartTimePartition(ScreenTypeEnum.getTimeCKPartition(screenTypeEnum));
        List<TypeCountVO> typeCountVOList = logVpnIntelligenceMapper.getCountryResourceNodeNumTop10();
        List<TypeCountVO> e2DataList = e2MachineLearningLogViewMapper.getCountryResourceNodeNumTop10(baseSearchVO);
        List<TypeCountVO> mergeTypeCountVOList = AggregateDataUtil.mergeTypeCountVOSameKeyString(typeCountVOList, e2DataList);
        SortUtil.sortTypeCountVODesc(mergeTypeCountVOList);
        List<TypeCountVO> result = new ArrayList<>();
        for (int i = 0; i < 10 && i < mergeTypeCountVOList.size(); i++) {
            result.add(mergeTypeCountVOList.get(i));
        }
        return result;
    }

    @Override
    public List<TypeCountVO> getVpnServicePersonNumTop10(ScreenTypeEnum screenTypeEnum) {
        BaseSearchVO baseSearchVO = new BaseSearchVO();
        baseSearchVO.setStartTimePartition(ScreenTypeEnum.getTimeCKPartition(screenTypeEnum));
        List<TypeCountVO> dataList = e1CommLogUserViewMapper.getVpnServicePersonNumTop10(baseSearchVO);
        List<String> vpnServiceCodes = new ArrayList<>();
        dataList.forEach(vo -> {
            vpnServiceCodes.add(vo.getKey());
        });
        translateVpnServiceName(dataList);
        removeUselessData(dataList);
        SortUtil.sortTypeCountVODesc(dataList);
        return dataList;
    }

    private void removeUselessData(List<TypeCountVO> dataList) {
        Iterator<TypeCountVO> iterator = dataList.iterator();
        while (iterator.hasNext()) {
            TypeCountVO vo = iterator.next();
            if (vo.getKey() == null || "".equals(vo.getKey())) {
                iterator.remove();
                ;
            }
        }
    }

    private void translateVpnServiceName(List<TypeCountVO> dataList) {
        List<String> vpnServiceCodes = new ArrayList<>();
        dataList.forEach(vo -> {
            if (vo.getKey() != null && !"".equals(vo.getKey())) vpnServiceCodes.add(vo.getKey());
        });
        if (vpnServiceCodes.isEmpty()) return;
        List<DictVO> dictVOList = logVpnIntelligenceMapper.getVpnAirportCodeAndName(vpnServiceCodes);
        Map<String, String> dictMap = new HashMap<>();
        dictVOList.forEach(dictVO -> {
            dictMap.put(dictVO.getVpnAirportCode(), dictVO.getVpnAirportName());
        });
        dataList.forEach(vo -> {
            if (dictMap.containsKey(vo.getKey())) vo.setKey(dictMap.get(vo.getKey()));
        });
    }

    @Override
    public List<VpnServicePageVO> getTheNewestVpnService(Integer num) {
        List<VpnServicePageVO> dataList = logVpnIntelligenceMapper.getVpnServicePage(num);
        List<String> vpnAirportCodes = new ArrayList<>();
        Map<String, VpnServicePageVO> vpnServicePageVOMap = new HashMap<>();
        dataList.forEach(vo -> {
            vpnAirportCodes.add(vo.getVpnAirportCode());
            vpnServicePageVOMap.put(vo.getVpnAirportCode(), vo);
        });
        if (!vpnAirportCodes.isEmpty()) {
            List<TypeCountVO> pageInfoList = e1CommLogUserViewMapper.getVpnServicePageInfo(vpnAirportCodes);
            pageInfoList.forEach(vo -> {
                VpnServicePageVO vpnServicePageVO = vpnServicePageVOMap.get(vo.getKey());
                vpnServicePageVO.setCustomerNum(vo.getNum());
                vpnServicePageVO.setServiceTimes(vo.getNum1());
            });
        }
        setResourceNodeNum(vpnAirportCodes, dataList);
        dataList.sort((a, b) -> {
            int aNum = 0;
            if (a.getCustomerNum() != null) aNum++;
            if (a.getServiceTimes() != null) aNum++;
            if (a.getResourceNodeNum() != null) aNum++;
            int bNum = 0;
            if (b.getCustomerNum() != null) bNum++;
            if (b.getServiceTimes() != null) bNum++;
            if (b.getResourceNodeNum() != null) bNum++;
            if (aNum > bNum) {
                return -1;
            } else if (aNum == bNum) {
                return 0;
            } else {
                return -1;
            }
        });
        return dataList;
    }

    private void setResourceNodeNum(List<String> vpnAirportCodes, List<VpnServicePageVO> dataList) {
        if (vpnAirportCodes.isEmpty() || vpnAirportCodes.isEmpty()) return;
        List<TypeCountVO> typeCountVOList = logVpnIntelligenceMapper.getVpnServiceResourceNodeNum(vpnAirportCodes);
        if (typeCountVOList.isEmpty()) return;
        Map<String, Long> map = new HashMap<>();
        typeCountVOList.forEach(vo -> map.put(vo.getKey(), vo.getNum()));
        dataList.forEach(vo -> {
            if (map.containsKey(vo.getVpnAirportCode())) vo.setResourceNodeNum(map.get(vo.getVpnAirportCode()));
        });

    }


    private List<VpnMapAggVO> getDictJSonQueryWrapper(QueryWrapper<GenericSysDataDict> queryWrapper, Boolean limitStatus) {
        List<VpnMapAggVO> result = new ArrayList<>();
        queryWrapper.eq("type_key", AntiVpnDataDictTypeEnum.AIRPORT_INFORMATION.name());
        queryWrapper.groupBy("key_type");
        queryWrapper.orderByDesc("num");
        if (limitStatus) {
            queryWrapper.last("limit 10");
        }
        List<GenericSysDataDict> sysDataDicts = genericSysDataDictService.list(queryWrapper);
        sysDataDicts.forEach(dict -> {
            VpnMapAggVO dto = new VpnMapAggVO();
            dto.setKey(dict.getKeyType());
            dto.setVpnServiceNum(dict.getNum());
            result.add(dto);
        });
        return result;
    }

    public List<VpnMapAggVO> getVpnServiceRegisteredTop10() {
        QueryWrapper<GenericSysDataDict> queryWrapper = new QueryWrapper();
        queryWrapper.select(" json_extract( enum_val_extend, '$.registered' ) AS key_type, count(*)  num");
        return getDictJSonQueryWrapper(queryWrapper, true);

    }


}
