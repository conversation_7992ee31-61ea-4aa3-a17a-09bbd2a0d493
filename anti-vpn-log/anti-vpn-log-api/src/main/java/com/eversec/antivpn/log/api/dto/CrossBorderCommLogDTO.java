package com.eversec.antivpn.log.api.dto;

import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * DTO
 * </p>
 * <p>
 * api接口数据传输对象（用于save、update接收参数，list、page、find返回对象等）
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-25
 */
@Getter
@Setter
@Schema(name = "CrossBorderCommLogDTO", description = "")
public class CrossBorderCommLogDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String version;

    private String comCode;

    private Integer networkBusinessId;

    private Long logId;

    private Long houseId;

    private String houseName;

    private Long logProvinceId;

    private Long logCityId;

    private Long logCountyId;

    private String srcIp;

    private Long srcPort;

    private String srcCountry;

    private String srcProvinceId;

    private String srcInfo;

    private String destIp;

    private Long destPort;

    private String dstCountry;

    private String dstProvinceId;

    private String destInfo;

    private Long trafficType;

    private Long protocolType;

    private Long applicationProtocol;

    private String url;

    private String contentType;

    private Long vpnId;

    private String vpnName;

    private Integer typeId;

    private Integer vpnContentType;

    private String vpnContent;

    private String userAgent;

    private String msisdnMd5;

    private String maskMsisdn;

    private String phoneProvinceId;

    private String phoneCityId;

    private String imsi;

    private String imei;

    private String apn;

    private String lac;

    private String ci;

    private String certInfoVersion;

    private String certInfoSerialNumber;

    private String certInfoAlgorithm;

    private String certInfoIssuer;

    private String certInfoAlidity;

    private String certInfoSubject;

    private LocalDateTime accessTime;

    private LocalDateTime createTime;

    private LocalDateTime timestamp;

    private Long day;

    private Integer hour;

    private Integer statusCode;

    private LocalDateTime endTime;

}