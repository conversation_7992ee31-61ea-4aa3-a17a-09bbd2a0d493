package com.eversec.antivpn.intelligence.factory;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.codec.Base64;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.eversec.antivpn.config.enums.AntiVpnDataDictTypeEnum;
import com.eversec.antivpn.intelligence.api.dto.VpnIntelligenceCommandDistributeDTO;
import com.eversec.antivpn.intelligence.api.dto.VpnIntelligenceDTO;
import com.eversec.antivpn.intelligence.api.dto.VpnIntelligenceFullDistributeDTO;
import com.eversec.antivpn.intelligence.api.enums.IntelligenceCommandDistributeStatusEnum;
import com.eversec.antivpn.intelligence.api.enums.IntelligenceCommandReportStatusEnum;
import com.eversec.antivpn.intelligence.api.enums.IntelligenceContentTypeEnum;
import com.eversec.antivpn.intelligence.api.enums.IntelligenceSourceEnum;
import com.eversec.antivpn.intelligence.dto.VpnIntelligenceImportDTO;
import com.eversec.antivpn.intelligence.entity.VpnIntelligence;
import com.eversec.stark.generic.sys.api.SysDataDictApi;
import com.eversec.stark.generic.sys.dto.SysDataDictDto;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;

@Component
public class VpnIntelligenceFactory {

    private final SysDataDictApi sysDataDictApi;


    /**
     * 部侧协议作为key
     */
    private ThreadLocal<LinkedHashMap<String, LinkedHashMap<String, SysDataDictDto>>> dictMap = new ThreadLocal<>();
    /**
     * eversec协议作为key
     */
    private ThreadLocal<LinkedHashMap<String, LinkedHashMap<String, SysDataDictDto>>> eversecProtocolKeyDictMap = new ThreadLocal<>();

    public VpnIntelligenceFactory(SysDataDictApi sysDataDictApi) {
        this.sysDataDictApi = sysDataDictApi;
    }

    public void refDataDict() {
        LinkedHashMap<String, LinkedHashMap<String, SysDataDictDto>> stringLinkedHashMapLinkedHashMap2 = sysDataDictApi.dictMapByTypeKey2(
                ListUtil.toList(AntiVpnDataDictTypeEnum.PROTOCOL_INFORMATION.name()));
        LinkedHashMap<String, LinkedHashMap<String, SysDataDictDto>> stringLinkedHashMapLinkedHashMap = sysDataDictApi.dictMapByType(
                ListUtil.toList(AntiVpnDataDictTypeEnum.PROTOCOL_INFORMATION.name(), AntiVpnDataDictTypeEnum.AIRPORT_INFORMATION.name(), AntiVpnDataDictTypeEnum.SOFTWARE_INFORMATION.name())
        );

        dictMap.set(stringLinkedHashMapLinkedHashMap);
        eversecProtocolKeyDictMap.set(stringLinkedHashMapLinkedHashMap2);

    }

    public VpnIntelligenceImportDTO csvToEntity(List<String> rowCsv) {
        VpnIntelligenceImportDTO importDTO = new VpnIntelligenceImportDTO();
        importDTO.setTypeId(NumberUtil.parseInt(rowCsv.get(0)));
        importDTO.setVpnId(NumberUtil.parseLong(rowCsv.get(1)));
        importDTO.setVpnName(rowCsv.get(2));
        importDTO.setVpnDomain(rowCsv.get(3));
        importDTO.setVpnIp(rowCsv.get(4));
        importDTO.setVpnUrlDecode(rowCsv.get(5));
        importDTO.setVpnLink(rowCsv.get(6));
        importDTO.setVpnPort(rowCsv.get(7));
        importDTO.setVpnCountry(rowCsv.get(8));
        importDTO.setVpnAirportCode(rowCsv.get(9));
        importDTO.setVpnSoftwareCodes(rowCsv.get(10));
        importDTO.setVpnProtocolCode(rowCsv.get(11));
        importDTO.setVpnProtocolEversecCode(rowCsv.get(12));
        importDTO.setApplicationProtocolCode(rowCsv.get(13));
        importDTO.setTimeStamp(rowCsv.get(14));
        importDTO.setAttachMent(rowCsv.get(15));
        return importDTO;
    }

    private String blankToNull(String input) {

        if (StrUtil.isBlank(input)) {
            return null;
        } else {
            return input;
        }
    }

    public VpnIntelligenceImportDTO entityToImportDto(VpnIntelligence vpnIntelligence){
        VpnIntelligenceImportDTO importDTO = new VpnIntelligenceImportDTO();
        importDTO.setTypeId(vpnIntelligence.getTypeId());
        importDTO.setVpnId(vpnIntelligence.getVpnId());
        importDTO.setVpnName(vpnIntelligence.getVpnName());
        importDTO.setVpnDomain(vpnIntelligence.getVpnDomain());
        importDTO.setVpnIp(vpnIntelligence.getVpnIp());
        importDTO.setVpnUrlDecode(vpnIntelligence.getVpnUrlDecode());
        importDTO.setVpnLink(vpnIntelligence.getVpnLink());
        importDTO.setVpnPort(vpnIntelligence.getVpnPort());
        importDTO.setVpnCountry(vpnIntelligence.getVpnCountry());
        importDTO.setVpnAirportCode(vpnIntelligence.getVpnAirportCode());
        importDTO.setVpnSoftwareCodes(vpnIntelligence.getVpnSoftwareCodes());
        importDTO.setVpnProtocolCode(vpnIntelligence.getVpnProtocolCode());
        importDTO.setVpnProtocolEversecCode(vpnIntelligence.getVpnProtocolEversecCode());
        importDTO.setApplicationProtocolCode(vpnIntelligence.getApplicationProtocolCode());
        importDTO.setTimeStamp(vpnIntelligence.getTimeStamp());
        return importDTO;
    }


    public VpnIntelligence importDtoToEntity(VpnIntelligenceImportDTO dto, String comCode, Long provinceId, String systemCode, Long commandId) {
        VpnIntelligence vpnIntelligence = new VpnIntelligence();
        vpnIntelligence.setCommandId(commandId);
        vpnIntelligence.setComCode(comCode);
        vpnIntelligence.setProvinceId(provinceId);
        vpnIntelligence.setSystemCode(systemCode);
        vpnIntelligence.setSource(IntelligenceSourceEnum.INPUT.name());

        vpnIntelligence.setTimeStamp(dto.getTimeStamp());

        vpnIntelligence.setTypeId(dto.getTypeId());
        vpnIntelligence.setVpnId(dto.getVpnId());
        setRuleIdAndVpnId(vpnIntelligence);
        vpnIntelligence.setVpnName(dto.getVpnName());

        vpnIntelligence.setVpnDomain(dto.getVpnDomain());
        vpnIntelligence.setVpnIp(dto.getVpnIp());
        if (dto.getVpnUrlDecode() != null) {
            vpnIntelligence.setVpnUrl(Base64.encode(dto.getVpnUrlDecode()));
        }
        vpnIntelligence.setVpnUrlDecode(dto.getVpnUrlDecode());
        vpnIntelligence.setVpnLink(dto.getVpnLink());
        vpnIntelligence.setAttachMent(dto.getAttachMent());
        vpnIntelligence.setVpnProtocolCode(dto.getVpnProtocolCode());

        vpnIntelligence.setVpnMessage(null);
        vpnIntelligence.setVpnPort(dto.getVpnPort());
        vpnIntelligence.setVpnAirportCode(dto.getVpnAirportCode());
        if (StrUtil.isNotBlank(dto.getVpnAirportCode())) {
            LinkedHashMap<String, SysDataDictDto> stringSysDataDictDtoLinkedHashMap = dictMap.get().get(AntiVpnDataDictTypeEnum.AIRPORT_INFORMATION.name());
            if (stringSysDataDictDtoLinkedHashMap != null) {
                SysDataDictDto sysDataDictDto = stringSysDataDictDtoLinkedHashMap.get(dto.getVpnAirportCode());
                if (sysDataDictDto != null) {
                    vpnIntelligence.setVpnAirportName(sysDataDictDto.getEnumVal());
                }
            }
        }
        vpnIntelligence.setVpnCountry(dto.getVpnCountry());
        vpnIntelligence.setApplicationProtocolCode(dto.getApplicationProtocolCode());
        if (StrUtil.isNotBlank(dto.getVpnSoftwareCodes())) {
            String[] softwareCodes = dto.getVpnSoftwareCodes().split(",");
            vpnIntelligence.setVpnSoftwareCodes(JSONUtil.toJsonStr(softwareCodes));
            LinkedHashMap<String, SysDataDictDto> stringSysDataDictDtoLinkedHashMap = dictMap.get().get(AntiVpnDataDictTypeEnum.AIRPORT_INFORMATION.name());
            if (stringSysDataDictDtoLinkedHashMap != null) {
                List<String> softwareNameList = new ArrayList<>();
                for (String softwareCode : softwareCodes) {
                    SysDataDictDto sysDataDictDto = stringSysDataDictDtoLinkedHashMap.get(softwareCode);
                    if (sysDataDictDto != null) {
                        softwareNameList.add(sysDataDictDto.getEnumVal());
                    }
                }
                vpnIntelligence.setVpnSoftwareNames(JSONUtil.toJsonStr(softwareNameList));
            }
        }
        vpnIntelligence.setVpnProtocolEversecCode(dto.getVpnProtocolEversecCode());
        vpnIntelligence.setReportStatus(IntelligenceCommandReportStatusEnum.TO_BE_REPORTED.name());
        vpnIntelligence.setDistributeStatus(IntelligenceCommandDistributeStatusEnum.TO_BE_DISTRIBUTED.name());
        setVpnContent(vpnIntelligence);

        return vpnIntelligence;
    }

    /**
     * entity转换为dto，用于页面查询
     * @param entity
     * @return
     */
    public VpnIntelligenceDTO entityToDto(VpnIntelligence entity) {
        VpnIntelligenceDTO dto = new VpnIntelligenceDTO();
        BeanUtil.copyProperties(entity, dto);
        String vpnSoftwareCodes = entity.getVpnSoftwareCodes();
        if (StrUtil.isNotBlank(vpnSoftwareCodes)) {
            dto.setVpnSoftwareCodeList(JSONUtil.toList(vpnSoftwareCodes, String.class));
        } else {
            dto.setVpnSoftwareCodeList(new ArrayList<>());
        }

        return dto;
    }

    /**
     * 生成情报入口1，页面录入、更新
     * @param dto
     * @return
     */
    public VpnIntelligence dtoToEntity(VpnIntelligenceDTO dto) {
        refDataDict();

        VpnIntelligence entity = new VpnIntelligence();
        BeanUtil.copyProperties(dto, entity);
        setVpnContent(entity);
        if (CollectionUtil.isNotEmpty(dto.getVpnSoftwareCodeList())) {
            entity.setVpnSoftwareCodes(JSONUtil.toJsonStr(dto.getVpnSoftwareCodeList()));
        }
        fillValue(entity);
        return entity;
    }

    /**
     * 生成情报入口2，增量-指令下发对象转为情报库对象
     *
     * @param dto
     * @return
     */
    public List<VpnIntelligence> increaseDistributeDtoToEntity(VpnIntelligenceCommandDistributeDTO dto, String systemCode) {
        refDataDict();

        List<VpnIntelligence> vpnIntelligenceList = new ArrayList<>();

        if (CollectionUtil.isNotEmpty(dto.getComintData())) {
            for (VpnIntelligenceCommandDistributeDTO.VpnIntelligenceIncreaseDistributeDTO comintDatum : dto.getComintData()) {
                VpnIntelligence vpnIntelligence = createAndFillCommandField(dto, systemCode);
                vpnIntelligence.setSource(IntelligenceSourceEnum.INCREASE.name());
                vpnIntelligence.setVpnSource(dto.getSource());
                vpnIntelligence.setTimeStamp(dto.getTimeStamp());
                vpnIntelligence.setCommandId(dto.getCommandId());
                vpnIntelligence.setTypeId(comintDatum.getTypeId());
                vpnIntelligence.setVpnId(comintDatum.getVpnId());
                vpnIntelligence.setVpnName(comintDatum.getVpnName());

                vpnIntelligence.setVpnDomain(comintDatum.getVpnDomain());
                vpnIntelligence.setVpnIp(comintDatum.getVpnIp());
                vpnIntelligence.setVpnUrl(comintDatum.getVpnUrl());
                vpnIntelligence.setVpnUrlDecode(Base64.decodeStr(comintDatum.getVpnUrl()));
                vpnIntelligence.setVpnLink(comintDatum.getVpnLink());

                vpnIntelligence.setVpnProtocolCode(null);

                vpnIntelligence.setVpnMessage(null);
                vpnIntelligence.setVpnPort(null);
                vpnIntelligence.setVpnAirportCode(null);
                vpnIntelligence.setVpnSoftwareCodes(null);
                vpnIntelligence.setVpnProtocolEversecCode(null);

                setVpnContent(vpnIntelligence);

                distributeSetDefaultValue(vpnIntelligence);
                fillValue(vpnIntelligence);
                vpnIntelligenceList.add(vpnIntelligence);
            }

        }

        return vpnIntelligenceList;
    }

    /**
     * 生成情报入口3，全量-指令下发对象转为情报库对象
     *
     * @param paramDto
     * @param dtos
     * @return
     */
    public List<VpnIntelligence> fullDistributeDtoToEntity(VpnIntelligenceCommandDistributeDTO paramDto, List<VpnIntelligenceFullDistributeDTO> dtos, String systemCode) {
        refDataDict();

        List<VpnIntelligence> list = new ArrayList<>();
        for (VpnIntelligenceFullDistributeDTO dto : dtos) {
            VpnIntelligence vpnIntelligence = fullDistributeDtoToEntity(paramDto, dto, systemCode);
            list.add(vpnIntelligence);
        }
        return list;
    }

    private VpnIntelligence fullDistributeDtoToEntity(VpnIntelligenceCommandDistributeDTO paramDto, VpnIntelligenceFullDistributeDTO dto, String systemCode) {
        VpnIntelligence vpnIntelligence = createAndFillCommandField(paramDto, systemCode);
        vpnIntelligence.setSource(IntelligenceSourceEnum.FULL.name());
        vpnIntelligence.setVpnSource(paramDto.getSource());
        vpnIntelligence.setTimeStamp(dto.getTimeStamp());
        vpnIntelligence.setTypeId(dto.getTypeId());
        vpnIntelligence.setVpnId(dto.getVpnId());
        vpnIntelligence.setVpnName(dto.getVpnName());
        vpnIntelligence.setVpnPort(null);
        vpnIntelligence.setVpnAirportCode(null);
        vpnIntelligence.setVpnSoftwareCodes(null);
        vpnIntelligence.setVpnLink(null);
        vpnIntelligence.setContentType(dto.getContentType());
        vpnIntelligence.setVpnContent(dto.getVpnContent());
        // 根据情报类型分别设置情报字段
        if (IntelligenceContentTypeEnum.DOMAIN.getCode() == dto.getContentType()) {
            vpnIntelligence.setVpnDomain(dto.getVpnContent());
        } else if (IntelligenceContentTypeEnum.IP.getCode() == dto.getContentType()) {
            vpnIntelligence.setVpnIp(dto.getVpnContent());
        } else if (IntelligenceContentTypeEnum.URL.getCode() == dto.getContentType()) {
            vpnIntelligence.setVpnUrl(dto.getVpnContent());
            vpnIntelligence.setVpnUrlDecode(Base64.decodeStr(dto.getVpnContent()));
        } else if (IntelligenceContentTypeEnum.MESSAGE.getCode() == dto.getContentType()) {
            vpnIntelligence.setVpnMessage(Base64.decodeStr(dto.getVpnContent()));
        } else if (IntelligenceContentTypeEnum.VPN_PROTOCOL.getCode() == dto.getContentType()) {
            vpnIntelligence.setVpnProtocolCode(dto.getVpnContent());
        }

        distributeSetDefaultValue(vpnIntelligence);
        fillValue(vpnIntelligence);
        return vpnIntelligence;
    }

    /**
     * 增量下发和页面录入时vpnContent没有值，需要单独设置
     * @param entity
     */
    private void setVpnContent(VpnIntelligence entity) {
        if (StrUtil.isNotBlank(entity.getVpnPort()) && StrUtil.isNotBlank(entity.getVpnIp())) {
            // ip 端口数据转为ip数据
            entity.setVpnIp(entity.getVpnIp() + ":" + entity.getVpnPort());
            entity.setVpnPort(null);
        }

        if (StrUtil.isNotBlank(entity.getVpnPort()) && StrUtil.isNotBlank(entity.getVpnIp())) {
            // 因为上面做了特殊处理，这种情况不存在
            entity.setContentType(IntelligenceContentTypeEnum.OTHER.getCode());
            entity.setVpnContent(entity.getVpnIp() + "-" + entity.getVpnPort());
        } else if (StrUtil.isNotBlank(entity.getVpnIp())) {
            entity.setContentType(IntelligenceContentTypeEnum.IP.getCode());
            entity.setVpnContent(entity.getVpnIp());
        } else if (StrUtil.isNotBlank(entity.getVpnDomain())) {
            entity.setContentType(IntelligenceContentTypeEnum.DOMAIN.getCode());
            entity.setVpnContent(entity.getVpnDomain());
        } else if (StrUtil.isNotBlank(entity.getVpnUrl())) {
            entity.setContentType(IntelligenceContentTypeEnum.URL.getCode());
            entity.setVpnContent(entity.getVpnUrl());
        } else if (StrUtil.isNotBlank(entity.getVpnMessage())) {
            entity.setContentType(IntelligenceContentTypeEnum.MESSAGE.getCode());
            entity.setVpnContent(entity.getVpnMessage());
        } else if (StrUtil.isNotBlank(entity.getVpnProtocolCode())) {
            entity.setContentType(IntelligenceContentTypeEnum.VPN_PROTOCOL.getCode());
            entity.setVpnContent(entity.getVpnProtocolCode());
        } else {
            entity.setContentType(IntelligenceContentTypeEnum.OTHER.getCode());
        }
    }

    /**
     * 下发填充默认值
     * @param vpnIntelligence
     */
    private void distributeSetDefaultValue(VpnIntelligence vpnIntelligence) {
        vpnIntelligence.setReportStatus(IntelligenceCommandReportStatusEnum.REPORTED.name());
        vpnIntelligence.setReportDatetime(LocalDateTime.now());
        vpnIntelligence.setDistributeStatus(IntelligenceCommandDistributeStatusEnum.TO_BE_DISTRIBUTED.name());
    }

    /**
     * 下发时填充并更新需要的字段
     * @param vpnIntelligenceList
     * @return 需要更新的数据
     */
    public List<VpnIntelligence> fillAndUpdateField(List<VpnIntelligence> vpnIntelligenceList) {
        // TODO: 2023-11-22 暂时先不做了
        List<VpnIntelligence> toBeFillList = new ArrayList<>();

        for (VpnIntelligence vpnIntelligence : vpnIntelligenceList) {

        }

        return toBeFillList;

    }

    /**
     * 填充信息
     *
     * 1.根据部侧协议填充eversec协议编号
     * 2.根据eversec协议编号填充部侧协议
     * 4.生成ruleId
     *
     * @param entity
     */
    private void fillValue(VpnIntelligence entity) {
        String vpnProtocolCode = entity.getVpnProtocolCode();
        String vpnProtocolEversecCode = entity.getVpnProtocolEversecCode();
        if (StrUtil.isNotBlank(vpnProtocolCode) && StrUtil.isBlank(vpnProtocolEversecCode)) {



            // 1.根据部侧协议填充eversec协议编号
            LinkedHashMap<String, SysDataDictDto> protocolMap = dictMap.get().get(AntiVpnDataDictTypeEnum.PROTOCOL_INFORMATION.name());
            if (protocolMap != null) {
                SysDataDictDto sysDataDictDto = protocolMap.get(vpnProtocolCode);
                if (sysDataDictDto != null) {
                    entity.setVpnProtocolEversecCode(sysDataDictDto.getEnumKey2());
                }
            }
        } else if (StrUtil.isNotBlank(vpnProtocolEversecCode) && StrUtil.isBlank(vpnProtocolCode)) {
            // 2.根据eversec协议编号填充部侧协议
            LinkedHashMap<String, SysDataDictDto> protocolMap = eversecProtocolKeyDictMap.get().get(AntiVpnDataDictTypeEnum.PROTOCOL_INFORMATION.name());
            if (protocolMap != null) {
                SysDataDictDto sysDataDictDto = protocolMap.get(vpnProtocolEversecCode);
                if (sysDataDictDto != null) {
                    entity.setVpnProtocolEversecCode(sysDataDictDto.getEnumKey());
                }
            }
        }
        setRuleIdAndVpnId(entity);

    }

    /**
     * 根据vpnId设置ruleId
     * @param entity
     */
    private void setRuleIdAndVpnId(VpnIntelligence entity) {
        Long ruleId = null;
        String ruleIdPrefix = VpnIntelligence.RULE_CLASS + VpnIntelligence.RULE_SUB_CLASS + IntelligenceSourceEnum.valueOf(entity.getSource()).getRuleIdPrefix();
        Long ruleIdTail = null;

        if (entity.getVpnId() != null && String.valueOf(entity.getVpnId()).length() == 14) {
            ruleId = entity.getVpnId();
        } else{
            if (entity.getVpnId() != null && entity.getVpnId() < 100000000L) {
                ruleIdTail = entity.getVpnId();
            } else {
                // 可能重复
                ruleIdTail = IdUtil.getSnowflakeNextId();
            }
            ruleId = Long.parseLong(ruleIdPrefix) * 100000000L + ruleIdTail % 100000000L;
        }

        if (entity.getRuleId() == null) {
            entity.setRuleId(ruleId);
        }
        // 页面录入
        if (entity.getVpnId() == null) {
            entity.setVpnId(ruleId);
        }
    }


    /**
     * 增量下发和全量下发时填充指令中的字段
     * 填充指令字段
     * 全量、增量下发时填充指令字段
     *
     * @return
     */
    private VpnIntelligence createAndFillCommandField(VpnIntelligenceCommandDistributeDTO dto, String systemCode) {
        VpnIntelligence vpnIntelligence = new VpnIntelligence();
        vpnIntelligence.setCommandId(dto.getCommandId());
        vpnIntelligence.setComCode(dto.getComCode());
        vpnIntelligence.setProvinceId(dto.getProvinceId());
        vpnIntelligence.setSystemCode(systemCode);
        return vpnIntelligence;
    }

}
