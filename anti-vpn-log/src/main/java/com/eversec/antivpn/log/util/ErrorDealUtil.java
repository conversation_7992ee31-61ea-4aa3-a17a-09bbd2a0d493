package com.eversec.antivpn.log.util;

import com.eversec.antivpn.log.mapper.vo.TypeCountVO;

import java.util.Iterator;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/9/1 17:34
 **/

public class ErrorDealUtil {

    public static void removeErrorData(List<TypeCountVO> list){
        if(list == null || list.isEmpty()) return;
        Iterator<TypeCountVO> iterator =  list.iterator();
        while (iterator.hasNext()){
            TypeCountVO vo = iterator.next();
            if(vo.getKey()==null || "".equals(vo.getKey())) iterator.remove();
        }
    }
}
