package com.eversec.antivpn.support.knowledge.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.eversec.antivpn.config.enums.AntiVpnBusinessExceptionEnum;
import com.eversec.antivpn.config.enums.AntiVpnDataDictTypeEnum;
import com.eversec.antivpn.support.knowledge.api.KnowledgeSoftwareApi;
import com.eversec.antivpn.support.knowledge.api.dto.ImportDownloadRequest;
import com.eversec.antivpn.support.knowledge.api.dto.ImportResult;
import com.eversec.antivpn.support.knowledge.api.dto.KnowledgeBaseDTO;
import com.eversec.antivpn.support.knowledge.api.dto.KnowledgeBaseQueryDto;
import com.eversec.antivpn.support.knowledge.api.dto.KnowledgeSoftwareDTO;
import com.eversec.antivpn.support.knowledge.convertor.KnowledgeDtoConvertor;
import com.eversec.antivpn.support.knowledge.service.IKnowledgeBaseService;
import com.eversec.antivpn.support.knowledge.upload.ImportSoftware;
import com.eversec.cloud.core.api.data.PageResult;
import com.eversec.cloud.core.common.utils.DownloadUtil;
import com.eversec.framework.webboot.common.exception.BusinessException;
import com.eversec.stark.generic.common.util.PageBuilder;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

//import cn.hutool.db.PageResult;

/**
 * <p>
 * 知识库-软件信息 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-12
 */
@RestController
@RequestMapping(KnowledgeSoftwareApi.PATH)
@AllArgsConstructor
@Slf4j
public class KnowledgeSoftwareController implements KnowledgeSoftwareApi {

    private final IKnowledgeBaseService service;

    private final KnowledgeDtoConvertor knowledgeDtoConvertor;


    @Override
    public void save(@Valid KnowledgeSoftwareDTO dto) {
        if(ObjectUtil.isEmpty(dto)){
            throw new BusinessException(AntiVpnBusinessExceptionEnum.CODE_10006);
        }
        dto.setTypeKey(AntiVpnDataDictTypeEnum.SOFTWARE_INFORMATION.name());
        service.save(knowledgeDtoConvertor.softwareToDomain(dto));
    }

    @Override
    public void updateById(@Valid KnowledgeSoftwareDTO dto) {
        if(ObjectUtil.isEmpty(dto)){
            throw new BusinessException(AntiVpnBusinessExceptionEnum.CODE_10006);
        }
        dto.setTypeKey(AntiVpnDataDictTypeEnum.SOFTWARE_INFORMATION.name());
        service.updateById(knowledgeDtoConvertor.softwareToDomain(dto));
    }

    @Override
    public void deleteByIds(List<Long> ids) {
        if(CollectionUtil.isEmpty(ids)){
            throw new BusinessException(AntiVpnBusinessExceptionEnum.CODE_10006);
        }
        service.deleteByIds(ids);
    }

    @Override
    public PageResult<KnowledgeSoftwareDTO> page(KnowledgeBaseQueryDto queryDto) {
        queryDto.setTypeKey(AntiVpnDataDictTypeEnum.SOFTWARE_INFORMATION.name());
        PageResult<KnowledgeBaseDTO> pageResult = service.page(queryDto);
        return PageBuilder.convertPageResult(pageResult, knowledgeDtoConvertor::toSoftwareDto);
    }

    @Override
    public void downloadTemplate(HttpServletResponse response) throws IOException {
        DownloadUtil.download(response,
            "软件信息导入模板.xlsx",
            this.getClass().getClassLoader().getResourceAsStream("template/import_software.xlsx"),
            DownloadUtil.APPLICATION_EXCEL_VALUE);
    }

    @Override
    public ImportResult importTemplate(Long userId, String userName, MultipartFile file) {
        if(ObjectUtil.isEmpty(file)){
            throw new BusinessException(AntiVpnBusinessExceptionEnum.CODE_10006);
        }
        return service.importTemplate( userId,  userName, file, ImportSoftware.class, AntiVpnDataDictTypeEnum.SOFTWARE_INFORMATION,ImportSoftware.MUST_FILL);
    }

    @Override
    public Object importDownload(ImportDownloadRequest req) throws IOException {
        return service.importDownload(req, ImportSoftware.class);
    }

}
