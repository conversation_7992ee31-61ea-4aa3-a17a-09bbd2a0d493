package com.eversec.antivpn.support.province.controller;

import com.eversec.antivpn.support.province.api.ProvincePlatformStatusApi;
import com.eversec.antivpn.support.province.api.dto.ProvincePlatformStatusDTO;
import com.eversec.antivpn.support.province.dto.ProvincePlatformStatusQueryConditionDTO;
import com.eversec.antivpn.support.province.entity.ProvincePlatformStatus;
import com.eversec.antivpn.support.province.service.IProvincePlatformStatusService;

import io.swagger.v3.oas.annotations.Operation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import lombok.AllArgsConstructor;
import java.util.List;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 省平台信息（企业测） 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-12
 */
@RestController
@RequestMapping(ProvincePlatformStatusApi.PATH)
@AllArgsConstructor
@Slf4j
public class ProvincePlatformStatusController implements ProvincePlatformStatusApi {

    private final IProvincePlatformStatusService service;

    @Operation(summary = "查询-所有省平台，只查询最新上报记录")
    @GetMapping("/listAllLatestReportProvincePlatform")
    public List<ProvincePlatformStatusDTO> listAllLatestReportProvincePlatform(ProvincePlatformStatusQueryConditionDTO paramDto) {
        List<ProvincePlatformStatusDTO> list = service.listAllLatestReportProvincePlatform(paramDto);
        return list;
    }

    @Operation(summary = "查询-分页-查询指定省平台系统分页")
    @GetMapping("/page")
    public Page<ProvincePlatformStatusDTO> page(
            ProvincePlatformStatusQueryConditionDTO paramDto, Page<ProvincePlatformStatus> pageInfo) {
        return service.page(paramDto, pageInfo);
    }
}
