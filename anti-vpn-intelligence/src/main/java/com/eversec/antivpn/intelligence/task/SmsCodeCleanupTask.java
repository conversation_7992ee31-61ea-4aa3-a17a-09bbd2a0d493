package com.eversec.antivpn.intelligence.task;

import com.eversec.antivpn.intelligence.service.ISmsCodeService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 短信验证码清理定时任务
 * 
 * <AUTHOR>
 * @since 2025-01-12
 */
@Component
@Slf4j
@AllArgsConstructor
public class SmsCodeCleanupTask {

    private final ISmsCodeService smsCodeService;

    /**
     * 每10分钟清理一次过期验证码
     */
    @Scheduled(fixedRate = 600000) // 10分钟 = 600000毫秒
    public void cleanupExpiredCodes() {
        try {
            log.debug("开始清理过期验证码");
            smsCodeService.cleanExpiredCodes();
            log.debug("清理过期验证码完成");
        } catch (Exception e) {
            log.error("清理过期验证码异常", e);
        }
    }

}
