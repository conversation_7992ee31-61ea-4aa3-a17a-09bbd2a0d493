package com.eversec.antivpn.log.util;

import com.eversec.antivpn.log.mapper.vo.TypeCountVO;
import com.eversec.antivpn.log.mapper.vo.VpnAggVO;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/9/27 11:08
 **/

public class SortUtil {
    public static void sortTypeCountVODesc(List<TypeCountVO> dataList){
        if(dataList==null || dataList.isEmpty()) return;
        dataList.sort((a, b) -> {
            if(a.getNum() == b.getNum()) {
                return 0;
            }else if(a.getNum() > b.getNum()) {
                return -1;
            }else {
                return 1;
            }
        });
    }

    public static void sortVpnAggVOPersonDesc(List<VpnAggVO> dataList){
        if(dataList==null || dataList.isEmpty()) return;
        dataList.sort((a, b) -> {
            if(a.getPersonNum() == b.getPersonNum()) {
                return 0;
            }else if(a.getPersonNum() > b.getPersonNum()) {
                return -1;
            }else {
                return 1;
            }
        });
    }

}
