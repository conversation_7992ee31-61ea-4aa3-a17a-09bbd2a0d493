package com.eversec.antivpn.support.knowledge.api.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <p>
 * 导出返回类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-12
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class ImportResult {
    /**
     * 错误数据是否存在
     */
    private boolean exists;
    /**
     * 错误提示信息
     */
    private Map<String, String> importErrors;
    /**
     * 导入成功列表
     */
    private List<Object> importBases;
    /**
     * 导入失败列表
     */
    private List<Object> importErrorBases;

    @JsonIgnore
    public Boolean exists() {
        return !exists && (importErrors == null || importErrors.size() == 0);
    }
}