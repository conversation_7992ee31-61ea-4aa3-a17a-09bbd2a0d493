package com.eversec.antivpn.log.service;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.eversec.antivpn.emnus.ScreenTypeEnum;
import com.eversec.antivpn.log.api.dto.CrossBorderCommLogDTO;
import com.eversec.antivpn.log.api.dto.CrossBorderCommLogRequest;
import com.eversec.antivpn.log.api.dto.UserLogPageDTO;
import com.eversec.antivpn.log.api.dto.base.AggCountDTO;
import com.eversec.antivpn.log.api.dto.base.DateHistogramDTO;
import com.eversec.antivpn.log.entity.E1CommLog;
import com.eversec.antivpn.log.mapper.vo.TypeCountVO;
import com.eversec.antivpn.log.mapper.vo.UserLogPageVO;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-12
 */
public interface IE1CommLogService extends IService<E1CommLog> {


    List<TypeCountVO> getApplicationProtocolTypeAggregate(ScreenTypeEnum screenTypeEnum);

    List<TypeCountVO> getProvinceAggregate(Long startTime, Long endTime);

    void deleteByLogId(Long logId);

    AggCountDTO countNum(ScreenTypeEnum screenTypeEnum);

    /**
     * 根据查询分区获取所有E1数据
     * @param screenTypeEnum
     * @return
     */
    List<E1CommLog> getE1LogsByPartition(ScreenTypeEnum screenTypeEnum);


    /**
     * 软件分组日志数量TOPO10
     * @param screenTypeEnum
     * @return
     */
    List<E1CommLog> getLogNumBySofaWareTop10(ScreenTypeEnum screenTypeEnum);

    /**
     * 软件分组用户数量TOPO10
     * @param screenTypeEnum
     * @return
     */
    List<E1CommLog> getLogNumByUserTop10(ScreenTypeEnum screenTypeEnum);

    /**
     * 用户量TOP10的软件提供服务次数趋势图
     * @param screenTypeEnum
     * @return
     */
    List<E1CommLog> getLogNumBySofaWareAndDayTop10(ScreenTypeEnum screenTypeEnum);
}
