package com.eversec.antivpn.log.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.eversec.antivpn.emnus.ScreenTypeEnum;
import com.eversec.antivpn.log.entity.E1CommLogView;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.eversec.antivpn.log.mapper.vo.BaseSearchVO;
import com.eversec.antivpn.log.mapper.vo.MonitorDataVO;
import com.eversec.antivpn.log.mapper.vo.TypeCountVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-17
 */
@Mapper
@DS("clickhouse")
public interface E1CommLogViewMapper extends BaseMapper<E1CommLogView> {
    Long countNum(BaseSearchVO baseSearchVO);

    List<TypeCountVO> getVpnTypeAggregate(BaseSearchVO baseSearchVO);

    List<TypeCountVO> getVpnType5ContentTypeAggregate(BaseSearchVO baseSearchVO);

    List<TypeCountVO> getDateHistogram(BaseSearchVO baseSearchVO);

    List<TypeCountVO> getProvinceAggregate(BaseSearchVO baseSearchVO);

    List<TypeCountVO> getApplicationProtocolTypeAggregate(BaseSearchVO baseSearchVO);


    List<TypeCountVO> getProtocolTypeAggregate(BaseSearchVO baseSearchVO);


    List<TypeCountVO> getLocationAggregate(BaseSearchVO baseSearchVO);

    List<TypeCountVO> userCountLogNum(BaseSearchVO baseSearchVO);


    List<TypeCountVO> getSystemLogAggCount(BaseSearchVO baseSearchVO);

    List<TypeCountVO> getVpnNameAggregate(BaseSearchVO baseSearchVO);

    List<MonitorDataVO> getSystemProvinceCount(@Param("day") Long day,@Param("hour") Integer hour);
}
