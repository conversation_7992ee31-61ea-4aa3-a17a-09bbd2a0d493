<template>
  <div class="screen-container">
    <main class="screen-main">
      <div>
        <div class="total-card">
          <p class="total-card__title">提供跨境通信的软件总量</p>
          <div class="total-card__content">
            <span class="total-card__count font-gradient">{{ crossBoardAppCount.num }}</span>
          </div>
        </div>
        <div class="total-card" style="margin-bottom: 15px;">
          <p class="total-card__title" style="margin-top: 8px">近七日跨境用户通信使用的软件数量</p>
          <div class="total-card__content">
            <span class="total-card__count font-gradient">{{ crossBoardAppCountLast7d.num }}</span>
            <span>环比：
              <template v-if="crossBoardAppCountLast7d.percent === 0">
                <span>-</span>
              </template>
              <template v-else>
                  <span>{{ crossBoardAppCountLast7d.percent > 0 ? '增加' : '减少' }}</span>
                  <strong class="font-gradient" style="margin-right: 20px">
                    {{
                      crossBoardAppCountLast7d.percent > 0 ? crossBoardAppCountLast7d.percent : -crossBoardAppCountLast7d.percent
                    }}%
                  </strong>
                  <span v-if="crossBoardAppCountLast7d.percent > 0" class="small-arrow"></span>
                  <span v-else class="small-arrow small-arrow__bottom"></span>
              </template>
            </span>
          </div>
        </div>
        <div class="chart-card">
          <p class="chart-card__title">软件用户量TOP10</p>
          <div class="chart-card__content" style="display: flex">
            <div style="position: relative;width: 600px; height: 340px; ">
              <Empty v-if="isEmpty['user-chart']" class="empty" color="#fff"/>
              <div
                v-show="!isEmpty['user-chart']"
                style="width: 100%; height: 100%;"
                ref="user-chart"
                v-loading="isLoading['user-chart']"
                element-loading-background="transparent"
              ></div>
            </div>
          </div>
        </div>
        <div class="chart-card">
          <p class="chart-card__title">软件日志访问量TOP10</p>
          <div class="chart-card__content" style="display: flex">
            <div style="position: relative;width: 600px; height: 340px; ">
              <Empty v-if="isEmpty['log-chart']" class="empty" color="#fff"/>
              <div
                v-show="!isEmpty['log-chart']"
                style="width: 600px; height: 340px;"
                ref="log-chart"
                v-loading="isLoading['log-chart']"
                element-loading-background="transparent"
              ></div>
            </div>
          </div>
        </div>
      </div>
      <div>
        <div class="chart-card">
          <p class="chart-card__title">用户量TOP10的软件提供服务次数趋势图</p>
          <div class="chart-card__content" style="display: flex">
            <div style="height: 560px; width: 100%; position: relative;">
              <Empty class="empty" v-if="isEmpty['serviceChart']" color="#fff"/>
              <div
                v-show="!isEmpty['serviceChart']"
                style="width: 100%; height: 100%;"
                ref="serviceChart"
                v-loading="isLoading['serviceChart']"
                element-loading-background="transparent"
              ></div>
            </div>
          </div>
        </div>
        <div class="chart-card">
          <p class="chart-card__title">软件对系统的适配度TOP10</p>
          <div class="chart-card__content">
            <ol
              class="line-wrap"
              v-loading="isLoading['appSystemAdaptability']"
              element-loading-background="transparent"
            >
              <Empty class="empty" v-if="isEmpty['appSystemAdaptability']" color="#fff"/>
              <template v-else>
                <li
                  class="line-item"
                  :class="[ index === 0 ? 'warning' : '']"
                  v-for="(item, index) in appSystemAdaptability"
                >
                  <div>
                    <span class="line-item__top">TOP {{ (index < 9 ? '0' : '') + (index + 1) }}</span>
                    <span class="line-item__name">{{ item.name }}</span>
                  </div>
                  <div class="line-item__percent">
                    {{ item.percent }}%
                  </div>
                  <div class="line-progress" :style="{ width: item.percent + '%' }"></div>
                </li>
              </template>
            </ol>
          </div>
        </div>
      </div>
      <div>
        <div class="chart-card">
          <p class="chart-card__title">软件授权类型占比</p>
          <div class="chart-card__content">
            <div style="width: 600px; height: 290px; position: relative;">
              <Empty class="empty" v-if="isEmpty['authorization-chart']" color="#fff"/>
              <div
                v-show="!isEmpty['authorization-chart']"
                style="width: 100%; height: 100%;"
                ref="authorization-chart"
                v-loading="isLoading['authorization-chart']"
                element-loading-background="transparent"
              ></div>
              <div class="custom-chart-legend-wrap">
                <p class="legend-item" v-for="item in appAuthTypePercentage" :key="item.name">
                  <span style="flex-basis: 115px;">
                    <span class="legend-item__icon"></span>
                    <span>{{ item.name }}</span>
                  </span>
                  <span style="flex-basis: 80px; text-align: right">
                    <span style="color: #4bd5d4; margin-right: 3px; font-size: 16px;">{{ item.value }}</span>
                    <span>项</span>
                  </span>
                  <span style="color: #4bd5d4; margin-left: auto; font-size: 16px;">{{ item.percentage }}%</span>
                </p>
              </div>
            </div>
          </div>
        </div>
        <div class="chart-card">
          <p class="chart-card__title" style="margin-bottom: 14px;">用户量TOP10的软件开发者信息</p>
          <div class="chart-card__content">
            <div class="custom-table">
              <div class="custom-table-header">
                <strong>排名</strong>
                <strong>软件名称</strong>
                <strong>开发者信息</strong>
              </div>
              <ul
                class="custom-table-body"
                v-loading="isLoading['top10UserAppDeveloperInfo']"
                element-loading-background="transparent"
              >
                <Empty class="empty" v-if="isEmpty['top10UserAppDeveloperInfo']" color="#fff"/>
                <template v-else>
                  <li v-for="(item, index) in top10UserAppDeveloperInfo">
                    <div role="cell">{{ (index < 9 ? '0' : '') + (index + 1) }}</div>
                    <div role="cell">{{ item.app }}</div>
                    <div role="cell">{{ item.developer }}</div>
                  </li>
                </template>
              </ul>
            </div>
          </div>
        </div>
        <div class="chart-card">
          <p class="chart-card__title" style="margin-bottom: 20px;">软件对协议的适配度TOP10</p>
          <div class="chart-card__content">
            <ul
              class="line-box-wrap"
              v-loading="isLoading['appProtocolAdaptabilityTop10']"
              element-loading-background="transparent"
            >
              <Empty class="empty" v-if="isEmpty['appProtocolAdaptabilityTop10']" color="#fff"/>
              <template v-else>
                <li :class="[ index === 0 ? 'warning' : '']" v-for="(item, index) in appProtocolAdaptabilityTop10">
                  <div>{{ (index < 9 ? '0' : '') + (index + 1) }}</div>
                  <div>{{ item.name }}</div>
                  <div>
                    <div class="line-arrow" :style="{ width: item.percent + '%' }"></div>
                  </div>
                  <div>{{ item.percent }}%</div>
                </li>
              </template>
            </ul>
          </div>
        </div>
      </div>

    </main>
  </div>
</template>


<script>
import * as echarts from 'echarts';
import api from './api/index'
import Empty from "@/components/empty.vue";

export default {
  name: 'CrossBorderSoftware',

  components: {Empty},

  data() {
    return {
      isLoading: {
        'user-chart': true,
        'log-chart': true,
        'serviceChart': true,
        'authorization-chart': true,
        'top10UserAppDeveloperInfo': true,
        'appProtocolAdaptabilityTop10': true,
        'appSystemAdaptability': true
      },
      isEmpty: {
        'user-chart': false,
        'log-chart': false,
        'serviceChart': false,
        'authorization-chart': false,
        'top10UserAppDeveloperInfo': false,
        'appProtocolAdaptabilityTop10': false,
        'appSystemAdaptability': false
      },


      dateType: 'TODAY',

      crossBoardAppCount: {
        num: '',
        percent: ''
      },

      crossBoardAppCountLast7d: {
        num: '',
        percent: ''
      },

      appSystemAdaptability: [],

      top10UserAppDeveloperInfo: [],

      appProtocolAdaptabilityTop10: [],

      appAuthTypePercentage: []
    }
  },

  methods: {
    update(value) {
      this.dateType = value;
      this.init();
    },

    init() {
      this.getCrossBoardAppCount();
      this.getCrossBoardAppCountLast7d();
      this.getAppUserCountTop10();
      this.getAppLogAccessTop10();
      this.getUserAppServiceCountTop10();
      this.getAppSystemAdaptability();
      this.getTop10UserAppDeveloperInfo();
      this.getAppProtocolAdaptabilityTop10();
      this.getAppAuthTypePercentage();
    },

    getCrossBoardAppCount() {
      api.getCrossBoardAppCount(this.dateType).then(res => {
        const {num, percent} = res;
        this.crossBoardAppCount.num = num || 0;
        this.crossBoardAppCount.percent = percent || 0;
      })
    },

    getCrossBoardAppCountLast7d() {
      api.getCrossBoardAppCountLast7d(this.dateType).then(res => {
        const {num, percent} = res;
        this.crossBoardAppCountLast7d.num = num || 0;
        this.crossBoardAppCountLast7d.percent = percent || 0;
      })
    },

    getAppUserCountTop10() {
      this.isLoading['user-chart'] = true;
      api.getAppUserCountTop10(this.dateType).then(res => {
        if (!res || res.length === 0) {
          this.isEmpty['user-chart'] = true;
          return;
        } else {
          this.isEmpty['user-chart'] = false;
        }

        const data = res.map(item => {
          return {
            name: item.key,
            value: item.num,
          }
        })

        const total = data.reduce((total, item) => total + item.value, 0)
        data.forEach(item => {
          item.percentage = Math.floor(item.value / total * 100)
        })

        this.$nextTick(() => {
          this.initPieChart('user-chart', data)
        })
      }).finally(() => {
        this.isLoading['user-chart'] = false;
      })
    },

    getAppLogAccessTop10() {
      this.isLoading['log-chart'] = true;
      api.getAppLogAccessTop10(this.dateType).then(res => {
        if (!res || res.length === 0) {
          this.isEmpty['log-chart'] = true;
          return;
        } else {
          this.isEmpty['log-chart'] = false;
        }

        const data = res.map(item => {
          return {
            name: item.key,
            value: item.num,
          }
        })

        const total = data.reduce((total, item) => total + item.value, 0)
        data.forEach(item => {
          item.percentage = Math.floor(item.value / total * 100)
        })

        this.$nextTick(() => {
          this.initPieChart('log-chart', data)
        })
      }).finally(() => {
        this.isLoading['log-chart'] = false;
      })
    },

    getUserAppServiceCountTop10() {
      this.isLoading['serviceChart'] = true;
      api.getUserAppServiceCountTop10(this.dateType).then(res => {
        if (!res || res.length === 0) {
          this.isEmpty['serviceChart'] = true;
          return;
        } else {
          this.isEmpty['serviceChart'] = false;
        }

        const data = res.map(item => {
          const {key, key2, e1Num, e2Num} = item;
          return [e1Num, e2Num, key, key2]
        })

        this.$nextTick(() => {
          this.initServiceChart(data)
        })
      }).finally(() => {
        this.isLoading['serviceChart'] = false;
      })
    },

    getAppSystemAdaptability() {
      this.isLoading['appSystemAdaptability'] = true;
      api.getAppSystemAdaptability(this.dateType).then(res => {
        if (!res || res.length === 0) {
          this.isEmpty['appSystemAdaptability'] = true;
          return;
        } else {
          this.isEmpty['appSystemAdaptability'] = false;
        }

        res = res.slice(0, 10);

        this.appSystemAdaptability = res.map(item => {
          return {
            name: item.key,
            percent: Math.floor(item.percent)
          }
        })
      })
        .finally(() => {
          this.isLoading['appSystemAdaptability'] = false;
        })
    },

    getTop10UserAppDeveloperInfo() {
      this.isLoading['top10UserAppDeveloperInfo'] = true;
      api.getTop10UserAppDeveloperInfo(this.dateType).then(res => {
        if (!res || res.length === 0) {
          this.isEmpty['top10UserAppDeveloperInfo'] = true;
          return;
        } else {
          this.isEmpty['top10UserAppDeveloperInfo'] = false;
        }

        res = res.slice(0, 10);

        this.top10UserAppDeveloperInfo = res.map(item => {
          return {
            app: item.key,
            developer: item.key2
          }
        })
      })
        .finally(() => {
          this.isLoading['top10UserAppDeveloperInfo'] = false;
        })
    },

    getAppAuthTypePercentage() {
      this.isLoading['authorization-chart'] = true;
      api.getAppAuthTypePercentage(this.dateType).then(res => {
        if (!res || res.length === 0) {
          this.isEmpty['authorization-chart'] = true;
          return;
        } else {
          this.isEmpty['authorization-chart'] = false;
        }

        let data = res.map(item => {
          return {
            name: item.key,
            value: item.num,
          }
        })

        const total = data.reduce((total, item) => total + item.value, 0)
        data.forEach(item => {
          item.percentage = Math.floor(item.value / total * 100)
        })

        data = data.sort((a, b) => {
          return b.name.length - a.name.length
        })

        this.appAuthTypePercentage = data;

        this.$nextTick(() => {
          this.initAuthorizationChart('authorization-chart', data)
        })
      }).finally(() => {
        this.isLoading['authorization-chart'] = false;
      })
    },

    getAppProtocolAdaptabilityTop10() {
      this.isLoading['appProtocolAdaptabilityTop10'] = true;
      api.getAppProtocolAdaptabilityTop10(this.dateType).then(res => {
        if (!res || res.length === 0) {
          this.isEmpty['appProtocolAdaptabilityTop10'] = true;
          return;
        } else {
          this.isEmpty['appProtocolAdaptabilityTop10'] = false;
        }

        res = res.slice(0, 10);

        this.appProtocolAdaptabilityTop10 = res.map(item => {
          return {
            name: item.key,
            percent: Math.floor(item.percent)
          }
        })
      })
        .finally(() => {
          this.isLoading['appProtocolAdaptabilityTop10'] = false;
        })
    },

    initAuthorizationChart(domRef, data) {
      const chartDom = this.$refs[domRef];
      const myChart = echarts.init(chartDom)
      window.addEventListener('resize', () => {
        myChart.resize()
      });

      const option = {
        color: [
          "#fe7b69",
          "#fff33a",
          "#a477ff"
        ],
        graphic: [
          {
            type: 'image',
            top: 80,
            left: 84,
            z: -10,
            bounding: 'raw',
            style: {
              image: require('./assets/chart/radar.png'),
              width: 132,
              height: 132
            }
          },
          {
            type: 'image',
            top: 65,
            left: 69,
            z: -10,
            bounding: 'raw',
            style: {
              image: require('./assets/chart/dashed-circle.png'),
              width: 161,
              height: 160
            },
          }
        ],
        tooltip: {
          trigger: 'item',
          backgroundColor: '#082140',
          borderWidth: 1,
          borderColor: '#393939',
          borderRadius: 0,
          formatter: e => {
            return `
            <div style="padding: 0 10px; font-size: 12px">
                <div style="color: ${e.color}; font-size: 16px; font-weight: 700">${e.name}</div>
                <div style="color: #6d737e; font-size: 14px">数量</div>
                <div style="color: #50c7ff; font-size: 15px;">${(e.value || 0).toLocaleString()}</div>
                <div style="color: #6d737e; font-size: 14px;">占比</div>
                <div style="color: #50c7ff; font-size: 15px;">${e.percent}%</div>
            </div>
            `
          }
        },

        legend: {
          show: false
        },

        series: [
          {
            type: 'pie',
            left: -300,
            top: 0,
            radius: [90, 100],

            label: {
              show: false,
              position: 'center',
              formatter: [
                '{a|TOP10}',
                '{b|软件用户量}',
              ].join('\n'),
              rich: {
                a: {
                  color: '#e45e60',
                  fontSize: 18,
                  lineHeight: 20,
                  fontWeight: 'bold'
                },
                b: {
                  color: '#fff',
                  fontSize: 16,
                  lineHeight: 20,
                  fontWeight: 'bold'
                }
              }
            },

            data: data
          },
        ]
      }

      myChart.setOption(option);
    },

    initServiceChart(data) {
      const chartDom = this.$refs['serviceChart']
      const myChart = echarts.init(chartDom)
      window.addEventListener('resize', () => {
        myChart.resize()
      });

      const colors = [
        "#fe7b69",
        "#ffd459",
        "#fff33a",
        "#ccf176",
        "#7ae15f",
        "#69edca",
        "#1ca4ff",
        "#2a84ff",
        "#77a4ff",
        "#a477ff"
      ]

      const minValue = Math.min(...data.map(item => item[1])); // 数据中的最小值
      const maxValue = Math.max(...data.map(item => item[1])); // 数据中的最大值
      const minSize = 20; // symbolSize 的最小值
      const maxSize = 120; // symbolSize 的最大值

      // 动态计算每个数据项的 symbolSize
      function calculateSymbolSize(item) {
        const value = item[1];

        // 检查是否所有值都相同
        const allValuesSame = data.every(item => item[1] === value);

        // 如果所有值都相同，返回固定尺寸
        if (allValuesSame) {
          return maxSize;
        }

        return ((value - minValue) / (maxValue - minValue)) * (maxSize - minSize) + minSize;
      }

      data = data.map((item, index) => {
        const colorIndex = index % colors.length
        const symbolSize = calculateSymbolSize(item);

        return {
          name: item[2],
          value: [item[0], item[1]],
          symbolSize,
          label: {
            show: true,
            formatter: () => item[2],
            color: '#fff',
            fontSize: 12
          },
          tooltip: {
            padding: 10,
            extraCssText: 'white-space:normal; background-color: rgba(13, 42, 78, 0.5); width: 350px; line-height: 2; font-weight: bold',
            formatter: () => item[3]
          },

          itemStyle: {
            color: new echarts.graphic.RadialGradient(0.5, 0.5, 0.5, [
              {
                offset: 0,
                color: 'transparent'
              },
              {
                offset: 1,
                color: colors[colorIndex]
              }
            ]),
            opacity: 0.8,
            shadowColor: '#101c33',
            shadowBlur: 10,
            shadowOffsetX: 1,
            shadowOffsetY: 1,
          },
        }
      })

      const option = {
        tooltip: {
          show: true
        },
        xAxis: {
          name: '用户量',
          nameTextStyle: {
            fontSize: 16,
            color: '#f5f7fa'
          },
          axisTick: false,
          axisLine: {
            show: true,
            lineStyle: {
              color: '#ffffff3b'
            }
          },
          splitLine: {
            show: false
          }
        },
        yAxis: {
          name: '日志量',
          nameTextStyle: {
            fontSize: 16,
            color: '#f5f7fa'
          },
          axisTick: false,
          axisLine: {
            show: true,
            lineStyle: {
              color: '#ffffff3b'
            }
          },
          splitLine: {
            show: false
          }
        },
        series: [
          {
            type: 'scatter',
            data: data
          }
        ]
      };

      myChart.setOption(option)
    },

    initPieChart(domRef, data) {
      const chartDom = this.$refs[domRef];
      const myChart = echarts.init(chartDom)
      window.addEventListener('resize', () => {
        myChart.resize()
      });

      const option = {
        color: [
          "#fe7b69",
          "#ffd459",
          "#fff33a",
          "#ccf176",
          "#7ae15f",
          "#69edca",
          "#1ca4ff",
          "#2a84ff",
          "#77a4ff",
          "#a477ff"
        ],
        graphic: [
          {
            type: 'image',
            top: 69,
            left: 48,
            z: -10,
            bounding: 'raw',
            style: {
              image: require('./assets/chart/pre-border.png'),
              width: 202,
              height: 202
            }
          }
        ],
        tooltip: {
          trigger: 'item',
          backgroundColor: '#082140',
          borderWidth: 1,
          borderColor: '#393939',
          borderRadius: 0,
          formatter: e => {
            return `
            <div style="padding: 0 10px; font-size: 12px">
                <div style="color: ${e.color}; font-size: 16px; font-weight: 700">${e.name}</div>
                <div style="color: #6d737e; font-size: 14px">数量</div>
                <div style="color: #50c7ff; font-size: 15px;">${(e.value || 0).toLocaleString()}</div>
                <div style="color: #6d737e; font-size: 14px;">占比</div>
                <div style="color: #50c7ff; font-size: 15px;">${e.percent}%</div>
            </div>
            `
          }
        },
        legend: {
          show: true,
          orient: 'vertical',
          align: 'left',
          left: 340,
          // top: 17,
          top: "middle",
          icon: 'circle',
          itemWidth: 8,
          itemHeight: 8,
          itemGap: 5,
          data: data.map(item => item.name),
          formatter: name => {
            const target = data.find(item => item.name === name);
            const {percentage} = target;
            if (name.length > 8) {
              name = name.slice(0, 8) + '...'
            }

            return `{itemName|${name}}{itemNum|${percentage}%}`;
          },
          textStyle: {
            color: '#999999',
            fontSize: 14,
            backgroundColor: 'rgba(31,36,49,0.5)',
            padding: [3, 2],
            lineHeight: 20,
            rich: {
              itemName: {
                fontSize: 14,
                width: 180,
                color: '#fff',
              },
              itemNum: {
                fontSize: 14,
                width: 80,
                color: '#94cfe6',
              }
            }
          }
        },

        series: [
          {
            type: 'pie',
            left: -300,
            top: 0,
            radius: [50, 90],
            itemStyle: {
              borderColor: '#292855',
              borderWidth: 2
            },
            label: {
              position: 'center',
              formatter: [
                '{a|TOP10}',
                `{b|${domRef === 'log-chart' ? '日志访问量' : '软件用户量'}}`,
              ].join('\n'),
              rich: {
                a: {
                  color: '#e45e60',
                  fontSize: 16,
                  lineHeight: 20,
                  fontWeight: 'bold'
                },
                b: {
                  color: '#fff',
                  fontSize: 13,
                  lineHeight: 20,
                  fontWeight: 'bold'
                }
              }
            },
            data: data
          }
        ]
      }

      myChart.setOption(option);
    }
  },
}
</script>

<style lang="scss" scoped>
.screen-container {
  display: flex;
  flex-direction: column;
  min-height: 100%;
  min-width: 100%;
}

.screen-main {
  display: flex;
  flex: 1;
  padding: 0 35px;

  & > div {
    flex: 1;

    .chart-card {
      &__title {
        font-size: 16px;
        display: flex;
        align-items: center;
        height: 37px;
        padding-left: 39px;
        color: #fff;
        background: url('assets/card/title-bg.png') no-repeat;
      }
    }
  }

  .total-card {
    &__title {
      font-size: 16px;
      color: white;
      margin-bottom: 8px;
    }

    &__content {
      color: white;
      height: 68px;
      background: url('assets/card/count-container.png') no-repeat;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 40px 0 30px;
    }

    &__count {
      font-size: 38px;
      font-weight: bold;
    }

    .font-gradient {
      background: linear-gradient(to top, #00aeff, #87defe);
      background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }

  .line-wrap {
    margin-top: 20px;
    position: relative;
    width: 600px;
    height: 300px;
    overflow: auto;

    .line-item {
      position: relative;
      width: 572px;
      height: 44px;
      margin-bottom: 6px;
      padding-bottom: 5px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      color: white;
      background: url('assets/card/border-blue.png') no-repeat;


      &__top {
        font-size: 18px;
        margin-left: 5px;
        padding: 2px 10px;
        background-color: #293e4f;
        color: #ade7fe;
        border-radius: 1px;
      }

      &__name {
        font-size: 16px;
        color: white;
        margin-left: 6px;
      }

      &__percent {
        font-size: 16px;
        margin-right: 10px;
        color: #ade7fe;
      }

      .line-progress {
        position: absolute;
        bottom: 5px;
        height: 3px;
        width: 100%;
        left: 5px;
        max-width: calc(100% - 25px);
        background: linear-gradient(
            to right,
            #4295b0 0%,
            #4295b0 calc(100% - 10px),
            #62dbfc calc(100% - 10px),
            #62dbfc 100%
        );
      }

      &.warning {
        background: url('assets/card/border-orange.png') no-repeat;

        .line-item__top {
          background-color: #3a322d;
          color: #d19350;
        }

        .line-item__percent {
          color: #d19350;
        }

        .line-progress {
          background: linear-gradient(
              to right,
              #936b43 0%,
              #936b43 calc(100% - 10px),
              #dd9b52 calc(100% - 10px),
              #dd9b52 100%
          )
        }
      }
    }
  }

  .custom-table {
    .custom-table-header {
      height: 30px;
      display: flex;
      align-items: center;
      color: #ffffff75;
      font-size: 15px;
      background-color: #111f3b;
      margin-bottom: 8px;

      strong {
        text-align: center;
      }

      & > strong:nth-child(1) {
        flex-basis: 70px;
      }

      & > strong:nth-child(2) {
        flex-basis: 200px;
      }

      & > strong:nth-child(3) {
        flex: 1;
      }
    }

    .custom-table-body {
      height: 180px;
      overflow: auto;
      position: relative;

      li {
        height: 35px;
        display: flex;
        align-items: center;
        color: white;
        font-size: 16px;
        margin-bottom: 10px;

        &:nth-child(odd) {
          & > div:nth-child(1), div:nth-child(2) {
            background-color: rgba(19, 30, 55, 0.7);
          }

          & > div:nth-child(3) {
            background-color: rgba(19, 30, 55, 0.5);
          }
        }

        &:nth-child(even) {
          & > div:nth-child(1), div:nth-child(2) {
            background-color: rgba(26, 33, 52, 0.7);
          }

          & > div:nth-child(3) {
            background-color: rgba(26, 33, 52, 0.5);
          }
        }


        [role="cell"] {
          text-align: center;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        & > div:nth-child(1) {
          flex-basis: 70px;
          color: #ade7fe;
        }

        & > div:nth-child(2) {
          flex-basis: 200px;
        }

        & > div:nth-child(3) {
          flex: 1;
        }
      }
    }
  }

  .line-box-wrap {
    height: 300px;
    overflow: auto;
    position: relative;

    li {
      display: flex;
      align-items: center;
      height: 44px;
      font-size: 16px;
      margin-bottom: 6px;
      padding-right: 20px;

      &.warning {
        & > div {
          &:nth-child(1) {
            color: #d19350;
          }

          &:nth-child(4) {
            color: #d19350;
          }
        }

        .line-arrow {
          background: linear-gradient(to right, transparent, #936b43);

          &:after {
            border-left: 7px solid #dd9b52;
          }
        }
      }

      & > div {
        display: flex;
        align-items: center;
        height: 100%;

        .line-arrow {
          position: relative;
          width: 100%;
          height: 14px;
          background: linear-gradient(to right, transparent, #469cb8);

          &:after {
            content: '';
            position: absolute;
            right: -7px;
            top: 0;
            width: 0;
            height: 0;
            border-top: 7px solid transparent;
            border-bottom: 7px solid transparent;
            border-left: 7px solid #63dbfd;
          }


        }

        &:nth-child(1) {
          justify-content: center;
          text-align: center;
          margin-right: 4px;
          flex-basis: 35px;
          color: #ade7fe;
          background-color: rgba(30, 38, 54, 0.5);
        }

        &:nth-child(2) {
          padding-left: 20px;
          color: white;
          font-size: 16px;
          margin-right: 6px;
          flex-basis: 85px;
          background-color: rgba(19, 30, 55, 0.5);
        }

        &:nth-child(3) {
          padding-right: 10px;
          margin-right: 6px;
          flex: 1;
          background-color: rgba(19, 30, 55, 0.5);
        }

        &:nth-child(4) {
          justify-content: center;
          background-color: rgba(8, 20, 37, 1.0);
          flex-basis: 100px;
          color: #ade7fe;
        }
      }
    }
  }
}

.empty {
  position: absolute;
  top: 40%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.small-arrow {
  display: inline-block;
  position: relative;
  width: 0;
  height: 0;
  border: 6px solid transparent;
  border-bottom: 6px solid #f67d6b;
  margin-bottom: 6px;

  &::after {
    content: "";
    position: absolute;
    bottom: -12px;
    left: -3px;
    width: 6px;
    height: 6px;
    background: #f67d6b;
  }

  &__bottom {
    border: 6px solid transparent;
    border-top: 6px solid #f67d6b;
    margin-bottom: -6px;

    &::after {
      content: "";
      position: absolute;
      bottom: 6px;
      left: -3px;
      width: 6px;
      height: 6px;
      background: #f67d6b;
    }
  }
}

.custom-chart-legend-wrap {
  position: absolute;
  top: 0;
  right: 0;
  height: 100%;
  width: 50%;
  display: flex;
  gap: 40px;
  flex-direction: column;
  justify-content: center;

  .legend-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px 0;
    font-size: 14px;
    color: white;
    border-bottom: 1px dashed #1e2230;

    &__icon {
      display: inline-block;
      width: 10px;
      height: 10px;
      margin-right: 5px;
    }

    &:nth-child(1) {
      .legend-item__icon {
        background-color: #fe7b69;
      }
    }

    &:nth-child(2) {
      .legend-item__icon {
        background-color: #fff33a;
      }
    }

    &:nth-child(3) {
      .legend-item__icon {
        background-color: #a477ff;
      }
    }

  }
}

</style>
