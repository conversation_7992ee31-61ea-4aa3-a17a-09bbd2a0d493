package com.eversec.antivpn.intelligence.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Getter;
import lombok.Setter;

/**
 * 情报导入DTO
 */
@Getter
@Setter
public class VpnMachineLearningCodeDictImportDTO {

    public static final String[] MUST_FILL =
            new String[] { "文件类型", "模型协议", "模型中文名称",
                    "关联vpn名称", "监测模型编号", "监测模型名称",
                    "监测模型版本", "模型及样本文件名称", "模型及样本生成时间"};

    @Excel(name = "文件类型")
    private Integer fileType;

    @Excel(name = "模型协议")
    private String protocolType;

    @Excel(name = "模型中文名称")
    private String modelChName;

    @Excel(name = "关联vpn名称")
    private String vpnName;

    @Excel(name = "监测模型编号")
    private String modelCode;

    @Excel(name = "监测模型名称")
    private String modelName;

    @Excel(name = "监测模型版本")
    private String modelVersion;

    @Excel(name = "模型及样本文件名称")
    private String modeFileName;

    @Excel(name = "模型及样本生成时间")
    private String timeStamp;



}
