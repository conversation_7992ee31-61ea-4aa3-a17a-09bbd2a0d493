package com.eversec.antivpn.intelligence.dto;

import com.eversec.antivpn.intelligence.entity.VpnIntelligenceCommand;
import com.eversec.stark.common.component.criteria.annotaion.Eq;
import com.eversec.stark.common.component.criteria.annotaion.Ge;
import com.eversec.stark.common.component.criteria.annotaion.Le;
import com.eversec.stark.common.component.criteria.query.AbstractQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 情报指令分页查询参数
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023-08-08 10:21
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class VpnIntelligenceCommandQueryConditionDTO extends AbstractQuery<VpnIntelligenceCommand> {

	@Eq(alias = "command_id")
	@Schema(description = "跨境通信情报库下发执行指令ID。当部侧下发时由部侧系统控制惟一；当企业侧系统上报时企业侧内惟一。")
	private Long commandId;

	@Eq(alias = "com_code")
	@Schema(description = "运营商编码，见附录F.2")
	private String comCode;

	@Eq(alias = "province_id")
	@Schema(description = "省级区域编号，见附录F.4")
	private Long provinceId;

	@Eq(alias = "system_code")
	@Schema(description = "省平台业务系统标识。省平台部署：当前省平台业务系统标识；集团部署：省平台业务系统标识")
	private String systemCode;

	@Eq(alias = "version")
	@Schema(description = "跨境通信情报库版本格式：主版本号.副版本号，本规范主版本号为1，副版本号为0，即1.0。")
	private String version;

	@Eq(alias = "network_business_id")
	@Schema(description = "监测网络类型code，见附录F.3。企业侧上报时必填。部侧下发时可选填，当部侧下发为空时表示所有网络类型生效")
	private Integer networkBusinessId;

	@Eq(alias = "operation_type")
	@Schema(description = "操作类型,对该记录的操作类型，包括： 0-增量情报下发； 1-上报； 2-删除； 3-全量情报下发； 4-模型下发； 5-样本下发。")
	private Integer operationType;

	@Eq(alias = "source")
	@Schema(description = "情报来源，FULL：全量下发。INCREASE：增量下发。REPORT：省端上报。INPUT：页面录入")
	private String source;


	@Ge(alias = "create_datetime")
	@Schema(description = "创建时间-start")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date createDatetimeStart;

	@Le(alias = "create_datetime")
	@Schema(description = "创建时间-end")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date createDatetimeEnd;

	@Ge(alias = "report_datetime")
	@Schema(description = "上报时间-start")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date reportDatetimeStart;

	@Le(alias = "report_datetime")
	@Schema(description = "上报时间-end")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date reportDatetimeEnd;


}
