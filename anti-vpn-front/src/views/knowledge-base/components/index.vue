<template>
  <div class="knowledge-base" v-loading="loading">
    <!-- form -->
    <publicForm
      ref="publicForm"
      :formDataLabel="formDataLabel"
      :labelWidth="'0'"
      :form-data="formData"
      @throwEvent="changeItem"
    />
    <section class="is-mb-10 is-text-right">
      <el-button
        type="danger"
        :disabled="!(activeList && activeList.length > 0)"
        @click="batchDelete"
      >
        批量删除
      </el-button>
      <el-button type="primary" @click="addFun">新增</el-button>
      <el-button type="primary" @click="importFun">导入</el-button>
    </section>
    <!-- table -->
    <publicTable
      :tableLabel="tableLabel"
      :tableData="dataList"
      ref="table"
      height="100%"
      :options="{
        checkbox: true,
        highlightCurrentRow: false,
        isString: true,
        border: false
      }"
      @handleSelectionChange="handleSelectionChange"
    >
      <template v-slot:img="{ data }">
        <img
          v-if="data.row.logo"
          :src="data.row.logo"
          width="100"
          height="80"
        />
      </template>
      <template v-slot:handle="{ data }">
        <el-link
          class="is-float-left"
          :underline="false"
          type="primary"
          @click="editBtn(data.row)"
        >
          编辑
        </el-link>
        <el-link
          class="is-ml-10 is-float-left"
          :underline="false"
          type="danger"
          @click="deleteBtn(data.row.id)"
        >
          删除
        </el-link>
      </template>
    </publicTable>
    <!-- 分页 -->
    <el-pagination
      class="is-pt-10 is-float-right"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="pagingInfo.pageNumber"
      :page-sizes="pagingInfo.pageSizes"
      :page-size="pagingInfo.pageSize"
      :layout="pagingInfo.layout"
      :total="pagingInfo.total"
      :key="pagingInfo.total"
    />
    <detailDialog
      ref="detailDialog"
      :type="type"
      @dataFormSubmit="getDataList"
    />
    <importDialog ref="importDialog" :type="type" @submitFun="getDataList" />
  </div>
</template>

<script>
import debounce from 'lodash/debounce'
import cloneDeep from 'lodash/cloneDeep'
import { pagingInfo } from '@/config/base'
import antiVpnService from '@/api/antiVpnService'
import config from '../components/config'

export default {
  name: 'knowledge-base',
  components: {
    detailDialog: () => import('./detail-dialog'),
    importDialog: () => import('./import-dialog')
  },
  props: {
    type: {
      type: String,
      default: () => ''
    }
  },
  data() {
    return {
      loading: false,
      formDataLabel: config[this.type].query, // form树
      formData: {},
      queryKey: config[this.type].queryKey, // 需要保留在query上的查询信息
      tableLabel: config[this.type].tableLabel,
      dataList: [], // table数据
      pagingInfo: cloneDeep(pagingInfo()), // 分页信息
      activeList: []
    }
  },
  computed: {},
  watch: {},
  mounted() {
    this.formData = {}
    for (let name in this.$route.query) {
      if (name === 'pageSize') {
        this.pagingInfo.pageSize = Number(this.$route.query[name])
      } else if (name === 'pageNumber') {
        this.pagingInfo.pageNumber = Number(this.$route.query[name])
      } else {
        this.$set(this.formData, name, this.$route.query[name])
        this.formData[name] = this.$route.query[name]
      }
    }
    this.getDataList()
  },
  methods: {
    // 表单监听回调
    changeItem(val) {
      if (val) {
        this.pagingInfo.pageNumber = 1
      }
      this.debounce(this)
    },
    // 列表查询
    getDataList() {
      this.activeList = []
      this.loading = true
      let param = {
        pageNumber: this.pagingInfo.pageNumber, //当前页
        pageSize: this.pagingInfo.pageSize, //每页展示数量
        ...this.formData
      }
      antiVpnService[config[this.type].getListFunName](param)
        .then(res => {
          this.dataList = res.items || []
          this.pagingInfo.total = res.total || 0
          this.loading = false
        })
        .catch(() => {
          this.loading = false
        })
    },
    // 防抖查询
    debounce: debounce(vm => {
      let tmpData = {
        pageSize: vm.pagingInfo.pageSize,
        pageNumber: vm.pagingInfo.pageNumber
      }
      vm.queryKey.map(e => {
        if (vm.formData[e]) vm.$set(tmpData, e, vm.formData[e])
      })
      vm.$router.push({
        path: vm.$route.path,
        query: tmpData
      })
      vm.getDataList()
    }, 300),
    // 选中框
    handleSelectionChange(val) {
      this.activeList = val
    },
    // 每页数
    handleSizeChange(val) {
      this.pagingInfo.pageSize = val
      this.pagingInfo.pageNumber = 1
      this.changeItem()
    },
    // 当前页
    handleCurrentChange(val) {
      this.pagingInfo.pageNumber = val
      this.changeItem()
    },
    // 编辑
    editBtn(item) {
      this.$refs.detailDialog.openDialog(item)
    },
    // 删除
    deleteBtn(id) {
      this.$confirm('此操作将删除该数据, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.loading = true
          antiVpnService[config[this.type].deleteFunName](id)
            .then(res => {
              this.loading = false
              this.$message({
                type: 'success',
                message: '删除成功!'
              })
              this.getDataList()
            })
            .catch(() => {
              this.loading = false
            })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    // 批量删除
    batchDelete() {
      this.$confirm('此操作将删除已选数据, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.loading = true
          antiVpnService[config[this.type].deleteFunName](
            this.activeList.map(e => e.id)
          )
            .then(res => {
              this.loading = false
              this.$message({
                type: 'success',
                message: '删除成功!'
              })
              this.getDataList()
            })
            .catch(() => {
              this.loading = false
            })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    // 新增
    addFun() {
      this.$refs.detailDialog.openDialog(null)
    },
    // 导入
    importFun() {
      this.$refs.importDialog.openDialog()
    }
  }
}
</script>

<style lang="scss" scoped>
.knowledge-base {
  height: 100%;
  .public-table {
    height: calc(100% - 120px);
  }
}
</style>
