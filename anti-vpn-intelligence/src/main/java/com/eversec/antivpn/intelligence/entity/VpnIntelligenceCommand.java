package com.eversec.antivpn.intelligence.entity;

import com.eversec.antivpn.intelligence.entity.po.VpnIntelligenceCommandPO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * vpn情报指令
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-10
 */
@Getter
@Setter
@Schema(name = "VpnIntelligenceCommand", description = "vpn情报指令")
public class VpnIntelligenceCommand extends VpnIntelligenceCommandPO implements java.io.Serializable {

    private static final long serialVersionUID = 1L;

    /*
     * 扩展字段，数据库中不存在的字段，数据库中不存在的字段需加 @TableField(exist = false)
     * 如字典翻译、子对象、子对象列表等
     */
//    @com.baomidou.mybatisplus.annotation.TableField(exist = false)
//    private String xxx;

}