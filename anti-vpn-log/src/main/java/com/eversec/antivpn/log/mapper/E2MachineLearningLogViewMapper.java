package com.eversec.antivpn.log.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.eversec.antivpn.log.entity.E2MachineLearningLogView;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.eversec.antivpn.log.mapper.vo.*;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-18
 */
@Mapper
@DS("clickhouse")
public interface E2MachineLearningLogViewMapper extends BaseMapper<E2MachineLearningLogView> {

    Long countNum(BaseSearchVO baseSearchVO);

    List<TypeCountVO> getDateHistogram(BaseSearchVO baseSearchVO);

    List<TypeCountVO> getProvinceAggregate(BaseSearchVO baseSearchVO);

    List<TypeCountVO> getProtocolTypeAggregate(BaseSearchVO baseSearchVO);

    Long getResourceNodeCount(BaseSearchVO baseSearchVO);

    List<VpnAggVO> getResourceNodeUserNum(BaseSearchVO baseSearchVO);

    List<VpnHistogramAggVO> getResourceNodeDateHistogram(BaseSearchVO baseSearchVO);

    /**
     * 省-所属市区域上报监测日志 TOP10
     * @param baseSearchVO
     * @return
     */
    List<TypeCountVO> getLogNumByCity(BaseSearchVO baseSearchVO);

    /**
     * 省区域上报监测日志 TOP10
     * @param baseSearchVO
     * @return
     */
    List<TypeCountVO> getLogNumByProvince(BaseSearchVO baseSearchVO);

    /**
     * 访问地区数量
     * @param baseSearchVO
     * @return
     */
    Long getLogNumVisitAreaAndProvinceId(BaseSearchVO baseSearchVO);

    /**
     * 目的地区数量
     * @param baseSearchVO
     * @return
     */
    Long getLogNumPurposeAreaAndProvinceId(BaseSearchVO baseSearchVO);

    /**
     * 获取AI模型识别日志量 TOP10
     * @param baseSearchVO
     * @return
     */
    List<E2MachineLearningLogView> getModelDiscernLogNum(BaseSearchVO baseSearchVO);

    /**
     * 传输层协议类型占比
     * @param baseSearchVO
     * @return
     */
    List<E2MachineLearningLogView> getProtocolTypeLogNum(BaseSearchVO baseSearchVO);

    /**
     * 查询每天按小时分组数据量
     * @param baseSearchVO
     * @return
     */
    List<E2MachineLearningLogView> getAILogByDay(BaseSearchVO baseSearchVO);
    /**
     * 查询周按天分组数据量
     * @param baseSearchVO
     * @return
     */
    List<E2MachineLearningLogView> getAILogByWeek(BaseSearchVO baseSearchVO);

    /**
     * 查询月按周分组数据量
     * @param baseSearchVO
     * @return
     */
    List<E2MachineLearningLogView> getAILogByMonth(BaseSearchVO baseSearchVO);
    /**
     * 查询年按周分组数据量
     * @param baseSearchVO
     * @return
     */
    List<E2MachineLearningLogView> getAILogByYear(BaseSearchVO baseSearchVO);

    /**
     * 根据置信度分组查询数量
     * @param baseSearchVO
     * @return
     */
    List<E2MachineLearningLogView> getLogNumByRateList(BaseSearchVO baseSearchVO);

    Long getVpnUserNum(BaseSearchVO baseSearchVO);

    List<String> getDestCountry(BaseSearchVO baseSearchVO);

    /**
     * 数据来源各系统日志量
     * @param baseSearchVO
     * @return
     */
    List<E2MachineLearningLogView> getSystemLogNum(BaseSearchVO baseSearchVO);


    List<Long> getUserLocation(BaseSearchVO baseSearchVO);


    List<TypeCountVO> getUserDest(BaseSearchVO baseSearchVO);

    List<TypeCountVO> getLocationAggregate(BaseSearchVO baseSearchVO);

    List<TypeCountVO> userCountLogNum(BaseSearchVO baseSearchVO);

    List<TypeCountVO>  getUserDateHistogram(BaseSearchVO baseSearchVO);

    List<TypeCountVO> getNetworkBusinessTypeAggregate(BaseSearchVO baseSearchVO);


    List<TypeCountVO> getSystemLogAggCount(BaseSearchVO baseSearchVO);

    List<TypeCountVO> getCountryResourceNodeNumTop10(BaseSearchVO baseSearchVO);

    List<UserViewVO> getUserAndSrcIp(BaseSearchVO baseSearchVO);

    List<MonitorDataVO> getSystemProvinceCount(@Param("day") Long day, @Param("hour") Integer hour);

    /**
     * 获取AI模型识别日志量Twiter
     * @param baseSearchVO
     * @return
     */
    List<E2MachineLearningLogView> getModelTwiterLogNum(BaseSearchVO baseSearchVO);
}
