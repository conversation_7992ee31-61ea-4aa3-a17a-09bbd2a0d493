<template>
  <div class="pagebox bigScreen" ref="bigPage" id="bigPage">
    <section ref="bigPage_inner" class="bigPage_inner">
      <Header @update="update" :active-tab="activeTab" @tab-click="tabClick" />
      <div>
        <overview ref="overview" v-if="activeTab === 'overview'" />
        <vpn ref="vpn" v-if="activeTab === 'vpn'" />
        <user ref="user" v-if="activeTab === 'user'" />
        <ai ref="ai" v-if="activeTab === 'ai'" />
        <cross ref="cross" v-if="activeTab === 'cross'" />
      </div>
    </section>
  </div>
</template>

<script>
import Header from "@/views/situation/common/Header.vue";
import Overview from "@/views/situation/overview/index.vue";
import Vpn from "@/views/situation/vpn/index.vue";
import User from "@/views/situation/user/index.vue";
import Ai from "@/views/situation/ai/index.vue";
import Cross from "@/views/situation/cross/index.vue";

export default {
  name: "SituationHome",

  components: {
    Header,
    Overview,
    Vpn,
    User,
    Ai,
    Cross
  },

  data() {
    return {
      activeTab: "overview",

      updateData: {
        screenType: "",
        ft: ""
      }
    };
  },

  watch: {
    "$route.query.tab": {
      handler(val) {
        this.updateMainStyle();
        val && this.tabClick(val);
      },
    }
  },

  created() {
    this.activeTab = this.$route.query.tab || "overview";
    this.updateMainStyle();
  },

  mounted() {
    this.$nextTick(() => {
      this.handleResize();
      window.addEventListener("resize", this.handleResize);
    });
  },

  methods: {
    updateMainStyle() {
      this.$emit("onUpdateMain", {
        pageContent: {
          padding: 0,
          background: "#F7F8FC"
        }
      });
    },

    // handleResize() {
    //   this.$refs.bigPage_inner.setAttribute(
    //     "style",
    //     "transform: scale(" +
    //       document.getElementById("bigPage").clientWidth / 1920 +
    //       ");transform-origin: 0 0 0"
    //   );
    // },
    handleResize() {
      let el = this.$refs.bigPage_inner;

      let bigPage = this.$refs.bigPage;
      let w = bigPage.offsetWidth;
      let h = bigPage.offsetHeight;
      // console.log("bigPage", w, h);
      // var w = window.innerWidth;
      // var h = window.innerHeight;
      var nw = 1920,
        nh = 1080;
      var left, top, scale;
      if (w / h > nw / nh) {
        if (w / h >= 2.6) {
          nw = 3120;
        }
        scale = w / nw;
        top = 0;
        left = 0;
      } else {
        scale = h / nh;
        left = 0;
        top = 0;
      }
      this.$nextTick(() => {
        el.style["transform-origin"] = "left top";
        el.style["transform"] = `scale(${scale})`;
        el.style["position"] = `relative`;
        el.style["top"] = `${top}`;
        el.style["left"] = `${left}`;
        el.style["width"] = `1920px`;
        el.style["height"] = `1080px`;
        // const invertedScale = 1 / scale;
        // let playground = document.getElementById('playground')
        // if (playground) {
        //   // 对 playground 元素应用逆缩放和偏移
        //   playground.style.transform = `scale(${invertedScale})`;
        //   playground.style.transformOrigin = 'top left';
        //   playground.style.width = `calc(100% * ${scale})`;
        //   playground.style.height = `calc(100% * ${scale})`;
        // }
      });
    },
    update(screenType, ft) {
      this.updateData.screenType = screenType;
      this.updateData.ft = ft;

      this.$nextTick(() => {
        this.$refs[this.activeTab].update(screenType, ft);
      });
    },

    tabClick(tab) {
      this.activeTab = tab;
      this.update(this.updateData.screenType, this.updateData.ft);
    }
  }
};
</script>

<style lang="scss" scoped>
@import "../common/bigScreen.scss";

.bigPage_inner {
  width: 1920px;
  height: 1080px;
  background: url("./img/bigscreenbg.png") no-repeat;
  background-size: 100% 100%;
}
.pagebox {
  // background: url("./img/bigscreenbg.png") no-repeat;
  // background-size: 100% 100%;
}
</style>
