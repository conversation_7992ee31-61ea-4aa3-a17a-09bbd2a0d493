<template>
  <div class="item">
    <template v-if="field.type=='select'">
      <div class="label-item">
        <label :for="field.label">{{field.label}}</label>
      </div>
      <!-- multiple
       collapse-tags
       多选判断条件
        -->
      <el-select v-model="form[field.prop]" :multiple="field.multiple?true:false" collapse-tags
        :size="size" :name="field.label" :placeholder="'请选择' + field.label" clearable
        class="filter-item rigW" filterable :remote="field.prop == 'organization_id'?true:false"
        :remote-method="(data)=>remoteMethod(data,field)" @change="handleSearch"
        @clear="handleSearch">
        <el-option v-for="(ov) in field.options"
          :key="typeof(ov) == 'string' ? ov : ov[field.valueKey] ? ov[field.valueKey] : ov.value"
          :label="typeof(ov) == 'string' ? ov : ov[field.labelKey] ? ov[field.labelKey] : ov.label"
          :value="typeof(ov) == 'string' ? ov : ov[field.valueKey] ? ov[field.valueKey] : ov.value" />
      </el-select>
    </template>
    <template v-else-if="field.type=='pickerDate'">
      <div class="label-item">
        <label :for="field.label">{{field.label}}</label>
      </div>
      <el-date-picker class="rigW" v-model="form[field.prop]" @change="handleSearch"
        @clear="handleSearch" :size="size" range-separator="至" type="datetimerange"
        :picker-options="pickerOptions" start-placeholder="开始日期" end-placeholder="结束日期"
        :value-format="field.valueFormat || 'timestamp'" />
    </template>
    <template v-else-if="field.type=='pickerDate1'">
      <div class="label-item">
        <label :for="field.label">{{field.label}}</label>
      </div>
      <el-date-picker class="rigW" v-model="form[field.prop]" @change="handleSearch"
        @clear="handleSearch" :size="size" range-separator="至" type="daterange"
        :picker-options="pickerOptions" start-placeholder="开始日期" end-placeholder="结束日期"
        :value-format="field.valueFormat || 'timestamp'" />
    </template>
    <template v-else-if="field.type=='date'">
      <div class="label-item">
        <label :for="field.label">{{field.label}}</label>
      </div>
      <el-date-picker class="rigW" v-model="form[field.prop]" @change="handleSearch"
        @clear="handleSearch" :size="size" :placeholder="field.label" type="date"
        :format="'yyyy-MM-dd'" :value-format="field.format?field.format:'yyyy-MM-dd'" />
    </template>
    <template v-else-if="field.type=='time'">
      <div class="label-item">
        <label :for="field.label">{{field.label}}</label>
      </div>
      <el-date-picker class="rigW" v-model="form[field.prop]" @change="handleSearch"
        @clear="handleSearch" :size="size" :placeholder="field.label" type="datetime"
        format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss" />
    </template>
    <template v-else-if="field.type=='province'">
      <div class="label-item">
        <label :for="field.label">{{field.label}}</label>
      </div>
      <div class="rigW">
        <el-select v-if="field.prop1" v-model="form[field.prop1]" :size="size" :name="field.label"
          :style="{width: '50%'}" clearable filterable class="filter-item mrSpace"
          placeholder="请选择省" @change="handleChange(form[field.prop1])" :disabled="disabledTag"
          @clear="handleSearch">
          <el-option v-for="(item, index) in province" :key="index" :label="item.itemName"
            :value="item.itemName">
          </el-option>
        </el-select>
        <el-select v-if="field.prop2" v-model="form[field.prop2]" :size="size"
          :style="{width: '50%'}" clearable class="filter-item" placeholder="请选择市" filterable
          @change="handleSearch" @clear="handleSearch">
          <el-option v-for="(item, index) in cityList" :key="index" :label="item.itemName"
            :value="item.itemName">
          </el-option>
        </el-select>
      </div>
      <!-- <AddressBox @updateAddr="updateAddr" :address="address2" :level="2"></AddressBox> -->
    </template>

    <template v-else-if="field.type=='groupingId'">
      <div class="label-item">
        <label :for="field.label">{{field.label}}</label>
      </div>
      <div class="rigW">
        <el-select v-if="field.prop1" v-model="form[field.prop1]" :size="size" :name="field.label"
          :style="{width: '50%'}" clearable class="filter-item mrSpace" placeholder="请选择一级分组"
          @change="handleGroupChange(form[field.prop1])" @clear="handleSearch">
          <el-option v-for="(item, index) in firstGroupingList" :key="index" :label="item.itemName"
            :value="item.id">
          </el-option>
        </el-select>
        <el-select v-if="field.prop2" v-model="form[field.prop2]" :size="size"
          :style="{width: '50%'}" clearable class="filter-item" placeholder="请选择二级分组"
          @change="handleGroupChildChange(form[field.prop2])" @clear="handleSearch">
          <el-option v-for="(item, index) in secondGroupingList" :key="index" :label="item.itemName"
            :value="item.id">
          </el-option>
        </el-select>
      </div>
      <!-- <AddressBox @updateAddr="updateAddr" :address="address2" :level="2"></AddressBox> -->
    </template>

    <template v-else-if="field.type=='province2'">
      <div class="label-item">
        <label :for="field.label">{{field.label}}</label>
      </div>
      <el-select v-if="field.prop" v-model="form[field.prop]" :size="size" :name="field.label"
        clearable class="filter-item rigW" placeholder="请选择省"
        @change="handleChange2(form[field.prop])" @clear="handleSearch">
        <el-option v-for="(item, index) in provinceList" :key="index" :label="item.label"
          :value="item.label">
        </el-option>
      </el-select>
    </template>
    <template v-else>
      <div class="label-item">
        <label :for="field.label">{{field.label}}</label>
      </div>
      <el-input class="filter-item rigW" v-model="form[field.prop]" :size="size" :name="field.label"
        :placeholder="'请输入' + field.label" clearable @keyup.enter.native="handleSearch"
        @clear="handleSearch" />
    </template>
  </div>
</template>

<script>
import { dicList } from '@/api/publicCodeTable';
import province from '@/utils/province';
import { apiSingleTable } from '@/utils/biQueryRequest';
export default {
  name: 'Item',
  props: {
    form: Object,
    field: Object,
    // width: {
    //   type: Number,
    //   default: '200'
    // },
    // selectWidth: {
    //   type: Number,
    //   default: 'auto'
    // },
    size: {
      type: String,
      default: 'small'
    }
  },
  data() {
    return {
      provinceList: province,
      firstGroupingList: [],
      secondGroupingList: [],
      currentOneLevelGroupId: '',
      province: [],
      cityList: [],
      countyList: [], // 区县列表
      disabledTag: false,
      pickerOptions: {
        shortcuts: [
          {
            text: '最近一天',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: '最近一周',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: '最近一个月',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit('pick', [start, end]);
            }
          }
        ]
      }
    };
  },
  async created() {
    if (this.field.prop1) {
      this.province = await dicList('brm_area');
      if (window.globalCache.userInfo.area.length > 1) {
        // this.$nextTick(() => {
        // this.handleChange(window.globalCache.userInfo.area[1], false)
        // this.disabledTag = true;
        // this.form[this.field.prop1] = window.globalCache.userInfo.area[1]
        // })
      }
    }
  },
  methods: {
    handleGroupChildChange(value, tag = true) {
      this.$forceUpdate();
      if (tag) {
        this.handleSearch();
      }
    },
    // methods: {
    filterMethod(data, field) {
      console.log(this.form[field.prop]);
      // this.form[field.prop]
      console.log(data, field);
    },
    remoteMethod(data, field) {
      apiSingleTable(
        {
          dsId: '71c8f6cf91fe4f8083cccdf6a5b96a89',
          tableName: 'dim_com_base_asset_organization',
          tblType: 'TABLE',
          tblId: '',
          conditions: [
            {
              fieldName: 'organization_name',
              conditionExpressions: [
                {
                  operator: 'like',
                  values: [`%${data}%`]
                }
              ]
            }
          ],
          paging: 1,
          limit: 100
        },
        true
      ).then((d) => {
        console.log(d.list, '模糊查询单位');
        let idList = [];
        d.list.forEach((item) => {
          idList.push({
            itemName: item.organization_name,
            itemValue: item.id
          });
        });
        field.options = idList;
      });
      // console.log(this.form[field.prop])
      // this.form[field.prop]
      console.log(data, field);
    },

    async handleGroupChange(val, tag = true) {
      this.currentOneLevelGroupId = val;
      await this.childGetDataGroupChange();

      if (tag) {
        this.handleSearch();
      }
      // console.log(val,'一级分组选择')
    },
    handleSearch() {
      this.$emit('search');
    },
    handleChange(val, tag = true) {
      // console.log(val, '执行到这里了么')
      this.form[this.field.prop2] = '';
      if (!val) {
        return (this.cityList = []);
      }
      this.cityList = this.province.filter(
        (item) => item.itemName === val
      )[0].childs;
      if (tag) {
        this.handleSearch();
      }
    },
    handleChange2(val) {
      this.form[this.field.prop2] = '';
      if (!val) {
        return (this.cityList = []);
      }
      this.cityList = province.filter((item) => item.label === val)[0].children;
      this.handleSearch();
    },
    updateAddr(item) {
      console.log('省市区2：', item);
    }
  },
  data() {
    return {
      firstGroupingList: [],
      secondGroupingList: [],
      currentOneLevelGroupId: '',
      province: [],
      cityList: [],
      countyList: [], // 区县列表
      disabledTag: false,
      pickerOptions: {
        shortcuts: [
          {
            text: '最近一天',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: '最近一周',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: '最近一个月',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit('pick', [start, end]);
            }
          }
        ]
      }
    };
  },
  async created() {
    // console.log(this.field, getDataGroupChange, '能获取到子系统中的方法么')

    if (this.field.prop1) {
      this.province = await dicList('brm_area');
      if (window.globalCache.userInfo.area.length > 1) {
        // this.$nextTick(() => {
        // this.handleChange(window.globalCache.userInfo.area[1], false)
        // this.disabledTag = true;
        // this.form[this.field.prop1] = window.globalCache.userInfo.area[1]
        // })
      }
    }
  },
  methods: {
    handleGroupChildChange(value, tag = true) {
      this.$forceUpdate();
      if (tag) {
        this.handleSearch();
      }
    },
    // methods: {
    filterMethod(data, field) {
      console.log(this.form[field.prop]);
      // this.form[field.prop]
      console.log(data, field);
    },
    remoteMethod(data, field) {
      apiSingleTable(
        {
          dsId: '71c8f6cf91fe4f8083cccdf6a5b96a89',
          tableName: 'dim_com_base_asset_organization',
          tblType: 'TABLE',
          tblId: '',
          conditions: [
            {
              fieldName: 'organization_name',
              conditionExpressions: [
                {
                  operator: 'like',
                  values: [`%${data}%`]
                }
              ]
            }
          ],
          paging: 1,
          limit: 100
        },
        true
      ).then((d) => {
        console.log(d.list, '模糊查询单位');
        let idList = [];
        d.list.forEach((item) => {
          idList.push({
            itemName: item.organization_name,
            itemValue: item.id
          });
        });
        field.options = idList;
      });
      // console.log(this.form[field.prop])
      // this.form[field.prop]
      console.log(data, field);
    },

    async handleGroupChange(val, tag = true) {
      this.currentOneLevelGroupId = val;
      await this.childGetDataGroupChange();

      if (tag) {
        this.handleSearch();
      }
      // console.log(val,'一级分组选择')
    },
    handleSearch() {
      this.$emit('search');
    },
    handleChange(val, tag = true) {
      // console.log(val, '执行到这里了么')
      this.form[this.field.prop2] = '';
      if (!val) {
        return (this.cityList = []);
      }
      this.cityList = this.province.filter(
        (item) => item.itemName === val
      )[0].childs;
      if (tag) {
        this.handleSearch();
      }
    },
    updateAddr(item) {
      console.log('省市区2：', item);
    }
  }
};
</script>
<style scoped lang="scss">
.item {
  float: left;
  display: flex;

  ::v-deep .filter-item.el-input .el-input__suffix {
    padding-bottom: 10px;
  }

  ::v-deep .el-radio-button {
    margin-bottom: 5px;
  }

  ::v-deep .el-radio-button:first-child {
    border-left: none;
  }

  .field-title {
    color: #606266;
    font-family: sans-serif;
    font-size: 14px;
  }

  .field-input {
    display: inline-block;
  }

  .label-item {
    font-family: 'Arial Normal', 'Arial', sans-serif;
    font-weight: 400;
    font-style: normal;
    font-size: 14px;
    letter-spacing: normal;
    color: #333333;
    // line-height: 40px;
    width: 100px;
    line-height: 20px;
    display: flex;
    align-items: center;
  }

  .rigW {
    width: calc(100% - 100px);
    display: flex;
  }

  ::v-deep .el-range-separator {
    padding: 0;
  }

  ::v-deep .el-range-editor {
    min-width: 260px;
  }

  .mrSpace {
    margin-right: 8px;
  }
}
</style>
