package com.eversec.antivpn.log.api.dto;

import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * DTO
 * </p>
 * <p>
 * api接口数据传输对象（用于save、update接收参数，list、page、find返回对象等）
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-08
 */
@Getter
@Setter
@Schema(name = "E2MachineLearningLogDTO", description = "")
public class E2MachineLearningLogDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String version;

    private String comCode;

    private Integer networkBusinessId;

    private Long logId;

    private Long houseId;

    private String houseName;

    private Long logProvinceId;

    private Long logCityId;

    private Long logCountyId;

    private String srcIp;

    private Long srcPort;

    private String srcCountry;

    private String srcProvinceId;

    private String srcInfo;

    private String destIp;

    private Long destPort;

    private String dstCountry;

    private String dstProvinceId;

    private String destInfo;

    private Long trafficType;

    private Long protocolType;

    private String modelCode;

    private String modelName;

    private String modelVersion;

    private String vpnName;

    private Integer rate;

    private String vpnDomain;

    private String vpnIp;

    private String vpnUrl;

    private String payLoad;

    private Integer isUploadFile;

    private String attachment;

    private LocalDateTime accessTime;

    private LocalDateTime timestamp;

    private Long day;

    private Integer hour;
}