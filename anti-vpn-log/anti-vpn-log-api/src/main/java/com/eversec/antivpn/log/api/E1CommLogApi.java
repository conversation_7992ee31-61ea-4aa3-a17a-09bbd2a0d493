package com.eversec.antivpn.log.api;


import com.eversec.antivpn.emnus.ScreenTypeEnum;
import com.eversec.antivpn.log.api.dto.TypeCountDTO;
import com.eversec.antivpn.log.api.dto.base.AggCountDTO;
import com.eversec.antivpn.log.api.dto.base.DateHistogramDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import com.eversec.framework.feignboot.conf.EversecFeignDecoderConfiguration;
import com.eversec.framework.feignboot.conf.EversecFeignLogConfiguration;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
*  Api接口
*
* 模块对外暴露的api
*
* @FeignClient 注解参数解释
* value: 服务提供者提供的服务名
* url: 开发、测试环境调试无法通过服务名调用，填写服务对外暴露的地址。该参数有值 value则不生效，该参数为空时使用value值
* path: 服务前缀，实现类 @RequestMapping 也使用该值
* contextId: 全局唯一，默认为包名加类名
* configuration:
*          EversecFeignDecoderConfiguration: 自动解包，异常传递。
*          EversecFeignLogConfiguration: 记录请求日志。默认开启日志，可通过如下参数控制是否记录日志
*                  eversec.feign.request-log=true
*                  eversec.feign.result-log=true
*
*/

@Validated
@Tag(name = "E1话单：跨境通信监测日志")
@FeignClient(value = "appnamexxxxxxxxx",
        path = com.eversec.antivpn.log.api.E1CommLogApi.PATH,
        url = "${appnamexxxxxxxx.log.url:}",
        contextId = "com.eversec.antivpn.log.api.E1CommLogApi",
        configuration = { EversecFeignDecoderConfiguration.class, EversecFeignLogConfiguration.class})
public interface E1CommLogApi {

    String PATH = "/log/e1CommLogApi";

    @Operation(summary = "总体态势-上报情报监测日志")
    @GetMapping("/timeRangeAndTodayCount")
    AggCountDTO timeRangeAndTodayCount(@RequestParam ScreenTypeEnum screenTypeEnum);

    @Operation(summary = "总体态势-匹配VPN协议TOP10")
    @GetMapping("/vpnType5ContentTypeAggregate")
    List<TypeCountDTO> vpnType5ContentTypeAggregate(ScreenTypeEnum screenTypeEnum);

    @Operation(summary = "总体态势-应用层协议占比TOP10")
    @GetMapping("/applicationProtocolTypeCount")
    List<TypeCountDTO> applicationProtocolTypeAggregate(@RequestParam(required = true) ScreenTypeEnum screenTypeEnum);

    @Operation(summary = "通过log_id删除")
    @GetMapping("/deleteByLogId")
    String deleteByLogId(@RequestParam Long logId);


}





