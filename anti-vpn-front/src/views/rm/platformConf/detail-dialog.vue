<template>
  <el-dialog
    class="dialog-handle"
    :title="title"
    :visible.sync="visible"
    :close-on-click-modal="false"
    :loading="loading"
  >
    <!-- form -->
    <publicForm
      ref="publicForm"
      :formDataLabel="formDataLabel"
      :labelWidth="'140px'"
      :form-data="formData"
    />
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" :loading="loading" @click="dataFormSubmit">
        确定
      </el-button>
    </span>
  </el-dialog>
</template>

<script>
import cloneDeep from 'lodash/cloneDeep'
import antiVpnService from '@/api/antiVpnService'

export default {
  name: 'knowledge-base-dialog',
  components: {},
  props: {
    type: {
      type: String,
      default: () => ''
    }
  },
  data() {
    return {
      loading: false,
      visible: false,
      title: '',
      formDataLabel: [
        [
          {
            label: '名称',
            val: 'name',
            type: 'input'
          }
        ],
        [
          {
            label: '配置类型',
            val: 'configType',
            type: 'input'
          }
        ],
        [
          {
            label: '启用状态',
            val: 'enabled',
            type: 'switch'
          }
        ],
        [
          {
            label: '运营商编码',
            val: 'comCode',
            type: 'dic-select',
            enumKey: 'ISP_CODE'
          }
        ],
        [
          {
            label: '省级区域编号',
            val: 'provinceId',
            type: 'dic-select',
            enumKey: 'PROVINCE_CODE'
          }
        ],
        [
          {
            label: '省平台业务系统标识',
            val: 'systemCode',
            type: 'dic-select',
            enumKey: 'SYSTEM_CODE'
          }
        ],
        [
          {
            label: 'DC消费的目录',
            val: 'southReceiveDir',
            type: 'input'
          }
        ],
        [
          {
            label: '上报ftp地址',
            val: 'northReportFtpAddress',
            type: 'input'
          }
        ],
        [
          {
            label: '上报ftp',
            val: 'northReportFtpUsername',
            type: 'input'
          }
        ],
        [
          {
            label: '上报ftp',
            val: 'northReportFtpPassword',
            type: 'input'
          }
        ],
        [
          {
            label: '上报加密密钥',
            val: 'northReportSecretKey',
            type: 'input'
          }
        ],
        [
          {
            label: '情报库ftp地址',
            val: 'northIntelligenceFtpAddress',
            type: 'input'
          }
        ],
        [
          {
            label: '情报库ftp',
            val: 'northIntelligenceFtpUsername',
            type: 'input'
          }
        ],
        [
          {
            label: '情报库ftp',
            val: 'northIntelligenceFtpPassword',
            type: 'input'
          }
        ],
        [
          {
            label: '情报库解密密钥',
            val: 'northIntelligenceSecretKey',
            type: 'input'
          }
        ],
        [
          {
            label: '上报企业状态',
            val: 'northCurrentNetworkBusinessIds',
            type: 'input'
          }
        ],
        [
          {
            label: '情报库目录',
            val: 'southIntelligenceDir',
            type: 'input'
          }
        ],
        [
          {
            label: '集团调用省平台webservice地址',
            val: 'southWsAddress',
            type: 'input'
          }
        ],
        [
          {
            label: '接收解密密钥',
            val: 'southReceiveSecretKey',
            type: 'input'
          }
        ],
        [
          {
            label: 'smart服务地址',
            val: 'southSmartAddress',
            type: 'input'
          }
        ],
        [
          {
            label: 'smart用户名',
            val: 'southSmartUsername',
            type: 'input'
          }
        ],
        [
          {
            label: 'smart密码',
            val: 'southSmartPassword',
            type: 'input'
          }
        ]
      ], // form树
      formData: {}
    }
  },
  computed: {},
  watch: {},
  mounted() {},
  methods: {
    // 打开dialog
    openDialog(e) {
      if (e) {
        this.formData = cloneDeep(e)
        this.title = `修改平台配置`
      } else {
        this.title = `新增平台配置`
        this.formData = {}
      }
      this.visible = true
      this.$nextTick(() => {
        this.$refs.publicForm.$refs.form.clearValidate()
      })
    },
    // 提交前数据验证以及格式转换
    dataFormSubmit() {
      this.$refs.publicForm.$refs.form.validate(valid => {
        if (valid) {
          this.loading = true
          let param = this.formData
          this.submit(param)
        }
      })
    },
    // 提交表单
    submit(param) {
      antiVpnService[
        this.formData.id
          ? 'platformInterfaceInfoUpdateById'
          : 'platformInterfaceInfoSave'
      ](param)
        .then(res => {
          this.$message({
            message: this.formData.id ? '修改成功' : '新增成功',
            type: 'success'
          })
          this.loading = false
          this.visible = false
          this.$emit('dataFormSubmit')
        })
        .catch(() => {
          this.loading = false
        })
    }
  }
}
</script>

<style lang="scss" scoped></style>
