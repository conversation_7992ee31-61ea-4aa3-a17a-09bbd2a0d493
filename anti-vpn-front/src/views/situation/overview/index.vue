<template>
  <!-- 综合态势-监测总览 -->
  <section class="content">
    <div class="con_left">
      <TopNum :numList="numList"></TopNum>
      <div class="leftbox mt20">
        <div class="leftbox_l" style="margin-top: 20px">
          <Mybox title="匹配VPN 协议 TOP10" style="height: 438px;">
            <Pie
              :pieData="vpnPro_list"
              v-loading="vpnPro_loading"
              element-loading-background="rgba(0, 0, 0, 0.1)"
            />
          </Mybox>
          <Mybox class="mt20" title="匹配检测方式" style="margin-top: 69px">
            <Pie2
              :pieData="pie1_list"
              v-loading="pie1_loading"
              element-loading-background="rgba(0, 0, 0, 0.1)"
              ref="pie2Ref"
              :color="[
                '#2884ff',
                '#1ba5fe',
                '#68eec9',
                '#7ae15e',
                '#fcf94c',
                '#fdd35a'
              ]"
            />
          </Mybox>
        </div>

        <div class="leftbox_mid">
          <section style="height:580px;">
            <MapChina
              ref="mapRef"
              v-loading="map_loading"
              element-loading-background="rgba(0, 0, 0, 0.2)"
              :mapD="mapD"
              v-if="!isProvince"
            />
            <MapShandong
              :mapD="mapD"
              :myTooltip="myTooltip"
              v-if="isProvince && provinceName == 'shandong'"
            />
          </section>

          <mid2 class="ml20" ref="mid2Ref" />
        </div>
      </div>
    </div>
    <div class="con_rig">
      <rig1 ref="rig1Ref" />

      <Mybox title="用户使用的网络类型占比">
        <Rank
          :lists="net_list"
          v-loading="net_loading"
          element-loading-background="rgba(0, 0, 0, 0.2)"
          style="height: 280px"
        />
      </Mybox>

      <!-- 集团端显示 -->
      <Mybox class="mt20" title="省区域上报监测日志TOP10" v-if="!isProvince">
        <AreaTop10
          v-loading="map_loading"
          element-loading-background="rgba(0, 0, 0, 0.2)"
          :lists="area_list"
        />
      </Mybox>
      <!-- 省端显示 -->
      <Mybox class="mt20" title="各系统上报监测日志占比" v-else>
        <Pie3 :pieData="rig3_pieD" />
      </Mybox>
    </div>
  </section>
</template>

<script>
import api from "@/api/rm/dic";
import { getDicAll, getDicKey2Val } from "@/assets/js/dic.js";
import overviewApi from "./overviewApi.js";
import Header from "../common/Header.vue";
import TopNum from "./TopNum.vue";
import Mybox from "../common/Mybox.vue";
import Pie from "../common/Pie.vue";
import Pie2 from "../common/Pie2.vue";
import Pie3 from "../common/Pie3.vue";
import rig1 from "./rig1.vue";
import mid2 from "./mid2.vue";
import AreaTop10 from "../common/AreaTop10.vue";
import Rank from "../common/Rank.vue";

import MapChina from "./MapChinaBar.vue";
import MapShandong from "../common/map/Shandong.vue";
export default {
  components: {
    Header,
    Mybox,
    Pie,
    Pie2,
    Pie3,
    TopNum,
    rig1,
    mid2,
    AreaTop10,
    Rank,
    MapChina,
    MapShandong
  },
  data() {
    return {
      searchTime: {
        screenTypeEnum: "TODAY"
      },
      numList: [
        {
          name: "上报情报监测日志",
          dateRangeCount: 0,
          todayCount: 0,
          icon: require("./img/icon1.png")
        },
        {
          name: "上报AI模型监测日志",
          dateRangeCount: 0,
          todayCount: 0,
          icon: require("./img/icon2.png")
        },
        {
          name: "上报DNS日志",
          dateRangeCount: 0,
          todayCount: 0,
          icon: require("./img/icon3.png")
        },
        {
          name: "上报企业情报",
          dateRangeCount: 0,
          todayCount: 0,
          icon: require("./img/icon4.png")
        },
        {
          name: "情报总量",
          dateRangeCount: 0,
          todayCount: 0,
          icon: require("./img/icon5.png")
        }
      ],

      receiveOpts: null,
      chuanshuOpt: null,
      netTypeOpt: null,
      bigPage: null,
      dicAll: {},
      map_loading: false,
      mapD: [],
      myTooltip:
        '<div class="tooltipbox">' +
        '<div class="provincename">${d.name} </div>' +
        '<div class="tooltip-item">' +
        '<div class="item-name">排行：</div>' +
        '<div class="item-value color_num">${d.idx}</div>' +
        "</div>" +
        '<div class="tooltip-item">' +
        '<div class="item-name">今日上报：</div>' +
        '<div class="item-value color_num">${d.todayNum}</div>' +
        "</div>" +
        '<div class="tooltip-item">' +
        '<div class="item-name">总上报：</div>' +
        '<div class="item-value color_num2"> ${d.numFt}</div>' +
        "</div>" +
        "</div>",
      map_loading: false,
      provinceOpt: [],
      cityOpt: [],
      pie1_list: [],
      pie1_loading: false,
      vpnPro_list: [],
      vpnPro_loading: false,
      net_list: [],
      net_loading: false,
      area_list: [],
      rig3_pieD: []
    };
  },
  computed: {
    isProvince() {
      return localStorage.isProvince == 'true';
    },
    provinceName() {
      return localStorage.provinceName;
    }
  },
  async created() {
    this.$emit("onUpdateMain", {
      pageContent: {
        padding: 0,
        background: "#F7F8FC"
      }
    });
  },
  async mounted() {
    this.bigPage = this.$refs.bigPage;
  },
  methods: {
    async update(screenType, ft) {
      if (screenType) {
        this.searchTime = {
          screenTypeEnum: screenType
        };
      }
      // 字典
      this.dicAll = await getDicAll();
      console.log("index-字典:", this.dicAll, screenType, ft);
      // 字典
      this.provinceOpt = await api.getDictList("PROVINCE_CODE");
      if (this.isProvince) {
        this.cityOpt = await api.getDictList("CITY_CODE");
        this.rig3();
      }

      this.getTopNum();
      this.left();

      this.getMapD();
      this.$refs.mid2Ref.init(this.searchTime);

      // rig
      this.$refs.rig1Ref.init(this.searchTime);
      this.getProtocal_net();
    },
    async rig3() {
      let systemOpt = await api.getDictList("SYSTEM_CODE");
      overviewApi.systemAggCount(this.searchTime).then(res => {
        console.log("rig3-province", res);
        let d = (res || []).filter(el => el.key);
        this.rig3_pieD = d.map(el => {
          return {
            ...el,
            name: getDicKey2Val(el.key, systemOpt),
            value: el.num
          };
        });
      });
    },
    getTopNum() {
      overviewApi.timeRangeAndTodayCount(this.searchTime).then(res => {
        this.numList[0].dateRangeCount = res.dateRangeCount;
        this.numList[0].todayCount = res.todayCount;
      });

      overviewApi.aiCount(this.searchTime).then(res => {
        console.log("topnum-2:", res);
        this.numList[1].dateRangeCount = res.dateRangeCount;
        this.numList[1].todayCount = res.todayCount;
      });
      overviewApi.dnsCount(this.searchTime).then(res => {
        console.log("topnum-3:", res);
        this.numList[2].dateRangeCount = res.dateRangeCount;
        this.numList[2].todayCount = res.todayCount;
      });
      overviewApi.reportVpnIntelligence(this.searchTime).then(res => {
        console.log("topnum-4:", res);
        this.numList[3].dateRangeCount = res.dateRangeCount;
        this.numList[3].todayCount = res.todayCount;
      });
      overviewApi.totalCount(this.searchTime).then(res => {
        console.log("topnum-5:", res);
        this.numList[4].dateRangeCount = res.dateRangeCount;
        this.numList[4].todayCount = res.todayCount || 0;
      });
    },
    async left() {
      this.pie1_loading = true;
      let checkTypeOpt = await api.getDictList("INTELLIGENCE_CONTENT_TYPE");
      overviewApi
        .vpnTypeAggregate(this.searchTime)
        .then(res => {
          console.log("l1-匹配监测方式", res);
          let d = (res || []).filter(el => el.key);
          this.pie1_list = d.map(el => {
            return {
              ...el,
              name: getDicKey2Val(el.key, checkTypeOpt),
              value: el.num
            };
          });
          this.$refs.pie2Ref.init("", "", this.pie1_list);
        })
        .finally(() => {
          this.pie1_loading = false;
        });

      this.vpnPro_loading = true;
      overviewApi
        .vpnType5ContentTypeAggregate(this.searchTime)
        .then(res => {
          console.log("l2-匹配VPN协议", res);
          let d = (res || []).filter(el => el.key);
          this.vpnPro_list = d.map(el => {
            return {
              ...el,
              name: getDicKey2Val(el.key, this.dicAll.VPN_PROTOCOL),
              value: el.num
            };
          });
        })
        .finally(() => {
          this.vpnPro_loading = false;
        });
    },
    getDicKey2Val(enumKey, dicArr, keyName = "enumVal") {
      if (!dicArr) return;
      let f = dicArr.find(el => el.enumKey == enumKey);
      if (f) return f[keyName];
      return false;
    },
    getNum(n) {
      let numFt = n;
      if (n >= 10000 && n < 100000000) {
        n = (n / 10000).toFixed(2);
        numFt = n + "万";
      } else if (n >= 100000000) {
        n = (n / 100000000).toFixed(2);
        numFt = n + "亿";
      }
      return numFt;
      // return { num: n, numFt };
    },
    getMapD() {
      this.map_loading = true;
      let opt = this.isProvince ? this.cityOpt : this.provinceOpt;

      overviewApi
        .provinceAggregate(this.searchTime)
        .then(res => {
          let total = (res || []).reduce((n, cur) => (n += cur.num), 0);

          let d = res.filter(el => {
            let nameFt = this.getDicKey2Val(el.key, opt); // 匹配不上的不返回
            el.name = nameFt;
            return nameFt;
          });

          this.mapD = d.map((el, i) => {
            return {
              ...el,
              value: el.num,
              percent: ((el.num * 100) / total).toFixed(0) + "%",
              numFt: this.getNum(el.num),
              todayNumFt: this.getNum(el.todayNum),
              idx: i + 1 // 排名
            };
          });

          this.area_list = this.mapD.slice(0, 10);
          console.log("mid-map", res);
          console.log("mid-map-过滤不匹配的", this.mapD);
        })
        .finally(() => (this.map_loading = false));

      // this.mapD = [
      //   { name: "济南市", value: 1000, todayNum: 10  },
      //   { name: "青岛市", value: 900, todayNum: 10 },
      //   { name: "淄博市", value: 500, todayNum: 10 },
      //   { name: "枣庄市", value: 400, todayNum: 10 },
      //   { name: "东营市", value: 200, todayNum: 10 },
      //   { name: "莱芜市", value: 100, todayNum: 10 }
      // ];
    },
    getProtocal_net() {
      this.net_loading = true;
      overviewApi
        .networkBusinessTypeAggregate(this.searchTime)
        .then(res => {
          let d = (res || []).filter(el => el.key);
          this.net_list = d.map((el, i) => {
            return {
              ...el,
              name: getDicKey2Val(el.key, this.dicAll.NETWORK_TYPE),
              value: el.num
            };
          });
          console.log("rig2-用户使用的网络类型占比", res, this.net_list);
        })
        .finally(el => (this.net_loading = false));
    }
  }
};
</script>

<style lang="scss" scoped>
.content {
  display: flex;
  // min-height: 700px;
  padding: 0 30px;
  width: 100%;
  justify-content: space-between;
  .con_left {
    width: 1380px;
    .leftbox {
      display: flex;
      justify-content: space-between;
      .leftbox_l {
        width: 440px;
      }
      .leftbox_mid {
        width: calc(100% - 440px);
      }
    }
  }
  .con_rig {
    width: 440px;
  }
}
</style>
