package com.eversec.antivpn.log.schedule;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.text.csv.CsvUtil;
import cn.hutool.core.text.csv.CsvWriter;
import com.eversec.antivpn.emnus.AntiVpnReportCodeEnum;
import com.eversec.antivpn.emnus.ScreenTypeEnum;
import com.eversec.antivpn.log.entity.ProvincePlatformStatus;
import com.eversec.antivpn.log.mapper.LogVpnIntelligenceMapper;
import com.eversec.antivpn.log.mapper.ProvincePlatformStatusMapperExt;
import com.eversec.antivpn.log.mapper.vo.BaseSearchVO;
import com.eversec.antivpn.log.mapper.vo.PlatformInterfaceVO;
import com.eversec.antivpn.log.mapper.vo.TypeCountVO;
import com.eversec.antivpn.support.province.api.dto.ProvinceStatusDTO;
import com.eversec.antivpn.util.ReportUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/12/4 17:54
 **/
@Slf4j
@Component
public class TestDataReport {

    @Value("${app.anti-vpn.deploy-place}")
    private String deployPlace;

    @Resource
    private ProvincePlatformStatusMapperExt provincePlatformStatusMapperExt;

    @Resource
    private LogVpnIntelligenceMapper logVpnIntelligenceMapper;

    @Resource
    private com.eversec.antivpn.config.AntiVpnProperties antiVpnProperties;

    public List<PlatformInterfaceVO> monitorSystemReport(Integer status) {
        log.info("当前节点为leaser节点，执行上报企业状态操作");
        log.info("当前平台类型为：" + deployPlace);
        if ("system".equals(deployPlace)) {
            log.info("执行system类型监控");
            return systemTypeMonitor(status);
        }
        if ("province".equals(deployPlace)) {
            log.info("执行" + deployPlace + "类型监控");
            return provinceTypeMonitor(status);
        }
        return null;
    }

    private List<PlatformInterfaceVO> systemTypeMonitor(Integer status) {
        PlatformInterfaceVO platformInterfaceVO = logVpnIntelligenceMapper.getCurrentPlatformInterfaceInfo();
        platformInterfaceVO.setCurrentState(status);
        List<PlatformInterfaceVO> dataList = new ArrayList<>();
        dataList.add(platformInterfaceVO);
        saveProvincePlatformStatus(dataList);
        doReport(dataList);
        return dataList;
    }


    public void saveProvincePlatformStatus(List<PlatformInterfaceVO> platformInterfaceVOList) {
        if (platformInterfaceVOList == null || platformInterfaceVOList.size() == 0) return;
        for (int i = 0; i < platformInterfaceVOList.size(); i++) {
            PlatformInterfaceVO platformInterfaceVO = platformInterfaceVOList.get(i);
            ProvincePlatformStatus provincePlatformStatus = new ProvincePlatformStatus();
            provincePlatformStatus.setProvinceId(platformInterfaceVO.getProvinceId());
            provincePlatformStatus.setComCode(platformInterfaceVO.getComCode());
            provincePlatformStatus.setSystemCode(platformInterfaceVO.getSystemCode());
            provincePlatformStatus.setCurrentState(platformInterfaceVO.getCurrentState());
            provincePlatformStatus.setNetworkBusinessIds(dealNetworkBusinessIds(platformInterfaceVO.getNetworkBusinessIds()));
            provincePlatformStatus.setTimeStamp(LocalDateTimeUtil.format(LocalDateTime.now(), "yyyy-MM-dd HH:mm:ss"));
            provincePlatformStatusMapperExt.insert(provincePlatformStatus);
        }
    }

    private String dealNetworkBusinessIds(String networkBusinessIds) {
        if (networkBusinessIds == null || "".equals(networkBusinessIds)) return null;
        String arrayStart = "[";
        String separator = ",";
        String arrayEnd = "]";
        String[] strs = networkBusinessIds.split("|");
        String str = arrayStart + strs[0];
        for (int i = 1; i < strs.length; i++) {
            if ("|".equals(strs[i])) continue;
            str = str + separator + strs[i];
        }
        str = str + arrayEnd;
        return str;
    }

    private List<PlatformInterfaceVO> provinceTypeMonitor(Integer status) {
        List<PlatformInterfaceVO> platformInterfaceVO = logVpnIntelligenceMapper.getPlatformInterfaceInfo();
        Iterator<PlatformInterfaceVO> iterator = platformInterfaceVO.iterator();
        while (iterator.hasNext()) {
            if (iterator.next().getConfigType().equals("CURRENT")) iterator.remove();
        }
        for (int i = 0; i < platformInterfaceVO.size(); i++) {
            platformInterfaceVO.get(i).setCurrentState(status);
        }
        saveProvincePlatformStatus(platformInterfaceVO);
        doReport(platformInterfaceVO);
        return platformInterfaceVO;
    }

    private void doReport(List<PlatformInterfaceVO> dataList) {
        if (dataList.isEmpty()) return;
        List<ProvinceStatusDTO> provinceStatusDTOList = new ArrayList<>();
        dataList.forEach(vo -> {
            ProvinceStatusDTO provinceStatusDTO = new ProvinceStatusDTO();
            provinceStatusDTO.setVersion("1.0");
            provinceStatusDTO.setComCode(vo.getComCode());
            provinceStatusDTO.setProvinceId(vo.getProvinceId());
            provinceStatusDTO.setNetworkBusinessId(vo.getNetworkBusinessIds());
            provinceStatusDTO.setProvinceId(vo.getProvinceId());
            provinceStatusDTO.setCurrentState(vo.getCurrentState());
            provinceStatusDTO.setTimeStamp(LocalDateTimeUtil.format(LocalDateTime.now(), "yyyy-MM-dd HH:mm:ss"));
            provinceStatusDTOList.add(provinceStatusDTO);
        });
        // 上报路径
        File reportFile = ReportUtil.getReportFile(antiVpnProperties.getReportDataDir(), AntiVpnReportCodeEnum.PROVINCE_PLATFORM_STATUS, dataList.get(0).getProvinceId(),  dataList.get(0).getComCode(), null);
        File reportTempFile = ReportUtil.getTempFile(reportFile);
        log.info("生成并上报企业状态文件:{}", reportFile);
        try (CsvWriter writer = CsvUtil.getWriter(reportTempFile, StandardCharsets.UTF_8)) {
            for (int i = 0; i < provinceStatusDTOList.size(); i++) {
                writer.writeLine(provinceStatusDTOList.get(i).toCvsFields());
                writer.flush();
            }
            FileUtil.move(reportTempFile, reportFile, true);
        }

    }

}
