package com.eversec.antivpn.log.service;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.eversec.antivpn.config.enums.AntiVpnDataDictTypeEnum;
import com.eversec.antivpn.emnus.ScreenTypeEnum;
import com.eversec.antivpn.log.api.dto.CrossBorderCommLogDTO;
import com.eversec.antivpn.log.api.dto.CrossBorderCommLogRequest;
import com.eversec.antivpn.log.api.dto.base.AggCountDTO;
import com.eversec.antivpn.log.api.dto.base.DateHistogramDTO;
import com.eversec.antivpn.log.entity.E1CommLog;
import com.eversec.antivpn.log.entity.po.GenericSysDataDict;
import com.eversec.antivpn.log.mapper.vo.TypeCountVO;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-12
 */
public interface GenericSysDataDictService extends IService<GenericSysDataDict> {
    /**
     * 获取系统所属code、name
     * @return
     */
    Map<String, String> getSystemNameMap();


    List<GenericSysDataDict> getSoftWareInfoByCode(List<String> codes);

    List<GenericSysDataDict> getDictList(AntiVpnDataDictTypeEnum antiVpnDataDictTypeEnum);

}
