package com.eversec.antivpn.log.api.dto.base;

import java.io.Serializable;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/7/12 14:18
 **/
@Data
public class DateHistogramDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String key;
    private String key2;
    private Long e1Num = 0l;
    private Long e2Num = 0l;
    private Long report = 0l;

}


