package com.eversec.antivpn.support.knowledge.service;

import com.eversec.antivpn.config.enums.AntiVpnDataDictTypeEnum;
import com.eversec.antivpn.support.knowledge.api.dto.ImportDownloadRequest;
import com.eversec.antivpn.support.knowledge.api.dto.ImportResult;
import com.eversec.antivpn.support.knowledge.api.dto.KnowledgeBaseDTO;
import com.eversec.antivpn.support.knowledge.api.dto.KnowledgeBaseQueryDto;
import com.eversec.cloud.core.api.data.PageResult;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <p>
 * 知识库 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-12
 */
public interface IKnowledgeBaseService {

    /**
     * 保存知识库
     * @param baseDTO
     */
    void save(KnowledgeBaseDTO baseDTO);
    /**
     * 修改知识库
     * @param baseDTO
     */
    void updateById(KnowledgeBaseDTO baseDTO);
    /**
     * 获取知识库分页
     * @param queryDto
     */
    PageResult<KnowledgeBaseDTO> page(KnowledgeBaseQueryDto queryDto);
    /**
     * 删除知识库
     * @param ids
     */
    void deleteByIds(List<Long> ids);

    /**
     * 导入知识库
     * @param userId
     * @param userName
     * @param file
     * @param cls 需要导入的类型
     * @param information
     * @param importFields 导入excel验证字段
     * @return
     */
    ImportResult importTemplate(Long userId, String userName, MultipartFile file, Class cls, AntiVpnDataDictTypeEnum information,String[] importFields);

    /**
     * 下载导入失败数据
     * @param req
     * @return
     */
    Object importDownload(ImportDownloadRequest req, Class cls);
}
