package com.eversec.antivpn.emnus;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import lombok.AllArgsConstructor;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

/**
 * 大屏不同时间查询方式
 * <AUTHOR>
 * @className: ScreenType
 * @create 2023/8/14
 */
@AllArgsConstructor
public enum ScreenTypeEnum {
    /**
     * 今日
     */
    TODAY("day","今日"),
    /**
     * 近一周
     */
    THISWEEK("week","近一周"),
    /**
     * 近一周之前7天
     */
    THISWEEK_BEFORE("beforeWeek","近一周之前7天"),
    /**
     * 近一月
     */
    THISMONTH("month","近一月"),
    /**
     * 至今
     */
    SOFAR("sofar","至今");

    //用于mybatis判断聚合维度
    public String code;

    private String desc;


    ScreenTypeEnum(String desc){
        this.desc = desc;
    }




    public String getCode(){
        return code;
    }
    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }


    /**
     * 根据枚举值获取 今日 近一周 近一个月 时间格式 yyyy-MM-dd
     * @param code
     * @return
     */
    public static Long getTimeCKPartition(ScreenTypeEnum code){
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
        Long timeStr = 0L;
        switch (code) {
            case TODAY:
                //今天日期 yyyyMMdd
                String today =dateFormat.format(new Date());
                timeStr = Long.valueOf(today);
                break;
            case THISWEEK:
                //上周今天（往前7天）
                DateTime dateTime = DateUtil.lastWeek();
                String lastWeek =dateFormat.format(dateTime);
                timeStr = Long.valueOf(lastWeek)+1;
                break;
            case THISWEEK_BEFORE:
                //上周今天（往前7天）
                DateTime newDate2 = DateUtil.offsetDay(new Date(), -14);
                String lastToTWOWeek =dateFormat.format(newDate2);
                timeStr = Long.valueOf(lastToTWOWeek);
                break;
            case THISMONTH:
                //上个月今天（往前一个月）
                DateTime lastMonthTime = DateUtil.lastMonth();
                String lastMonth =dateFormat.format(lastMonthTime);
                timeStr = Long.valueOf(lastMonth)+1;
                break;
            case SOFAR:
                timeStr = null;
                break;
            default:
                timeStr =Long.valueOf(DateUtil.format(new Date(), "yyyyMMdd"));
                break;
        }
        return timeStr;

    }

    /**
     * 根据枚举值获取 一天开始 HH:mm:ss
     * @return
     */
    public static Long beginOfDay(){
        Calendar todayStart = Calendar.getInstance();
        todayStart.set(Calendar.HOUR_OF_DAY, 0);
        todayStart.set(Calendar.MINUTE, 0);
        todayStart.set(Calendar.SECOND, 0);
        todayStart.set(Calendar.MILLISECOND, 0);
        long time = todayStart.getTimeInMillis();
        return time;
    }

    /**
     * 根据枚举值获取 一天结束 HH:mm:ss
     * @return
     */
    public static Long endOfDay(){
        Calendar todayEnd = Calendar.getInstance();
        todayEnd.set(Calendar.HOUR_OF_DAY, 23);
        todayEnd.set(Calendar.MINUTE, 59);
        todayEnd.set(Calendar.SECOND, 59);
        todayEnd.set(Calendar.MILLISECOND, 999);
        long time = todayEnd.getTimeInMillis();
        return time;
    }

    public static Integer getBeforeDayNum(ScreenTypeEnum code){
        Integer beforeDay;
        switch (code) {
            case THISWEEK:
                beforeDay = 6;
                break;
            case THISWEEK_BEFORE:
                beforeDay = 13;
                break;
            case THISMONTH:
                //上个月今天（往前一个月）
                beforeDay =  29;
                break;
            case SOFAR:
                beforeDay = Integer.MAX_VALUE;
                break;
            default:
                beforeDay = 0;
                break;
        }
        return beforeDay;
    }
}
