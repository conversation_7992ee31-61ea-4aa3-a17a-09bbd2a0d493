package com.eversec.antivpn.log.service;

import com.eversec.antivpn.emnus.ScreenTypeEnum;
import com.eversec.antivpn.log.api.dto.base.AggCountDTO;
import com.eversec.antivpn.log.entity.E4DnsLog;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-08
 */
public interface IE4DnsLogService extends IService<E4DnsLog> {
    AggCountDTO countNum(ScreenTypeEnum screenTypeEnum);
}
