-- vpn情报指令表
CREATE TABLE IF NOT EXISTS `vpn_intelligence_command` (
    `id`  bigint(20) NOT NULL COMMENT '主键，雪花',
    `command_id` bigint(20) NOT NULL COMMENT '跨境通信情报库下发执行指令ID。当部侧下发时由部侧系统控制惟一；当企业侧系统上报时企业侧内惟一。',
    `version` varchar(20) NOT NULL DEFAULT '1.0' COMMENT '跨境通信情报库版本格式：主版本号.副版本号，本规范主版本号为1，副版本号为0，即1.0。',
    `com_code` varchar(18) NOT NULL COMMENT '运营商编码，见附录F.2',
    `province_id` bigint(2) NOT NULL COMMENT '省级区域编号，见附录F.4',
    `system_code` varchar(100) default NULL COMMENT '省平台业务系统标识',
    `network_business_id` int(8) DEFAULT NULL COMMENT '监测网络类型code，见附录F.3。企业侧上报时必填。部侧下发时可选填，当部侧下发为空时表示所有网络类型生效',
    `file_name` varchar(256) DEFAULT NULL COMMENT '全量情报库文件名称,当操作类型为3时填写的是全量跨境通信情报库文件名；当操作类型为4、5时填写的是模型或样本文件名，多个用|线分隔； 当填写Vpn名称时，优先使用中文名称，没有中文名称时使用英文名称',

    `is_upload_file` int(2) DEFAULT NULL COMMENT '是否上报取证文件： 0：上报； 1：不上报。 企业侧系统上报时必填，部侧系统下发时为空。',
    `attach_ment` varchar(256) DEFAULT NULL COMMENT '取证文件名称,当isUploadFile为0时必填。上报方法参见7.2.2章节。',
    `time_stamp` varchar(19) NOT NULL COMMENT '创建时间,生成该文件的时间',
    `operation_type` int(8) NOT NULL COMMENT '操作类型,对该记录的操作类型，包括： 0-增量情报下发； 1-上报； 2-删除； 3-全量情报下发； 4-模型下发； 5-样本下发。',

    `source` varchar(50) NOT NULL COMMENT '情报来源，FULL：全量下发。INCREASE：增量下发。REPORT：省端上报。INPUT：页面录入',

    `report_status` varchar(50) NOT NULL COMMENT '上报状态，REPORTED：未上报，TO_BE_REPORTED：待上报',
    `report_datetime` datetime default NULL COMMENT '上报时间，上报到部侧时间',
    `distribute_status` varchar(50) NOT NULL COMMENT '下发状态，集团录入需要下发到省端。DISTRIBUTED：已下发，TO_BE_DISTRIBUTED：待下发',
    `distribute_datetime` datetime default NULL COMMENT '下发时间，下发到省端',

    `create_user` varchar(100) DEFAULT NULL COMMENT '创建人员',
    `update_user` varchar(100) DEFAULT NULL COMMENT '修改人员',
    `create_datetime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间,入库时间',
    `update_datetime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间,记录最近修改时间',
    `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除状态,0未删除，1已删除',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='vpn情报指令';

-- vpn情报表
CREATE TABLE IF NOT EXISTS `vpn_intelligence` (
    `id`  bigint(20) NOT NULL COMMENT '主键，雪花',
    `version` varchar(20) NOT NULL DEFAULT '1.0' COMMENT '跨境通信情报库版本格式：主版本号.副版本号，本规范主版本号为1，副版本号为0，即1.0。',
    `command_id` bigint(20) default 0 COMMENT '跨境通信情报库下发执行指令ID。当部侧下发时由部侧系统控制惟一；当企业侧系统上报时企业侧内惟一。当企业录入时默认为0，上报后填充commandId',
    `rule_id` bigint(20) default 0 COMMENT '规则id，14位',
    `com_code` varchar(18) NOT NULL COMMENT '运营商编码，见附录F.2',
    `province_id` bigint(2) NOT NULL COMMENT '省级区域编号，见附录F.4',
    `system_code` varchar(100) default NULL COMMENT '省平台业务系统标识',
    `network_business_id` int(8) DEFAULT NULL COMMENT '监测网络类型code，见附录F.3。企业侧上报时必填。部侧下发时可选填，当部侧下发为空时表示所有网络类型生效',
    `type_id` int(8) DEFAULT NULL COMMENT '情报类型code： 1-VPN服务地址,例:提供VPN下载、注册、登录、购买、登出等各类访问地址和链接； 2-VPN 跨境服务地址,例：海外域名通道或海外IP通道地址； 3-模型研判； 4-样本库； 999-其他。 当操作类型为0、1、2时必填，当操作类型为3、4、5时选填。 ',
    `vpn_id` bigint(20) DEFAULT NULL COMMENT '情报库ID，当部侧系统下发时必填；企业侧系统上报、全量库下发时可不填写',
    `vpn_name` varchar(1024) DEFAULT NULL COMMENT 'Vpn名称或文件名称，当操作类型为0、1、2时填写VPN名称，当操作类型为3时填写的是全量跨境通信情报库文件名；当操作类型为4、5时填写的是模型或样本文件名，多个用|线分隔；当填写Vpn名称时，优先使用中文名称，没有中文名称时使用英文名称。',
    `vpn_domain` varchar(1024) DEFAULT NULL COMMENT '域名。VPN活动所使用的域名地址。',
    `vpn_ip` varchar(200) DEFAULT NULL COMMENT 'IP地址,VPN活动所使用的IP地址；IPv4使用点分表示法，IPv6使用冒分小写0位压缩表示法。',
    `vpn_url` varchar(1024) DEFAULT NULL COMMENT 'URL地址,VPN活动所使用的URL地址，采用base64编码后的URL。',
    `vpn_url_decode` varchar(1024) DEFAULT NULL COMMENT 'URL地址,VPN活动所使用的URL地址，未编码。',
    `vpn_link` varchar(1024) DEFAULT NULL COMMENT '取证链接,包含VMess、VLESS、Shadowsocks、Socks、Trojan等链接地址。企业侧系统上报时根据实际情况填写。',
    `vpn_message` varchar(1024) DEFAULT NULL COMMENT '报文（最少支持snort规则，版本语法为2.9)，base64编码；',
    `vpn_message_decode` varchar(1024) DEFAULT NULL COMMENT '报文（最少支持snort规则，版本语法为2.9)，未编码；',
    `content_type` int(8) DEFAULT NULL COMMENT '匹配内容类型。1-域名；2-IP；3-URL；4-报文（最少支持snort规则，版本语法为2.9)；5-VPN协议（具体参见F.5节）；999-其他。',
    `vpn_content` varchar(2048) DEFAULT NULL COMMENT '匹配内容。当类型为3和4时采用base64编码后结果。当类型为5时填写的是VPN协议编号。',
    `vpn_port` varchar(10) DEFAULT NULL COMMENT '端口',
    `vpn_airport_code` varchar(20) DEFAULT NULL COMMENT 'vpn机场编码',
    `vpn_country` varchar(100) DEFAULT NULL COMMENT '所属国家',
    `vpn_software_codes` json DEFAULT NULL COMMENT 'vpn软件编码，数组存储 ',
    `vpn_protocol_code` varchar(20) DEFAULT NULL COMMENT 'vpn协议编码',
    `vpn_protocol_eversec_code` varchar(20) DEFAULT NULL COMMENT 'vpn协议编码（eversec）',
    `time_stamp` varchar(19) NOT NULL COMMENT '创建时间,生成该文件的时间',
    `source` varchar(50) NOT NULL COMMENT '情报来源，FULL：全量下发。INCREASE：增量下发。REPORT：省端上报。INPUT：页面录入',

    `report_status` varchar(50) NOT NULL COMMENT '上报状态，REPORTED：未上报，TO_BE_REPORTED：待上报',
    `report_datetime` datetime default NULL COMMENT '上报时间，上报到部侧时间',
    `distribute_status` varchar(50) NOT NULL COMMENT '下发状态，集团录入需要下发到省端。DISTRIBUTED：已下发，TO_BE_DISTRIBUTED：待下发',
    `distribute_datetime` datetime default NULL COMMENT '下发时间，下发到省端',


    `create_user` varchar(100) DEFAULT NULL COMMENT '创建人员',
    `update_user` varchar(100) DEFAULT NULL COMMENT '修改人员',
    `create_datetime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间,入库时间',
    `update_datetime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间,记录最近修改时间',
    `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除状态,0未删除，1已删除',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='vpn情报';







