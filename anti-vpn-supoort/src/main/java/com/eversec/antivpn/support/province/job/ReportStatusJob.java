package com.eversec.antivpn.support.province.job;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.text.csv.CsvUtil;
import cn.hutool.core.text.csv.CsvWriter;
import com.eversec.antivpn.config.AntiVpnProperties;
import com.eversec.antivpn.emnus.AntiVpnReportCodeEnum;
import com.eversec.antivpn.support.config.api.PlatformInterfaceInfoApi;
import com.eversec.antivpn.support.config.api.dto.PlatformInterfaceInfoDTO;
import com.eversec.antivpn.support.province.api.dto.ProvinceStatusDTO;
import com.eversec.antivpn.util.ReportUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.integration.support.leader.LockRegistryLeaderInitiator;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 上报企业测状态
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023-08-11 16:45
 */
//@Component
@AllArgsConstructor
@Slf4j
public class ReportStatusJob {

	private final LockRegistryLeaderInitiator leaderElectionLockRegistryLeaderInitiator;

	private final AntiVpnProperties antiVpnProperties;

	private final PlatformInterfaceInfoApi platformInterfaceInfoApi;

	/**
	 * 上报企业状态定时任务
	 * 默认每10分钟上报一次
	 */
	//@Scheduled(fixedDelayString = "${app.anti-vpn.report-status-delay:600000}")
	public void reportStatus() {
		if (leaderElectionLockRegistryLeaderInitiator.getContext().isLeader()) {
			log.info("当前节点为leaser节点，执行上报企业状态操作");
			ProvinceStatusDTO provinceStatusDTO = new ProvinceStatusDTO();
			provinceStatusDTO.setVersion("1.0");
			PlatformInterfaceInfoDTO currentPlatform = platformInterfaceInfoApi.getCurrentPlatform();
			provinceStatusDTO.setComCode(currentPlatform.getComCode());
			provinceStatusDTO.setProvinceId(currentPlatform.getProvinceId());
			provinceStatusDTO.setNetworkBusinessId(currentPlatform.getNorthCurrentNetworkBusinessIds());
			provinceStatusDTO.setCurrentState(0);
			provinceStatusDTO.setTimeStamp(LocalDateTimeUtil.format(LocalDateTime.now(), "yyyy-MM-dd HH:mm:ss"));

			// 生成csv到指定目录
			// 上报路径
			File reportFile = ReportUtil.getReportFile(antiVpnProperties.getReportDataDir(), AntiVpnReportCodeEnum.PROVINCE_PLATFORM_STATUS, currentPlatform.getProvinceId(), currentPlatform.getComCode(), null);
			File reportTempFile = ReportUtil.getTempFile(reportFile);
			log.info("生成并上报企业状态文件:{}", reportFile);
			try (CsvWriter writer = CsvUtil.getWriter(reportTempFile, StandardCharsets.UTF_8)) {
//				writer.writeHeaderLine(AntiVpnReportCodeEnum.PROVINCE_PLATFORM_STATUS.getHeader());
				writer.writeLine(provinceStatusDTO.toCvsFields());
				writer.flush();
			}
			FileUtil.move(reportTempFile, reportFile, true);

		} else {
			log.info("非leader节点，不进行上报企业测状态");
		}

	}

	public static void main(String[] args) {
		try (CsvWriter writer = CsvUtil.getWriter("xxxxxxxxxxxxxxxxxxxxx.csv", StandardCharsets.UTF_8)) {
			writer.writeHeaderLine("国家", "省");
			writer.writeLine("1", "2");
			writer.writeLine("1");
			writer.writeLine("1", "2", "3");
			writer.flush();
		}
	}

}
