<template>
  <ul class="topnum">
    <li v-for="(item, i) in numList" :key="i">
      <div class="name"><img :src="item.icon" /> {{ item.name }}</div>
      <p class="line"></p>
      <div class="num_bot">
        <p class="bot_l">
          <span>{{ getNum(item.dateRangeCount) }}</span>
          <span v-if="i == 1">%</span>
        </p>
        <p v-if="i != 4 && i != 1">今日：{{ getNum(item.todayCount) }}</p>
      </div>
    </li>
  </ul>
</template>

<script>
export default {
  props: {
    numList: {
      type: Array,
    },
  },
  watch: {
    numList: {
      handler(val) {
        this.listD = val;
      },
      immediate: true,
    },
  },
  data() {
    return {
      listD: [],
    };
  },
  methods: {
    getNum(n) {
      let numFt = n;
      console.log(n)
      if (n >= 10000 && n < 100000000) {
        n = (n / 10000).toFixed(2);
        numFt = n + "万";
      } else if(n>=100000000){
        n = (n / 100000000).toFixed(2);
        numFt = n + "亿";
      }
      return numFt;
    },
  },
};
</script>

<style scoped lang="scss">
.topnum {
  display: flex;
  justify-content: space-around;
  > li {
    width: 258px;
    height: 94px;
    background: url(./img/kuang.png) no-repeat;
    color: #fff;
    padding: 20px 20px;
    font-size: 14px;
    .name {
      display: flex;
      > img {
        margin-right: 6px;
        height: 16px;
      }
    }
    .line {
      height: 1px;
      background: url(./img/line.png) no-repeat;
      margin: 10px 0;
    }
    .num_bot {
      display: flex;
      justify-content: space-between;
      .bot_l {
        color: #a7e9ff;
        font-size: 12px;
        > span {
          font-size: 16px;
        }
      }
    }
  }
}
</style>
