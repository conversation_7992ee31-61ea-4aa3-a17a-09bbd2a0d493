package com.eversec.antivpn.config.enums;

/**
 * 业务异常枚举
 * 结合 @com.eversec.framework.webboot.common.exception.BusinessException 使用
 * {
 *     "code": "10001",
 *     "msg": 数据不存在
 * }
 *
 */
public enum AntiVpnBusinessExceptionEnum {

    // business
    CODE_10001("数据不存在"),
    CODE_10002("暂不支持该下发类型操作，当前下发类型为：{0}"),

    CODE_10003("该指令已经存在"),

    CODE_10004("下载情报库文件失败"),
    CODE_10005("下发规则到smart异常"),

    CODE_10006("数据参数不能为空"),

    CODE_10007("不存在当前平台配置记录，请先进行配置"),

    CODE_10008("当前平台配置记录已经存在，请基于现有记录进行修改"),

    CODE_10009("调用smart接口异常，请联系管理员。数据已保存，无需重新导入或保存，点击页面的重新下发到smart即可"),

    CODE_10010("当前为省平台部署或者和业务平台一起部署。情报的运营商、省份、系统编码和系统当前系统配置不一致，只能保存当前省份记录"),

    CODE_10011("压缩包中不存在xlsx、xls文件，请检查文件内容"),
    CODE_10012("文件不存在"),

    ;

    private String message;
    private String description;

    AntiVpnBusinessExceptionEnum(String message) {
        this.message = message;
    }

    AntiVpnBusinessExceptionEnum(String message, String description) {
        this.message = message;
        this.description = description;
    }
}
