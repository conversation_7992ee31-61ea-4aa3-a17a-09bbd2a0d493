export const rulesConfig = {
  nullCheck: (msg, label) => ({
    required: true,
    message: msg || `请输入${label || ''}`,
    trigger: 'blur'
  }),
  selectCheck: (msg, label) => ({
    required: true,
    message: msg || `请选择${label || ''}`,
    trigger: 'change'
  }),
  // 手机号校验
  telCheck: {
    required: true,
    pattern: /^1(3|4|5|6|7|8|9)\d{9}$/,
    message: '手机号格式不正确',
    trigger: 'blur'
  },
  // 身份证号校验
  IDCardCheck: {
    required: true,
    pattern: /(^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$)|(^[1-9]\d{5}\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{2}$)/,
    message: '身份证号码格式不正确',
    trigger: 'blur'
  },
  // 牌照校验
  licensePlateCheck: {
    required: true,
    pattern: /[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-HJ-NP-Z0-9]{4}[A-HJ-NP-Z0-9挂学警港澳]{1}/,
    message: '牌照格式不正确',
    trigger: 'blur'
  },
  // 中英文域名校验
  websiteCheck: {
    required: true,
    pattern: /^[0-9a-zA-Z\u4e00-\u9faf]+[0-9a-zA-Z\u4e00-\u9faf\.-]*\.[a-zA-Z\u4e00-\u9faf]{2,4}$/,
    message: '域名格式不正确',
    trigger: 'blur'
  }
}
