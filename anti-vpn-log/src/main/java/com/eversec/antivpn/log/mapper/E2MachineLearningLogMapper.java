package com.eversec.antivpn.log.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.eversec.antivpn.log.api.dto.TypeCountDTO;
import com.eversec.antivpn.log.entity.E2MachineLearningLog;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.eversec.antivpn.log.mapper.vo.BaseSearchVO;
import com.eversec.antivpn.log.mapper.vo.TypeCountVO;
import com.eversec.antivpn.log.mapper.vo.VpnAggVO;
import com.eversec.antivpn.log.mapper.vo.VpnHistogramAggVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-08
 */
@Mapper
@DS("clickhouse")
public interface E2MachineLearningLogMapper extends BaseMapper<E2MachineLearningLog> {

    //传输层协议聚合分析
    List<TypeCountVO> getProtocolTypeAggregate(@Param("startTimePartition") Long startTimePartition, @Param("endTimePartition") Long endTimePartition);
    //省聚合分析
    List<TypeCountVO> getProvinceAggregate(@Param("startTimePartition") Long startTimePartition, @Param("endTimePartition") Long endTimePartition);

    List<TypeCountVO> getNetworkBusinessTypeAggregate(@Param("startTimePartition") Long startTimePartition, @Param("endTimePartition") Long endTimePartition);

    List<TypeCountVO> getTodayProvinceAggregate(@Param("timePartition") Integer timePartition);

    List<TypeCountVO> getDateHistogram(@Param("startTimePartition") Long startTimePartition, @Param("endTimePartition") Long endTimePartition);

    Long getResourceNodeCount(@Param("startTimePartition") Long startTimePartition, @Param("endTimePartition") Long endTimePartition);

    List<VpnAggVO> getUsedResourceNodeAggregate(@Param("startTimePartition") Long startTimePartition, @Param("endTimePartition") Long endTimePartition);

}
