package com.eversec.antivpn.intelligence.controller;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.Pair;
import com.eversec.antivpn.emnus.ScreenTypeEnum;
import com.eversec.antivpn.intelligence.api.VpnIntelligenceApi;
import com.eversec.antivpn.intelligence.api.dto.VpnIntelligenceDTO;
import com.eversec.antivpn.intelligence.factory.VpnIntelligenceFactory;
import com.eversec.antivpn.intelligence.dto.AggCountDTO;
import com.eversec.antivpn.intelligence.dto.VpnIntelligenceQueryConditionDTO;
import com.eversec.antivpn.intelligence.entity.VpnIntelligence;
import com.eversec.antivpn.intelligence.service.IVpnIntelligenceService;
import com.eversec.stark.generic.common.types.ContentDispositionTypeEnum;
import com.eversec.stark.generic.common.types.auth.Login;
import com.eversec.stark.generic.common.util.DownloadUtil;
import com.eversec.stark.generic.common.util.PageBuilder;
import io.swagger.v3.oas.annotations.Operation;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.web.bind.annotation.*;
import lombok.AllArgsConstructor;

import java.io.BufferedInputStream;
import java.io.File;
import java.io.InputStream;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.List;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

/**
 * <p>
 * vpn情报 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-10
 */
@RestController
@RequestMapping(VpnIntelligenceApi.PATH)
@AllArgsConstructor
@Slf4j
@Login
public class VpnIntelligenceController implements VpnIntelligenceApi {

    private final IVpnIntelligenceService service;

    private final HttpServletResponse response;

    private final ResourceLoader resourceLoader;

    private final VpnIntelligenceFactory vpnIntelligenceFactory;

    @Operation(summary = "查询-分页")
    @GetMapping("/page")
    public Page<VpnIntelligenceDTO> page(@ModelAttribute @ParameterObject VpnIntelligenceQueryConditionDTO paramDto,
                                         @ModelAttribute @ParameterObject Page<VpnIntelligence> pageInfo) {
        Page<VpnIntelligence> page = service.page(paramDto, pageInfo);
        Page<VpnIntelligenceDTO> vpnIntelligenceDTOPage = PageBuilder.convertMybatisPlusPage(page, vpnIntelligenceFactory::entityToDto);
        return vpnIntelligenceDTOPage;
    }

    @Operation(summary = "导入情报")
    @PostMapping(value = {"/import/{comCode}/{provinceId}/{systemCode}", "/import/{comCode}/{provinceId}", "/import/{comCode}/{provinceId}/"}, consumes = "multipart/form-data")
    public void importVpnIntelligence(@PathVariable("comCode") String comCode, @PathVariable("provinceId")  Long provinceId, @PathVariable(value = "systemCode", required = false)  String systemCode, MultipartFile file) {
        service.importVpnIntelligence(comCode, provinceId, systemCode, file);
    }

    @Operation(summary = "上报取证文件")
    @PostMapping(value = "/uploadAttachMent/{id}", consumes = "multipart/form-data")
    @Login(false)
    public void uploadAttachMent(@PathVariable("id") Long id, MultipartFile file) {
        service.uploadAttachMent(id, file);
    }

    @SneakyThrows
    @Operation(summary = "下载模型文件")
    @GetMapping("/downloadAttachMent/{fileName}")
    @Login(false)
    public void downloadAttachMent(@PathVariable(value = "fileName") String fileName) {
        File moduleFile = service.getAttachMent(fileName);
        DownloadUtil.download(response, FileUtil.getInputStream(moduleFile), ContentDispositionTypeEnum.attachment,
                fileName, moduleFile.length());
    }



    @SneakyThrows
    @Operation(summary = "下载导入情报模板")
    @GetMapping("/downloadTemplate")
    @Login(false)
    public void downloadTemplate() {
        Resource resource = resourceLoader.getResource("classpath:template/import_vpn_intelligence.csv");
        DownloadUtil.download(response, resource.getInputStream(), ContentDispositionTypeEnum.attachment,
                "情报信息导入模板.xlsx", resource.contentLength());

    }



    @Operation(summary = "上报情报")
    @PostMapping("/report/{networkBusinessId}")
    public void reportVpnIntelligence(@RequestBody ArrayList<Long> idList, @PathVariable("networkBusinessId")  Integer networkBusinessId) {
        service.reportVpnIntelligence(idList, networkBusinessId);
    }

    @Operation(summary = "新增")
    @PostMapping("/save")
    public void save(@RequestBody @Valid final VpnIntelligenceDTO paramDto) {
        service.save(paramDto);
    }

    @Operation(summary = "根据id修改")
    @PutMapping("/updateById")
    public void updateById(@RequestBody @Valid final VpnIntelligenceDTO paramDto) {
        paramDto.setId(null);
        service.updateById(paramDto);
    }


    @Operation(summary = "根据id批量删除，逗号分割")
    @DeleteMapping("/deleteByIds/{ids}")
    public void deleteByIds(@PathVariable("ids")  List<Long> ids) {
        service.removeByIds(ids);
    }

    @Operation(summary = "根据条件删除")
    @DeleteMapping("/deleteByCondition")
    public void deleteByCondition(@ModelAttribute @ParameterObject VpnIntelligenceQueryConditionDTO paramDto) {
        service.deleteByCondition(paramDto);
    }


    @Operation(summary = "总体态势-上报情报")
    @GetMapping("/reportVpnIntelligence")
    public AggCountDTO reportVpnIntelligence(@RequestParam ScreenTypeEnum screenTypeEnum) {
        return service.reportVpnIntelligence(screenTypeEnum);
    }

    @Operation(summary = "总体态势-情报总量")
    @GetMapping("/totalCount")
    public AggCountDTO totalCount(@RequestParam ScreenTypeEnum screenTypeEnum) {
        return service.totalCount(screenTypeEnum);
    }

    @Operation(summary = "重新下发到smart")
    @GetMapping("/redistributeToSmart")
    public void redistributeToSmart(){
        Path path = service.callSmart();
        if (path!=null) {
            BufferedInputStream inputStream = FileUtil.getInputStream(path.toFile());
            DownloadUtil.download(response, inputStream, ContentDispositionTypeEnum.attachment, path.toFile().getName(), path.toFile().length());
        }
    }

    @Operation(summary = "重新下发到态势感知")
    @GetMapping("/redistributeToNssa")
    public void redistributeToNssa(){
        Path path = service.redistributeToNssa();
        if (path!=null) {
            BufferedInputStream inputStream = FileUtil.getInputStream(path.toFile());
            DownloadUtil.download(response, inputStream, ContentDispositionTypeEnum.attachment, path.toFile().getName(), path.toFile().length());
        }
    }


}
