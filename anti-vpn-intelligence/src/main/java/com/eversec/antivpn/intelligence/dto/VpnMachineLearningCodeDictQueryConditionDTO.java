package com.eversec.antivpn.intelligence.dto;

import com.eversec.antivpn.intelligence.entity.VpnIntelligence;
import com.eversec.antivpn.intelligence.entity.VpnMachineLearningCodeDict;
import com.eversec.stark.common.component.criteria.annotaion.Eq;
import com.eversec.stark.common.component.criteria.annotaion.Ge;
import com.eversec.stark.common.component.criteria.annotaion.Le;
import com.eversec.stark.common.component.criteria.annotaion.Like;
import com.eversec.stark.common.component.criteria.query.AbstractQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 *
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023-08-08 10:21
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class VpnMachineLearningCodeDictQueryConditionDTO extends AbstractQuery<VpnMachineLearningCodeDict> {

	@Eq(alias = "version")
	@Schema(description = "跨境通信情报库版本格式：主版本号.副版本号，本规范主版本号为1，副版本号为0，即1.0。")
	private String version;

	@Eq(alias = "com_code")
	@Schema(description = "运营商编码，见附录F.2")
	private String comCode;

	@Eq(alias = "province_id")
	@Schema(description = "省级区域编号，见附录F.4")
	private Long provinceId;

	@Eq(alias = "system_code")
	@Schema(description = "省平台业务系统标识。省平台部署：当前省平台业务系统标识；集团部署：省平台业务系统标识")
	private String systemCode;

	@Like(alias = "model_code")
	@Schema(description = "模型编码")
	private String modelCode;

	@Like(alias = "model_ch_name")
	@Schema(description = "模型中文名称")
	private String modelChName;

	@Like(alias = "vpn_name")
	@Schema(description = "关联vpn名称")
	private String vpnName;

	@Eq(alias = "network_business_id")
	@Schema(description = "监测网络类型code，见附录F.3。企业侧上报时必填。部侧下发时可选填，当部侧下发为空时表示所有网络类型生效")
	private Integer networkBusinessId;

	@Ge(alias = "create_date")
	@Schema(description = "创建时间-start")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date createDateStart;

	@Le(alias = "create_date")
	@Schema(description = "创建时间-end")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date createDateEnd;


	@Ge(alias = "time_stamp")
	@Schema(description = "发现时间-start")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date timeStampStart;

	@Le(alias = "time_stamp")
	@Schema(description = "发现时间-end")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date timeStampEnd;



}
