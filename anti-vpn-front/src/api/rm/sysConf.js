import httpRequest from '@/utils/httpRequest'
import serverProxy from '@/config/serverProxy'
let gateway = serverProxy.generic

export default {
  // 根据key获取系统配置参数
  getByKey(key) {
    return httpRequest({
      url: `${gateway}/sys-conf-item/getByKey/${key}`,
      method: 'get'
    })
  },
  // 获取app列表
  getList(o) {
    return httpRequest({
      url: gateway + '/sys-conf-item/page',
      params: o
    })
  },
  // 新增配置
  add(o) {
    return httpRequest({
      url: gateway + '/sys-conf-item/save',
      method: 'post',
      data: o
    })
  },
  // 修改配置
  update(o) {
    return httpRequest({
      url: gateway + '/sys-conf-item/updateById/' + o.id,
      method: 'put',
      data: o
    })
  },
  // 删除配置
  deleteByIds(ids) {
    return httpRequest({
      url: gateway + '/sys-conf-item/deleteByIds/' + ids,
      method: 'delete'
    })
  },
  // 获取文件信息
  getFile(fileId) {
    return httpRequest({
      url: gateway + `/common-file/get/${fileId}`
    })
  },
  // 根据ID下载附件
  downloadById(fileId) {
    window.location.href =
      '/service/' + gateway + `/common-file/downloadFeign/${fileId}`
  },
  // 根据ID预览附件
  openFile(fileId) {
    window.open(
      '/service/' +
        gateway +
        `/common-file/download/${fileId}?contentDispositionType=inline`
    )
  },
  // 常规上传文件
  uploadFile(o, bizType, callback) {
    return httpRequest({
      url: gateway + `/common-file/upload/${bizType}`,
      method: 'post',
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      data: o,
      onUploadProgress: progressEvent => {
        // 对原生进度事件的处理
        if (progressEvent.lengthComputable) {
          callback && callback(progressEvent)
        }
      }
    })
  },
  // 获取分片上传初始化数据
  initiateMultipartUpload(o) {
    return httpRequest({
      url: gateway + '/common-file/initiateMultipartUpload',
      params: o
    })
  },
  // 分片上传
  uploadPart(o) {
    return httpRequest({
      url: gateway + '/common-file/uploadPart',
      method: 'post',
      data: o
    })
  },
  // 分片上传后合并分片
  completeMultipartUpload(o) {
    return httpRequest({
      url: gateway + '/common-file/completeMultipartUpload',
      method: 'post',
      data: o
    })
  }
}
