package com.eversec.antivpn.intelligence.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.StrUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * 情报导入DTO
 */
@Getter
@Setter
public class VpnIntelligenceImportDTO {


    public static final String[] MUST_FILL =
            new String[] { "情报类型编码", "情报库ID", "VPN名称",
                    "VPN域名", "IP地址", "URL地址",
                    "取证链接", "端口", "节点所属国家", "所属VPN服务商编码",
                    "软件ID编码", "协议ID", "eversec协议编码", "应用协议编码", "发现时间", "取证文件名称"};

    @Excel(name = "情报类型编码")
    private Integer typeId;

    @Excel(name = "情报库ID")
    private Long vpnId;

    @Excel(name = "VPN名称")
    private String vpnName;

    @Excel(name = "VPN域名")
    private String vpnDomain;

    @Excel(name = "IP地址")
    private String vpnIp;

    @Excel(name = "URL地址")
    private String vpnUrlDecode;

    @Excel(name = "取证链接")
    private String vpnLink;

    @Excel(name = "端口")
    private String vpnPort;

    @Excel(name = "节点所属国家")
    private String vpnCountry;

    @Excel(name = "所属VPN服务商编码")
    private String vpnAirportCode;

    @Excel(name = "软件ID编码")
    private String vpnSoftwareCodes;

    @Excel(name = "协议ID")
    private String vpnProtocolCode;

    @Excel(name = "eversec协议编码")
    private String vpnProtocolEversecCode;

    @Excel(name = "应用协议编码")
    private String applicationProtocolCode;

    @Excel(name = "发现时间")
    private String timeStamp;

    @Excel(name = "取证文件名称")
    private String attachMent;


    public String toCsvLine() {
        List<String> fields = new ArrayList<>();
        fields.add(nullToEmpty(typeId));
        fields.add(nullToEmpty(vpnId));
        fields.add(nullToEmpty(vpnName));
        fields.add(nullToEmpty(vpnDomain));
        fields.add(nullToEmpty(vpnIp));
        fields.add(nullToEmpty(vpnUrlDecode));
        fields.add(nullToEmpty(vpnLink));
        fields.add(nullToEmpty(vpnPort));
        fields.add(nullToEmpty(vpnCountry));
        fields.add(nullToEmpty(vpnAirportCode));
        fields.add(nullToEmpty(vpnSoftwareCodes));
        fields.add(nullToEmpty(vpnProtocolCode));
        fields.add(nullToEmpty(vpnProtocolEversecCode));
        fields.add(nullToEmpty(applicationProtocolCode));
        fields.add(nullToEmpty(timeStamp));
        fields.add(nullToEmpty(attachMent));
        return StrUtil.join(",", fields);
    }

    private String nullToEmpty(Object value) {
        if (value == null) {
            return "";
        } else {
            return String.valueOf(value);
        }
    }


}
