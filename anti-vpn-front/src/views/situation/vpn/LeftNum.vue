<template>
  <div class="numbox">
    <ul class="numtop">
      <li>
        <div>VPN服务商总量</div>
        <div class="color_blue">
          <span class="num">{{ total | num2Ft }}</span> <span> 个</span>
        </div>
      </li>
      <li>
        <div>资源节点总量</div>
        <div class="color_blue">
          <span class="num">{{ total_node | num2Ft }}</span> <span> 个</span>
        </div>
      </li>
    </ul>
    <div class="num_item">
      <div class="item_l">
        <p>近7日活跃的VPN服务商数量</p>
        <p class="color_blue"><span class="num">{{ day7num.nowWeek | num2Ft }}</span> <span> 个</span></p>
      </div>
      <div class="item_r">
        <p class="color_gray">环比上周</p>
        <p class="color_blue huanbi">
          <span class="num"> {{ day7num.increasePercent }}%</span>
          <i class="triangle" :class="day7num.nowWeek > day7num.lastWeek ? 'up' : 'down'"></i>
        </p>
      </div>
    </div>
    <div class="num_item">
      <div class="item_l">
        <p>近7日活跃的资源节点数量</p>
        <p class="color_blue"><span class="num">{{ day7num_node.nowWeek | num2Ft }}</span> <span> 个</span></p>
      </div>
      <div class="item_r">
        <p class="color_gray">环比上周</p>
        <p class="color_blue huanbi">
          <!-- <span>增加</span> -->
          <span class="num"> {{ day7num_node.increasePercent }}%</span>
          <i class="triangle" :class="day7num_node.nowWeek > day7num_node.lastWeek ? 'up' : 'down'"></i>
        </p>
      </div>
    </div>
  </div>
</template>

<script>
import vpnApi from './vpnApi.js'
export default {
  data() {
    return {
      total: 0,
      total_node: 0,
      day7num: {
        increasePercent: 0,
        lastWeek: 0,
        nowWeek: 0
      },
      day7num_node: {
        increasePercent: 0,
        lastWeek: 0,
        nowWeek: 0
      },
    }
  },
  methods: {
    getNum(p) {
      vpnApi.vpnServiceCount(p).then(res => {
        console.log('num--1', res)
        if(res) this.total = res;
      })
      vpnApi.resourceNodeCount(p).then(res => {
        console.log('num--2', res)
        if(res) this.total_node = res;
      })
      vpnApi.day7_service(p).then(res => {
        console.log('num--3', res)
        if(res) this.day7num = res;
      })
      vpnApi.day7_node(p).then(res => {
        console.log('num--4', res)
        if(res) this.day7num_node = res;
      })
    },
  }
}
</script>


<style lang="scss" scoped>
.numbox {
  color: #fffeff;
  font-size: 14px;
  line-height: 28px;
  .color_blue {
    color: #a6e7fb;
  }
  .color_gray {
    color: #8a9199;
  }
  .num {
    font-size: 16px;
  }

  .triangle {
    width: 0;
    height: 0;
    display: inline-block;
    border: 6px solid;
    border-color: #ff716f transparent transparent;
  }
  .numtop {
    display: flex;
    justify-content: space-between;
    > li {
      width: 214px;
      height: 83px;
      background: url(./img/kuang1.png) no-repeat;
      padding: 10px 0 0 90px;
    }
  }
  .num_item {
    padding: 10px 0 10px 90px;
    display: flex;
    height: 83px;
    background: url(./img/kuang2.png) no-repeat;
    margin-top: 2px;

    .item_l {
      width: 200px;
      border-right: 1px solid #1a3c61;
      margin-right: 15px;
    }

    
    .huanbi {
      position: relative;
      .triangle {
        position: absolute;
        left: 53px;
        top: 11px;
        transform-origin: center 3px;
        &.down {
          transform: rotateZ(0deg);
        }
        &.up {
          transform: rotateZ(180deg);
        }
      }
    }
  }
}
</style>
