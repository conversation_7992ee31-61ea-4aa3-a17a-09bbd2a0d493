<template>
  <div>
    <el-dialog
      class="dialog-handle"
      :title="title"
      :visible.sync="visible"
      :close-on-click-modal="false"
      :loading="loading"
    >
      <el-form ref="ruleForm" label-width="100px" class="demo-ruleForm">
        <el-form-item label="下载模板">
          <el-button type="primary" @click="exportTemplate">
            下载{{ config[this.type].keyVal }}信息模板
          </el-button>
          <br />
          <span style="color: #ddd;">*导入信息时需严格按照此模板填写</span>
        </el-form-item>
        <el-form-item :label="`导入${config[this.type].keyVal}信息`" prop="">
          <el-upload
            class="upload-demo"
            action="/safemanage/ipinfo/upload"
            :beforeUpload="beforeAvatarUpload"
            :http-request="handleUpload"
            :on-success="handleproSuccess"
            :on-remove="removeFile"
            :file-list="fileList"
            :limit="1"
            :on-change="changFile"
          >
            <el-button type="primary">选择文件</el-button>
          </el-upload>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="visible = false">取消</el-button>
        <el-button
          type="primary"
          :loading="loading"
          :disabled="!(fileList && fileList.length > 0)"
          @click="submitFun"
        >
          确定
        </el-button>
      </span>
    </el-dialog>
    <el-dialog
      class="dialog-handle"
      title="导入失败数据"
      :visible.sync="visibleError"
      :close-on-click-modal="false"
      :loading="loading"
    >
      <div class="is-mb-20">
        共计导入数据 {{ total }} 条，成功
        <span class="is-color-green">{{ successList }}</span>
        条， 失败
        <span class="is-color-red">{{ failureList }}</span>
        条
      </div>
      <div class="is-mb-10" v-for="(val, key) of importErrors" :key="key">
        <span class="is-text-weight-700">{{ key }}</span>
        : {{ val }}
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="visibleError = false">关 闭</el-button>
        <el-button type="primary" :loading="loading" @click="importerror">
          下载错误数据
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import cloneDeep from 'lodash/cloneDeep'
import antiVpnService from '@/api/antiVpnService'
import config from '../components/config'
import { downloadExcel } from '@/utils/download'

export default {
  name: 'knowledge-base-import',
  components: {},
  props: {
    type: {
      type: String,
      default: () => ''
    }
  },
  data() {
    return {
      loading: false,
      visible: false,
      visibleError: false,
      config: config,
      title: `导入${config[this.type].keyVal}信息`,
      fileList: [],
      total: null,
      successList: null,
      failureList: null,
      importErrors: {},
      importErrorBases: {}
    }
  },
  computed: {},
  watch: {},
  mounted() {},
  methods: {
    handleUpload(param) {},
    handleproSuccess(res, file) {},
    openDialog() {
      this.fileList = []
      this.visible = true
      this.total = null
      this.successList = null
      this.failureList = null
      this.importErrors = {}
      this.importErrorBases = {}
    },
    // 下载模版
    exportTemplate() {
      antiVpnService[config[this.type].downloadTemplateFunName]()
    },
    // 上传回调
    changFile(file, fileList) {
      this.fileList = fileList
    },
    // 上传验证
    beforeAvatarUpload(file) {
      let fileExt = file.name.substring(file.name.lastIndexOf('.') + 1)
      const extension =
        fileExt === 'xlsx' ||
        fileExt === 'xls' ||
        fileExt === 'XLSX' ||
        fileExt === 'XLS'
      if (!extension) {
        this.$message({
          message: '上传文件只能是.XLSX、.XLS格式!',
          type: 'warning'
        })
      }
      return extension
    },
    // 移除附件
    removeFile(file, fileList) {
      let index = this.fileList.indexOf(file)
      this.fileList.splice(index, 1)
    },
    // 上传
    submitFun() {
      if (this.fileList.length === 0) {
        this.$message({
          message: '请选择需要导入的文件',
          type: 'error'
        })
        return
      }
      this.loading = true
      let formData = new FormData()
      formData.append('file', this.fileList[0].raw)
      antiVpnService[config[this.type].importTemplateFunName](formData)
        .then(res => {
          this.loading = false
          if (res.exists) {
            this.total =
              res.importErrorBases.length || 0 + res.importBases.length || 0
            this.successList = res.importBases.length || 0
            this.failureList = res.importErrorBases.length || 0
            this.$message({
              message: `${res.importErrorBases.length || 0}条数据导入失败, ${res
                .importBases.length || 0}条数据成功导入`,
              type: 'warning'
            })
            this.importErrors = res.importErrors
            this.importErrorBases = res.importErrorBases
            this.visibleError = true
          } else {
            this.$message({
              message: `${res.importBases.length || 0}条数据成功导入`,
              type: 'success'
            })
          }
          this.visible = false
          this.$emit('submitFun')
        })
        .catch(() => {
          this.loading = false
        })
    },
    // 下载错误数据
    importerror() {
      antiVpnService[config[this.type].importerrorFunName]({
        importErrorBases: this.importErrorBases
      }).then(res => {
        let name =
          res.headers['content-disposition'].split('filename=') &&
          res.headers['content-disposition'].split('filename=').length > 1 &&
          res.headers['content-disposition'].split('filename=')[1]
        if (res) downloadExcel(res.data, name)
      })
    }
  }
}
</script>

<style lang="scss" scoped></style>
