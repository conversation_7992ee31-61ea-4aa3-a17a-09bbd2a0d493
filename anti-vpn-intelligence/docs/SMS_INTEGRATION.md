# 联通云网能力开放平台短信对接功能

## 功能概述

本功能集成了中国联通云网能力开放平台的短信发送能力，支持通过REST API发送短信。

## 配置说明

### 1. 配置参数

在 `application.yml` 中添加以下配置：

```yaml
app:
  anti-vpn:
    unicom-cloud-ability:
      enabled: true  # 是否启用短信功能
      client-id: YOUR_CLIENT_ID  # 联通分配的客户端ID
      client-secret: YOUR_CLIENT_SECRET  # 联通分配的客户端密钥
      gateway-url: http://172.26.63.140:8180/api/v1  # 网关地址（测试环境）
```

### 2. 环境配置

#### 测试环境
- 网关地址：`http://172.26.63.140:8180/api/v1`
- 测试用clientId：`AWDTAPLTTY5XGGF5`

#### 生产环境
- 网关地址：`http://172.26.63.188:8180/api/v1`
- 需要向联通申请正式的clientId和clientSecret

### 3. 环境变量配置

可以通过环境变量进行配置：

```bash
UNICOM_SMS_ENABLED=true
UNICOM_CLIENT_ID=your_client_id
UNICOM_CLIENT_SECRET=your_client_secret
UNICOM_GATEWAY_URL=http://172.26.63.140:8180/api/v1
```

## API接口

### 发送短信

**接口地址：** `POST /intelligence/vpnIntelligence/sendSms`

**请求参数：**

```json
{
  "serialNumber": ["8618612345678"],  // 接收短信号码数组，号码需以86开头
  "message": "您的验证码是1234，在5分钟内有效。如非本人操作请忽略本短信。"  // 短信内容，最大255字符
}
```

**响应参数：**

```json
{
  "respCode": "1",  // 返回码：1-成功，0-失败
  "respDesc": "success"  // 返回描述
}
```

### 参数说明

- `serialNumber`: 接收短信的手机号码数组，号码必须以"86"开头（中国大陆手机号）
- `message`: 短信内容，长度不能超过255个字符
- 注意：该接口只支持联通号码

## 使用示例

### Java代码示例

```java
@Autowired
private ISmsService smsService;

public void sendVerificationCode(String phoneNumber, String code) {
    SmsRequestDTO request = new SmsRequestDTO();
    request.setSerialNumber(Arrays.asList("86" + phoneNumber));
    request.setMessage("您的验证码是" + code + "，在5分钟内有效。如非本人操作请忽略本短信。");
    
    SmsResponseDTO response = smsService.sendSms(request);
    if (response.isSuccess()) {
        log.info("短信发送成功");
    } else {
        log.error("短信发送失败：{}", response.getRespDesc());
    }
}
```

### HTTP请求示例

```bash
curl -X POST "http://localhost:8080/intelligence/vpnIntelligence/sendSms" \
  -H "Content-Type: application/json" \
  -H "userid: 1" \
  -H "username: admin" \
  -d '{
    "serialNumber": ["8618612345678"],
    "message": "您的验证码是1234，在5分钟内有效。如非本人操作请忽略本短信。"
  }'
```

## 错误处理

### 常见错误码

- `respCode: "0"` - 发送失败
  - 配置未启用
  - 配置参数不完整
  - 网络异常
  - 联通平台异常

### 日志说明

系统会记录详细的发送日志，包括：
- 请求参数
- 响应结果
- 异常信息

## 注意事项

1. **号码格式**：手机号码必须以"86"开头
2. **号码限制**：仅支持联通号码
3. **内容长度**：短信内容不能超过255个字符
4. **频率限制**：请注意联通平台的频率限制
5. **安全性**：clientSecret等敏感信息请妥善保管，不要提交到代码仓库

## 依赖说明

本功能依赖以下组件：
- `cb-ability-sdk-1.4.3.jar` - 联通云网能力开放平台SDK
- Spring Boot Validation - 参数校验
- Hutool - JSON处理

## 故障排查

1. **检查配置**：确认配置参数是否正确
2. **检查网络**：确认能否访问联通网关地址
3. **检查日志**：查看应用日志中的错误信息
4. **检查号码**：确认手机号码格式正确且为联通号码
