package com.eversec.antivpn.log.service;


import com.eversec.antivpn.emnus.ScreenTypeEnum;
import com.eversec.antivpn.log.api.dto.MonitorDataDTO;
import com.eversec.antivpn.log.api.dto.TypeCountDTO;
import com.eversec.antivpn.log.mapper.vo.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

public interface LogService {
    List<TypeCountVO> getNetworkBusinessTypeAggregate(ScreenTypeEnum screenTypeEnum);

    List<TypeCountVO> getProvinceAggregate(ScreenTypeEnum screenTypeEnum);

    List<TypeCountVO> getProtocolTypeAggregate(ScreenTypeEnum screenTypeEnum);

    DateHistogramCombinationVO getDateHistogram(ScreenTypeEnum screenTypeEnum);

    List<TypeCountVO> getVpnTypeAggregate(ScreenTypeEnum screenTypeEnum);

    List<TypeCountVO> getSystemLogAggCount(ScreenTypeEnum screenTypeEnum);

    Map<String, List<String>> getMonitorDataMap();

}
