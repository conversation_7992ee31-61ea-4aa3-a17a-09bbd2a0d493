package com.eversec.antivpn.intelligence.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 跨境通信情报库指令下发载体。解密前
 */
@Getter
@Setter
@Schema(name = "VpnIntelligenceCommandDistributeResponseDTO", description = "跨境通信情报库指令下发返回对象")
public class VpnIntelligenceCommandDistributeResponseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "版本号。协议版本格式：主版本号.副版本号，本规范主版本号为1，副版本号为0，即1.0。\n")
    private String version;

    @Schema(description = "错误代码。0：处理完成；\n" + "1：文件解密失败；\n" + "2：文件校验失败；\n" + "3：文件解压缩失败；\n" + "4：文件格式异常；\n" + "5：文件内容异常；\n"
            + "900：其他异常（存在其他错误，需重新发送）。\n")
    private Integer resultCode;

    @Schema(description = "返回信息。描述错误原因")
    private String msg;



    public static VpnIntelligenceCommandDistributeResponseDTO buildSuccess() {
        VpnIntelligenceCommandDistributeResponseDTO vpnIntelligenceCommandDistributeResponseDTO = new VpnIntelligenceCommandDistributeResponseDTO();
        vpnIntelligenceCommandDistributeResponseDTO.setVersion("1.0");
        vpnIntelligenceCommandDistributeResponseDTO.setResultCode(0);
        vpnIntelligenceCommandDistributeResponseDTO.setMsg("处理完成");
        return vpnIntelligenceCommandDistributeResponseDTO;
    }


    public static VpnIntelligenceCommandDistributeResponseDTO buildError(int resultCode, String msg) {
        VpnIntelligenceCommandDistributeResponseDTO vpnIntelligenceCommandDistributeResponseDTO = new VpnIntelligenceCommandDistributeResponseDTO();
        vpnIntelligenceCommandDistributeResponseDTO.setVersion("1.0");
        vpnIntelligenceCommandDistributeResponseDTO.setResultCode(resultCode);
        vpnIntelligenceCommandDistributeResponseDTO.setMsg(msg);
        return vpnIntelligenceCommandDistributeResponseDTO;
    }

}
