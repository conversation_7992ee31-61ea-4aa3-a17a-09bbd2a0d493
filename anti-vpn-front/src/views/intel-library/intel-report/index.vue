<template>
  <div class="intel-report" v-loading="loading">
    <!-- form -->
    <publicForm
      ref="publicForm"
      :formDataLabel="formDataLabel"
      :labelWidth="'0'"
      :form-data="formData"
      @throwEvent="changeItem"
    />
    <div class="is-overflow-hidden is-mb-10">
      <el-button
        class="is-float-right is-ml-10"
        type="danger"
        :disabled="!(activeList && activeList.length > 0)"
        @click="batchDelete"
      >
        批量删除
      </el-button>
      <el-button
        class="is-float-right is-ml-10"
        type="danger"
        @click="batchDeleteByCondition"
      >
        根据查询条件批量删除
      </el-button>
      <el-button
        class="is-float-right"
        type="primary"
        :disabled="!(activeList && activeList.length > 0)"
        @click="escalationFun(null)"
      >
        批量上报
      </el-button>
      <el-button
        class="is-float-right"
        type="primary"
        @click="batchReporting()"
      >
        自定义上报
      </el-button>
      <el-button class="is-float-right" type="primary" @click="importFun">
        导入
      </el-button>
      <el-button class="is-float-right" type="primary" @click="addFun">
        新增
      </el-button>
      <el-button
        class="is-float-right"
        type="primary"
        @click="redistributeToSmart"
      >
        重新下发到smart
      </el-button>
      <el-button
        class="is-float-right"
        type="primary"
        @click="redistributeToNssa"
      >
        重新下发到态感
      </el-button>
    </div>
    <!-- table -->
    <publicTable
      :tableLabel="tableLabel"
      :tableData="dataList"
      ref="table"
      height="100%"
      :options="{
        highlightCurrentRow: false,
        isString: true,
        checkbox: true,
        border: false
      }"
      @handleSelectionChange="handleSelectionChange"
    >
      <template v-slot:download="{ data }">
        <el-link
          class="is-ml-10 is-float-left"
          type="primary"
          v-if="data.row.attachMent"
          @click="downloadFun(data.row.attachMent)"
        >
          下载取证文件
        </el-link>
      </template>

      <template v-slot:handle="{ data }">
        <el-link
          class="is-ml-10 is-float-left"
          type="primary"
          @click="uploadFun(data.row.id)"
        >
          上传取证文件
        </el-link>
        <el-link
          class="is-ml-10 is-float-left"
          :underline="false"
          type="primary"
          @click="escalationFun(data.row.id)"
        >
          上报
        </el-link>
        <el-link
          class="is-ml-10 is-float-left"
          :underline="false"
          type="primary"
          @click="editBtn(data.row)"
        >
          编辑
        </el-link>
        <el-link
          class="is-ml-10 is-float-left"
          :underline="false"
          type="danger"
          @click="deleteBtn(data.row.id)"
        >
          删除
        </el-link>
      </template>
    </publicTable>
    <!-- 分页 -->
    <el-pagination
      class="is-pt-10 is-float-right"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="pagingInfo.current"
      :page-sizes="pagingInfo.pageSizes"
      :page-size="pagingInfo.size"
      :layout="pagingInfo.layout"
      :total="pagingInfo.total"
      :key="pagingInfo.total"
    />
    <detailDialog ref="detailDialog" @dataFormSubmit="getDataList" />
    <importDialog ref="importDialog" @submitFun="getDataList" />
    <uploadDialog ref="uploadDialog" @submitFun="getDataList" />
    <el-dialog
      class="dialog-handle"
      :title="'选择类型'"
      :visible.sync="visible"
      :close-on-click-modal="false"
      :loading="loading"
    >
      <el-form ref="ruleForm" label-width="100px" class="demo-ruleForm">
        <el-form-item label="网络类型">
          <dic-select
            v-model="networkBusinessId"
            :enum-key="'NETWORK_TYPE'"
            :clearable="true"
            :placeholder="`请选择网络类型`"
          />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="visible = false">取消</el-button>
        <el-button type="primary" :loading="loading" @click="submitBtn(false)">
          确定
        </el-button>
      </span>
    </el-dialog>
    <el-dialog
      class="dialog-handle"
      title="自定义上报"
      :visible.sync="visible1"
      :close-on-click-modal="false"
    >
      <!-- form -->
      <publicForm
        ref="publicForm"
        :formDataLabel="formDataLabel1"
        :labelWidth="'0'"
        :form-data="formData1"
        @throwEvent="changeItem1"
        :loading="loading"
      />
      <div class="is-overflow-hidden is-mb-10">
        获取到
        <span class="is-color-red">{{ ids.length }}</span>
        条数据。共计
        <span class="is-color-red">{{ formData1.total || 0 }}</span>
        条数据；
        <span class="is-color-orange">* 建议单次上报数量不超过 10000 次</span>
      </div>
      <el-form ref="ruleForm" label-width="100px" class="demo-ruleForm">
        <el-form-item label="网络类型">
          <dic-select
            v-model="networkBusinessId"
            :enum-key="'NETWORK_TYPE'"
            :clearable="true"
            :placeholder="`请选择网络类型`"
          />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="visible1 = false">取消</el-button>
        <el-button type="primary" :loading="loading" @click="submitBtn(true)">
          确定
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import cloneDeep from 'lodash/cloneDeep'
import { customPagingInfo } from '@/config/base'
import debounce from 'lodash/debounce'
import antiVpnService from '@/api/antiVpnService'

export default {
  name: 'intel-report',
  components: {
    detailDialog: () => import('./detail-dialog'),
    importDialog: () => import('./import-dialog'),
    uploadDialog: () => import('./upload-dialog')
  },
  data() {
    return {
      loading: false,
      visible: false,
      visible1: false,
      formDataLabel: [
        [
          {
            val: 'ruleId',
            type: 'input',
            funName: 'changeItem',
            className: 'is-one-fifth is-pr-10',
            placeholder: '请输入规则ID'
          },
          {
            val: 'vpnId',
            type: 'input',
            funName: 'changeItem',
            className: 'is-one-fifth is-pr-10',
            placeholder: '请输入情报库ID'
          },
          {
            val: 'comCode',
            type: 'dic-select',
            enumKey: 'ISP_CODE',
            showAll: true,
            funName: 'changeItem',
            className: 'is-one-fifth is-pr-10',
            placeholder: '请选择运营商编码'
          },
          {
            val: 'provinceId',
            type: 'dic-select',
            enumKey: 'PROVINCE_CODE',
            showAll: true,
            funName: 'changeItem',
            className: 'is-one-fifth is-pr-10',
            placeholder: '请选择省级区域'
          },
          {
            val: 'systemCode',
            type: 'dic-select',
            enumKey: 'SYSTEM_CODE',
            showAll: true,
            funName: 'changeItem',
            className: 'is-one-fifth is-pr-10',
            placeholder: '请选择业务系统标识'
          }
        ],
        [
          {
            val: 'vpnName',
            type: 'input',
            funName: 'changeItem',
            className: 'is-one-fifth is-pr-10',
            placeholder: '请输入VPN名称'
          },
          {
            val: 'vpnDomain',
            type: 'input',
            funName: 'changeItem',
            className: 'is-one-fifth is-pr-10',
            placeholder: '请输入VPN域名'
          },
          {
            val: 'vpnIp',
            type: 'input',
            funName: 'changeItem',
            className: 'is-one-fifth is-pr-10',
            placeholder: '请输入IP地址'
          },
          {
            val: 'vpnAirportCode',
            type: 'dic-select',
            enumKey: 'AIRPORT_INFORMATION',
            funName: 'changeItem',
            className: 'is-one-fifth is-pr-10',
            placeholder: '请选择VPN服务商'
          },
          {
            val: 'vpnProtocolCode',
            type: 'dic-select',
            enumKey: 'VPN_PROTOCOL',
            showAll: true,
            funName: 'changeItem',
            className: 'is-one-fifth is-pr-10',
            placeholder: '请选择协议'
          }
        ],
        [
          {
            val: 'commandId',
            type: 'input',
            funName: 'changeItem',
            className: 'is-one-fifth is-pr-10',
            placeholder: '请输入指令ID'
          },
          {
            val: 'source',
            type: 'dic-select',
            enumKey: 'INTELLIGENCE_SOURCE',
            funName: 'changeItem',
            className: 'is-one-fifth is-pr-10',
            placeholder: '请选择情报来源'
          },
          {
            val: 'reportStatus',
            type: 'dic-select',
            enumKey: 'INTELLIGENCE_COMMAND_REPORT_STATUS',
            funName: 'changeItem',
            className: 'is-one-fifth is-pr-10',
            placeholder: '请选择上报状态'
          },
          {
            val: 'typeId',
            type: 'dic-select',
            enumKey: 'INTELLIGENCE_TYPE',
            showAll: true,
            funName: 'changeItem',
            className: 'is-one-fifth is-pr-10',
            placeholder: '请选择情报类型'
          },
          {
            val: 'applicationProtocolCode',
            type: 'dic-select',
            enumKey: 'APPLICATION_LAYER_PROTOCOL',
            showAll: true,
            funName: 'changeItem',
            className: 'is-one-fifth is-pr-10',
            placeholder: '请选择应用协议编号'
          }
        ],
        [
          {
            val: 'timestamp',
            type: 'date-time-range',
            funName: 'changeItem',
            className: 'is-4 is-pr-10',
            startP: '请选择发现开始时间',
            endP: '请选择发现结束时间'
          },
          {
            val: 'createDatetime',
            type: 'date-time-range',
            funName: 'changeItem',
            className: 'is-4 is-pr-10',
            startP: '请选择创建开始时间',
            endP: '请选择创建结束时间'
          },

          {
            type: 'btn',
            btnTxt: '刷新',
            funName: 'refresh',
            btnType: ''
          }
        ]
      ],
      formDataLabel1: [
        [
          {
            val: 'ruleId',
            type: 'input',
            className: 'is-one-fifth is-pr-10',
            placeholder: '请输入规则ID'
          },
          {
            val: 'vpnId',
            type: 'input',
            className: 'is-one-fifth is-pr-10',
            placeholder: '请输入情报库ID'
          },
          {
            val: 'comCode',
            type: 'dic-select',
            enumKey: 'ISP_CODE',
            showAll: true,
            className: 'is-one-fifth is-pr-10',
            placeholder: '请选择运营商编码'
          },
          {
            val: 'provinceId',
            type: 'dic-select',
            enumKey: 'PROVINCE_CODE',
            showAll: true,
            className: 'is-one-fifth is-pr-10',
            placeholder: '请选择省级区域'
          },
          {
            val: 'systemCode',
            type: 'dic-select',
            enumKey: 'SYSTEM_CODE',
            showAll: true,
            className: 'is-one-fifth is-pr-10',
            placeholder: '请选择业务系统标识'
          }
        ],
        [
          {
            val: 'vpnName',
            type: 'input',
            className: 'is-one-fifth is-pr-10',
            placeholder: '请输入VPN名称'
          },
          {
            val: 'vpnDomain',
            type: 'input',
            className: 'is-one-fifth is-pr-10',
            placeholder: '请输入VPN域名'
          },
          {
            val: 'vpnIp',
            type: 'input',
            className: 'is-one-fifth is-pr-10',
            placeholder: '请输入IP地址'
          },
          {
            val: 'vpnAirportCode',
            type: 'dic-select',
            enumKey: 'AIRPORT_INFORMATION',
            className: 'is-one-fifth is-pr-10',
            placeholder: '请选择VPN服务商'
          },
          {
            val: 'vpnProtocolCode',
            type: 'dic-select',
            enumKey: 'VPN_PROTOCOL',
            showAll: true,
            className: 'is-one-fifth is-pr-10',
            placeholder: '请选择协议'
          }
        ],
        [
          {
            val: 'commandId',
            type: 'input',
            className: 'is-one-fifth is-pr-10',
            placeholder: '请输入指令ID'
          },
          {
            val: 'source',
            type: 'dic-select',
            enumKey: 'INTELLIGENCE_SOURCE',
            className: 'is-one-fifth is-pr-10',
            placeholder: '请选择情报来源'
          },
          {
            val: 'reportStatus',
            type: 'dic-select',
            enumKey: 'INTELLIGENCE_COMMAND_REPORT_STATUS',
            className: 'is-one-fifth is-pr-10',
            placeholder: '请选择上报状态',
            disabled: true
          },
          {
            val: 'typeId',
            type: 'dic-select',
            enumKey: 'INTELLIGENCE_TYPE',
            showAll: true,
            className: 'is-one-fifth is-pr-10',
            placeholder: '请选择情报类型'
          },
          {
            val: 'applicationProtocolCode',
            type: 'dic-select',
            enumKey: 'APPLICATION_LAYER_PROTOCOL',
            showAll: true,
            className: 'is-one-fifth is-pr-10',
            placeholder: '请选择应用协议编号'
          }
        ],
        [
          {
            val: 'timestamp',
            type: 'date-time-range',
            startP: '请选择发现开始时间',
            endP: '请选择发现结束时间',
            className: 'is-pr-10'
          },
          {
            val: 'createDatetime',
            type: 'date-time-range',
            startP: '请选择创建开始时间',
            endP: '请选择创建结束时间'
          }
        ],
        [
          {
            val: 'size',
            type: 'input-number',
            className: 'is-6 is-pr-10',
            placeholder: '请输入查询条数，默认10000条，最大50000条。',
            maxNum: 50000,
            maxlength: 5,
            limit: true
          },
          {
            type: 'btn',
            btnTxt: '查询',
            funName: 'isAllQuery',
            btnType: ''
          }
        ]
      ],
      queryKey: [
        'commandId',
        'comCode',
        'provinceId',
        'typeId',
        'applicationProtocolCode',
        'ruleId',
        'vpnId',
        'vpnName',
        'vpnDomain',
        'vpnIp',
        'vpnAirportCode',
        'vpnProtocolCode',
        'source',
        'reportStatus',
        'systemCode',
        'timestamp',
        'createDatetime'
      ],
      formData: {
        createDatetime: [],
        vpnSoftwareCodeList: []
      },
      formData1: {
        createDatetime: [],
        vpnSoftwareCodeList: [],
        reportStatus: 'TO_BE_REPORTED',
        size: 10000
      },
      ids: [],
      pagingInfo: cloneDeep(customPagingInfo({ pageSizes: [10, 30, 50, 100] })), // 分页信息
      tableLabel: [
        {
          label: '执行指令ID',
          val: 'commandId',
          width: 120
        },
        {
          label: '运营商编码',
          val: 'comCode',
          type: 'dic-val',
          enumKey: 'ISP_CODE',
          width: 100
        },
        {
          label: '省级区域编号',
          val: 'provinceId',
          type: 'dic-val',
          enumKey: 'PROVINCE_CODE',
          width: 120
        },
        {
          label: '业务系统标识',
          val: 'systemCode',
          type: 'dic-val',
          enumKey: 'SYSTEM_CODE',
          width: 120
        },
        {
          label: '情报类型',
          val: 'typeId',
          type: 'dic-val',
          enumKey: 'INTELLIGENCE_TYPE',
          width: 140
        },
        {
          label: '应用协议编号',
          val: 'applicationProtocolCode',
          type: 'dic-val',
          enumKey: 'APPLICATION_LAYER_PROTOCOL',
          width: 140
        },
        {
          label: '网络类型',
          val: 'networkBusinessId',
          type: 'dic-val',
          enumKey: 'NETWORK_TYPE',
          width: 140
        },
        {
          label: '规则ID',
          val: 'ruleId',
          width: 140
        },
        {
          label: '情报库ID',
          val: 'vpnId',
          width: 140
        },
        {
          label: 'VPN名称',
          val: 'vpnName',
          width: 180
        },
        {
          label: 'VPN域名',
          val: 'vpnDomain',
          width: 160
        },
        {
          label: 'IP地址',
          val: 'vpnIp',
          width: 140
        },
        {
          label: 'URL地址',
          val: 'vpnUrlDecode',
          width: 200
        },
        {
          label: '报文',
          val: 'vpnMessageDecode',
          width: 200
        },
        {
          label: '取证链接',
          val: 'vpnLink',
          width: 200
        },
        {
          label: '端口',
          val: 'vpnPort',
          width: 80
        },
        {
          label: '节点所属国家',
          val: 'vpnCountry',
          width: 160
        },
        {
          label: '所属VPN服务商',
          val: 'vpnAirportCode',
          type: 'dic-val',
          enumKey: 'AIRPORT_INFORMATION',
          width: 140
        },
        {
          label: '软件',
          val: 'vpnSoftwareCodeList',
          type: 'dic-val',
          enumKey: 'SOFTWARE_INFORMATION',
          multiple: true,
          width: 120
        },
        {
          label: '协议',
          val: 'vpnProtocolCode',
          type: 'dic-val',
          enumKey: 'VPN_PROTOCOL',
          width: 120
        },
        {
          label: '情报来源',
          val: 'source',
          type: 'dic-val',
          enumKey: 'INTELLIGENCE_SOURCE',
          width: 120
        },
        {
          label: '发现时间',
          val: 'timeStamp',
          type: 'date',
          width: 180
        },
        {
          label: '创建时间',
          val: 'createDatetime',
          type: 'date',
          width: 180
        },
        {
          label: '上报时间',
          val: 'reportDatetime',
          type: 'date',
          width: 180
        },
        {
          label: '上报状态',
          val: 'reportStatus',
          type: 'dic-val',
          enumKey: 'INTELLIGENCE_COMMAND_REPORT_STATUS',
          width: 120
        },
        {
          label: '取证文件',
          val: 'downloadFile',
          type: 'slot',
          slotName: 'download',
          width: 120
        },

        {
          label: '操作',
          type: 'slot',
          slotName: 'handle',
          fixed: 'right',
          headerAlign: 'center',
          width: 240
        }
      ],
      dataList: [],
      activeList: [],
      networkBusinessId: null,
      param: null
    }
  },
  computed: {},
  watch: {},
  mounted() {
    this.formData = {
      createDatetime: [],
      vpnSoftwareCodeList: []
    }
    for (let name in this.$route.query) {
      if (name === 'size') {
        this.pagingInfo.size = Number(this.$route.query[name])
      } else if (name === 'current') {
        this.pagingInfo.current = Number(this.$route.query[name])
      } else {
        this.$set(this.formData, name, this.$route.query[name])
        this.formData[name] = this.$route.query[name]
      }
    }
    this.getDataList()
  },
  methods: {
    // 表单监听回调
    changeItem(val) {
      if (val && val.funName === 'refresh') {
        this.getDataList()
        return
      }
      if (val && val.funName === 'isAllQuery') {
        this.isAllQuery()
        return
      }
      if (val) {
        this.pagingInfo.current = 1
      }
      this.debounce(this)
    },
    // 表单监听回调
    changeItem1(val) {
      if (val && val.funName === 'isAllQuery') {
        this.isAllQuery()
      }
    },
    // 防抖查询
    debounce: debounce(vm => {
      let tmpData = {
        size: vm.pagingInfo.size,
        current: vm.pagingInfo.current
      }
      vm.queryKey.map(e => {
        if (vm.formData[e]) vm.$set(tmpData, e, vm.formData[e])
      })
      vm.$router.push({
        path: vm.$route.path,
        query: tmpData
      })
      vm.getDataList()
    }, 300),
    // 选中框
    handleSelectionChange(val) {
      this.activeList = val
    },
    // 列表查询
    getDataList() {
      this.loading = true
      let param = {
        current: this.pagingInfo.current, //当前页
        size: this.pagingInfo.size //每页展示数量
      }
      this.queryKey.map(e => {
        if (this.formData[e]) {
          if (e === 'createDatetime' && this.formData[e].length > 0) {
            this.$set(param, 'createDatetimeStart', this.formData[e][0])
            this.$set(param, 'createDatetimeEnd', this.formData[e][1])
          } else if (e === 'timestamp' && this.formData[e].length > 0) {
            this.$set(param, 'timeStampStart', this.formData[e][0])
            this.$set(param, 'timeStampEnd', this.formData[e][1])
          } else {
            this.$set(param, e, this.formData[e])
          }
        }
      })
      antiVpnService
        .getVpnIntelligence(param)
        .then(res => {
          this.dataList = res.records || []
          this.pagingInfo.total = res.total || 0
          this.loading = false
        })
        .catch(() => {
          this.loading = false
        })
    },
    // 每页数
    handleSizeChange(val) {
      this.pagingInfo.size = val
      this.pagingInfo.current = 1
      this.changeItem()
    },
    // 当前页
    handleCurrentChange(val) {
      this.pagingInfo.current = val
      this.changeItem()
    },
    // 删除
    deleteBtn(id) {
      this.$confirm('此操作将删除该数据, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.loading = true
          antiVpnService
            .vpnIntelligenceDeleteByIds(id)
            .then(res => {
              this.loading = false
              this.$message({
                type: 'success',
                message: '删除成功!'
              })
              this.getDataList()
            })
            .catch(() => {
              this.loading = false
            })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    // 批量删除
    batchDelete() {
      this.$confirm('此操作将删除已选数据, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.loading = true
          antiVpnService
            .vpnIntelligenceDeleteByIds(this.activeList.map(e => e.id))
            .then(res => {
              this.loading = false
              this.$message({
                type: 'success',
                message: '删除成功!'
              })
              this.getDataList()
            })
            .catch(() => {
              this.loading = false
            })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    // 根据条件批量删除
    batchDeleteByCondition() {
      this.$confirm('此操作将根据查询条件删除数据，如无查询条件将删除所有数据，是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.loading = true

          let param = {
          }
          this.queryKey.map(e => {
            if (this.formData[e]) {
              if (e === 'createDatetime' && this.formData[e].length > 0) {
                this.$set(param, 'createDatetimeStart', this.formData[e][0])
                this.$set(param, 'createDatetimeEnd', this.formData[e][1])
              } else if (e === 'timestamp' && this.formData[e].length > 0) {
                this.$set(param, 'timeStampStart', this.formData[e][0])
                this.$set(param, 'timeStampEnd', this.formData[e][1])
              } else {
                this.$set(param, e, this.formData[e])
              }
            }
          })

          antiVpnService
            .vpnIntelligenceDeleteByCondition(param)
            .then(res => {
              this.loading = false
              this.$message({
                type: 'success',
                message: '删除成功!'
              })
              this.getDataList()
            })
            .catch(() => {
              this.loading = false
            })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    // 编辑
    editBtn(item) {
      this.$refs.detailDialog.openDialog(item)
    },
    // 新增
    addFun() {
      this.$refs.detailDialog.openDialog(null)
    },
    // 导入
    importFun() {
      this.$refs.importDialog.openDialog()
    },
    // 重新下发到smart
    redistributeToSmart() {
      this.loading = true
      antiVpnService
        .redistributeToSmart()
        .then(res => {
          this.loading = false
          this.$message({
            type: 'success',
            message: '操作成功!'
          })
        })
        .catch(() => {
          this.loading = false
        })
    },
    // 重新下发到smart
    redistributeToNssa() {
      this.loading = true
      antiVpnService
        .redistributeToNssa()
        .then(res => {
          this.loading = false
          this.$message({
            type: 'success',
            message: '操作成功!'
          })
        })
        .catch(() => {
          this.loading = false
        })
    },
    // 上报
    escalationFun(e) {
      this.visible = true
      this.networkBusinessId = null
      this.param = null
      this.param = e ? [e] : this.activeList.map(e => e.id)
    },
    // 批量上报
    batchReporting(e) {
      this.visible1 = true
      this.networkBusinessId = null
      this.formData1 = {
        createDatetime: [],
        vpnSoftwareCodeList: [],
        size: 10000,
        reportStatus: 'TO_BE_REPORTED'
      }
    },
    // 列表查询
    isAllQuery() {
      this.loading = true
      let param = {
        current: 1, //当前页
        size: this.formData1.size || 10000 //每页展示数量
      }
      this.queryKey.map(e => {
        if (this.formData1[e]) {
          if (e === 'createDatetime' && this.formData1[e].length > 0) {
            this.$set(param, 'createDatetimeStart', this.formData1[e][0])
            this.$set(param, 'createDatetimeEnd', this.formData1[e][1])
          } else if (e === 'timestamp' && this.formData1[e].length > 0) {
            this.$set(param, 'timeStampStart', this.formData1[e][0])
            this.$set(param, 'timeStampEnd', this.formData1[e][1])
          } else {
            this.$set(param, e, this.formData1[e])
          }
        }
      })
      antiVpnService
        .getVpnIntelligence(param)
        .then(res => {
          this.ids = (res.records || []).map(e => e.id)
          this.formData1.total = res.total || 0
          this.loading = false
        })
        .catch(() => {
          this.loading = false
        })
    },
    // 上传取证文件
    uploadFun(id) {
      this.$refs.uploadDialog.openDialog(id)
      // antiVpnService
      //   .uploadEvidenceFile(id)
      //   .then(res => {
      //     console.log(res);
      //     this.$message({
      //       message: `上传取证文件成功`,
      //       type: "success"
      //     });
      //     this.loading = false;
      //     this.visible = false;
      //     this.getDataList();
      //   })
      //   .catch(() => {
      //     this.loading = false;
      //   });
    },
    // 下载取证文件
    downloadFun(attachMent) {
      antiVpnService.downloadEvidenceFile(attachMent)
    },
    // 提交上报
    submitBtn(isAll) {
      if (!this.networkBusinessId) {
        this.$message({
          message: '请选择需网络类型',
          type: 'error'
        })
        return
      }
      this.loading = true
      antiVpnService
        .vpnIntelligenceReport(
          isAll ? this.ids : this.param,
          this.networkBusinessId
        )
        .then(res => {
          this.$message({
            message: `上报成功`,
            type: 'success'
          })
          this.loading = false
          this.visible = false
          this.getDataList()
          if (isAll) this.isAllQuery()
        })
        .catch(() => {
          this.loading = false
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.intel-report {
  height: 100%;
  ::v-deep .public-table {
    height: calc(100% - 155px - 40px - 40px - 40px);
  }
}
</style>
