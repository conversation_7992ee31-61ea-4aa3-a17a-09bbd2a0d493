<template>
  <!--搜索-->
  <div class="search_container" :style="{ height: searchBoxH }">
    <template v-if="isOpen">
      <item v-for="(f, index) in data" :key="index" :field="f" :form="filters" :width="width" :size="size"
        :select-width="selectWidth" @search="handleSearch" ref="itemComponents" />
      <div class="right-buttons item">
        <a style="cursor:pointer" class="iconfont icon-shaixuan mr20" @click="customSearch" v-if="hasCustomSearch">自定义搜索</a>
        <el-button class="mr10" :size="size" icon="el-icon-refresh" @click="handleClear">重置
        </el-button>
        <el-button :size="size" type="primary" icon="el-icon-search" @click="handleSearch">查询
        </el-button>
      </div>
    </template>
    <div v-if="iconShow" class="iconBox">
      <i :class="isOpen ? 'up' : 'down'" :title="isOpen ? '收起' : '展开'" @click="isOpen = !isOpen"></i>
    </div>
  </div>
</template>
<script>
import item from './item';
export default {
  name: 'Search',
  components: {
    item,
  },
  props: {
    hasCustomSearch: {
      type: Boolean,
      default: false,
    },
    width: {
      type: String,
      default: '',
    },
    selectWidth: {
      type: Number,
      default: 200,
    },
    size: {
      type: String,
      default: '—', // medium
    },
    data: Array,
    // 收起、展开是否显示
    iconShow: {
      type: Boolean,
      default: true,
    },
    // 是否加载进来就执行，默认执行，如果tab形式都有搜索可关闭
    createdRun: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      filters: {},
      alias: [], // 存在别名的字段
      isOpen: true,
    };
  },
  computed: {
    searchBoxH() {
      if (this.iconShow) {
        return this.isOpen ? 'auto' : '24px';
      }
      return 'auto';
    },
  },
  watch: {
    data: {
      handler(val) {
        this.filters = {};
        this.alias = [];
        val.forEach((el) => {
          if (el.type == 'province') {
            this.$set(this.filters, el.prop1, null);
            this.$set(this.filters, el.prop2, null);
          } else if (el.aliasProps && el.aliasProps.length > 0) {
            // 存在像时间选择器那样的别名
            this.alias.push(el.prop);
            el.aliasProps.forEach((item) => {
              item && this.$set(this.filters, item, null);
            });
          } else {
            this.$set(this.filters, el.prop, el.defaultValue || null);
          }
        });

        // console.log('search-index-watch', this.filters);
        this.$nextTick(() => {
          this.createdRun && this.handleSearch(); // 搜索
        });
        // if (window.globalCache.userInfo.area.length > 1) {
        //   this.filters.province = window.globalCache.userInfo.area[1];
        // this.refs.itemComponents.handleChange(this.filters.province, false);
        // }
      },
      deep: true,
      immediate: true,
    },
  },

  methods: {
    customSearch() {
      this.$emit('custom-search');
    },
    handleSearch() {
      this.handlerAlias();
      delete this.filters.undefined;
      this.$emit('on-search', this.filters);
    },
    handleClear() {
      const keys = Object.keys(this.filters);
      if (keys.length > 0) {
        keys.forEach((el) => {
          this.filters[el] = null;
        });
      }
      this.$emit('on-clear');
    },
    // 处理别名
    handlerAlias() {
      const keys = Object.keys(this.filters);
      if (keys.length > 0) {
        keys.forEach((el) => {
          if (this.alias.includes(el)) {
            let k =
                this.data.find((e) => e.prop == el) &&
                this.data.find((e) => e.prop == el)['aliasProps'];
            k.length > 0 &&
                k.forEach((e, i) => {
                  this.filters[e] = this.filters[el][i];
                });
            // this.filters[el] = null;
          }
        });
      }
    },
  },
};

</script>
<style scoped lang="scss">
  .search_container {
    position: relative;
    overflow: hidden;
    transition: all 0.4s ease;
    .item {
      // display: flex;
      float: left;
      margin-bottom: 20px;
      width: calc(calc(100% - 96px) / 3);

      &:not(:nth-of-type(3n)) {
        margin-right: 48px;
      }

      &.right-buttons {
        padding-right: 0;
        margin-right: 0;
        text-align: right;
        float: right;
      }
    }

    .filter-item {
      margin-right: 10px;
    }

    .iconBox {
      clear: both;
      border-top: 1px solid #e1e3ed;
      position: relative;
      height: 16px;
      margin-bottom: 20px;

      >i {
        position: absolute;
        top: 0;
        right: 0;
        width: 24px;
        height: 16px;
        cursor: pointer;

        &.up {
          background: url(./img/arrow_up.png) no-repeat;
        }

        &.down {
          background: url(./img/arrow_down.png) no-repeat;
          bottom: 9px;
        }
      }
    }
  }

</style>
