package com.eversec.antivpn.log.service;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.eversec.antivpn.emnus.ScreenTypeEnum;
import com.eversec.antivpn.log.api.dto.CrossBorderCommLogDTO;
import com.eversec.antivpn.log.api.dto.CrossBorderCommLogRequest;
import com.eversec.antivpn.log.api.dto.TypeCountDTO;
import com.eversec.antivpn.log.api.dto.base.AggCountDTO;
import com.eversec.antivpn.log.api.dto.base.DateHistogramDTO;
import com.eversec.antivpn.log.entity.E1CommLog;
import com.eversec.antivpn.log.mapper.vo.TypeCountVO;
import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-12
 */
public interface SoftWareCommLogService {


    /**
     * 获取软件总量
     * @return
     */
    TypeCountDTO getDateSoftWareSum();

    /**
     * 近7日用户跨境通信使用的软件数量
     * @return
     */
    TypeCountDTO last7Days();

    /**
     * 软件用户量 TOP10
     * @param screenTypeEnum
     * @return
     */
    List<TypeCountDTO> getLogNumByUserTop10(ScreenTypeEnum screenTypeEnum);

    /**
     * 软件日志访问量
     * @param screenTypeEnum
     * @return
     */
    List<TypeCountDTO> getLogNumBySofaWareTop10(ScreenTypeEnum screenTypeEnum);

    /**
     * 用户量TOP10的软件提供服务次数趋势图（折线图）
     * @param screenTypeEnum
     * @return
     */
    List<DateHistogramDTO> getLogNumBySofaWareAndDayTop10(ScreenTypeEnum screenTypeEnum);

    /**
     * 用户量TOP10的软件开发者信息
     * @param screenTypeEnum
     * @return
     */
    List<DateHistogramDTO> getUserNumDevelop(ScreenTypeEnum screenTypeEnum);

    /**
     * 软件协议适配度Topo
     * @param screenTypeEnum
     * @return
     */
    List<TypeCountDTO> getsoftWareProtocolTopo10(ScreenTypeEnum screenTypeEnum);

    /**
     * 授权占比
     * @param screenTypeEnum
     * @return
     */
    List<TypeCountDTO> getSoftwareLicence(ScreenTypeEnum screenTypeEnum);

    /**
     *软件对协议的适配度 top10
     * @param screenTypeEnum
     * @return
     */
    List<TypeCountDTO> getsoftWareSystemTopo10(ScreenTypeEnum screenTypeEnum);
}
