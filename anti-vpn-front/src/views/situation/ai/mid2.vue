<template>
  <section class="title_box2" style="margin-top: 12px">
    <div class="box_tit">
      AI 模型监测跨境通信日志趋势图
      <span class="fr color_blue">总上报数：{{ getNum(total) }} </span>
    </div>
    <div class="box_con" style="height: 260px; padding-top: 20px">
      <Empty class="empty" v-if="isEmpty" color="#fff" />
      <e-charts v-else type="line" :options="options"></e-charts>
    </div>
  </section>
</template>

<script>
import echarts from "echarts";
import Empty from "@/components/empty.vue";

export default {
  components: {
    Empty,
  },
  props: {
    tendency: {
      type: Object,
    },
  },
  watch: {
    tendency: {
      handler(val) {
        this.lineData = val;
        this.init();
      },
      immediate: true,
      deep: true,
    },
  },
  data() {
    return {
      options: null,
      lineData: {},
      total: 0,
      isEmpty: false,
    };
  },
  mounted() {
    this.init();
  },
  methods: {
    init() {
      this.lineData.xData.length == 0
        ? (this.isEmpty = true)
        : (this.isEmpty = false);
      this.total = 0;
      this.lineData.yData.map((item) => {
        this.total += item;
      });
      this.options = {
        grid: {
          left: "20",
          right: "20",
          bottom: "20",
          top: "10px",
          containLabel: true,
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            lineStyle: {
              color: "#ddd",
            },
          },
          backgroundColor: "rgba(255, 140, 140,0.2)",
          padding: [5, 10],
          textStyle: {
            color: "#fff",
          },
          extraCssText: "box-shadow: 0 0 5px rgba(0,0,0,0.3)",
        },
        xAxis: {
          type: "category",
          data: this.lineData.xData,
          boundaryGap: false,

          splitLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          axisLine: {
            lineStyle: {
              color: "#444956",
            },
          },
          axisLabel: {
            margin: 10,
            textStyle: {
              fontSize: 12,
              color: "#a7acb4",
            },
          },
        },
        yAxis: {
          type: "value",
          splitLine: {
            lineStyle: {
              color: ["#1a2234"],
            },
          },
          axisTick: {
            show: false,
          },
          axisLine: {
            show: false,
            lineStyle: {
              color: "#778599",
            },
          },
          axisLabel: {
            textStyle: {
              fontSize: 12,
            },
          },
        },
        series: [
          {
            name: "上报数",
            type: "line",
            showSymbol: false,
            symbol: "circle",
            symbolSize: 6,
            smooth: true,
            data: this.lineData.yData,
            areaStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  0.8,
                  [
                    {
                      offset: 0,
                      color: "rgba(255, 140, 140,0.3)",
                    },
                    {
                      offset: 1,
                      color: "rgba(255, 140, 140,0)",
                    },
                  ],
                  false
                ),
              },
            },
            itemStyle: {
              normal: {
                color: "#ff8c8c",
              },
            },
            lineStyle: {
              normal: {
                width: 1,
              },
            },
          },
        ],
      };
    },
    getNum(n) {
      let numFt = n;
      if (n >= 10000 && n < 1000000) {
        n = (n / 10000).toFixed(2);
        numFt = n + "万";
      } else if (n >= 1000000 && n < 100000000) {
        n = (n / 1000000).toFixed(2);
        numFt = n + "百万";
      } else if (n >= 100000000) {
        n = (n / 100000000).toFixed(2);
        numFt = n + "亿";
      }
      return numFt;
    },
  },
};
</script>

<style scoped lang="scss">
.title_box2 {
  margin-top: -20px;
  .box_tit {
    color: #fff;
    font-size: 16px;
    padding: 0 40px;
    line-height: 40px;
    height: 37px;
    background: url(./img/tit2_bg.png) no-repeat;
    background-size: 100% 100%;
    position: relative;
    .todayft {
      position: absolute;
      right: 40px;
      top: 40px;
      color: #fff;
      font-size: 14px;
    }
    .color_blue {
      color: #51c8ff;
      font-size: 14px;
    }
    span {
      cursor: pointer;
      &.active {
        color: #fff;
      }
    }
  }
  .box_con {
    // height: 254px;
    clear: both;
  }
}
</style>
