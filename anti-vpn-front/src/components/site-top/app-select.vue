<template>
  <!-- el-select 不能自适应宽度 -->
  <!-- <el-select :value="currentApp.key" @change="onChangeApp" size="mini">
    <el-option v-for="app in appList" :key="app.key" :value="app.key" :label="app.name"></el-option>
  </el-select>-->
  <div ref="topBtns" class="app-select">
    <template v-if="globalConfig.appSelectStyle === 1">
      <ul class="top-btns">
        <li
          v-for="app in appTopList"
          :key="app.key"
          :class="['app-btn', { selected: checkSelected(app) }]"
          @click="handleCommandLeft(app)"
        >
          {{ app.name }}
        </li>
        <li v-if="extraApp" class="app-btn selected">
          {{ extraApp.name }}
        </li>
      </ul>
      <el-dropdown
        v-if="appMoreList.length > 0"
        size="default"
        style="line-height: inherit"
        trigger="hover"
        placement="bottom"
        @command="handleCommandRight"
      >
        <i class="app-btn el-icon-more" />
        <el-dropdown-menu
          slot="dropdown"
          :class="{ 'dropdown-max-height': appMoreList.length > 10 }"
        >
          <el-dropdown-item
            v-for="app in appMoreList"
            :key="app.key"
            :command="app"
            :class="{ selected: checkSelected(app) }"
          >
            {{ app.name }}
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </template>
    <el-dropdown
      v-else
      trigger="hover"
      size="default"
      placement="bottom"
      @command="onSelect"
    >
      <span class="el-dropdown-link">
        <span>{{ currentApp ? currentApp.name : compassApp.name }}</span>
        <i class="el-icon-arrow-down el-icon--right" />
      </span>
      <el-dropdown-menu
        slot="dropdown"
        :class="{ 'dropdown-max-height': appList.length > 10 }"
      >
        <el-dropdown-item
          v-for="app in appList"
          :key="app.key"
          :command="app"
          :class="{ selected: checkSelected(app) }"
        >
          {{ app.name }}
        </el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
  </div>
</template>

<script>
import { mapState, mapGetters, mapMutations } from 'vuex'
import { switchApp } from '@/router'
import cloneDeep from 'lodash/cloneDeep'
import { textSize } from '@/utils/base'
import { COMPASS_KEY, QUERY_APPKEY } from '@/consts'

export default {
  data() {
    return {
      globalConfig: window.globalConfig,
      topBtnNum: 0,
      topBtnMarginPadding: 20,
      extraApp: null,
      compassApp: {
        key: COMPASS_KEY,
        name: window.globalConfig.compassBtnName
      }
    }
  },
  computed: {
    ...mapState(['apps', 'theme']),
    ...mapGetters('apps', ['currentApp']),
    appList() {
      let tmp = this.apps.list.filter(
        app => app.showInHome == 1 && app.deploy != 0
      )
      if (window.globalConfig.useBiCompass) {
        if (window.globalConfig.compassStyle === 1) {
          // 新版应用导航未做排序功能，以三件套中排序为准
          tmp = tmp.sort((a, b) => a.seq - b.seq)
          tmp.unshift(this.compassApp)
        } else {
          // 旧版功能罗盘有做排序功能，先根据罗盘中拖拽的顺序排序，再根据应用管理中的序号排序
          tmp = tmp.sort((a, b) => {
            if (a.compSeq && b.compSeq) return a.compSeq - b.compSeq
            else if (a.compSeq && !b.compSeq) return -1
            else if (!a.compSeq && b.compSeq) return 1
            else return a.seq - b.seq
          })
        }
      }
      return tmp
    },
    appTopList() {
      return this.appList.slice(0, this.topBtnNum)
    },
    appMoreList() {
      return this.appList.slice(this.topBtnNum)
    },
    topBtnWidthArr() {
      let fs = '12px'
      let mp = this.topBtnMarginPadding
      if (this.theme.name === 'blue-nssa') {
        fs = '14px'
        mp += 4
      }
      return this.appList.map(app => textSize(app.name, fs).width + mp)
    },
    isCompassApp() {
      return this.$route.query[QUERY_APPKEY] === COMPASS_KEY
    }
  },
  watch: {
    extraApp(v) {
      this.resize()
    },
    appMoreList(v) {
      this.getExtraApp(v)
    },
    isCompassApp: function() {
      this.getExtraApp(this.appMoreList)
    }
  },
  mounted() {
    this.resize()
    window.addEventListener('resize', this.resize)
  },
  destroyed() {
    window.removeEventListener('resize', this.resize)
  },
  methods: {
    resize() {
      this.topBtnNum = this.getTopBtnNum()
    },
    getTopBtnNum() {
      // 处理顶部菜单显示个数
      let num = 0
      let width = 0
      let tmpList = cloneDeep(this.topBtnWidthArr)

      // 将右侧下拉列表中的选中项，直接显示在顶部
      const extraW = this.extraApp
        ? textSize(this.extraApp.name).width + this.topBtnMarginPadding
        : 0

      const containerW = this.$refs.topBtns.clientWidth - 50 - extraW

      if (containerW <= 0) return 0

      while (width < containerW) {
        num++
        const addW = tmpList.shift()
        width += addW
        // console.log(addW, width, containerW)
      }

      return Math.max(0, num - 1)
    },
    handleCommandLeft(app) {
      this.extraApp = null
      this.onSelect(app)
    },
    handleCommandRight(app) {
      this.extraApp = app
      this.onSelect(app)
    },
    onSelect(app) {
      switchApp(app)
    },
    checkSelected(app) {
      if (this.isCompassApp) return app.key === COMPASS_KEY
      return app.key === this.apps.currentKey
    },
    getExtraApp(v) {
      if (
        v.find(app => app.key === this.apps.currentKey && !this.isCompassApp)
      ) {
        this.extraApp = this.currentApp
      } else {
        this.extraApp = null
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.el-dropdown-link {
  font-size: 12px;
}
// 加样式会造成小箭头不可见，所以只在必要时才加
.dropdown-max-height {
  max-height: 360px;
  overflow-x: hidden;
  overflow-y: auto;
  &::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
}
.app-select {
  display: inline-flex;
  align-items: center;
  white-space: nowrap;
  overflow: hidden;
}
.top-btns {
  display: inline-block;
  // flex: 1;
  // white-space: nowrap;
  // overflow: hidden;
}

.app-btn {
  display: inline-block;
  padding: 0 10px;
  line-height: inherit;
  cursor: pointer;
  &:hover {
    background: var(--pop-hover-bg);
    color: var(--pop-hover-txt);
  }
}

.selected {
  background: var(--pop-active-bg);
  color: var(--pop-active-txt);
  font-weight: 700;
}
</style>
