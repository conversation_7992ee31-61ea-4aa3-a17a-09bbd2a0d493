package com.eversec.antivpn.log.controller;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/8/17 16:01
 **/


import com.eversec.antivpn.emnus.ScreenTypeEnum;
import com.eversec.antivpn.log.aop.RedisCache;
import com.eversec.antivpn.log.api.dto.TypeCountDTO;
import com.eversec.antivpn.log.api.dto.UserLogPageDTO;
import com.eversec.antivpn.log.api.dto.base.CommonDateHistogramDTO;
import com.eversec.antivpn.log.mapper.vo.UserDateHistogramVO;
import com.eversec.antivpn.log.mapper.vo.TypeCountVO;
import com.eversec.antivpn.log.mapper.vo.UserLogPageVO;
import com.eversec.antivpn.log.schedule.MonitorDataReport;
import com.eversec.antivpn.log.service.UserOverallService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.hutool.core.bean.BeanUtil;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
@Tag(name = "用户态势")
@RestController
@RequestMapping("/log/useroverall")
public class UserOverallController {

    @Resource
    private UserOverallService userOverallService;

    @Resource
    private MonitorDataReport monitorDataReport;


    @RedisCache
    @Operation(summary = "用户态势-跨境通信用户数")
    @GetMapping("/vpnUserNum")
    public Long getVpnUserNum(ScreenTypeEnum screenTypeEnum) {
        Long result = userOverallService.getVpnUserNum(screenTypeEnum);
        return result;
    }

    @RedisCache
    @Operation(summary = "用户态势-有手机号码的用户")
    @GetMapping("/userHaveTelPhoneNum")
    public Long getUserHaveTelPhoneNum(ScreenTypeEnum screenTypeEnum) {
        Long result = userOverallService.getUserHaveTelPhoneNum(screenTypeEnum);
        return result;
    }

    @RedisCache
    @Operation(summary = "用户态势-访问者地区数量")
    @GetMapping("/userProvinceNum")
    public Integer getUserProvinceNum(ScreenTypeEnum screenTypeEnum) {
        Integer result = userOverallService.getUserLocationNum(screenTypeEnum);
        return result;
    }

    @RedisCache
    @Operation(summary = "用户态势-目的地地区数量")
    @GetMapping("/destProvinceNum")
    public Integer getDestProvinceNum(ScreenTypeEnum screenTypeEnum) {
        Integer result = userOverallService.getDestNum(screenTypeEnum);
        return result;
    }

    @RedisCache
    @Operation(summary = "用户态势-总跨境通信次数")
    @GetMapping("/crossBorderTotalNum")
    public Long getCrossBorderTotalNum(ScreenTypeEnum screenTypeEnum) {
        Long result = userOverallService.getCrossBorderTotalNum(screenTypeEnum);
        return result;
    }

    @RedisCache
    @Operation(summary = "用户态势-访问者所在地区分布TOP10")
    @GetMapping("/userProvinceTop10")
    public List<TypeCountDTO> getUserProvinceTop10(ScreenTypeEnum screenTypeEnum) {
        List<TypeCountDTO> result = dealTypeCountVOList(userOverallService.getUserLocationTop10(screenTypeEnum));
        return result;
    }

    @RedisCache
    @Operation(summary = "用户态势-访问者的目的地区分布 TOP10")
    @GetMapping("/userDestTop10")
    public List<TypeCountDTO> getUserDestTop10(ScreenTypeEnum screenTypeEnum) {
        List<TypeCountDTO> result =  dealTypeCountVOList(userOverallService.getUserDestTop10(screenTypeEnum));
        return result;
    }

    @RedisCache
    @Operation(summary = "用户态势-用户跨境通信时间分布图")
    @GetMapping("/userCrossBorderCommDateHistogram")
    public List<CommonDateHistogramDTO> getUserCrossBorderCommDateHistogram(ScreenTypeEnum screenTypeEnum) {
        List<CommonDateHistogramDTO> result = dealCommonDateHistogramVOList(userOverallService.getUserCrossBorderCommDateHistogram(screenTypeEnum));
        return result;
    }

    @RedisCache
    @Operation(summary = "用户态势-跨境通信用户量趋势图")
    @GetMapping("/userDateHistogram")
    public List<TypeCountDTO> getUserDateHistogram(ScreenTypeEnum screenTypeEnum) {
        List<TypeCountDTO> result = dealTypeCountVOList(userOverallService.getUserDateHistogram(screenTypeEnum));
        return result;
    }

    @RedisCache
    @Operation(summary = "用户态势-单用户跨境通信次数 TOP10")
    @GetMapping("/userCrossBorderCommTimesTop10")
    public List<TypeCountDTO> getUserCrossBorderCommTimesTop10(ScreenTypeEnum screenTypeEnum) {
        List<TypeCountVO> list = userOverallService.getUserCrossBorderCommTimesTop10(screenTypeEnum);
        List<TypeCountDTO> result = dealTypeCountVOList(list);
        return result;
    }

    @RedisCache
    @Operation(summary = "用户态势-单用户跨境通信使用资源节点总量 TOP10")
    @GetMapping("/userCrossBorderCommResourceNodeTop10")
    public List<TypeCountDTO> getUserCrossBorderCommResourceNodeTop10(ScreenTypeEnum screenTypeEnum) {
        List<TypeCountVO> list =  userOverallService.getUserCrossBorderCommResourceNodeTop10(screenTypeEnum);
        List<TypeCountDTO> result = dealTypeCountVOList(list);
        return result;
    }

    @RedisCache
    @Operation(summary = "用户态势-地图")
    @GetMapping("/map")
    public List<TypeCountDTO> getMap(ScreenTypeEnum screenTypeEnum) {
        List<TypeCountDTO> result = dealTypeCountVOList(userOverallService.getMap(screenTypeEnum));
        return result;
    }

    @RedisCache
    @Operation(summary = "用户态势-用户跨境访问日志")
    @GetMapping("/userCrossBorderLogPage")
    public List<UserLogPageDTO> userCrossBorderLogPage() {
        List<UserLogPageDTO> result =  new ArrayList<>();
        List<UserLogPageVO> dataList = userOverallService.getUserLogPageList();
        dataList.forEach(vo -> result.add(BeanUtil.toBean(vo,UserLogPageDTO.class)));
        return result;
    }

    @RedisCache
    @Operation(summary = "系统上报监控")
    @GetMapping("/systemReportMonitor")
    public Map<String,Boolean> systemReportMonitor() {
        return monitorDataReport.getSystemCodeTypeStatus();
    }



    private List<CommonDateHistogramDTO> dealCommonDateHistogramVOList(List<UserDateHistogramVO> voList) {
        List<CommonDateHistogramDTO> result = new ArrayList<>();
        if (voList == null || voList.size() == 0) return result;
        voList.forEach(vo -> result.add(BeanUtil.toBean(vo, CommonDateHistogramDTO.class)));
        return result;
    }

    private List<TypeCountDTO> dealTypeCountVOList(List<TypeCountVO> voList) {
        List<TypeCountDTO> result = new ArrayList<>();
        if (voList == null || voList.size() == 0) return result;
        voList.forEach(vo -> result.add(BeanUtil.toBean(vo, TypeCountDTO.class)));
        return result;
    }

}
