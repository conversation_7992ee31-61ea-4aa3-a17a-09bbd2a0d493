<template>
  <section class="title_box">
    <div class="box_tit">{{title}}</div>
    <div class="box_con">
      <slot></slot>
    </div>
  </section>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: ''
    }
  },
  data() {
    return {

    }
  }
}
</script>

<style scoped lang="scss">
.title_box {
  position: relative;
  .box_tit {
    color: #fff;
    font-size: 16px;
    padding: 0 39px;
    line-height: 38px;
    height: 37px;
    background: url(../overview/img/tit_bg.png) no-repeat;
  }
  .box_con {
    // height: 254px;
  }
}
</style>