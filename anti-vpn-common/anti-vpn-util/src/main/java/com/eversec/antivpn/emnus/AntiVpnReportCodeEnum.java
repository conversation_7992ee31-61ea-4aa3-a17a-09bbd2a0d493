package com.eversec.antivpn.emnus;

import lombok.Getter;

/**
 * 上报业务类型
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023-08-15 19:03
 */
@Getter
public enum AntiVpnReportCodeEnum {

	/*
		1	跨境通信监测日志
		2	跨境通信机器学习监测日志
		3	跨境通信情报库信息（企业上报）
		4	DNS解析日志
		5	机器学习监测模型及样本（企业自有）
		6	系统活动状态
	 */

	VPN_LOG(1, "跨境通信监测日志"),
	MACHINE_LEARNING_LOG(2, "跨境通信机器学习监测日志"),

	VPN_INTELLIGENCE(3, "跨境通信情报库信息（企业上报）", new String[] { "version", "comCode", "provinceId", "networkBusinessId", "typeId", "vpnName", "vpnDomain", "vpnIp", "vpnUrl", "vpnLink", "isUploadFile", "attachMent", "operationType", "timeStamp" }),

	DNS_LOG(4, "DNS解析日志"),

	VPN_MACHINE_LEARNING_MODULE(5, "机器学习监测模型及样本（企业自有）"),

	PROVINCE_PLATFORM_STATUS(6, "系统活动状态", new String[] { "version", "provinceId", "comCode", "networkBusinessId", "currentState", "timeStamp" }),


	;
	AntiVpnReportCodeEnum(int code, String desc) {
		this.code = code;
		this.desc = desc;
	}

	AntiVpnReportCodeEnum(int code, String desc, String[] header) {
		this.code = code;
		this.header = header;
		this.desc = desc;
	}

	/**
	 * 上报csv头
	 */
	private String[] header;

	private String desc;
	/**
	 * 上报类型代码
	 */
	private int code;
}
