eureka:
  client:
    register-with-eureka: false
    enabled: false

spring:
  datasource:
    dynamic:
      datasource:
        anti-vpn:
          url: jdbc:mysql://${DATABASE_HOST:mysql}:${DATABASE_PORT:3306}/${DATABASE_NAME:anti_vpn_test}?createDatabaseIfNotExist=true&useUnicode=true&characterEncoding=utf-8&useSSL=false&allowMutiQueries=true&serverTimezone=Asia/Shanghai
          username: ${DATABASE_USERNAME:root}
          password: ${DATABASE_PASSWORD:netDc@#su}
        system:
          url: jdbc:mysql://${DATABASE_HOST:mysql}:${DATABASE_PORT:3306}/${SYSTEM_DATABASE_NAME:system}?createDatabaseIfNotExist=true&useUnicode=true&characterEncoding=utf-8&useSSL=false&allowMutiQueries=true&serverTimezone=Asia/Shanghai
          username: ${DATABASE_USERNAME:root}
          password: ${DATABASE_PASSWORD:netDc@#su}
        clickhouse:
          url: jdbc:clickhouse://${CLICKHOUSE_DATABASE_HOST:clickhouse}:${CLICKHOUSE_DATABASE_PORT:8123}/${CLICKHOUSE_DATABASE_NAME:default}
          username: ${CLICKHOUSE_DATABASE_USERNAME:default}
          password: ${CLICKHOUSE_DATABASE_PASSWORD:}
  redis:
    host: ${REDIS_HOST:**************}
    port: ${REDIS_PORT:31748}
    password: ${REDIS_PASSWORD:netDc@#su}
    database: ${REDIS_DATABASE:6}
clickhouse:
  datasource:
    url: jdbc:clickhouse://${CLICKHOUSE_DATABASE_HOST:***************}:${CLICKHOUSE_DATABASE_PORT:8123}/${CLICKHOUSE_DATABASE_NAME:anti_vpn}
    username: ${CLICKHOUSE_DATABASE_USERNAME:}
    password: ${CLICKHOUSE_DATABASE_PASSWORD:}
    driver: ru.yandex.clickhouse.ClickHouseDriver

DATABASE_HOST: cloud.beijing.everdevcloud.com
DATABASE_PORT: 30640

CLICKHOUSE_DATABASE_HOST: ***************
CLICKHOUSE_DATABASE_PORT: 8123
CLICKHOUSE_DATABASE_NAME: anti_vpn
#开发环境
REDIS_HOST: **************
REDIS_PORT: 31748
REDIS_PASSWORD: netDc@#su

springdoc:
  swagger-ui:
    enabled: true

app:
  generic:
    url: http://security-tech-generic-vpn.anti-vpn.beijing.everdevcloud.com
  cacheEnabled : true
  cache:
    seconds: 10
  anti-vpn:
    aiModelAccuracy: 85
    deploy-place: SYSTEM
    yun-xin-an: true
    #开启VPN大屏省所属市日志查询及部署省份行政编码
    start-province-search:
      openStatus: true
server:
  port: 8081
