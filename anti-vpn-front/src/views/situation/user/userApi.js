import httpRequest from '@/utils/httpRequest.js';
const BaseServer = '/anti-vpn-service';

// 用户态势
export default {
  // num 1---跨境通信用户数
  vpnUserNum(o) {
    return httpRequest({
      url: BaseServer + `/log/useroverall/vpnUserNum`,
      method: "get",
      params: o,
    });
  },

  // num 2---有手机的用户
  userHaveTelPhoneNum(o) {
    return httpRequest({
      url: BaseServer + `/log/useroverall/userHaveTelPhoneNum`,
      method: "get",
      params: o,
    });
  },
  // num 3---访问者地区数量
  userProvinceNum(o) {
    return httpRequest({
      url: BaseServer + `/log/useroverall/userProvinceNum`,
      method: "get",
      params: o,
    });
  },
  // num 4---目的地地区数量
  destProvinceNum(o) {
    return httpRequest({
      url: BaseServer + `/log/useroverall/destProvinceNum`,
      method: "get",
      params: o,
    });
  },
  // num 5---总跨境通信次数
  crossBorderTotalNum(o) {
    return httpRequest({
      url: BaseServer + `/log/useroverall/crossBorderTotalNum`,
      method: "get",
      params: o,
    });
  },

  // left1 -- 访问者所在地区分布TOP10
  userProvinceTop10(o) {
    return httpRequest({
      url: BaseServer + `/log/useroverall/userProvinceTop10`,
      method: "get",
      params: o,
    });
  },
  // left2 ---访问者的目的地区分布 TOP10
  userDestTop10(o) {
    return httpRequest({
      url: BaseServer + `/log/useroverall/userDestTop10`,
      method: "get",
      params: o,
    });
  },
  // left3 ---用户跨境通信时间分布图
  userCrossBorderCommDateHistogram(o) {
    return httpRequest({
      url: BaseServer + `/log/useroverall/userCrossBorderCommDateHistogram`,
      method: "get",
      params: o,
    });
  },

  // mid -- 地图
  map(o) {
    return httpRequest({
      url: BaseServer + `/log/useroverall/map`,
      method: "get",
      params: o
    });
  },
  
  // mid2 -- 用户跨境访问日志
  userCrossBorderLogPage(o) {
    return httpRequest({
      url: BaseServer + `/log/useroverall/userCrossBorderLogPage`,
      method: "get",
      params: o,
    });
  },
  // rig3 -- 跨境通信用户量趋势图
  userDateHistogram(o) {
    return httpRequest({
      url: BaseServer + `/log/useroverall/userDateHistogram`,
      method: "get",
      params: o,
    });
  },


  // rig1 -- 单用户跨境通信次数 TOP10
  userCrossBorderCommTimesTop10(o) {
    return httpRequest({
      url: BaseServer + `/log/useroverall/userCrossBorderCommTimesTop10`,
      method: "get",
      params: o
    });
  },

  // rig2 -- 单用户跨境通信使用资源节点总量 TOP10
  userCrossBorderCommResourceNodeTop10(o) {
    return httpRequest({
      url: BaseServer + `/log/useroverall/userCrossBorderCommResourceNodeTop10`,
      method: "get",
      params: o
    });
  },
  
};
