<script lang="jsx">
import Empty from '@/components/empty';
// 获取元素的计算属性样式
// function getStyle(element, property) {
//   if(!element) return null;
//   let proValue = null;
//   if (element.currentStyle) {
//     proValue = element.currentStyle[property];
//   } else {
//     proValue = document.defaultView.getComputedStyle(element)[property];
//   }
//   return proValue;
// }

const gblen = function(str) {
  let len = 0;
  for (let i = 0; i < str.length; i++) {
    if (str.charCodeAt(i) > 127 || str.charCodeAt(i) == 94) {
      len += 2;
    } else {
      len++;
    }
  }
  return len;
};
export default {
  name: 'CustomTable',
  components: { Empty },
  render(h) {
    const { tableData, size, expandRowKeys, highlightCurrentRow, tooltipEffect, hasSelection, hasIndex, tableHeader, hasPagination, page, height, maxHeight, emptyType, emptyColor, emptyText , getIndex, hasRowKey} = this;
    let vol = {};
    if (hasRowKey) {
      vol = {
        props: {
          rowKey: this.getRowKeys,
          expandRowKeys: expandRowKeys
        },
      };
    }
    return (
      <div class="table-theme-globle">
        <el-table
          { ...vol }
          class="table-data"
          height={height}
          max-height={maxHeight}
          ref="table"
          data={tableData}
          size={size}
          highlight-current-row={highlightCurrentRow}
          tooltipEffect={tooltipEffect}
          onSelection-change={this.handleSelectionChange}
          onSort-change={this.sortChange}
          onCurrent-change={e => {
            this.$emit('current-change', e);
          }}
          onRow-click={(row, column, event) => {
            this.$emit('row-click', row, column, event);
          }}
          onExpand-change={(a,b) => {
            this.$emit('expand-change', a,b);
          }}
        >
          <transition slot="empty" name="fade">
            {<Empty type={emptyType} color={emptyColor} description={emptyText} class="empty"></Empty>}
          </transition>

          {this.$slots.start}

          {hasSelection ? <el-table-column align="center" type="selection" width="50"></el-table-column> : ''}
          {hasIndex ? <el-table-column align="center" type="index" width="50" label='序号' index={getIndex}></el-table-column> : ''}
          {tableHeader.map(el => (
            <el-table-column
              type={el.type || '—'}
              fixed={el.fixed}
              label={el.label}
              prop={el.key}
              align={el.align}
              key={el.key}
              min-width={el['minWidth'] || 'auto'}
              width={el['width']}
              formatter={el.formatter}
              show-overflow-tooltip={el.overflowTip}
              header-align={el.headerAlign || el.align || 'left'}
              sortable={el.sortable || false}
              scopedSlots={{
                header: ({ column, $index }) => <span class={'cell_' + (column.property || $index)}>{column.label}</span>,
                default: ({ row }) =>
                  el.customComponent ? (
                    <el.customComponent field={el.key} rowData={row} parent={this.$parent} />
                  ) : el.customRender ? (
                    el.customRender(row)
                  ) : (
                    <span class={['cell-value', 'cell_' + el.key]} style={typeof el.formatterStyle === 'function' ? el.formatterStyle(row) : {}}>
                      {typeof el.formatter === 'function' ? el.formatter(row) : row[el.key]}
                    </span>
                  )
              }}
            ></el-table-column>
          ))}
          {this.$slots.default}
        </el-table>
        {hasPagination ? (
          <el-pagination
            background
            class="txt_r mt10"
            current-page={page.paging}
            page-size={page.limit}
            total={page.total}
            page-sizes={page.pageSizes}
            layout="total, sizes, prev, pager, next, jumper"
            onSize-change={this.sizeChange}
            onCurrent-change={this.pageChange}
          ></el-pagination>
        ) : (
          ''
        )}
      </div>
    );
  },
  props: {
    height: {
      type: [String, Number]
    },
    maxHeight: {
      type: [String, Number]
    },
    tooltipEffect: {
      type: String,
      default: 'light'
    },
    tableHeader: {
      type: Array,
      default() {
        return [];
      }
    },
    tableData: {
      type: Array,
      default() {
        return [];
      }
    },
    hasSelection: {
      type: Boolean,
      default: false
    },
    hasIndex: {
      type: Boolean,
      default: false
    },
    hasPagination: {
      type: Boolean,
      default: true
    },
    // 分页参数
    page: {
      type: Object,
      default() {
        return {
          paging: 1,
          pageSizes: [10, 20, 50, 200],
          limit: 10,
          total: 0
        };
      }
    },
    highlightCurrentRow: {
      type: Boolean,
      default: false
    },
    emptyType: {
      type: [String, Number],
      default: 3
    },
    emptyColor: {
      type: String,
      default: 'rgba(0,0,0,0.25)'
    },
    emptyText: {
      type: String,
      default: '暂无数据'
    },
    size: {
      type: String,
      default: '—'
    },
    expandRowKeys: {
      type: [Array,String],
      default: () => []
    },
    // 是否有 row-key(唯一值)
    hasRowKey: {
      type: Boolean,
      default: false,
    },
    // row-key 的key，如id（唯一值）
    rowKeyAttr: {
      type: String,
      default: '',
    }
  },
  watch: {
    tableData(n, o) {
      this.$nextTick(() => {
        this.doLayout();
      });
    },
    tableHeader(n, o) {
      this.$nextTick(() => {
        this.doLayout();
      });
    }
  },
  methods: {
    getIndex(index) {
		   return (this.page.paging-1)*this.page.limit+1+index;
    },
    getRowKeys(row) {
      // return row.id;
      return row[this.rowKeyAttr];
   	},
    // 清除排序参数
    clearSort() {
      this.$refs.table && this.$refs.table.clearSort();
    },
    sortChange({ column, prop, order }) {
      // console.log("sortChange", {column, prop, order});
      this.$emit('sort-change', { column, prop, order });
    },
    async doLayout() {
      if (this.tableData.length === 0 || !this.$refs.table) return;
      let tableEl = this.$refs.table.$el;
      // let fontSize = parseInt(getStyle(tableEl.querySelector('.cell-value'), 'fontSize')) || 12;
      this.tableHeader.forEach(el => {
        // 如果设置了 minWidth 数值
        if (typeof el.minWidth === 'number') return;
        let cells = tableEl.querySelectorAll('.cell_' + el.key);
        let lengths = [];
        cells.forEach(cell => {
          lengths.push(gblen(cell.innerText));
        });
        if (lengths.length > 0) {
          // let maxLength = Math.max.apply(null, lengths);
          // el.minWidth = (maxLength * fontSize) / 2 + 40;
        }
      });
      await this.TableDolayout()
    },
    async TableDolayout() {
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      })
    },
    handleSelectionChange(val) {
      this.$emit('selection-change', val);
    },
    sizeChange(val) {
      this.$emit('update:page', Object.assign({}, this.page, { paging: 1, limit: val }));
      this.$emit('query');
    },
    pageChange(val) {
      console.log('pageChange', val)
      this.$emit('update:page', Object.assign({}, this.page, { paging: val }));
      this.$emit('query');
    }
  }
};
</script>

<style lang="scss">
.txt_r {
  text-align: right;
}
.table-theme-globle {
  box-sizing: border-box;
  width: 100%;
  padding: 0;
  a {
    color: var(--primary-bg);
    cursor: pointer;
  }
  .table-data {
    font-size: 14px;
    border-top: 1px solid var(--assist-border);
  }
  .el-table--border::after,
  .el-table--group::after {
    width: 0;
  }
  .el-table::before {
    height: 0;
  }
  .el-table .cell,
  .el-table th.el-table__cell > .cell {
    padding: 0 5px;
  }
  .el-table th.el-table__cell {
    background: var(--assest-head-bg);
    color: var(--assest-head-txt);
  }

  /* 滚动条 */
  ::-webkit-scrollbar {
    width: 10px;
    height: 10px;
  }
  /*滚动条里面小方块*/
  ::-webkit-scrollbar-thumb {
    /* background: rgba(150, 150, 150, 0.3); */
    /* box-shadow: inset 0 0 4px #ccc; */
    border-radius: 5px;
    background: rgba(187, 187, 187, 0.8);
    height: 6px;
  }
  ::-webkit-scrollbar-thumb:hover {
    background: rgba(187, 187, 187, 0.9);
  }
  /*滚动条里面轨道*/
  ::-webkit-scrollbar-track {
    background: #f7f8fc;
    border: 1px solid #e0dfea;
  }
  /* ::-webkit-scrollbar-track:hover {
		background: rgba(150, 150, 150, 0.2);
		box-shadow: inset 0 0 1px #ccc;
	} */
  ::-webkit-scrollbar-corner {
    background: transparent;
  }
}
</style>
