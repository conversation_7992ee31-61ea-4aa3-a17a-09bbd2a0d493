/** BI 请求数据封装 **/

import api_singleTable from '@/api/bi_query/singleTable';
import api_olap from '@/api/bi_query/olap';
import api_execQuery from '@/api/bi_query/execQuery';
import { cloneDeep } from 'lodash';
export class Condition {
  alias = '';
  constructor(vm = {}) {
    let { alias, values = [], filterExpression = 'eq' } = vm;
    this.alias = alias;
    if (!Array.isArray(values)) {
      values = [values];
    }
    if (filterExpression === 'like' || filterExpression === 'notLike') {
      values = values.map((v) => `%${v}%`);
    }
    let condition = {
      filterExpression,
      values,
    };
    this.filterExpressions = [condition];
  }
}
export class SingleTableCondition {
  fieldName = '';
  constructor(vm = {}) {
    let { fieldName, values = [], operator = 'eq' } = vm;
    this.fieldName = fieldName;
    if (!Array.isArray(values)) {
      values = [values];
    }
    if (operator === 'like' || operator === 'notLike') {
      values = values.map((v) => `%${v}%`);
    }
    let condition = {
      operator,
      values,
    };
    this.conditionExpressions = [condition];
  }
}

/** {
    "dsId": "c8cae0ae2ba54c20ab1d7e42190a10df",
    "tableName": "2eddd99185543c728ec3b01976b712a4",
    "conditions": [{
        "fieldName": "province_id",
        "conditionExpressions": [{
            "operator": "eq",
            "values": ["31"]
        }]
    }],
    "paging": 1,
    "limit": 10,
    "order": [{
        "fieldName": "province_id",
        "sort": "desc"
    }, {
        "fieldName": "province_name",
        "sort": "desc"
    }],
    "group": [{
        "fieldName": "province_id"
    }],
    "fields": [{
        "fieldName": "province_id"
    }, {
        "fieldName": "province_name",
        "function": "count",
        "aliasName": "总数"
    }],
    "tblType": "TABLE",
    "traceId": "1589445886511-2389"
}
单表查询参数格式如上
**/

export function apiSingleTable(
  param = {},
  isSeparation = false,
  isArea = false
) {
  // 统一增加权限过滤
  let province = '',
    city = '',
    county = '';
  const alias = JSON.parse(window.localStorage.getItem('userInfo')) || {
    org: {
      alias: '',
    },
  };
  const arr = alias.org.alias.split('/');
  province = arr.length > 1 ? arr[1] : null;
  city = arr.length > 2 ? arr[2] : null;
  county = arr.length > 3 ? arr[3] : null;
  let areaArr = [province, city, county];
  let arrObj = {
    0: 'province',
    1: 'city',
    2: 'county',
  };
  // let arrObj1 = {
  //   0: "省份",
  //   1: "城市",
  //   2: "区县",
  // };
  let baseFilters = [];
  areaArr.forEach((el, index) => {
    if (el) {
      // 由于可能权限过滤关键字是area,故同时加上area和county
      if (arrObj[index] == 'county' && isArea) {
        baseFilters.push({
          fieldName: 'area',
          conditionExpressions: [
            {
              operator: 'like',
              values: [`%${el}%`],
            },
          ],
        });
      } else if (arrObj[index] == 'county' && !isArea) {
        baseFilters.push({
          fieldName: 'county',
          conditionExpressions: [
            {
              operator: 'like',
              values: [`%${el}%`],
            },
          ],
        });
      } else {
        baseFilters.push({
          fieldName: arrObj[index],
          conditionExpressions: [
            {
              operator: 'like',
              values: [`%${el}%`],
            },
          ],
        });
      }
    }
  });
  return new Promise((resolve, reject) => {
    if (isSeparation) {
      param.conditions = param.conditions.concat(baseFilters);
    }
    api_singleTable
      .getList({
        ...param,
      })
      .then((data) => {
        resolve(data);
      })
      .catch((e) => {
        reject(e);
      });
  });
}
export function apiSingleTableExcel(param = {}) {
  return new Promise((resolve, reject) => {
    api_singleTable
      .exportExcel({
        ...param,
      })
      .then((data) => {
        resolve(data);
      })
      .catch((e) => {
        reject(e);
      });
  });
}
export function apiOlap(
  ChartId,
  filters = [],
  pageIndex = 1,
  pageSize = 10,
  flag = true
) {
  // 统一增加权限过滤
  let province = '',
    city = '',
    county = '';
  const alias = JSON.parse(window.localStorage.getItem('userInfo')) || {
    org: {
      alias: '',
    },
  };
  const arr = alias.org.alias.split('/');
  province = arr.length > 1 ? arr[1] : null;
  city = arr.length > 2 ? arr[2] : null;
  county = arr.length > 3 ? arr[3] : null;
  let areaArr = [province, city, county];
  let arrObj = {
    0: 'province',
    1: 'city',
    2: 'county',
  };
  let arrObj1 = {
    0: '省份',
    1: '城市',
    2: '区县',
  };
  let baseFilters = [];
  areaArr.forEach((el, index) => {
    if (el) {
      baseFilters.push({
        alias: flag ? arrObj[index] : '所属' + arrObj1[index],
        filterExpressions: [
          {
            filterExpression: 'eq',
            values: [el],
          },
        ],
      });
    }
  });
  return new Promise((resolve, reject) => {
    api_olap
      .getChartConfByChartId(ChartId)
      .then((chartConf) => {
        // console.log("图表配置", chartConf);
        chartConf.filters = [...baseFilters, ...filters];
        api_olap.getDataByChartConf(
          chartConf,
          {
            page: {
              pageSize: pageSize,
              pageIndex: pageIndex,
            },
          },
          (data) => {
            // console.log("图表数据", data);
            resolve(data);
          },
          () => {
            reject('图表数据获取失败!');
          }
        );
      })
      .catch((e) => {
        console.log(e);
        reject(e);
      });
  });
}

export function apiExecQuery(strSql) {
  return new Promise((resolve, reject) => {
    api_execQuery
      .execQuerySql({
        // strSql: `select * from xuwei_show`,
        strSql: strSql,
        paging: 1,
        limit: 10,
      })
      .then((data) => {
        resolve(data);
      })
      .catch((e) => {
        console.log(e);
        reject(e);
      });
  });
}
// bi图表管理接口
export function getChartIdData(id, callback, params = []) {
  api_olap
    .getChartConfByChartId(id)
    .then((chartConf) => {
      chartConf.filters = [];
      chartConf.table.chartId = id;
      if (params) {
        chartConf.filters.push(params);
      }
      api_olap.getDataByChartConf(chartConf, {}, (data) => {
        callback(data);
      });
    })
    .catch((e) => {
      console.log(e);
    });
}
// bi图表管理接口，由于callBack应用比较麻烦，所以改写下
export function getChartIdData1(
  id,
  params = [],
  isSeparation = false,
  isArea = false,
  pageIndex,
  pageSize,
  transDic = true// 是否翻译字典
) {
  // 统一增加权限过滤
  let province = '',
    city = '',
    county = '';
  // area = "";
  const alias = JSON.parse(window.localStorage.getItem('userInfo')) || {
    org: {
      alias: '',
    },
  };
  const arr = alias.org.alias.split('/');
  province = arr.length > 1 ? arr[1] : null;
  city = arr.length > 2 ? arr[2] : null;
  county = arr.length > 3 ? arr[3] : null;
  let arrObj = {
    0: 'province',
    1: 'city',
    2: 'county',
  };
  // let arrObj1 = {
  //   0: "省份",
  //   1: "城市",
  //   2: "区县",
  // };
  let baseFilters = [];
  let areaArr = [province, city, county];
  areaArr.forEach((el, index) => {
    if (el) {
      // 由于可能权限过滤关键字是area,故同时加上area和county
      if (arrObj[index] == 'county' && isArea) {
        baseFilters.push({
          alias: 'area',
          filterExpressions: [
            {
              filterExpression: 'like',
              values: [`%${el}%`],
            },
          ],
        });
      } else if (arrObj[index] == 'county' && !isArea) {
        baseFilters.push({
          alias: 'county',
          filterExpressions: [
            {
              filterExpression: 'like',
              values: [`%${el}%`],
            },
          ],
        });
      } else {
        baseFilters.push({
          alias: arrObj[index],
          filterExpressions: [
            {
              filterExpression: 'like',
              values: [`%${el}%`],
            },
          ],
        });
      }
    }
  });
  return new Promise((resolve, reject) => {
    api_olap
      .getChartConfByChartId(id)
      .then((chartConf) => {
        if (!chartConf.filter) chartConf.filters = [];
        if (isSeparation) {
          chartConf.filters = [...baseFilters];
        }
        params.forEach((vm) => {
          chartConf.filters.push(new Condition(vm));
        });
        if (id == '814d7f7986d443718de8c0da91940cf9') {
          // console.log(chartConf, '~~~~~~~~~~~~');
        }
        chartConf.table.chartId = id;
        let page = {};
        if (pageIndex) page.pageIndex = pageIndex;
        if (pageSize) page.pageSize = pageSize;
        api_olap.getDataByChartConf(chartConf, { page }, (data) => {
          resolve(data);
        }, error => { reject(error) }, transDic);
      })
      .catch((e) => {
        reject(e);
        console.log(e);
      });
  });
}

/**
 * 获取BI维度信息
 */
export function getBIconfig(
  id,
  params = [],
  isSeparation = false,
  isArea = false,
  pageIndex,
  pageSize,
) {
  // 统一增加权限过滤
  let province = '',
    city = '',
    county = '';
  // area = "";
  const alias = JSON.parse(window.localStorage.getItem('userInfo')) || {
    org: {
      alias: '',
    },
  };
  const arr = alias.org.alias.split('/');
  province = arr.length > 1 ? arr[1] : null;
  city = arr.length > 2 ? arr[2] : null;
  county = arr.length > 3 ? arr[3] : null;
  let arrObj = {
    0: 'province',
    1: 'city',
    2: 'county',
  };
  let baseFilters = [];
  let areaArr = [province, city, county];
  areaArr.forEach((el, index) => {
    if (el) {
      // 由于可能权限过滤关键字是area,故同时加上area和county
      if (arrObj[index] == 'county' && isArea) {
        baseFilters.push({
          alias: 'area',
          filterExpressions: [
            {
              filterExpression: 'like',
              values: [`%${el}%`],
            },
          ],
        });
      } else if (arrObj[index] == 'county' && !isArea) {
        baseFilters.push({
          alias: 'county',
          filterExpressions: [
            {
              filterExpression: 'like',
              values: [`%${el}%`],
            },
          ],
        });
      } else {
        baseFilters.push({
          alias: arrObj[index],
          filterExpressions: [
            {
              filterExpression: 'like',
              values: [`%${el}%`],
            },
          ],
        });
      }
    }
  });
  return new Promise((resolve) => {
    api_olap
      .getChartConfByChartId(id)
      .then((chartConf) => {
        if (!chartConf.filter) chartConf.filters = [];
        if (isSeparation) {
          chartConf.filters = [...baseFilters];
        }
        params.forEach((vm) => {
          chartConf.filters.push(new Condition(vm));
        });
        if (id == '814d7f7986d443718de8c0da91940cf9') {
          // console.log(chartConf, '~~~~~~~~~~~~');
        }
        chartConf.table.chartId = id;
        let page = {};
        if (pageIndex) page.pageIndex = pageIndex;
        if (pageSize) page.pageSize = pageSize;
        // console.log('chartConf', chartConf)
        resolve(chartConf)
        // api_olap.getDataByChartConf(chartConf, { page }, (data) => {
        //   resolve(data);
        // });
      })
      .catch((e) => {
        resolve(false);
        console.log(e);
      });
  });
}

export function extendDimensionBI(chartConf, extendParam = {}) {
  return new Promise((resolve) => {
    api_olap.getDataByChartConf(chartConf, extendParam, (data) => {
      resolve(data);
    })
  })
}
