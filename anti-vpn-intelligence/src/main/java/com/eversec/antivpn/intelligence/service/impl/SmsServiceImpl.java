package com.eversec.antivpn.intelligence.service.impl;

import cn.hutool.json.JSONUtil;
import com.dimpt.ability.sdk.AbilityApi;
import com.dimpt.ability.sdk.AbilityPost;
import com.dimpt.ability.sdk.exception.AbilityException;
import com.eversec.antivpn.config.AntiVpnProperties;
import com.eversec.antivpn.intelligence.api.dto.SmsRequestDTO;
import com.eversec.antivpn.intelligence.api.dto.SmsResponseDTO;
import com.eversec.antivpn.intelligence.service.ISmsService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 短信服务实现类
 * 
 * <AUTHOR>
 * @since 2025-01-12
 */
@Service
@Slf4j
@AllArgsConstructor
public class SmsServiceImpl implements ISmsService {

    private final AntiVpnProperties antiVpnProperties;

    @Override
    public SmsResponseDTO sendSms(SmsRequestDTO smsRequest) {
        AntiVpnProperties.UnicomCloudAbility unicomConfig = antiVpnProperties.getUnicomCloudAbility();
        
        // 检查是否启用短信功能
        if (!unicomConfig.getEnabled()) {
            log.warn("联通云网能力开放平台短信功能未启用");
            SmsResponseDTO response = new SmsResponseDTO();
            response.setRespCode("0");
            response.setRespDesc("短信功能未启用");
            return response;
        }

        // 验证配置参数
        if (unicomConfig.getClientId() == null || unicomConfig.getClientSecret() == null || unicomConfig.getGatewayUrl() == null) {
            log.error("联通云网能力开放平台配置参数不完整");
            SmsResponseDTO response = new SmsResponseDTO();
            response.setRespCode("0");
            response.setRespDesc("短信服务配置参数不完整");
            return response;
        }

        try {
            log.info("开始发送短信，接收号码：{}，内容：{}", smsRequest.getSerialNumber(), smsRequest.getMessage());
            
            // 使用SDK发送短信
            AbilityPost post = AbilityApi.post(
                    unicomConfig.getClientId(),
                    unicomConfig.getClientSecret(),
                    unicomConfig.getGatewayUrl()
            );
            
            // 设置API URL
            post.setApiUrl(unicomConfig.getSmsApiUrl());
            
            // 设置请求内容
            String requestBody = JSONUtil.toJsonStr(smsRequest);
            post.setContent(requestBody);
            
            log.info("发送短信请求：{}", requestBody);
            
            // 发送请求
            String result = post.send();
            
            log.info("短信发送响应：{}", result);
            
            // 解析响应
            SmsResponseDTO response = JSONUtil.toBean(result, SmsResponseDTO.class);
            
            if (response.isSuccess()) {
                log.info("短信发送成功，接收号码：{}", smsRequest.getSerialNumber());
            } else {
                log.warn("短信发送失败，响应码：{}，描述：{}", response.getRespCode(), response.getRespDesc());
            }
            
            return response;
            
        } catch (AbilityException e) {
            log.error("调用联通云网能力开放平台发送短信异常，错误码：{}，错误信息：{}", e.getCode(), e.getMessage(), e);
            SmsResponseDTO response = new SmsResponseDTO();
            response.setRespCode("0");
            response.setRespDesc("短信发送异常：" + e.getMessage());
            return response;
        } catch (Exception e) {
            log.error("发送短信异常", e);
            SmsResponseDTO response = new SmsResponseDTO();
            response.setRespCode("0");
            response.setRespDesc("短信发送异常：" + e.getMessage());
            return response;
        }
    }

}
