package com.eversec.antivpn.config.enums;

/**
 * 数据字典类型
 *
 * 代码中用到的字典枚举类型在此定义
 *
 *
 */
public enum AntiVpnDataDictTypeEnum {
    //
    AIRPORT_INFORMATION("知识库-机场信息"),
    SOFTWARE_INFORMATION("知识库-软件信息"),
    // enumKey：协议ID。enumKey2: 恒安协议编码.
    PROTOCOL_INFORMATION("知识库-协议信息"),
    SYSTEM_CODE("日志来源-所属系统"),

    APPLICATION_LAYER_PROTOCOL("应用层协议"),
    ISP_CODE("运营商"),
    NETWORK_TYPE("网络类型"),
    PROVINCE_CODE("省级代码"),
    INTELLIGENCE_COMMAND_OPERATION_TYPE("指令操作类型"),
    INTELLIGENCE_TYPE("情报类型"),
    INTELLIGENCE_CONTENT_TYPE("情报内容类型"),
    PROVINCE_PLATFORM_CURRENT_STATE("省平台当前状态"),


    // 系统配置

    IGNORE_TO_SMART_VPN_IDS("下发到smart忽略的vpnId,数组方式存储"),

    ;

    AntiVpnDataDictTypeEnum(String desc) {
        this.desc = desc;
    }

    private String desc;

}
