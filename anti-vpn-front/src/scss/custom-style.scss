
.hr {
  margin: 0.5rem 0;
  height: 1px;
  background: #eee;
}

.box {
  background-color: white;
  border-radius: 4px;
  //box-shadow: 3px 3px 3px #0000001a;
  border: 1px solid #ebeef5;
  display: block;
  @media screen and (min-width: 769px) {
    margin-bottom: 10px;
  }
}

.hollow-box {
  background-color: white;
  box-shadow: 1px 1px 2px #0000001a;
  border: 1px solid #ebeef5;
  border-radius: 5px;
}

.header {
  padding: 0 0.625rem;
  font-size: 1.25rem;
  font-weight: 700;
  line-height: 32px;
  overflow: hidden;
  height: auto !important;
  background: none !important;
}
.section {
  padding: 0.625rem;
}
// TODO 布局修饰
// 内容从左到右开始堆叠
.is-start-layout {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
}
// 内容从右到左开始堆叠
.is-end-layout {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
}
// 两边对其
.is-spacing-layout {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}

// TODO 屏幕大于769px
@media screen and (min-width: 769px) {
  // TODO 栅格
  .columns {
    display: flex;
    .column {
      width: 0;
      display: block;
      flex-basis: 0;
      flex-grow: 1;
      flex-shrink: 1;
    }
    @for $i from 1 through 11
    {
      .column.is-#{$i}
      {
        flex: none;
        width:(100%/12*$i);
      }
    }
    // 五分之一
    .column.is-one-fifth {
      flex: none;
      width: 20%;
    }
    // 十分之一
    .column.is-one-tenth {
      flex: none;
      width: 20%;
    }
  }
}
// TODO 屏幕小于768px
@media screen and (max-width: 768px) {
  // TODO 栅格
  .columns {
    .column {
      display: block;
      flex-basis: 0;
      flex-grow: 1;
      flex-shrink: 1;
    }
  }
  .columns.is-mobile {
    display: flex;
    .column {
      width: 0;
      display: block;
      flex-basis: 0;
      flex-grow: 1;
      flex-shrink: 1;
    }
    @for $i from 1 through 11
    {
      .column.is-#{$i}
      {
        flex: none;
        width:(100%/12*$i);
      }
    }
    // 五分之一
    .column.is-one-fifth {
      flex: none;
      width: 20%;
    }
    // 十分之一
    .column.is-one-tenth {
      flex: none;
      width: 20%;
    }
  }
}

@for $i from 1 through 12
{
  .has-#{$i}
  {
    float: left;
    width:(100%/12*$i);
  }
}

// overflow-hidden
.is-overflow-hidden {
  overflow: hidden !important;
}

// TODO font-weight
.is-text-weight-300
{
  font-weight: 300 !important;
}
.is-text-weight-700
{
  font-weight: 700 !important;
}

// TODO text-align

.is-text-center {
  text-align: center;
}

.is-text-left {
  text-align: left;
}

.is-text-right {
  text-align: right;
}

// TODO float
.is-float-left {
  float: left;
}
.is-float-right {
  float: right;
}

@for $i from 0 through 60
{
  // TODO margin
  .is-ma-#{$i}
  {
    margin: ($i + px) !important;
  }
  // TODO margin-top
  .is-mt-#{$i}
  {
    margin-top: ($i + px) !important;
  }
  // TODO margin-right
  .is-mr-#{$i}
  {
    margin-right: ($i + px) !important;
  }
  // TODO margin-bottom
  .is-mb-#{$i}
  {
    margin-bottom: ($i + px) !important;
  }
  // TODO margin-left
  .is-ml-#{$i}
  {
    margin-left: ($i + px) !important;
  }
  // TODO margin-top\bottom
  .is-my-#{$i}
  {
    margin-top: ($i + px) !important;
    margin-bottom: ($i + px) !important;
  }
  // TODO margin-left\right
  .is-mx-#{$i}
  {
    margin-left: ($i + px) !important;
    margin-right: ($i + px) !important;
  }
  // TODO padding
  .is-pa-#{$i}
  {
    padding: ($i + px) !important;
  }
  // TODO padding-top
  .is-pt-#{$i}
  {
    padding-top: ($i + px) !important;
  }
  // TODO padding-right
  .is-pr-#{$i}
  {
    padding-right: ($i + px) !important;
  }
  // TODO padding-bottom
  .is-pb-#{$i}
  {
    padding-bottom: ($i + px) !important;
  }
  // TODO padding-left
  .is-pl-#{$i}
  {
    padding-left: ($i + px) !important;
  }
  // TODO padding-top\bottom
  .is-py-#{$i}
  {
    padding-top: ($i + px) !important;
    padding-bottom: ($i + px) !important;
  }
  // TODO padding-left\right
  .is-px-#{$i}
  {
    padding-left: ($i + px) !important;
    padding-right: ($i + px) !important;
  }
  // TODO font-size
  .is-text-#{$i}
  {
    font-size: ($i + px) !important;
  }
  .is-text-indent-#{$i}
  {
    text-indent: ($i + px) !important;
  }
  .is-text-indentEm-#{$i}
  {
    text-indent: ($i + em) !important;
  }
}

// TODO dialog自适应大小
.dialog-handle {
  .el-dialog {
    //overflow: scroll;
    max-height: 80vh;

    .el-dialog__body {
      max-height: calc(80vh - 37px - 54px - 68px);
      overflow-y: auto;


      &::-webkit-scrollbar {
        /*滚动条整体样式*/
        width : 10px;  /*高宽分别对应横竖滚动条的尺寸*/
        height: 1px;
      }
      &::-webkit-scrollbar-thumb {
        /*滚动条里面小方块*/
        border-radius: 10px;
        box-shadow   : inset 0 0 5px rgba(0, 0, 0, 0.2);
        background   : #dcdcdc;
      }
      &::-webkit-scrollbar-track {
        /*滚动条里面轨道*/
        box-shadow   : inset 0 0 5px rgba(0, 0, 0, 0.2);
        border-radius: 10px;
        background   : #f8f8f8;
      }
    }

    @media screen and (max-width: 768px) {
      width: 95%;
    }
    @media screen and (min-width: 769px) {
      width: 90%;
    }
    @media screen and (min-width: 960px) {
      width: 80%;
    }
    @media screen and (min-width: 1200px) {
      width: 50%;
    }

    .el-dialog__body {
      padding: 0 20px;
    }
    .el-dialog__footer {
      padding: 10px 20px;
      overflow: hidden;
    }
    min-width: 840px;
  }
}

.is-scroll-x {
  overflow-x: auto;
}

.is-scroll-y {
  overflow-y: auto;
}
// TODO 滚动条样式
.is-scrollbar {
  &::-webkit-scrollbar {
    /*滚动条整体样式*/
    width : 10px;  /*高宽分别对应横竖滚动条的尺寸*/
    height: 1px;
  }
  &::-webkit-scrollbar-thumb {
    /*滚动条里面小方块*/
    border-radius: 10px;
    box-shadow   : inset 0 0 5px rgba(0, 0, 0, 0.2);
    background   : #dcdcdc;
  }
  &::-webkit-scrollbar-track {
    /*滚动条里面轨道*/
    box-shadow   : inset 0 0 5px rgba(0, 0, 0, 0.2);
    border-radius: 10px;
    background   : #f8f8f8;
  }
}
.is-scrollbar-none {
  &::-webkit-scrollbar {
    display: none;
  }
}

// TODO 自动换行
.is-text-wrap {
  text-align: justify;
  text-justify: newspaper;
  word-break: break-all;
}

// TODO 单行文本溢出显示省略号
.is-text-nowrap {
  overflow: hidden;
  text-overflow:ellipsis;
  white-space: nowrap;
}
// TODO 双行文本溢出显示省略号
.is-text-nowrap-2 {
  overflow: hidden;
  text-overflow:ellipsis;

  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  word-break: break-all;
}
// TODO 三行文本溢出显示省略号
.is-text-nowrap-3 {
  overflow: hidden;
  text-overflow:ellipsis;

  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  word-break: break-all;
}
// TODO 鼠标变小手
.is-cursor-pointer {
  cursor: pointer;
}
// TODO 鼠标常规样式
.is-cursor-default {
  cursor: default;
}
// TODO 鼠标变禁止
.is-cursor-noDrop {
  cursor: no-drop;
}

// TODO 分割标签
.is-split {
  position: relative;
  height: 20px;
  margin: 20px 0;
  .is-split-label {
    padding: 0 20px;
    position: absolute;
    left: 0;
    color: #7e7e7e;
    background: #fff;
    font-size: 16px;
    line-height: 20px;
    font-weight: 700;
  }
  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    border: solid 1px #eee;
    width: 100%;
    z-index: 0;
  }
}

// 颜色代码
$white-code: #fff;
$yellow-code: #ffff00;
$blue-code: var(--primary-bg);
$wathet-code: #cbe1fc;
$grey-code: #00000061;
$darkGrey-code: #eee;
$red-code: #F56C6C;
$buff-code: #f8eeea;
$orange-code: #f47822;
$green-code: #7be998;


// 字体颜色
.is-color-white {
  color: $white-code !important;
}
.is-color-blue {
  color: $blue-code !important;
}
.is-color-wathet {
  color: $wathet-code !important;
}
.is-color-grey {
  color: $grey-code !important;
}
.is-color-darkGrey {
  color: $darkGrey-code !important;
}
.is-color-red {
  color: $red-code !important;
}
.is-color-orange {
  color: $orange-code !important;
}
.is-color-yellow {
  color: $yellow-code;
}
.is-color-green {
  color: $green-code;
}

// 背景颜色
.is-box-white {
  background: $white-code !important;
}
.is-box-blue {
  background: $blue-code !important;
}
.is-box-wathet {
  background: $wathet-code !important;
}
.is-box-grey {
  background: $grey-code !important;
}
.is-box-darkGrey {
  background: $darkGrey-code !important;
}
.is-box-red {
  background: $red-code !important;
}
.is-box-orange {
  background: $orange-code !important;
}
.is-box-yellow {
  background: $yellow-code;
}
.is-box-green {
  background: $green-code;
}

// 边线
.is-bw-1 {
  border-width: 1px;
  border-style: solid;
}
.is-bw-2 {
  border-width: 2px;
  border-style: solid;
}
.is-bt-1 {
  border-top-width: 1px;
  border-top-style: solid;
}
.is-bt-2 {
  border-top-width: 2px;
  border-top-style: solid;
}
.is-bl-1 {
  border-left-width: 1px;
  border-left-style: solid;
}
.is-bl-2 {
  border-left-width: 2px;
  border-left-style: solid;
}
.is-br-1 {
  border-right-width: 1px;
  border-right-style: solid;
}
.is-br-2 {
  border-right-width: 2px;
  border-right-style: solid;
}
.is-bb-1 {
  border-bottom-width: 1px;
  border-bottom-style: solid;
}
.is-bb-2 {
  border-bottom-width: 2px;
  border-bottom-style: solid;
}



// 边线颜色
.is-b-white {
  border-color: $white-code !important;
}
.is-b-blue {
  border-color: $blue-code !important;
}
.is-b-wathet {
  border-color: $wathet-code !important;
}
.is-b-grey {
  border-color: $grey-code !important;
}
.is-b-darkGrey {
  border-color: $darkGrey-code !important;
}
.is-b-red {
  border-color: $red-code !important;
}
.is-b-orange {
  border-color: $orange-code !important;
}
.is-b-green {
  border-color: $green-code !important;
}
