package com.eversec.antivpn.log.aop;

import org.springframework.context.ApplicationContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.annotation.Annotation;
import java.lang.reflect.Method;
import java.util.Map;

/**
 * @Description 缓存预热，利用空闲资源，缓存大屏数据。实现大屏毫秒级别的展示
 * <AUTHOR>
 * @Date 2023/10/30 15:45
 **/

//@Component
public class RedisCachePreHeatProcessor {

    //@Resource
    private ApplicationContext applicationContext;

    public void processBeansWithAnnotation() {
        Map<String, Object> beansWithAnnotation = applicationContext.getBeansWithAnnotation(CachePreHeat.class);
        for (Object bean : beansWithAnnotation.values()) {
            Method[] methods = bean.getClass().getDeclaredMethods();
            Annotation[] annotations = methods[1].getDeclaredAnnotations();
            for(Annotation annotation: annotations){
                if(annotation instanceof RedisCache){

                }
            }
        }
    }

}
