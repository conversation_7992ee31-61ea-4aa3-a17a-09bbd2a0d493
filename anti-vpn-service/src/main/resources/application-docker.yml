spring:
  datasource:
    hikari:
      maximum-pool-size: 20
    dynamic:
      datasource:
        anti-vpn:
          url: jdbc:mysql://${DATABASE_HOST:mysql.kube-public}:${DATABASE_PORT:3306}/${DATABASE_NAME:anti_vpn}?createDatabaseIfNotExist=true&useUnicode=true&characterEncoding=utf-8&useSSL=false&allowMutiQueries=true&serverTimezone=Asia/Shanghai
          username: ${DATABASE_USERNAME:root}
          password: ${DATABASE_PASSWORD:r9xh*yH*DcEFQE}
        system:
          url: jdbc:mysql://${DATABASE_HOST:mysql.kube-public}:${DATABASE_PORT:3306}/${SYSTEM_DATABASE_NAME:system}?createDatabaseIfNotExist=true&useUnicode=true&characterEncoding=utf-8&useSSL=false&allowMutiQueries=true&serverTimezone=Asia/Shanghai
          username: ${DATABASE_USERNAME:root}
          password: ${DATABASE_PASSWORD:r9xh*yH*DcEFQE}
        clickhouse:
          driver-class-name: ru.yandex.clickhouse.ClickHouseDriver
          url: jdbc:clickhouse://${CLICKHOUSE_DATABASE_HOST:clickhouse-1.kube-public}:${CLICKHOUSE_DATABASE_PORT:8123}/${CLICKHOUSE_DATABASE_NAME:anti_vpn}
          username: ${CLICKHOUSE_DATABASE_USERNAME:default}
          password: ${CLICKHOUSE_DATABASE_PASSWORD:}
  redis:
    host: ${REDIS_HOST:redis.kube-public}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:netDc@#100}
    database: ${REDIS_DATABASE:6}
app:
  cacheEnabled : true
  cache:
    seconds: 600
  anti-vpn:
    # 部署地：集团：group;省-对接多个业务平台：province。省-和业务平台部署在一起：system.
    deploy-place: system
    aiModelAccuracy: 85
    #开启VPN大屏省所属市日志查询及部署省份行政编码
    start-province-search:
      openStatus: false
    reportDataDir: /app/data/south_receive_dir
    fileDataStorageDir: /app/data/storage
    # 联通云网能力开放平台配置
    unicom-cloud-ability:
      enabled: ${UNICOM_SMS_ENABLED:false}
      client-id: ${UNICOM_CLIENT_ID:}
      client-secret: ${UNICOM_CLIENT_SECRET:}
      gateway-url: ${UNICOM_GATEWAY_URL:http://*************:8180/api/v1}  # 生产环境网关地址
clickhouse:
  datasource:
    url: jdbc:clickhouse://${CLICKHOUSE_DATABASE_HOST:clickhouse-1.kube-public}:${CLICKHOUSE_DATABASE_PORT:8123}/${CLICKHOUSE_DATABASE_NAME:anti_vpn}
    username: ${CLICKHOUSE_DATABASE_USERNAME:default}
    password: ${CLICKHOUSE_DATABASE_PASSWORD:}
    driver: ru.yandex.clickhouse.ClickHouseDriver


springdoc:
  swagger-ui:
    enabled: false
swagger:
  enabled: false

springfox:
  documentation:
    enabled: false
    auto-startup: false
    swagger-ui:
      enabled: false