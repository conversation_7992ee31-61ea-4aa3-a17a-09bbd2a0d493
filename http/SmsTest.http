### 发送短信测试
POST {{base}}/intelligence/vpnIntelligence/sendSms
Content-Type: application/json
userid: 1
username: admin

{
  "serialNumber": ["8618612345678"],
  "message": "您的验证码是1234，在5分钟内有效。如非本人操作请忽略本短信。"
}

### 发送短信测试 - 多个号码
POST {{base}}/intelligence/vpnIntelligence/sendSms
Content-Type: application/json
userid: 1
username: admin

{
  "serialNumber": ["8618612345678", "8618687654321"],
  "message": "这是一条测试短信，请忽略。"
}

### 发送短信测试 - 参数验证错误（空号码）
POST {{base}}/intelligence/vpnIntelligence/sendSms
Content-Type: application/json
userid: 1
username: admin

{
  "serialNumber": [],
  "message": "测试短信内容"
}

### 发送短信测试 - 参数验证错误（空内容）
POST {{base}}/intelligence/vpnIntelligence/sendSms
Content-Type: application/json
userid: 1
username: admin

{
  "serialNumber": ["8618612345678"],
  "message": ""
}
