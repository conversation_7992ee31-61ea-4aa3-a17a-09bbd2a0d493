<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eversec.antivpn.support.province.mapper.ProvincePlatformStatusExtMapper">

    <select id="lisNewestProvincePlatformStatus" resultType="com.eversec.antivpn.support.province.entity.ProvincePlatformStatus">
        SELECT t.*
        FROM province_platform_status t
                 INNER JOIN (
            SELECT  province_id,com_code,system_code, MAX(create_datetime) AS max_create_datetime
            FROM province_platform_status
            GROUP BY province_id,com_code,system_code
        ) max_times ON t.province_id = max_times.province_id
            AND t.com_code = max_times.com_code
            AND t.system_code = max_times.system_code
            AND t.create_datetime = max_times.max_create_datetime
    </select>
</mapper>