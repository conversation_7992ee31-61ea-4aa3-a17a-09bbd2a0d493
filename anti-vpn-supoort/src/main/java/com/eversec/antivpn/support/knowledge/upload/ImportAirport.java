package com.eversec.antivpn.support.knowledge.upload;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

/**
 * 机场信息导入类
 * <AUTHOR>
 */
@Getter
@Setter
public class ImportAirport {

    public static final String[] MUST_FILL = new String[] { "序号", "VPN服务商ID", "VPN服务商名称", "官网", "注册地", "简介", "价格", "订阅渠道", "付款方式", "发现时间" };

    @Excel(name = "序号")
    public Integer index;
    @Excel(name = "VPN服务商ID")
    private String enumKey;
    @Excel(name = "VPN服务商名称")
    public String enumVal;
    @Excel(name = "官网")
    private String officialWebsite;

    @Excel(name = "注册地")
    private String registered;

    @Excel(name = "简介")
    private String introduction;

    @Excel(name = "价格")
    private String price;

    @Excel(name = "订阅渠道")
    private String subscriptionChannel;

    @Excel(name = "付款方式")
    private String paymentMethod;

    @Excel(name = "发现时间")
    private String discoveryTime;
//    @Excel(name = "入库时间")
//    private String insert_time;
}
