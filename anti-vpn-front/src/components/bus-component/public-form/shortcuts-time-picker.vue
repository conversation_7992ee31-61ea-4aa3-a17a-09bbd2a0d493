<template>
  <article class="shortcuts-time-picker">
    <el-radio-group v-model="dateType" size="mini" @change="changeTime">
      <el-radio-button
        v-for="(item, key) in timePickerOptions"
        :key="key"
        :label="item.label"
      />
    </el-radio-group>
    <el-date-picker
      v-model="date"
      class="is-ml-10"
      size="mini"
      :disabled="dateType !== '自定义'"
      type="datetimerange"
      :value-format="valueFormat"
      :range-separator="placeholder.range || '至'"
      :start-placeholder="placeholder.start || '开始日期'"
      :end-placeholder="placeholder.end || '结束日期'"
      :default-time="['00:00:00', '23:59:59']"
    />
  </article>
</template>

<script>
import {
  formatTime,
  designatedDate,
  getLastMonth,
  getLast3Month
} from '@/utils/time'
export default {
  model: {
    prop: 'dicValue',
    event: 'change'
  },
  props: {
    // 双绑数据
    dicValue: {
      type: Array,
      default: () => []
    },
    valueFormat: {
      type: String,
      default: () => 'yyyy-MM-dd HH:mm:ss'
    },
    // 提示信息
    placeholder: {
      type: Object,
      default: () => ({
        range: '',
        start: '',
        end: ''
      })
    },
    // 需要过滤的值
    filterRange: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      dateType: null, // 选中的时间类型
      date: null, // 时间反显值
      initState: true // 是否是初始化状态
    }
  },
  computed: {
    // 选择时间范围初始化
    timePickerOptions() {
      let timeList = [
        {
          label: '全部',
          start: '',
          end: ''
        },
        {
          label: '今日',
          start: formatTime(
            new Date(new Date(new Date().toLocaleDateString()).getTime())
          ),
          end: formatTime(
            new Date(
              new Date(new Date().toLocaleDateString()).getTime() +
                24 * 60 * 60 * 1000 -
                1
            )
          )
        },
        {
          label: '7日',
          start: designatedDate(new Date(), -6),
          end: formatTime(
            new Date(
              new Date(new Date().toLocaleDateString()).getTime() +
                24 * 60 * 60 * 1000 -
                1
            )
          )
        },
        {
          label: '15日',
          start: designatedDate(new Date(), -14),
          end: formatTime(
            new Date(
              new Date(new Date().toLocaleDateString()).getTime() +
                24 * 60 * 60 * 1000 -
                1
            )
          )
        },
        {
          label: '一个月',
          start: getLastMonth().last + ' 00:00:00',
          end: formatTime(
            new Date(
              new Date(new Date().toLocaleDateString()).getTime() +
                24 * 60 * 60 * 1000 -
                1
            )
          )
        },
        {
          label: '三个月',
          start: getLast3Month().last + ' 00:00:00',
          end: formatTime(
            new Date(
              new Date(new Date().toLocaleDateString()).getTime() +
                24 * 60 * 60 * 1000 -
                1
            )
          )
        },
        {
          label: '自定义',
          type: 'custom'
        }
      ]
      let tempTime = []
      if (this.filterRange && this.filterRange.length > 0) {
        tempTime = timeList.filter(e => !this.filterRange.includes(e.label))
      } else {
        tempTime = timeList
      }
      return tempTime
    }
  },
  watch: {
    date(newTime, oldTime) {
      if (oldTime !== null && newTime && newTime.join() !== oldTime.join()) {
        this.$emit('change', [newTime[0], newTime[1]])
      }
    },
    dicValue(e) {
      if (this.initState) {
        this.initState = false
        if (this.dicValue && this.dicValue.filter(e => e !== '').length > 1) {
          this.date = e
          e.map(v => {
            if (v !== '') {
              this.dateType = '自定义'
            }
          })
        }
      }
    }
  },
  mounted() {
    this.dateType = this.timePickerOptions[0].label
    let timePickerOptions = this.timePickerOptions.filter(
      e => e.label === this.dateType
    )[0]
    this.date = [timePickerOptions.start, timePickerOptions.end]
  },
  methods: {
    // 选择时间范围，改变时间
    changeTime(e) {
      let tempDate = this.timePickerOptions.filter(v => v.label === e)[0]
      if (e !== '自定义') this.date = [tempDate.start, tempDate.end]
    }
  }
}
</script>
<style lang="scss">
.shortcuts-time-picker {
  .el-date-editor--datetimerange {
    width: 346px !important;
    padding: 3px 0 3px 10px;
    .el-range-separator {
      padding: 0 !important;
    }
  }
}
</style>
