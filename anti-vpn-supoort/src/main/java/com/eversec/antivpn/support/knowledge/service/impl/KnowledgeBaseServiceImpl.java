package com.eversec.antivpn.support.knowledge.service.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.codec.Base64Encoder;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import com.eversec.antivpn.config.enums.AntiVpnDataDictTypeEnum;
import com.eversec.antivpn.support.knowledge.api.dto.ImportDownloadRequest;
import com.eversec.antivpn.support.knowledge.api.dto.ImportResult;
import com.eversec.antivpn.support.knowledge.api.dto.KnowledgeAirportDTO;
import com.eversec.antivpn.support.knowledge.api.dto.KnowledgeBaseDTO;
import com.eversec.antivpn.support.knowledge.api.dto.KnowledgeBaseQueryDto;
import com.eversec.antivpn.support.knowledge.api.dto.KnowledgeProtocolDTO;
import com.eversec.antivpn.support.knowledge.api.dto.KnowledgeSoftwareDTO;
import com.eversec.antivpn.support.knowledge.convertor.KnowledgeDtoConvertor;
import com.eversec.antivpn.support.knowledge.service.IKnowledgeBaseService;
import com.eversec.cloud.core.api.data.PageResult;
import com.eversec.cloud.core.api.exception.ClientException;
import com.eversec.framework.core.exception.BusinessException;
import com.eversec.stark.generic.common.util.PageBuilder;
import com.eversec.stark.generic.sys.api.SysDataDictApi;
import com.eversec.stark.generic.sys.dto.SysDataDictDto;
import com.eversec.stark.generic.sys.dto.SysDataDictQueryDto;
import java.util.Iterator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * <p>
 * 省平台信息（企业测） 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-12
 */
@Service
@Slf4j
public class KnowledgeBaseServiceImpl implements IKnowledgeBaseService {

    @Autowired
    private  SysDataDictApi sysDataDictApi;

    @Autowired
    private KnowledgeDtoConvertor knowledgeDtoConvertor;

    @Override
    public void save(KnowledgeBaseDTO baseDTO) {
        SysDataDictDto dict = new SysDataDictDto();
        BeanUtil.copyProperties(baseDTO,dict);
        try{
            sysDataDictApi.saveDict(dict);
        }catch (BusinessException e){

            throw new ClientException(e.getMessage());
        }
    }

    @Override
    public void updateById(KnowledgeBaseDTO baseDTO) {
        SysDataDictDto dict = new SysDataDictDto();
        BeanUtil.copyProperties(baseDTO,dict);
        try{
            sysDataDictApi.updateDictById(dict);
        }catch (BusinessException e){

            throw new ClientException(e.getMessage());
        }
    }

    @Override
    public PageResult<KnowledgeBaseDTO> page(KnowledgeBaseQueryDto queryDto) {

        SysDataDictQueryDto queryDictDto = new SysDataDictQueryDto();
        BeanUtil.copyProperties(queryDto,queryDictDto);
        PageResult<SysDataDictDto> dictPage;
        try{
            dictPage = sysDataDictApi.page2(queryDictDto);
        }catch (BusinessException e){

            throw new ClientException(e.getMessage());
        }
        return PageBuilder.convertPageResult(dictPage, knowledgeDtoConvertor::toBaseDTO);
    }

    @Override
    public void deleteByIds(List<Long> ids) {

        try{
            sysDataDictApi.deleteDictByIds(ids);
        }catch (BusinessException e){

            throw new ClientException(e.getMessage());
        }
    }


    public ImportResult importTemplate(Long userId, String userName, MultipartFile file, Class cls, AntiVpnDataDictTypeEnum information,String[] importFields) {
        AtomicBoolean exists = new AtomicBoolean(false);
        List<Object> imports = null;
        log.info("解析excel文件");
        try {
            imports = ExcelImportUtil.importExcel(file.getInputStream(), cls, new ImportParams() {{
                this.setHeadRows(1);
                // 设置验证支持
                this.setNeedVerfiy(true);
                this.setImportFields(importFields);

            }});
        } catch (Exception e) {
            log.error("解析文件异常", e);
            throw new ClientException("解析导入文件错误");
        }
        if (CollectionUtils.isEmpty(imports)) {
            return new ImportResult();
        }
        //查询字典服务
        List<SysDataDictDto> sysDataDictDtos = sysDataDictApi.listByType(Arrays.asList(information.name()));
        Map<String,Object> dictValMap = CollectionUtil.isNotEmpty(sysDataDictDtos)?sysDataDictDtos.stream().collect(Collectors.toMap(SysDataDictDto::getEnumVal,a->a, (k1,k2) ->k1)) :new HashMap<>();
        Map<String,Object> dictKeyMap = CollectionUtil.isNotEmpty(sysDataDictDtos)?sysDataDictDtos.stream().collect(Collectors.toMap(SysDataDictDto::getEnumKey,a->a, (k1,k2) ->k1)) :new HashMap<>();
        List<Object> importBases = new ArrayList<>();
        List<Object> importErrorBases = new ArrayList<>();
        Map<String, String> importErrors = new HashMap<>();
        //排除已经添加过的
        List<Object> finalImports = imports.stream().collect(Collectors.toList());

        Iterator<Object> iterator = imports.iterator();
        while (iterator.hasNext()) {
            Object obj = iterator.next();
            String enumVal = getEnumVal(cls, obj,"enumVal");
            String enumKey = getEnumVal(cls, obj,"enumKey");
            if(ObjectUtil.isNotEmpty(dictValMap.get(enumVal))|| ObjectUtil.isNotEmpty(dictKeyMap.get(enumKey))){
                importErrors.put(enumKey+" : "+enumVal,"数据已存在");
                exists.set(true);
                importErrorBases.add(obj);
                finalImports.remove(obj);
            };
        }


        for (Object airport : finalImports) {
            importBases.add(airport);
            try {
                this.save(getKnowledgeBaseDTO(information,userName,airport));
            }catch (Exception e){
                log.error("导入文件异常", e);
                exists.set(true);
                String enumVal = getEnumVal(cls, airport,"enumVal");
                String enumKey = getEnumVal(cls, airport,"enumKey");
                enumKey = StringUtils.isNotBlank(enumKey)?enumKey:"id为空";
                enumVal = StringUtils.isNotBlank(enumVal)?enumVal:"名称为空";
                importErrors.put(enumKey+" : "+enumVal,"数据异常");
                importErrorBases.add(airport);
                importBases.removeIf( obj -> obj.equals(airport));
            }

        }
        log.info("解析excel文件完成");
        return  new ImportResult(exists.get(),importErrors,importBases,importErrorBases);
    }


    @Override
    public Object importDownload(ImportDownloadRequest req, Class cls) {
        if(CollectionUtils.isEmpty(req.getImportErrorBases())){
            throw new ClientException("下载数据不能为空");
        }
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder
            .currentRequestAttributes();
        HttpServletResponse response = servletRequestAttributes.getResponse();
        try {
            List<Object> result = new ArrayList<>();
            req.getImportErrorBases().stream().forEach(u -> {

                try {
                    Object  importTeamPlayer = cls.newInstance();
                    BeanUtil.copyProperties(u,importTeamPlayer);
                    result.add(importTeamPlayer);
                } catch (Exception e) {
                    log.error("创建导出类异常", e.getMessage());
                }
            });
            // 告诉浏览器用什么软件可以打开此文件
            response.setHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_OCTET_STREAM_VALUE);
            // 下载文件的默认名称
            response.setHeader(HttpHeaders.CONTENT_DISPOSITION,
                "attachment;filename=" + URLEncoder.encode("导入错误数据", "UTF-8") + ".xls");
            // 编码
            response.setCharacterEncoding(StandardCharsets.UTF_8.name());
            ExportParams exportParams = new ExportParams();
            exportParams.setTitleHeight((short) 15);
            Workbook workbook = ExcelExportUtil.exportExcel(exportParams, cls,
                result);
            workbook.write(response.getOutputStream());
            return null;
        } catch (Exception e) {
            throw new ClientException("导出excel文件出错啦，请稍后再试");
        }
    }

    /**
     * 反射获取字段值
     * @param cls 对象类
     * @param obj 提取对象
     * @return EnumVal值
     */
    private String getEnumVal(Class cls, Object obj,String reflectField ) {
        String enumVal = "";
        try {
            Field field  = cls.getDeclaredField(reflectField);
            field.setAccessible(true);
            enumVal = (String)field.get(obj);
        } catch (Exception e) {
            log.error("解析文件获取enumVal 出错啦", e);
        }
        return enumVal;
    }




    /**
     * 根据类型获取KnowledgeBaseDTO
     * @param knowledgeEnum 知识库枚举
     * @param userName 用户名
     * @param importObj 导入对象
     * @return knowledgeBaseDTO
     */
    private KnowledgeBaseDTO getKnowledgeBaseDTO(AntiVpnDataDictTypeEnum knowledgeEnum,String userName,Object importObj) {
        KnowledgeBaseDTO knowledgeBaseDTO = null;
        switch (knowledgeEnum){
            case AIRPORT_INFORMATION:
                KnowledgeAirportDTO dto = new KnowledgeAirportDTO();
                dto.setCreateUser(userName);
                dto.setUpdateUser(userName);
                BeanUtil.copyProperties(importObj,dto);
                knowledgeBaseDTO = knowledgeDtoConvertor.airporToDomain(dto);
                break;
            case SOFTWARE_INFORMATION:
                KnowledgeSoftwareDTO software = new KnowledgeSoftwareDTO();
                software.setCreateUser(userName);
                software.setUpdateUser(userName);
                //图片转Base64
                BeanUtil.copyProperties(importObj,software);
                String logoPath = software.getLogo();
                String base64A = "data:image/png;base64," + Base64Encoder.encode(FileUtil.readBytes(logoPath));
                software.setLogo(base64A);
                FileUtil.del(logoPath);
                knowledgeBaseDTO = knowledgeDtoConvertor.softwareToDomain(software);
                break;
            case PROTOCOL_INFORMATION:
                KnowledgeProtocolDTO protocol = new KnowledgeProtocolDTO();
                protocol.setCreateUser(userName);
                protocol.setUpdateUser(userName);
                BeanUtil.copyProperties(importObj,protocol);
                knowledgeBaseDTO = knowledgeDtoConvertor.protocolToDomain(protocol);
                break;
            default:
                break;
        }
        return knowledgeBaseDTO;
    }


}
