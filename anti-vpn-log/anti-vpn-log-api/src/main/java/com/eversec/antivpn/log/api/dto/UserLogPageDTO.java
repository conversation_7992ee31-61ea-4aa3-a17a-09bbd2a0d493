package com.eversec.antivpn.log.api.dto;


import lombok.Data;

import java.io.Serializable;
import java.util.Date;


/**
 * @Description
 * <AUTHOR>
 * @Date 2023/8/23 16:41
 **/
@Data
public class UserLogPageDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    private String srcIp;
    private String vpnAirportCode;
    private String vpnSoftwareName;
    private String resourceIp;
    private Integer provinceId;
    private String destCountry;
    private Date accessDateTime;
    private String vpnSoftwareProtocol;
    private String vpnAirportName;
}
