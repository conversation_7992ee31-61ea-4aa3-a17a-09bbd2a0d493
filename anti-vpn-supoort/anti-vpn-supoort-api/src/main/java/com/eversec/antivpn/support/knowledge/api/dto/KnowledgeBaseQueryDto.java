package com.eversec.antivpn.support.knowledge.api.dto;

import cn.hutool.db.Page;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <p>
 * 知识库查询 DTO
 * </p>
 * <AUTHOR>
 * @since 2023-07-12
 */
@Schema(name = "KnowledgeBaseQueryDto", description = "知识库查询")
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper=true)
public class KnowledgeBaseQueryDto extends Page {

    private static final long serialVersionUID = 1L;

    @Parameter(description = "ID")
    private Long id;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Parameter(description = "创建-开始时间(格式：yyyy-MM-dd HH:mm:ss)")
    private Date createStartDatetime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Parameter(description = "创建-结束时间(格式：yyyy-MM-dd HH:mm:ss)")
    private Date createEndDatetime;

    @Parameter(description = "字典编码")
    private String enumKey;

    @Parameter(description = "字典编码2")
    private String enumKey2;

    @Parameter(description = "字典名称")
    private String enumVal;

    @Parameter(description = "字典类型")
    private String typeKey;

    @Parameter(description = "上级字典编码")
    private String parentEnumKey;

    @Parameter(description = "有效标识")
    private Boolean valid;

    @Parameter(description = "字典类型-列表")
    private List<String> typeKeyList;

    @Parameter(description = "条件-上级字典编码为空")
    private boolean conditionParentEnumKeyNull;

    @Parameter(description = "条件-字典拓展Json字段")
    private Map<String,Object> enumValExtend;

}