<template>
  <el-cascader
    :value="dicValue"
    :options="dicValues"
    :disabled="disabled"
    :placeholder="placeholder ? placeholder : '--请选择--'"
    :clearable="clearable"
    :props="props"
    :show-all-levels="showAllLevels"
    :collapse-tags="collapseTags"
    v-bind="$attrs"
    v-on="inputListeners"
  />
</template>

<script>
import { mapGetters } from 'vuex'
export default {
  model: {
    prop: 'dicValue',
    event: 'change'
  },
  props: {
    dicValue: {},
    enumKey: {
      type: String
    },
    // 是否多选
    multiple: {
      type: Boolean,
      default: false
    },
    // 是否全部显示
    showAllLevels: {
      type: Boolean,
      default: true
    },
    // 是否折叠多选
    collapseTags: {
      type: Boolean,
      default: false
    },
    // 是否可以选择任意一级
    checkStrictly: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    },
    placeholder: {
      type: String,
      default: '请选择'
    },
    clearable: {
      type: Boolean,
      default: true
    },
    // 需要过滤的数据
    filterList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {}
  },
  computed: {
    ...mapGetters('sys-dict', ['dictionaryData']),
    inputListeners: function() {
      var vm = this
      // `Object.assign` 将所有的对象合并为一个新对象
      return Object.assign(
        {},
        // 我们从父级添加所有的监听器
        this.$listeners,
        // 然后我们添加自定义监听器，
        // 或覆写一些监听器的行为
        {
          // 这里确保组件配合 `v-model` 的工作
          change(value) {
            vm.$emit('change', value)
          }
        }
      )
    },
    props() {
      return {
        value: 'enumKey',
        label: 'enumVal',
        multiple: this.multiple || false,
        checkStrictly: this.checkStrictly || false
      }
    },
    // 获取字典数据
    dicValues() {
      let value = []
      if (
        this.dictionaryData &&
        Object.keys(this.dictionaryData).length > 0 &&
        this.dictionaryData[this.enumKey]
      ) {
        if (this.filterList.length > 0) {
          value = this.dictionaryData[this.enumKey].filter(e =>
            this.filterList.includes(e.enumKey)
          )
        } else {
          value = this.dictionaryData[this.enumKey]
        }
      }
      return this.dicHandle(value)
    }
  },
  methods: {
    // 字典过滤
    dicHandle(items) {
      const dicHandle = item => {
        if (item && item.length) {
          for (let i in item) {
            if (item[i] && item[i].children && item[i].children.length) {
              dicHandle(item[i].children)
            } else {
              delete item[i].children
            }
          }
        }
        return item
      }
      let data = dicHandle(items)
      return this.dicDisabledHandle(data)
    },
    // 字典禁用处理
    dicDisabledHandle(items) {
      const dicHandle = (item, disabled) => {
        if (item && item.length) {
          for (let i in item) {
            item[i].disabled = disabled || !item[i].valid
            if (item[i] && item[i].children && item[i].children.length) {
              dicHandle(
                item[i].children,
                this.checkStrictly ? false : item[i].disabled
              )
            }
          }
        }
        return item
      }
      let data = dicHandle(items)
      return data
    }
  }
}
</script>

<style lang="scss">
.icon-select-popover {
  max-width: 300px;
}
.icon-select-list {
  max-height: 300px;
  padding: 0;
  margin: -8px 0 0 -8px;
  overflow: auto;
  > .el-button {
    padding: 8px;
    margin: 8px 0 0 8px;
    > span {
      display: inline-block;
      vertical-align: middle;
      width: 18px;
      height: 18px;
      font-size: 18px;
    }
  }
}
</style>
