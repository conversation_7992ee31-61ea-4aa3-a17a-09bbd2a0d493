package com.eversec.antivpn.log.api.dto;

import com.eversec.antivpn.log.api.dto.base.DateHistogramDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/8/15 11:58
 **/
@Data
public class DateHistogramCombinationDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long totalReport = 30000l;
    private Long todayReport = 10000l;
    private List<DateHistogramDTO> dtoList;
}
