import { getDic, getNameByValues, parseDicByLevel, getLevelPropsByLevelValues } from "@/utils/dic";
import cloneDeep from "lodash/cloneDeep";

let parsedDicCache = {};

export function getDicData(dataList, dicCodes, dicColumns, columns, callback) {
  if (!callback) return;
  let datas = cloneDeep(dataList);
  // 转换字典
  getDic(dicCodes, dicData => {
    for (let index = 0; index < datas.length; index++) {
      let data = datas[index];
      let oldData = cloneDeep(data);

      for (let index1 = 0; index1 < dicColumns.length; index1++) {
        const col = dicColumns[index1];
        let id = col.key;
        let value = data[id];
        const dic = dicData[col.dicCode];
        // 字典已删除
        if (dic == null) continue;

        if (col.labelDicLevel > 0 && col.valueDicLevel > 0) {
          const parsedDic = getParsedDic(col.dicCode, dic);
          if (parsedDic) {
            const parentName = getLevelPropsByLevelValues(
              parsedDic,
              value,
              col.valueDicLevel,
              col.labelDicLevel,
              col.multivalue,
              "itemName"
            );
            if (parentName) data[id] = parentName;
          }
        } else {
          let value1 = getDicList(id, oldData, columns);
          data[id] = getNameByValues(value1, dic, col.multivalue) || value;
        }

        data[id + '_origin_'] = value;
      }
    }

    callback(datas);
  });
}

/**
   * 支持多级字典转换
   * @param {*} key
   * @param {*} values
   * @param {*} columns
   */
function getDicList(key, values, columns) {
  let datas = [];
  for (let index = 0; index < columns.length; index++) {
    const element = columns[index];

    if (element.key == key) {
      datas.push(key);
      if (element.pidColumn != null && element.pidColumn != "") {
        getParent(element.pidColumn, datas, columns);
      }
      break;
    }
  }

  let datas1 = [];
  for (let index = 0; index < datas.length; index++) {
    const element = datas[index];
    datas1.push(values[element]);
  }

  return datas1.reverse();
}

function getParent(key, datas, columns) {
  for (let index = 0; index < columns.length; index++) {
    const element = columns[index];

    if (element.value == key) {
      datas.push(key);

      if (element.pidColumn != null && element.pidColumn != "") {
        getParent(element.pidColumn, datas, columns);
      }
      break;
    }
  }
}

function getParsedDic(dicCode, dic) {
  if (!dic) return;
  if (!parsedDicCache[dicCode]) {
    parsedDicCache[dicCode] = parseDicByLevel(dic);
    // console.log(this.parsedDicCache[dicCode]);
  }
  return parsedDicCache[dicCode];
}
