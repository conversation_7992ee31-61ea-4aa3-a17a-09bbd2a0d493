package com.eversec.antivpn.log.api;


import com.eversec.antivpn.emnus.ScreenTypeEnum;
import com.eversec.antivpn.log.api.dto.TypeCountDTO;
import com.eversec.antivpn.log.api.dto.base.DateHistogramDTO;
import com.eversec.framework.feignboot.conf.EversecFeignDecoderConfiguration;
import com.eversec.framework.feignboot.conf.EversecFeignLogConfiguration;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import java.util.Map;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
*  Api接口
*
* 模块对外暴露的api
*
* @FeignClient 注解参数解释
* value: 服务提供者提供的服务名
* url: 开发、测试环境调试无法通过服务名调用，填写服务对外暴露的地址。该参数有值 value则不生效，该参数为空时使用value值
* path: 服务前缀，实现类 @RequestMapping 也使用该值
* contextId: 全局唯一，默认为包名加类名
* configuration:
*          EversecFeignDecoderConfiguration: 自动解包，异常传递。
*          EversecFeignLogConfiguration: 记录请求日志。默认开启日志，可通过如下参数控制是否记录日志
*                  eversec.feign.request-log=true
*                  eversec.feign.result-log=true
*
*/

@Validated
@Tag(name = "跨境软件态势：E1话单对应知识库软件信息")
@FeignClient(value = "anti-vpn-service",
        path = SoftWareScreenCommLogApi.PATH,
        url = "${app.anti-vpn.service-url:}",
        contextId = "com.eversec.antivpn.log.api.SoftWareScreenCommLogApi",
        configuration = { EversecFeignDecoderConfiguration.class, EversecFeignLogConfiguration.class})
public interface SoftWareScreenCommLogApi {

    String PATH = "/log/softwareScreen";


    @Operation(summary = "提供跨境通信的软件总量")
    @GetMapping("/dateSoftWareSum")
    TypeCountDTO dateSoftWareSum();


    @Operation(summary = "近7日用户跨境通信使用的软件数量")
    @GetMapping("/last7Days")
    TypeCountDTO last7Days(@RequestParam ScreenTypeEnum screenTypeEnum);

    @Operation(summary = "软件用户量 TOP10（环形图）")
    @GetMapping("/softwareUserPie")
    List<TypeCountDTO> softwareUserPie(@RequestParam ScreenTypeEnum screenTypeEnum);

    @Operation(summary = "软件访问日志量 TOP10（环形图）")
    @GetMapping("/softwareAccessLog")
    List<TypeCountDTO> softwareAccessLog(@RequestParam ScreenTypeEnum screenTypeEnum);

    @Operation(summary = "用户量TOP10的软件提供服务次数趋势图（散列图）")
    @GetMapping("/userNumChart")
    List<DateHistogramDTO> userNumChart(@RequestParam ScreenTypeEnum screenTypeEnum);


    @Operation(summary = "软件对系统的适配度 top10")
    @GetMapping("/softWareSystemTopo10")
    List<TypeCountDTO> softWareSystemTopo10(@RequestParam ScreenTypeEnum screenTypeEnum);

    @Operation(summary = "用户量TOP10的软件开发者信息")
    @GetMapping("/userNumDevelop")
    List<DateHistogramDTO> userNumDevelop(@RequestParam ScreenTypeEnum screenTypeEnum);

    @Operation(summary = "软件对协议的适配度 top10")
    @GetMapping("/softWareProtocolTopo10")
    List<TypeCountDTO> softWareProtocolTopo10(@RequestParam ScreenTypeEnum screenTypeEnum);


    @Operation(summary = "软件授权类型占比")
    @GetMapping("/softwareLicence")
    List<TypeCountDTO> softwareLicence(@RequestParam ScreenTypeEnum screenTypeEnum);

}





