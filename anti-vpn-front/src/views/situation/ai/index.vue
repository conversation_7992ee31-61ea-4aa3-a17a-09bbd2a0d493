<template>
  <!--  <div class="pagebox bigScreen" ref="bigPage" id="bigPage">-->
  <!--    &lt;!&ndash; 综合态势-监测总览 &ndash;&gt;-->
  <!--    <Header @update="update" />-->

  <!--    -->
  <!--  </div>-->
  <section class="content">
    <div class="con_left">
      <TopNum :numList="numList"></TopNum>
      <div class="leftbox mt20">
        <div class="leftbox_l">
          <Mybox title="">
            <div class="tabTitle">
              <span :class="{ active: currentTab == 1 }" @click="topChange(1)"
                >AI模型识别日志量TOP10</span
              >
              <span v-if="openTwiter=='true'" :class="{ active: currentTab == 2 }" @click="topChange(2)"
                > | 重点应用排名</span
              >
            </div>
            <linelist :topList="top10" />
          </Mybox>
          <Mybox class="mt20" title="传输层协议占比">
            <vpn-pie ref="vpnPie" :protocolType="protocolType" />
          </Mybox>
        </div>

        <div class="leftbox_mid">
          <section style="height: 580px">
            <MapShandong
              :mapD="mapD"
              :myTooltip="myTooltip"
              v-if="openStatus && provinceName == 'shandong'"
            />
            <MapChina ref="mapRef" v-if="!openStatus" />
          </section>

          <mid2 ref="mid2Ref" :tendency="tendency" />
        </div>
      </div>
    </div>
    <div class="con_rig">
      <rig1 ref="rig1Ref" :rateData="rateData" />

      <Mybox title="AI模型识别跨境通信日志置信度占比">
        <Pie
          :pieData="ratePie"
          v-loading="vpnPro_loading"
          element-loading-background="rgba(0, 0, 0, 0.1)"
        />
      </Mybox>
      <Mybox v-if="openStatus" title="各系统上报监测日志占比">
        <system-log :systemLogList="systemLogList"></system-log>
      </Mybox>
      <Mybox v-else title="省区域上报监测日志TOP10">
        <AreaTop10 :areaList="provinceTop" />
      </Mybox>
    </div>
  </section>
</template>

<script>
import { getDicAll, getDicKey2Val } from "@/assets/js/dic.js";
import { formatTime } from "@/utils/time";
import aiApi from "./aiApi.js";
import { barFn, hunheFn, publicPieFn } from "../echartOption";
import { getProvinceMapInfo } from "@/utils/map_utils";

import Header from "../common/Header.vue";
import TopNum from "./TopNum.vue";
import MapChina from "./MapChinaBar.vue";
import Mybox from "../common/Mybox.vue";
import Pie from "./Pie.vue";
import Pie2 from "../common/Pie2.vue";
import rig1 from "./rig1.vue";
import mid2 from "./mid2.vue";
import AreaTop10 from "./AreaTop10.vue";
import linelist from "./linelist.vue";
import vpnPie from "./vpn-pie.vue";
import systemLog from "./system-log.vue";
import MapShandong from "../common/map/Shandong.vue";
import sysConf from "@/api/rm/sysConf";

export default {
  components: {
    Header,
    MapChina,
    Mybox,
    Pie,
    Pie2,
    TopNum,
    rig1,
    mid2,
    AreaTop10,
    linelist,
    vpnPie,
    systemLog,
    MapShandong
  },
  data() {
    return {
      searchTime: {
        screenTypeEnum: "TODAY"
      },
      bigPage: null,
      dicAll: {},
      vpnPro_loading: false,
      numList: [
        {
          name: "AI模型监测日志",
          dateRangeCount: 0,
          todayCount: 0,
          icon: require("./img/icon1.png")
        },
        {
          name: "模型识别准确率",
          dateRangeCount: 0,
          todayCount: 0,
          icon: require("./img/icon2.png")
        },
        {
          name: "访问者地区数量",
          dateRangeCount: 0,
          todayCount: 0,
          icon: require("./img/icon3.png")
        },
        {
          name: "目的地地区数量",
          dateRangeCount: 0,
          todayCount: 0,
          icon: require("./img/icon4.png")
        },
        {
          name: "AI模型总量",
          dateRangeCount: 0,
          todayCount: 0,
          icon: require("./img/icon5.png")
        }
      ],
      top10: [],
      protocolType: [],
      tendency: {
        xData: [],
        yData: []
      },
      rateData: {},
      ratePie: [
        {
          name: "0-20分",
          min: 0,
          max: 20,
          value: 0,
          percent: 0
        },
        {
          name: "21-40分",
          min: 21,
          max: 40,
          value: 0,
          percent: 0
        },
        {
          name: "41-60分",
          min: 41,
          max: 60,
          value: 0,
          percent: 0
        },
        {
          name: "61-80分",
          min: 61,
          max: 80,
          value: 0,
          percent: 0
        },
        {
          name: "81-100分",
          min: 81,
          max: 100,
          value: 0,
          percent: 0
        }
      ],
      provinceTop: [],
      status: {},
      // openStatus: false,
      systemLogList: [],
      mapD: [],
      myTooltip: "",
      openTwiter: false,
      currentTab: 1
    };
  },
  computed: {
    openStatus() {
      if (localStorage.isProvince == "true") return true;
      else return false;
    },
    provinceName() {
      return localStorage.provinceName;
    }
  },
  watch: {},
  async created() {
    this.$emit("onUpdateMain", {
      pageContent: {
        padding: 0,
        background: "#F7F8FC"
      }
    });
  },
  async mounted() {
    this.bigPage = this.$refs.bigPage;
    sysConf.getByKey("AI_TWITTERT").then(res => {
      this.openTwiter = res.itemValue;
    });
    console.log(this.openTwiter,"open")
  },

  methods: {
    async update(screenType, ft) {
      // 字典
      // this.dicAll = await getDicAll();
      if (screenType) {
        this.searchTime = {
          screenTypeEnum: screenType
        };
      }
      this.getTopNum();
      this.getDatedDiscernTop();
      this.getProtocolTypeAggregate();
      this.getTendencyChart(screenType);
      this.getRateChar();
      this.getRatePie();
      this.getDatedProvinceTop();
      this.getMapList();
      this.getSystemRatio();
    },
    // getStartProvince() {
    //   aiApi.startProvince().then((res) => {
    //     this.openStatus = res.openStatus;
    //   });
    // },
    getTopNum() {
      aiApi
        .dateMonitoring({ TODAY: this.searchTime.screenTypeEnum })
        .then(res => {
          this.numList[0].dateRangeCount = res.dateRangeCount;
          this.numList[0].todayCount = res.todayCount;
        });
      aiApi.dateRecognition(this.searchTime).then(res => {
        this.numList[1].dateRangeCount = res;
      });
      aiApi.dateVisitorArea(this.searchTime).then(res => {
        this.numList[2].dateRangeCount = res.dateRangeCount;
        this.numList[2].todayCount = res.todayCount;
      });
      aiApi.dateDestinationArea(this.searchTime).then(res => {
        this.numList[3].dateRangeCount = res.dateRangeCount;
        this.numList[3].todayCount = res.todayCount;
      });
      aiApi.dateAISum(this.searchTime).then(res => {
        this.numList[4].dateRangeCount = res;
      });
    },
    getDatedDiscernTop() {
      aiApi.datedDiscernTop(this.searchTime).then(res => {
        this.top10 = res;
      });
    },
    datedDiscernTopTwitter() {
      aiApi.datedDiscernTopTwitter(this.searchTime).then(res => {
        this.top10 = res;
      });
    },
    getProtocolTypeAggregate() {
      aiApi.protocolTypeAggregate(this.searchTime).then(res => {
        this.protocolType = res;
        this.protocolType.map(item => {
          item.numFt = this.getNum(item.num);
        });
        this.$refs.vpnPie.init(this.protocolType);
      });
    },
    getTendencyChart(screenType) {
      this.tendency.xData = [];
      this.tendency.yData = [];
      aiApi.tendencyChart(this.searchTime).then(res => {
        console.log(res, "ooooo");
        let arr = Object.keys(res);
        for (const i in res) {
          this.tendency.yData.push(res[i].num);
          if (screenType == "THISMONTH") {
            this.tendency.xData.push(`第${Number(i) + 1}周`);
            // for (const key in arr) {
            //   console.log('for-in', key)
            //   if (arr[key] == i) {
            //     this.tendency.xData.push(`第${Number(key) + 1}周`);
            //   }
            // }
          } else {
            let name = res[i].key;
            if (name.length > 7) {
              name = `${name.slice(4, 6)}-${name.slice(6, 8)}`;
            }
            this.tendency.xData.push(name);
            // this.tendency.xData.push(res[i].key);
          }
        }
      });
    },
    getRateChar() {
      aiApi.rateChar(this.searchTime).then(res => {
        this.rateData = this.getChartData(res, "");
        console.log(this.rateData);
      });
    },
    getRatePie() {
      this.vpnPro_loading = true;
      for (const i of this.ratePie) {
        i.value = 0;
      }
      aiApi
        .ratePie(this.searchTime)
        .then(res => {
          res.map(item => {
            for (const i of this.ratePie) {
              if (item.key >= i.min && item.key <= i.max) {
                i.value += item.e2Num;
              }
            }
          });
        })
        .finally(() => {
          this.vpnPro_loading = false;
        });
    },
    getDatedProvinceTop() {
      aiApi.datedProvinceTop(this.searchTime).then(res => {
        res.map(item => {
          item.name = getProvinceMapInfo(item.key + "0000");
          item.value = this.getNum(item.num);
        });
        this.provinceTop = res;
      });
    },
    getMapList() {
      aiApi.mapList(this.searchTime).then(res => {
        if (!this.openStatus) {
          res.map(item => {
            item.name = getProvinceMapInfo(item.key + "0000");
            item.value = item.num;
            item.todayNumFt = this.getNum(item.todayNum);
            item.totalFt = this.getNum(item.num);
          });
          this.$refs.mapRef.drawMap(res);
        } else {
          this.mapD = [];
          res.map(item => {
            this.mapD.push({
              name: getProvinceMapInfo(item.key),
              value: item.num,
              todayNum: item.todayNum,
              todayNumFt: this.getNum(item.todayNum),
              totalFt: this.getNum(item.num)
            });
          });
          this.myTooltip =
            '<div class="tooltipbox">' +
            '<div class="provincename">${d.name} </div>' +
            '<div class="tooltip-item">' +
            '<div class="item-name">今日上报：</div>' +
            '<div class="item-value color_num">${d.todayNumFt}</div>' +
            "</div>" +
            '<div class="tooltip-item">' +
            '<div class="item-name">总上报：</div>' +
            '<div class="item-value color_num2"> ${d.totalFt}</div>' +
            "</div>" +
            "</div>";
        }
      });
    },
    getNum(n) {
      let numFt = n;
      if (n >= 10000 && n < 100000000) {
        n = (n / 10000).toFixed(2);
        numFt = n + "万";
      } else if (n >= 100000000) {
        n = (n / 100000000).toFixed(2);
        numFt = n + "亿";
      }
      return numFt;
    },
    getSystemRatio() {
      aiApi.systemRatio(this.searchTime).then(res => {
        this.systemLogList = res;
      });
    },
    getChartData(baseData) {
      var data = {
        baseData: [],
        xAxisData: [], //x轴
        realData: [], //实际数值
        stdplotData: [] //正态分布数值
      };

      for (var i = 0; i <= 100; i++) {
        data.xAxisData.push(i);
        const item = baseData.find(item => {
          return item.e1Num == i;
        });
        if (item) {
          console.log();
          data.realData.push(item.e2Num);
        } else {
          data.realData.push(0);
        }
      }

      return data;
    },
    topChange(val) {
      this.currentTab = val;
      console.log(val,'vals')
      if(val==1){
        this.getDatedDiscernTop()
      }else{
        this.datedDiscernTopTwitter()
      }
    }
  }
};
</script>

<style lang="scss" scoped>
//@import "../common/bigScreen.scss";
//.pagebox {
//  position: relative;
//  background: url(./img/bigscreenbg.png) no-repeat;
//  height: 1080px;
//  width:1920px;
//  min-width: 1910px;
//  background-size: 100% 100%;
//  overflow: auto;
//  .item_card {
//    height: 320px;
//  }
//
//
//}
.content {
  display: flex;
  // min-height: 700px;
  padding: 0 30px;
  width: 100%;
  justify-content: space-between;
  .con_left {
    width: 1380px;
    .leftbox {
      display: flex;
      justify-content: space-between;
      .leftbox_l {
        width: 440px;
      }
      .leftbox_mid {
        width: calc(100% - 440px);
      }
    }
  }
  .con_rig {
    width: 440px;
  }
  .tabTitle {
    position: absolute;
    top: 8px;
    color: #fff;
    left: 30px;
    span{
      color: #a5acb4;
      cursor: pointer;
      &.active{
        color: #fff;
      }
    }
  }
}
</style>
