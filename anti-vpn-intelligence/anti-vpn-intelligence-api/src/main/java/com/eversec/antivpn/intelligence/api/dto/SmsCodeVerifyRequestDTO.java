package com.eversec.antivpn.intelligence.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * 短信验证码验证请求DTO
 * 
 * <AUTHOR>
 * @since 2025-01-12
 */
@Data
@Schema(name = "SmsCodeVerifyRequestDTO", description = "短信验证码验证请求")
public class SmsCodeVerifyRequestDTO {

    @NotBlank(message = "手机号码不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号码格式不正确")
    @Schema(description = "手机号码（11位，不需要加86前缀）", example = "***********")
    private String phoneNumber;

    @NotBlank(message = "验证码不能为空")
    @Schema(description = "验证码", example = "1234")
    private String code;

    @Schema(description = "业务类型（可选，用于区分不同业务场景）", example = "LOGIN")
    private String businessType;

}
