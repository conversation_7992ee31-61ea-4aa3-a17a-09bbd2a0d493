<?xml version="1.0" encoding="UTF-8"?>
<root>
    <!--
    version	版本号	必填	字符串	8	跨境通信情报库版本格式：主版本号.副版本号，本规范主版本号为1，副版本号为0，即1.0。
commandId	执行指令ID	必填	长整型	8	跨境通信情报库下发执行指令ID。
当部侧下发时由部侧系统控制惟一；当企业侧系统上报时企业侧内惟一。
comCode	运营商编码	必填	字符串	18	运营商编码，见附录F.2 REF _Ref73644258 \h  \* MERGEFORMAT 节。
provinceId	省级区域编号	必填	长整型	2	省级区域编号，见附录F.4 REF _Ref73644258 \h  \* MERGEFORMAT 节。
networkBusinessId	网络类型	选填	整型	/	监测网络类型，见附录F.3 REF _Ref73644258 \h  \* MERGEFORMAT 节。企业侧上报时必填。部侧下发时可选填，当部侧下发为空时表示所有网络类型生效。
typeId	情报类型	选填	整型	/	情报类型：
1-VPN服务地址,例:提供VPN下载、注册、登录、购买、登出等各类访问地址和链接；
2-VPN 跨境服务地址,例：海外域名通道或海外IP通道地址；
3-模型研判；
4-样本库；
999-其他。
当操作类型为0、1、2时必填，当操作类型为3、4、5时选填。
vpnId	情报库ID	选填	长整型	/	情报库ID，当部侧系统下发时必填；企业侧系统上报、全量库下发时可不填写。
vpnName	Vpn名称或文件名称	必填	字符串	256	当操作类型为0、1、2时填写VPN名称，当操作类型为3时填写的是全量跨境通信情报库文件名；当操作类型为4、5时填写的是模型或样本文件名，多个用|线分隔；
当填写Vpn名称时，优先使用中文名称，没有中文名称时使用英文名称。
vpnDomain	域名	选填	字符串	128	VPN活动所使用的域名地址。
vpnIp	IP地址	选填	字符串	64	VPN活动所使用的IP地址；IPv4使用点分表示法，IPv6使用冒分小写0位压缩表示法。
vpnUrl	URL地址	选填	字符串	1024	VPN活动所使用的URL地址，采用base64编码后的URL。
vpnLink	取证链接	选填	字符串	1024	取证链接，包含VMess、VLESS、Shadowsocks、Socks、Trojan等链接地址。企业侧系统上报时根据实际情况填写。
isUploadFile	是否上报取证文件	选填	整型	/	是否上报取证文件：
0：上报；
1：不上报。
企业侧系统上报时必填，部侧系统下发时为空。
attachMent	取证文件名称	选填	字符串	256	当isUploadFile为0时必填。上报方法参见7.2.2章节。
timeStamp	生成时间	必填	字符串	19	生成该文件的时间。
operationType	操作类型	必填	整型	/	对该记录的操作类型，包括：
0-增量情报下发；
1-上报；
2-删除；
3-全量情报下发；
4-模型下发；
5-样本下发。
    -->
    <command>
        <version>1.0</version>
        <commandId>1000001</commandId>
        <comCode>01</comCode>
        <provinceId>11</provinceId>
        <networkBusinessId>xxxx</networkBusinessId>
        <typeId>xxxx</typeId>
        <vpnId>xxxx</vpnId>
        <vpnName>xxxx</vpnName>
        <vpnDomain>xxxx</vpnDomain>
        <vpnIp>xxxx</vpnIp>
        <vpnUrl>xxxx</vpnUrl>
        <vpnLink>xxxx</vpnLink>
        <timeStamp>xxxx</timeStamp>
        <operationType>xxxx</operationType>
    </command>

</root>
