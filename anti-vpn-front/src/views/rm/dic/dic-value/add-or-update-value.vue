<template>
  <el-dialog
    :title="dataForm.id == null ? '新增' : '修改'"
    :visible.sync="visible"
    :close-on-click-modal="false"
    @closed="reset"
  >
    <el-form
      ref="dataForm"
      :model="dataForm"
      :rules="dataRule"
      label-width="80px"
      @keyup.enter.native="dataFormSubmit"
    >
      <el-form-item
        v-if="dataForm.parentEnumKey"
        label="所属父级"
        prop="enumKey"
      >
        <el-input
          v-model="dataForm.parentEnumKey"
          disabled
          placeholder="所属父级"
        />
      </el-form-item>
      <el-form-item label="枚举键" prop="enumKey">
        <el-input
          v-if="state === 2"
          v-model="dataForm.enumKey"
          placeholder="枚举键"
        >
          <template slot="prepend">
            {{ dataForm.parentEnumKey + '_' }}
          </template>
        </el-input>
        <el-input
          v-else
          v-model="dataForm.enumKey"
          :disabled="state === 1"
          placeholder="枚举键"
        />
      </el-form-item>
      <el-form-item label="枚举值" prop="enumVal">
        <el-input v-model="dataForm.enumVal" placeholder="枚举值" />
      </el-form-item>
      <el-form-item label="枚举序列" prop="seq">
        <el-input
          v-model.number="dataForm.seq"
          placeholder="枚举序列"
          oninput="value=value.replace(/[^0-9.]/g,'')"
          @paste.native.capture.prevent="() => {}"
        />
      </el-form-item>
      <el-form-item label="是否有效" prop="valid">
        <el-switch v-model="dataForm.valid" />
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button
        type="primary"
        :loading="submitLoading"
        @click="dataFormSubmit"
      >
        确定
      </el-button>
    </span>
  </el-dialog>
</template>

<script>
import api_setManager from '@/api/rm/dic'
import cloneDeep from 'lodash/cloneDeep'

export default {
  data() {
    return {
      visible: false,
      submitLoading: false,
      dataForm: {},
      dataRule: {
        enumKey: [
          { required: true, message: '枚举键不能为空', trigger: 'blur' }
        ],
        enumVal: [
          { required: true, message: '枚举值不能为空', trigger: 'blur' }
        ]
      },
      state: null
    }
  },
  methods: {
    init(row, state) {
      // state 0新增第一级， 1修改第一级， 2新增子集
      this.visible = true
      this.$nextTick(() => {
        this.dataForm.valid = true
        this.$refs.dataForm.clearValidate()
        if (state === 1) this.dataForm = cloneDeep(row)
        if (state === 2)
          this.dataForm = {
            valid: true,
            parentEnumKey: row.enumKey
          }
        this.state = state
        this.$forceUpdate()
      })
    },
    reset() {
      this.dataForm = {}
      this.$refs.dataForm.resetFields()
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs.dataForm.validate(valid => {
        if (valid) {
          this.submitLoading = true
          const type = this.dataForm.id == null ? 'addDicVal' : 'updateDicVal'
          const params = {
            typeKey: this.$route.query.typeKey,
            ...(this.state === 2
              ? {
                  enumKey:
                    this.dataForm.parentEnumKey + '_' + this.dataForm.enumKey,
                  parentEnumKey: this.dataForm.parentEnumKey
                }
              : {
                  enumKey: this.dataForm.enumKey
                }),
            enumVal: this.dataForm.enumVal,
            seq: this.dataForm.seq,
            valid: this.dataForm.valid,
            id: this.dataForm.id
          }
          api_setManager[type](params)
            .then(res => {
              this.visible = false
              this.submitLoading = false
              //子组件触发父组件事件
              this.$emit('onSubmit')
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500
              })
            })
            .catch(() => {
              this.submitLoading = false
            })
        }
      })
    }
  }
}
</script>
