{"name": "evervue-front", "version": "1.2.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint", "start": "npm run serve", "serve:prod": "vue-cli-service serve --mode production"}, "dependencies": {"@vue/eslint-config-prettier": "^6.0.0", "axios": "^0.18.0", "core-js": "^2.6.5", "crypto-js": "^4.0.0", "echarts": "^4.9.0", "element-ui": "^2.15.13", "eslint-plugin-prettier": "^3.1.3", "html2canvas": "^1.0.0-alpha.12", "lodash": "^4.17.10", "moment": "^2.29.2", "nprogress": "^0.2.0", "prettier": "^1.19.1", "pubsub-js": "^1.7.0", "qs": "^6.5.2", "spark-md5": "3.0.2", "vue": "~2.6.11", "vue-class-component": "^6.0.0", "vue-clipboard2": "^0.2.1", "vue-cookie": "^1.1.4", "vue-property-decorator": "^7.0.0", "vue-router": "~3.5.4", "vuedraggable": "^2.16.0", "vuex": "^3.0.1"}, "devDependencies": {"@vue/cli-plugin-babel": "^3.11.0", "@vue/cli-plugin-eslint": "^3.11.0", "@vue/cli-plugin-typescript": "^3.11.0", "@vue/cli-service": "^3.11.0", "@vue/eslint-config-typescript": "^4.0.0", "babel-eslint": "^10.0.1", "babel-plugin-component": "^1.1.1", "eslint": "^5.16.0", "eslint-plugin-vue": "^5.0.0", "mockjs": "^1.0.1-beta3", "sass": "^1.51.0", "sass-loader": "^7.0.1", "svg-sprite-loader": "^6.0.11", "typescript": "~3.2.2", "uglifyjs-webpack-plugin": "^2.0.1", "vue-template-compiler": "~2.6.11"}, "postcss": {"plugins": {"autoprefixer": {}}}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"]}