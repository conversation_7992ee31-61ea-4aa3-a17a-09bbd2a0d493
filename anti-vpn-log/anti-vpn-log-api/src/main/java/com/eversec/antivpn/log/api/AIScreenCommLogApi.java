package com.eversec.antivpn.log.api;


import com.eversec.antivpn.emnus.ScreenTypeEnum;
import com.eversec.antivpn.log.api.dto.TypeCountDTO;
import com.eversec.antivpn.log.api.dto.base.AggCountDTO;
import com.eversec.antivpn.log.api.dto.base.DateHistogramDTO;
import com.eversec.framework.feignboot.conf.EversecFeignDecoderConfiguration;
import com.eversec.framework.feignboot.conf.EversecFeignLogConfiguration;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import java.util.Map;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
*  Api接口
*
* 模块对外暴露的api
*
* @FeignClient 注解参数解释
* value: 服务提供者提供的服务名
* url: 开发、测试环境调试无法通过服务名调用，填写服务对外暴露的地址。该参数有值 value则不生效，该参数为空时使用value值
* path: 服务前缀，实现类 @RequestMapping 也使用该值
* contextId: 全局唯一，默认为包名加类名
* configuration:
*          EversecFeignDecoderConfiguration: 自动解包，异常传递。
*          EversecFeignLogConfiguration: 记录请求日志。默认开启日志，可通过如下参数控制是否记录日志
*                  eversec.feign.request-log=true
*                  eversec.feign.result-log=true
*
*/

@Validated
@Tag(name = "AI模型态势：E2 话单")
@FeignClient(value = "anti-vpn-service",
        path = AIScreenCommLogApi.PATH,
        url = "${app.anti-vpn.service-url:}",
        contextId = "com.eversec.antivpn.log.api.AIScreenCommLogApi",
        configuration = { EversecFeignDecoderConfiguration.class, EversecFeignLogConfiguration.class})
public interface AIScreenCommLogApi {

    String PATH = "/log/aiScreen";


    @Operation(summary = "服务监控-AI模型监测日志")
    @GetMapping("/dateMonitoring")
//    TypeCountDTO dateMonitoring(@RequestParam ScreenType screenType, @RequestParam(required = false) Long startTime, @RequestParam(required = false) Long endTime);
    AggCountDTO dateMonitoring(@RequestParam(value = "TODAY" ) ScreenTypeEnum screenTypeEnum);


    @Operation(summary = "服务监控-模型识别准确率")
    @GetMapping("/dateRecognition")
    Integer dateRecognition(@RequestParam ScreenTypeEnum screenTypeEnum);

    @Operation(summary = "服务监控-AI模型总量")
    @GetMapping("/dateAISum")
    Integer dateAISum(@RequestParam ScreenTypeEnum screenTypeEnum);


    @Operation(summary = "服务监控-访问者地区数量")
    @GetMapping("/dateVisitorArea")
    AggCountDTO dateVisitorArea(@RequestParam ScreenTypeEnum screenTypeEnum);

    @Operation(summary = "服务监控-目的地地区数量")
    @GetMapping("/dateDestinationArea")
    AggCountDTO dateDestinationArea(@RequestParam ScreenTypeEnum screenTypeEnum);

    @Operation(summary = "AI模型识别日志量 TOP10")
    @GetMapping("/datedDiscernTopo10")
    List<TypeCountDTO> datedDiscernTopo10(@RequestParam ScreenTypeEnum screenTypeEnum);

    @Operation(summary = "传输层协议类型占比")
    @GetMapping("/protocolTypeAggregate")
    List<TypeCountDTO> protocolTypeAggregate(@RequestParam ScreenTypeEnum screenTypeEnum);

    //https://echarts.apache.org/examples/zh/editor.html?c=line-smooth
    @Operation(summary = "AI模型监测跨境通信日志趋势图")
    @GetMapping("/tendencyChart")
    Map<String, Object> tendencyChart(@RequestParam ScreenTypeEnum screenTypeEnum);

    //https://echarts.apache.org/examples/zh/editor.html?c=dynamic-data
    @Operation(summary = "AI模型识别通信日志置信度分布")
    @GetMapping("/rateChart")
    List<DateHistogramDTO> rateChart(@RequestParam ScreenTypeEnum screenTypeEnum);



    //https://echarts.apache.org/examples/zh/editor.html?c=pie-doughnut
    @Operation(summary = "AI模型识别跨境通信日志置信度占比(饼图)")
    @GetMapping("/ratePie")
    List<DateHistogramDTO> ratePie(@RequestParam ScreenTypeEnum screenTypeEnum);

    @Operation(summary = "省区域上报监测日志 TOP10")
    @GetMapping("/datedProvinceTopo10")
    List<TypeCountDTO> datedProvinceTopo10(@RequestParam ScreenTypeEnum screenTypeEnum);


    @Operation(summary = "全国地图")
    @GetMapping("/chinaMap")
    List<TypeCountDTO> chinaMap(@RequestParam ScreenTypeEnum screenTypeEnum);

    @Operation(summary = "各系统上报监测日志占比")
    @GetMapping("/systemRatio")
    List<TypeCountDTO>  systemRatioMap(@RequestParam ScreenTypeEnum screenTypeEnum);

    @Operation(summary = "AI模型Twiter识别日志量")
    @GetMapping("/modelTwiter")
    List<TypeCountDTO> modelTwiter(@RequestParam ScreenTypeEnum screenTypeEnum);

}





