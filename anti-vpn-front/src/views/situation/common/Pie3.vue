<template>
  <div class="chartbox">
    <e-charts class="piebg" type="pie" :options="pieOpt" emptyColor="#fff"></e-charts>
    
  </div>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: ""
    },
    pieData: {
      default: () => [
        // { name: "域名", value: 500 },
        // { name: "URL", value: 1000 },
        // { name: "IP", value: 1000 },
        // { name: "AI模型", value: 1000 },
        // { name: "VPN协议", value: 10 }
      ],
      type: Array,
    }
  },
  data() {
    return {
      pieOpt: null,
      color: ["#2884ff", "#1ba5fe", "#68eec9", "#7ae15e", "#fcf94c","#fdd35a","#fe7b69", "#b52be2", "#6646ee", "#9442f1"],
    };
  },
  watch: {
    pieData: {
      handler(val) {
        this.init(val)
      },
      immediate: true,
      deep:true
    },
  },
  mounted() {
    // this.init();
  },
  methods: {
    init(data) {
      let color = this.color;

      let total = data.reduce((n,cur) => n+=cur.value, 0);
      data = data.map(el => {
        return {
          ...el,
          percent: (el.value*100/total).toFixed(2),
        }
      })

      if(data.length==0) {
        this.pieOpt = null;
        return;
      }

      this.pieOpt = {
        color: [
          "#8670ed",
          "#c0f05a",
          "#fb5355",

          "#ccf176",
          "#7ae15f",
          "#69edca",
          "#1ca4ff",
          "#2a84ff",
          "#77a4ff",
          "#a477ff"
        ],
        graphic: [
          {
            type: 'image',
            top: 80,
            left: 55,
            z: -10,
            bounding: 'raw',
            style: {
              image: require('./img/chart/radar.png'),
              width: 132,
              height: 132
            }
          },
          {
            type: 'image',
            top: 65,
            left: 40,
            z: -10,
            bounding: 'raw',
            style: {
              image: require('./img/chart/dashed-circle.png'),
              width: 161,
              height: 160
            },
          }
        ],
        tooltip: {
          trigger: 'item',
          backgroundColor: '#082140',
          borderWidth: 1,
          borderColor: '#393939',
          borderRadius: 0,
          formatter: p => {
            let e = p.data;
            return `
            <div style="padding: 0 10px; font-size: 12px">
                <div style="color: ${e.color}; font-size: 16px; font-weight: 700">${e.name}</div>
                <div style="color: #6d737e; font-size: 14px">数量</div>
                <div style="color: #50c7ff; font-size: 15px;">${(e.value || 0).toLocaleString()}</div>
                <div style="color: #6d737e; font-size: 14px;">占比</div>
                <div style="color: #50c7ff; font-size: 15px;">${e.percent}%</div>
            </div>
            `
          }
        },
        legend: {
          type: 'scroll',
          show: true,
          orient: 'vertical',
          align: 'left',
          right: 0,
          top: 'center',
          icon: 'rect',
          itemWidth: 8,
          itemHeight: 8,
          itemGap: 20,
          data: data.map(item => item.name),
          formatter: name => {
            const target = data.find(item => item.name === name);
            const {percent, value} = target;
            if (name.length > 8) {
              name = name.slice(0, 8) + '...'
            }

            return `{itemName|${name}}{itemNum|${percent}%}`;
          },
          textStyle: {
            borderColor: 'red',
            borderWidth: 0,
            color: '#999999',
            fontSize: 16,
            padding: [3, 2],
            lineHeight: 20,
            rich: {
              itemName: {
                fontSize: 15,
                width: 100,
                color: '#fff',
              },
              itemValue: {
                fontSize: 16,
                width: 45,
                color: '#4bd5d4',
              },
              itemValueUnit: {
                fontSize: 15,
                width: 80,
                color: '#fff',
              },
              itemNum: {
                fontSize: 16,
                width: 80,
                color: '#4bd5d4',
              }
            }
          }
        },

        series: [
          {
            type: 'pie',
            left: -200,
            top: 'center',
            radius: [90, 100],

            label: {
              show: false,
              position: 'center',
              formatter: [
                '{a|TOP10}',
                '{b|软件用户量}',
              ].join('\n'),
              rich: {
                a: {
                  color: '#e45e60',
                  fontSize: 18,
                  lineHeight: 20,
                  fontWeight: 'bold'
                },
                b: {
                  color: '#fff',
                  fontSize: 16,
                  lineHeight: 20,
                  fontWeight: 'bold'
                }
              }
            },
            itemStyle: {
              borderColor: 'transparent',
              borderWidth: 50
            },
            data: data
          },
        ]
      }

    }
  }
};
</script>

<style scoped lang="scss">
.chartbox {
  .piebg {
    // background: url(./img/pie_bg.png) no-repeat center 34px;
    height: 290px;
  }
  .chartUl {
    display: flex;
    flex-wrap: wrap;
    margin-top: -10px;
    li {
      color: #fff;
      font-size: 12px;
      background: #1e2432;
      height: 24px;
      line-height: 24px;
      clear: both;
      padding: 0 10px;
      margin-bottom: 5px;
      width: calc(50% - 8px);
      &:nth-of-type(2n-1) {
        margin-right: 16px;
      }
      .dian {
        display: inline-block;
        width: 7px;
        height: 7px;
        border-radius: 50%;
        margin-right: 6px;
      }
      .percent {
        color: #a7e7ff;
      }
    }
  }
}
</style>
