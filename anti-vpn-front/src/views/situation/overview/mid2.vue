<template>
  <section class="title_box2">
    <div class="box_tit">
      {{ provinceName }}接收监测日志与上报
      <span class="fr color_blue"
        >总上报数：{{ totalReport | num2Ft }}</span
      >
      <div class="todayft">今日上报：{{ todayReport | num2Ft }}</div>
    </div>
    <div
      class="box_con"
      style="height:270px; padding-top:20px; margin-top: 20px"
    >
      <e-charts type="bar" :options="receiveOpts" emptyColor="#fff"></e-charts>
    </div>
  </section>
</template>

<script>
import overviewApi from "./overviewApi.js";
import Pie2 from "../common/Pie2.vue";
import { barFn, hunheFn } from "../echartOption";
import { provinceArr } from "@/assets/mapStatic.js";
export default {
  components: { Pie2 },
  // props: {
  //   title: {
  //     type: String,
  //     default: ''
  //   }
  // },
  computed: {
    isProvince() {
      return localStorage.isProvince == "true";
    },
    provinceName() {
      if (this.isProvince) {
        let f = provinceArr.find(el => el.name == localStorage.provinceName);
        return (f && f.nameFt) || "";
      } else {
        return "各省企业";
      }
    }
  },
  data() {
    return {
      receiveOpts: null,
      todayReport: 0,
      totalReport: 0
    };
  },
  methods: {
    getNum(n) {
      let numFt = n;
      if (n >= 10000 && n < 100000000) {
        n = (n / 10000).toFixed(2);
        numFt = n + "万";
      } else if (n >= 100000000) {
        n = (n / 100000000).toFixed(2);
        numFt = n + "亿";
      }
      return numFt;
    },
    init(p) {
      overviewApi.dateHistogram(p).then(res => {
        console.log("mid2-折线图", res);
        this.todayReport = res.todayReport;
        this.totalReport = res.totalReport;
        let xData = [];
        let yData1 = [];
        let yData2 = [];
        // 过滤空
        let d = (res.dtoList || []).filter(el => el.key);
        d = d.map((el, i) => {
          let name = el.key;
          if (el.key.length > 7) {
            name = `${el.key.slice(4, 6)}-${el.key.slice(6, 8)}`;
          } else {
            // if (p.screenTypeEnum == "THISMONTH") name = `第${Number(el.key) + 1}周`;
          }
          return {
            ...el,
            name,
            value: el.e1Num,
            value2: el.e2Num,
            value3: el.report,
            valueFt: this.getNum(el.e1Num),
            value2Ft: this.getNum(el.e2Num),
            value3Ft: this.getNum(el.report),
            total: this.getNum(el.e1Num + el.e2Num), // 只做label展示
          };
        });
        this.receiveOpts = hunheFn(null, d);
      });
    }
  }
};
</script>

<style scoped lang="scss">
.title_box2 {
  margin-top: -20px;
  .box_tit {
    color: #fff;
    font-size: 16px;
    padding: 0 40px;
    line-height: 40px;
    height: 37px;
    background: url(../common/img/tit2_bg.png) no-repeat;
    background-size: 100% 100%;
    position: relative;
    .todayft {
      position: absolute;
      right: 40px;
      top: 40px;
      color: #fff;
      font-size: 14px;
    }
    .color_blue {
      color: #51c8ff;
      font-size: 14px;
    }
    span {
      cursor: pointer;
      &.active {
        color: #fff;
      }
    }
  }
  .box_con {
    // height: 254px;
    clear: both;
  }
}
</style>
