<template>
  <el-form
    ref="form"
    v-loading="loading"
    class="public-form"
    :label-position="labelPosition"
    :model="formData"
    :rules="rules"
    :label-width="labelWidth"
    :size="formSize"
    :disabled="disabled"
  >
    <section
      v-for="(items, indexs) in formDataLabel"
      :key="indexs"
      class="columns"
    >
      <el-form-item
        v-for="(item, index) in items"
        :key="index"
        :label="item.label"
        :prop="
          rulesHandle(item.specialRules) &&
          rulesHandle(item.specialRules).length > 0
            ? item.val
            : item.rules
        "
        :width="item.width"
        :label-width="item.labelWidth"
        :rules="rulesHandle(item.specialRules, item.ruleMsg, item.label)"
        class="column"
        :class="item.className"
      >
        <!-- input输入框 -->
        <el-input
          v-if="item.type === 'input'"
          v-model="formData[item.val]"
          :placeholder="item.placeholder || `请输入${item.label}`"
          :disabled="disableMethod(item)"
          :clearable="item.clearable || true"
          :size="item.size"
          :maxlength="item.maxlength"
          :show-word-limit="item.limit"
          :show-password="item.password"
          @input="throwEvent(item.funName, formData[item.val])"
        />
        <!-- input输入框，带按钮 -->
        <el-input
          v-else-if="item.type === 'input-btn'"
          v-model="formData[item.val]"
          :placeholder="item.placeholder || `请输入${item.label}`"
          :clearable="item.clearable || true"
          :disabled="disableMethod(item)"
          :size="item.size"
        >
          <el-button
            slot="append"
            :size="item.btnSize"
            @click="throwEvent(item.funName, formData[item.val])"
          >
            {{ item.txt }}
          </el-button>
        </el-input>
        <!-- input输入框-slot -->
        <el-input
          v-else-if="item.type === 'input-slot'"
          v-model="formData[item.val]"
          :placeholder="item.placeholder || `请输入${item.label}`"
          :disabled="disableMethod(item)"
          :clearable="item.clearable || true"
          :size="item.size"
          :maxlength="item.maxlength"
          :show-word-limit="item.limit"
          :show-password="item.password"
          @input="throwEvent(item.funName, formData[item.val])"
        >
          <template :slot="item.slot || 'prepend'">
            {{ item.slotName }}
          </template>
        </el-input>
        <!-- input输入框-slot-num -->
        <el-input
          v-else-if="item.type === 'input-slot-num'"
          v-model.number="formData[item.val]"
          :placeholder="item.placeholder || `请输入${item.label}`"
          :clearable="item.clearable || true"
          :disabled="disableMethod(item)"
          :size="item.size"
          :min="item.min"
          :max="item.maxNum"
          :on-keypress="item.keypress || null"
          :maxlength="item.maxlength"
          :show-word-limit="item.limit"
          oninput="value=value.replace(/[^0-9.]/g,'')"
          @paste.native.capture.prevent="() => {}"
          @blur="blurFun($event.target.value, item.val)"
          @change="throwEvent(item.funName, formData[item.val])"
        >
          <template :slot="item.slot || 'prepend'">
            {{ item.slotName }}
          </template>
        </el-input>
        <!-- input输入框number类型 -->
        <el-input
          v-else-if="item.type === 'input-num'"
          v-model.number="formData[item.val]"
          :placeholder="item.placeholder || `请输入${item.label}`"
          :clearable="item.clearable || true"
          :disabled="disableMethod(item)"
          :size="item.size"
          :min="item.min"
          :max="item.maxNum"
          :maxlength="item.maxlength"
          :show-word-limit="item.limit"
          :on-keypress="item.keypress || null"
          oninput="value=value.replace(/[^0-9.]/g,'')"
          @paste.native.capture.prevent="() => {}"
          @blur="blurFun($event.target.value, item.val)"
          @change="throwEvent(item.funName, formData[item.val])"
        />
        <!-- input输入框number类型 -->
        <el-input-number
          v-else-if="item.type === 'input-number'"
          v-model.number="formData[item.val]"
          :placeholder="item.placeholder || `请输入${item.label}`"
          :disabled="disableMethod(item)"
          :size="item.size"
          :min="item.min"
          :max="item.maxNum"
          :maxlength="item.maxlength"
          :controls="item.controls"
          oninput="value=value.replace(/[^0-9.]/g,'')"
          @paste.native.capture.prevent="() => {}"
          @blur="blurFun($event.target.value, item.val)"
          @change="throwEvent(item.funName, formData[item.val])"
        />
        <!-- 文本域 -->
        <el-input
          v-else-if="item.type === 'textarea'"
          v-model="formData[item.val]"
          :placeholder="item.placeholder || `请选择${item.label}`"
          :clearable="item.clearable || true"
          :disabled="disableMethod(item)"
          type="textarea"
          :maxlength="item.maxlength"
          :show-word-limit="item.limit"
          :size="item.size"
          :autosize="item.autosize"
          @change="throwEvent(item.funName, formData[item.val])"
        />
        <!-- 单选select -->
        <el-select
          v-else-if="item.type === 'select'"
          v-model="formData[item.val]"
          :placeholder="item.placeholder || `请选择${item.label}`"
          :clearable="item.clearable || true"
          :disabled="disableMethod(item)"
          :allow-create="item.allowCreate"
          :filterable="item.allowCreate || item.filterable"
          :default-first-option="item.defaultFirstOption"
          :size="item.size"
          @change="throwEvent(item.funName, formData[item.val])"
        >
          <el-option
            v-for="(opt, key) in options[item.options] || []"
            :key="key"
            :label="opt.label"
            :value="opt.val"
          />
        </el-select>
        <!-- 复选select -->
        <el-select
          v-else-if="item.type === 'check-select'"
          v-model="formData[item.val]"
          :placeholder="item.placeholder || `请选择${item.label}`"
          :clearable="item.clearable || true"
          :disabled="disableMethod(item)"
          :size="item.size"
          :multiple-limit="item.limit || 0"
          :allow-create="item.allowCreate"
          :filterable="item.allowCreate || item.filterable"
          :default-first-option="item.defaultFirstOption"
          multiple
          @change="throwEvent(item.funName, formData[item.val])"
        >
          <el-option
            v-for="(opt, key) in options[item.options] || []"
            :key="key"
            :label="opt.label"
            :value="opt.val"
          />
        </el-select>
        <!-- 日期选择器 -->
        <el-date-picker
          v-else-if="item.type === 'date-picker'"
          v-model="formData[item.val]"
          :placeholder="item.placeholder || '请选择日期'"
          :clearable="item.clearable || true"
          :disabled="disableMethod(item)"
          :value-format="item.format || 'yyyy-MM-dd'"
          type="date"
          :size="item.size"
          @change="throwEvent(item.funName, formData[item.val])"
        />
        <!-- 时间日期选择器 -->
        <el-date-picker
          v-else-if="item.type === 'date-time'"
          v-model="formData[item.val]"
          :placeholder="item.placeholder || '请选择日期时间'"
          :clearable="item.clearable || true"
          :disabled="disableMethod(item)"
          :size="item.size"
          :value-format="item.format || 'yyyy-MM-dd HH:mm:ss'"
          type="datetime"
          @change="throwEvent(item.funName, formData[item.val])"
        />
        <!-- 单选框 -->
        <el-radio-group
          v-else-if="item.type === 'radio-group'"
          v-model="formData[item.val]"
          size="mini"
          @change="throwEvent(item.funName || '', formData[item.val])"
        >
          <div v-if="item.radioType === 'button'">
            <el-radio-button
              v-for="(element, key) in options[item.options] || []"
              :key="key"
              :label="element.val"
            >
              {{ element.label }}
            </el-radio-button>
          </div>
          <div v-else-if="item.radioType === 'border'">
            <el-radio
              v-for="(element, key) in options[item.options]"
              :key="key"
              :label="element.val"
              border
            >
              {{ element.label }}
            </el-radio>
          </div>
          <div v-else>
            <el-radio
              v-for="(element, key) in options[item.options] || []"
              :key="key"
              :label="element.val"
            >
              {{ element.label }}
            </el-radio>
          </div>
        </el-radio-group>
        <!-- 复选框 -->
        <el-checkbox-group
          v-else-if="item.type === 'checkbox'"
          v-model="formData[item.val]"
          @change="throwEvent(item.funName, formData[item.val])"
        >
          <div v-if="item.checkboxType === 'button'">
            <el-checkbox-button
              v-for="(element, key) in options[item.options] || []"
              :key="key"
              :label="element.val"
              :disabled="element.disabled"
            >
              {{ element.label }}
            </el-checkbox-button>
          </div>
          <div v-else-if="item.checkboxType === 'border'">
            <el-checkbox
              v-for="(element, key) in options[item.options] || []"
              :key="key"
              :label="element.val"
              :disabled="element.disabled"
              border
            >
              {{ element.label }}
            </el-checkbox>
          </div>
          <div v-else>
            <el-checkbox
              v-for="(element, key) in options[item.options] || []"
              :key="key"
              :label="element.val"
              :disabled="element.disabled"
            >
              {{ element.label }}
            </el-checkbox>
          </div>
        </el-checkbox-group>
        <!-- 单张图片显示 -->
        <img v-else-if="item.type === 'img'" :src="formData[item.val]" />
        <!-- 单独按钮 -->
        <el-button
          v-else-if="item.type === 'btn'"
          :size="item.size"
          :disabled="disabledList.includes(item.disabledName)"
          :class="item.paClassName"
          :type="item.btnType || 'primary'"
          @click="throwEvent(item.funName)"
        >
          {{ item.btnTxt }}
        </el-button>
        <!-- 日期范围选择器 -->
        <el-date-picker
          v-else-if="item.type === 'date-picker-range'"
          v-model="formData[item.val]"
          :placeholder="item.placeholder || '请选择日期'"
          :clearable="item.clearable || true"
          :disabled="disableMethod(item)"
          :size="item.size"
          :value-format="item.format || 'yyyy-MM-dd'"
          type="daterange"
          :range-separator="item.range || '至'"
          :start-placeholder="item.startP || '开始日期'"
          :end-placeholder="item.endP || '结束日期'"
          @change="throwEvent(item.funName, formData[item.val])"
        />
        <el-date-picker
          v-else-if="item.type === 'date-time-range'"
          v-model="formData[item.val]"
          :disabled="disableMethod(item)"
          :clearable="item.clearable || true"
          :size="item.size"
          :value-format="item.format || 'yyyy-MM-dd HH:mm:ss'"
          type="datetimerange"
          :range-separator="item.range || '至'"
          :start-placeholder="item.startP || '开始日期'"
          :end-placeholder="item.endP || '结束日期'"
          :picker-options="item.pickerOptions || {}"
          :default-time="item.defaultTime || []"
          @change="throwEvent(item.funName, formData[item.val])"
        />
        <!-- 级联选择器 -->
        <el-cascader
          v-else-if="item.type === 'cascader'"
          v-model="formData[item.val]"
          :clearable="item.clearable || true"
          :options="options[item.options] || []"
          :props="{ label: 'label', value: 'val' }"
          :disabled="disableMethod(item)"
          @change="throwEvent(item.funName, formData[item.val])"
        />
        <!-- 开关 -->
        <el-switch
          v-else-if="item.type === 'switch'"
          v-model="formData[item.val]"
          :active-color="item.activeColor"
          :inactive-color="item.inactiveColor"
          :active-value="item.active || true"
          :inactive-value="item.inActive || false"
          :disabled="disableMethod(item)"
          @change="throwEvent(item.funName, formData[item.val])"
        />
        <!-- 时间范围选择 -->
        <shortcuts-time-picker
          v-else-if="item.type === 'shortcuts-time-picker'"
          v-model="formData[item.val]"
          :placeholder="item.placeholder"
          :filter-range="item.filterRange"
          :default-value="item.defaultValue"
          :value-format="item.valueFormat"
          :disabled="disableMethod(item)"
          @change="throwEvent(item.funName, formData[item.val])"
        />
        <!-- btnList -->
        <btnList
          v-else-if="item.type === 'btn-list'"
          :btn-list="item.btnList"
          :pa-class-name="item.paClassName || 'is-pl-5'"
          :disabled-list="disabledList"
          @callbackEvent="throwEvent($event.funName)"
        />
        <!-- 字典下拉 select -->
        <dic-select
          v-else-if="item.type === 'dic-select'"
          v-model="formData[item.val]"
          :enum-key="item.enumKey"
          :allow-create="item.allowCreate"
          :filterable="item.allowCreate || item.filterable"
          :default-first-option="item.defaultFirstOption"
          :clearable="item.clearable || true"
          :multiple="item.multiple"
          :placeholder="item.placeholder || `请选择${item.label}`"
          :disabled="disableMethod(item)"
          :showAll="item.showAll"
          @change="throwEvent(item.funName, formData[item.val])"
        />
        <!-- 字典下拉 层级选择器 cascader -->
        <dic-cascader
          v-else-if="item.type === 'dic-cascader'"
          v-model="formData[item.val]"
          :enum-key="item.enumKey"
          :multiple="item.multiple"
          :show-all-levels="item.showAllLevels"
          :check-strictly="item.checkStrictly"
          :clearable="item.clearable || true"
          :placeholder="item.placeholder || `请选择${item.label}`"
          :disabled="disableMethod(item)"
          @change="throwEvent(item.funName, formData[item.val])"
        />
        <!-- 文件上传 -->
        <uploadFile
          v-else-if="item.type === 'rich-file'"
          v-model="formData[item.val]"
          :is-preview="item.isPreview"
          :accept="item.accept"
          :biz-type="item.bizType"
          :disabled="disableMethod(item)"
          :validation-format="item.validationFormat"
          :limit="item.limit"
          @change="throwEvent(item.funName, formData[item.val])"
        />
        <!-- 需要过滤转换的数据 -->
        <dic-value
          v-else-if="item.type === 'dic-val'"
          :dic-key="formData[item.val]"
          :enum-key="item.enumKey"
          :default-value="formData[item.val]"
        />
        <!-- slot -->
        <slot
          v-else-if="item.type === 'slot'"
          :name="item.slotName"
          :data="{ ...item, disabled: disableMethod(item) }"
        />
        <!-- 文字 -->
        <span v-else :class="item.paClassName">
          {{ formData[item.val] }}
        </span>
      </el-form-item>
    </section>
  </el-form>
</template>

<script>
import { rulesConfig } from './rules'
export default {
  name: 'PublicForm',
  components: {
    btnList: () => import('../btn-list'),
    'shortcuts-time-picker': () => import('./shortcuts-time-picker')
  },
  props: {
    labelPosition: {
      type: String,
      default: () => 'right'
    },
    formDataLabel: {
      type: Array,
      default: () => []
    },
    formData: {
      type: Object,
      default: () => {}
    },
    options: {
      type: Object,
      default: () => {}
    },
    rules: {
      type: Object,
      default: () => {}
    },
    labelWidth: {
      type: String,
      default: () => '100px'
    },
    formSize: {
      type: String,
      default: () => ''
    },
    loading: {
      type: Boolean,
      default: () => false
    },
    disabledList: {
      type: Array,
      default: () => []
    },
    disabledItemList: {
      type: Array,
      default: () => []
    },
    disabled: {
      type: Boolean,
      default: () => false
    }
  },
  data() {
    return {}
  },
  computed: {
    disableMethod() {
      return item => {
        return (
          this.disabledItemList.includes(item.disabledName) ||
          item.disabled ||
          false
        )
      }
    }
  },
  mounted() {},
  methods: {
    // 解决正则校验数字类型后，双绑失效
    blurFun(e, key) {
      this.$set(this.formData, key, e)
    },
    // 自定义校验
    rulesHandle(item, ruleMsg, label) {
      if (item) {
        let param = item.split(',')
        let rulesList = []
        param.map(e => {
          if (rulesConfig[e](ruleMsg))
            rulesList.push(rulesConfig[e](ruleMsg, label))
        })
        return rulesList
      }
    },
    // 值改变回调
    throwEvent(funName, val = null) {
      this.$emit('throwEvent', { funName, val })
    }
  }
}
</script>

<style lang="scss">
.public-form {
  .el-cascader {
    width: 100%;
  }
  .el-select.el-input.el-date-editor.el-input.el-cascader,
  .el-input-number {
    width: 100%;
  }
  .el-select,
  .el-input__inner {
    width: 100%;
  }
  .el-date-editor.el-input,
  .el-date-editor.el-input__inner {
    width: 100%;
  }
  .el-select.el-input.el-date-editor.el-input.el-cascader,
  .el-date-editor.el-input__inner {
    width: 100%;
  }
}
</style>
