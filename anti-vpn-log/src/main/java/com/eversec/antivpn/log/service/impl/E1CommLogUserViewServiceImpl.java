package com.eversec.antivpn.log.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.eversec.antivpn.emnus.ScreenTypeEnum;
import com.eversec.antivpn.log.api.dto.base.AggCountDTO;
import com.eversec.antivpn.log.constants.CommonField;
import com.eversec.antivpn.log.entity.E1CommLog;
import com.eversec.antivpn.log.entity.E1CommLogUserView;
import com.eversec.antivpn.log.mapper.E1CommLogMapper;
import com.eversec.antivpn.log.mapper.E1CommLogUserViewMapper;
import com.eversec.antivpn.log.mapper.vo.BaseSearchVO;
import com.eversec.antivpn.log.service.IE1CommLogUserViewService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eversec.antivpn.log.util.CKPartitionUtil;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 *  E1CommLogUserView服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-18
 */
@Service
@Slf4j
public class E1CommLogUserViewServiceImpl extends ServiceImpl<E1CommLogUserViewMapper, E1CommLogUserView> implements IE1CommLogUserViewService {
    @Resource
    private E1CommLogUserViewMapper e1CommLogUserViewMapper;

    @Override
    public AggCountDTO countNum(ScreenTypeEnum screenTypeEnum) {

        AggCountDTO result = new AggCountDTO();
        BaseSearchVO baseSearchVO = new BaseSearchVO();
        baseSearchVO.setTodayTimePartition(ScreenTypeEnum.getTimeCKPartition(ScreenTypeEnum.TODAY));
        Long todayCount = e1CommLogUserViewMapper.countNum(baseSearchVO);
        result.setTodayCount(todayCount);
        if (screenTypeEnum == ScreenTypeEnum.TODAY) {
            result.setDateRangeCount(todayCount);
        } else {
            baseSearchVO.clear();
            baseSearchVO.setScreenTypeEnumCode(screenTypeEnum.getCode());
            baseSearchVO.setStartTimePartition(ScreenTypeEnum.getTimeCKPartition(screenTypeEnum));
            Long dateRangeCount = e1CommLogUserViewMapper.countNum(baseSearchVO);
            result.setDateRangeCount(dateRangeCount);
        }
        return result;

    }

    @Override
    public List<E1CommLogUserView> getE1LogsByPartition(ScreenTypeEnum screenTypeEnum) {
        BaseSearchVO baseSearchVO = CKPartitionUtil.setScreenTimeByBaseSearchVO(screenTypeEnum);
        List<E1CommLogUserView>  e1CommLogs = e1CommLogUserViewMapper.selectListByPartition(baseSearchVO);
        return e1CommLogs;

    }

    @Override
    public List<String> getE1LogBySoftWareByPartition(ScreenTypeEnum screenTypeEnum) {
        BaseSearchVO baseSearchVO = CKPartitionUtil.setScreenTimeByBaseSearchVO(screenTypeEnum);
        List<String>  vpnSoftwareCodes = e1CommLogUserViewMapper.selectSoftwareCodeByPartition(baseSearchVO);
        return vpnSoftwareCodes;
    }

    @Override
    public List<E1CommLogUserView> getLogNumBySofaWareTop10(ScreenTypeEnum screenTypeEnum) {
        BaseSearchVO baseSearchVO = CKPartitionUtil.setScreenTimeByBaseSearchVO(screenTypeEnum);
        List<E1CommLogUserView> e1CommLogs = e1CommLogUserViewMapper.getLogNumBySofaWareTop10(baseSearchVO);
        return e1CommLogs;
    }

    @Override
    public List<E1CommLogUserView> getLogNumByUserTop10(ScreenTypeEnum screenTypeEnum) {

        BaseSearchVO baseSearchVO = CKPartitionUtil.setScreenTimeByBaseSearchVO(screenTypeEnum);
        List<E1CommLogUserView> e1CommLogs = e1CommLogUserViewMapper.getLogNumByUserTop10(baseSearchVO);
        return e1CommLogs;
    }

    @Override
    public List<E1CommLogUserView> getLogNumBySofaWareAndDayTop10(ScreenTypeEnum screenTypeEnum) {
        BaseSearchVO baseSearchVO = CKPartitionUtil.setScreenTimeByBaseSearchVO(screenTypeEnum);
        List<E1CommLogUserView> e1CommLogs = e1CommLogUserViewMapper.getLogNumBySofaWareAndDayTop10(baseSearchVO);
        return e1CommLogs;
    }

}
