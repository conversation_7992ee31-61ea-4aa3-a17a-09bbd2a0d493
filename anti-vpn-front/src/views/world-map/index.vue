<template>
  <section class="world-map1">
    <div class="title">
      {{ title }}
    </div>
    <MapWorld ref="mapRef" />
  </section>
</template>

<script>
import MapWorld from './MapWorld.vue'

export default {
  name: 'MapWorldNote',
  components: { MapWorld },
  data() {
    return {
      // title: 'vpngate.net'
      // title: 'wanshanziwo.eu.org'
      // title: '悠兔'
      title: '跨境通道分布统计'
    }
  },
  mounted() {
    let data = [
      {
        name: '韩国',
        value: 10401
      },
      {
        name: '日本',
        value: 9209
      },
      // {
      //   name: '保留地址',
      //   value: 6077
      // },
      {
        name: '美国',
        value: 4243
      },
      {
        name: '德国',
        value: 3143
      },
      {
        name: '墨西哥',
        value: 3134
      },
      {
        name: '澳大利亚',
        value: 3090
      },
      // {
      //   name: '突尼斯',
      //   value: 3050
      // },
      {
        name: '爱尔兰',
        value: 3045
      },
      {
        name: '比利时',
        value: 3041
      },
      {
        name: '泰国',
        value: 2309
      },
      {
        name: '俄罗斯',
        value: 1584
      },
      {
        name: '越南',
        value: 1357
      },
      {
        name: '印度',
        value: 204
      },
      {
        name: '阿根廷',
        value: 170
      },
      {
        name: '中国',
        value: 160
      },
      {
        name: '印度尼西亚',
        value: 160
      },
      {
        name: '加拿大',
        value: 134
      },
      {
        name: '巴西',
        value: 127
      },
      {
        name: '柬埔寨',
        value: 107
      },
      {
        name: '乌克兰',
        value: 91
      },
      // {
      //   name: 'CLOUDFLARE.COM',
      //   value: 88
      // },
      {
        name: '英国',
        value: 40
      },
      {
        name: '匈牙利',
        value: 40
      },
      {
        name: '厄瓜多尔',
        value: 37
      },
      // {
      //   name: '亚太地区',
      //   value: 36
      // },
      {
        name: '波兰',
        value: 31
      },
      {
        name: '智利',
        value: 28
      },
      {
        name: '哥伦比亚',
        value: 27
      },
      {
        name: '捷克',
        value: 26
      },
      {
        name: '土耳其',
        value: 25
      },
      {
        name: '新加坡',
        value: 21
      },
      {
        name: '菲律宾',
        value: 21
      },
      // {
      //   name: '欧洲地区',
      //   value: 21
      // },

      {
        name: '阿联酋',
        value: 19
      },
      {
        name: '荷兰',
        value: 19
      },
      {
        name: '瑞典',
        value: 18
      },
      {
        name: '沙特阿拉伯',
        value: 18
      },
      {
        name: '保加利亚',
        value: 17
      },
      {
        name: '克罗地亚',
        value: 17
      },
      {
        name: '西班牙',
        value: 15
      },
      {
        name: '白俄罗斯',
        value: 14
      },
      {
        name: '阿尔及利亚',
        value: 14
      },
      {
        name: '哈萨克斯坦',
        value: 13
      },
      {
        name: '法国',
        value: 13
      },
      // {
      //   name: '孟加拉',
      //   value: 11
      // },
      {
        name: '马来西亚',
        value: 11
      },
      {
        name: '奥地利',
        value: 10
      },
      {
        name: '拉脱维亚',
        value: 10
      },
      {
        name: '芬兰',
        value: 10
      },
      {
        name: '葡萄牙',
        value: 10
      },
      {
        name: '罗马尼亚',
        value: 9
      },
      {
        name: '埃及',
        value: 9
      },
      // {
      //   name: '塞尔维亚',
      //   value: 8
      // },
      {
        name: '丹麦',
        value: 8
      },
      {
        name: '秘鲁',
        value: 8
      },
      {
        name: '摩尔多瓦',
        value: 7
      },
      {
        name: '立陶宛',
        value: 7
      },
      {
        name: '乌兹别克斯坦',
        value: 6
      },
      {
        name: '蒙古',
        value: 6
      },
      {
        name: '巴基斯坦',
        value: 6
      },
      {
        name: '老挝',
        value: 6
      },
      {
        name: '意大利',
        value: 6
      },
      {
        name: '委内瑞拉',
        value: 6
      },
      {
        name: '伊朗',
        value: 5
      },
      {
        name: '瑞士',
        value: 4
      },
      {
        name: '约旦',
        value: 4
      },
      {
        name: '爱沙尼亚',
        value: 4
      },
      {
        name: '希腊',
        value: 4
      },
      {
        name: '新西兰',
        value: 4
      },
      {
        name: '挪威',
        value: 4
      },
      {
        name: '亚美尼亚',
        value: 3
      },
      {
        name: '伊拉克',
        value: 2
      },
      {
        name: '卡塔尔',
        value: 2
      },
      // {
      //   name: '非洲地区',
      //   value: 2
      // },
      {
        name: '斯里兰卡',
        value: 2
      },
      {
        name: '斯洛伐克',
        value: 2
      },
      {
        name: '巴拉圭',
        value: 2
      },
      {
        name: '加纳',
        value: 2
      },
      // {
      //   name: '关岛',
      //   value: 2
      // },
      {
        name: '南非',
        value: 1
      },
      {
        name: '哥斯达黎加',
        value: 1
      },
      {
        name: '阿塞拜疆',
        value: 1
      },
      {
        name: '缅甸',
        value: 1
      },
      // {
      //   name: '特立尼达和多巴哥',
      //   value: 1
      // },
      {
        name: '尼日利亚',
        value: 1
      },
      {
        name: '萨尔瓦多',
        value: 1
      }
    ]
    let data1 = [
      {
        name: '美国',
        value: 5534
      },
      {
        name: '中国',
        value: 4614
      },
      {
        name: '日本',
        value: 2103
      },
      {
        name: '新加坡',
        value: 1640
      },
      {
        name: '德国',
        value: 793
      },
      {
        name: '澳大利亚',
        value: 742
      },
      {
        name: '英国',
        value: 679
      },
      {
        name: '荷兰',
        value: 654
      },
      {
        name: '俄罗斯',
        value: 630
      },
      // {
      //   name: '亚太地区',
      //   value: 380
      // },
      {
        name: '以色列',
        value: 284
      },
      {
        name: '加拿大',
        value: 252
      },
      {
        name: '韩国',
        value: 222
      },
      {
        name: '法国',
        value: 199
      },
      {
        name: '亚美尼亚',
        value: 190
      },
      {
        name: '菲律宾',
        value: 122
      },
      {
        name: '保加利亚',
        value: 118
      },
      {
        name: '立陶宛',
        value: 113
      },
      {
        name: '印度',
        value: 94
      },
      {
        name: '巴西',
        value: 84
      },
      {
        name: '爱尔兰',
        value: 73
      },
      {
        name: '乌克兰',
        value: 54
      },
      {
        name: '卢森堡',
        value: 42
      },
      {
        name: '希腊',
        value: 41
      },
      {
        name: '丹麦',
        value: 41
      },
      {
        name: '瑞士',
        value: 36
      },
      {
        name: '瑞典',
        value: 28
      },
      {
        name: '西班牙',
        value: 25
      },
      {
        name: '南非',
        value: 24
      },
      {
        name: '斯洛伐克',
        value: 18
      },
      {
        name: '意大利',
        value: 16
      },
      {
        name: '摩尔多瓦',
        value: 13
      },
      {
        name: '越南',
        value: 10
      },
      {
        name: '马来西亚',
        value: 9
      },
      {
        name: '波兰',
        value: 9
      },
      {
        name: '挪威',
        value: 8
      },
      {
        name: '比利时',
        value: 8
      },
      {
        name: '土耳其',
        value: 7
      },
      {
        name: '伊朗',
        value: 6
      },
      {
        name: '罗马尼亚',
        value: 5
      },
      {
        name: '阿联酋',
        value: 4
      },
      {
        name: '爱沙尼亚',
        value: 4
      },
      {
        name: '哥斯达黎加',
        value: 3
      },
      // {
      //   name: '拉美地区',
      //   value: 3
      // },
      {
        name: '阿根廷',
        value: 3
      },
      {
        name: '奥地利',
        value: 3
      },
      {
        name: '埃及',
        value: 3
      },
      {
        name: '捷克',
        value: 3
      },
      // {
      //   name: '马恩岛',
      //   value: 3
      // },
      {
        name: '冰岛',
        value: 3
      },
      // {
      //   name: '局域网',
      //   value: 2
      // },
      {
        name: '葡萄牙',
        value: 2
      },
      {
        name: '利比亚',
        value: 2
      },
      // {
      //   name: '孟加拉',
      //   value: 2
      // },
      {
        name: '印度尼西亚',
        value: 2
      },
      {
        name: '泰国',
        value: 2
      },
      {
        name: '芬兰',
        value: 2
      },
      {
        name: '智利',
        value: 1
      },
      {
        name: '墨西哥',
        value: 1
      },
      {
        name: '乌拉圭',
        value: 1
      },
      {
        name: '阿曼',
        value: 1
      },
      {
        name: '巴拿马',
        value: 1
      },
      {
        name: '拉脱维亚',
        value: 1
      },
      // {
      //   name: '欧洲地区',
      //   value: 1
      // },
      {
        name: '哥伦比亚',
        value: 1
      },
      {
        name: '伊拉克',
        value: 1
      },
      {
        name: '斯洛文尼亚',
        value: 1
      },
      // {
      //   name: '塞尔维亚',
      //   value: 1
      // },
      {
        name: '加蓬',
        value: 1
      }
    ]
    let data2 = [
      {
        name: '美国',
        value: 744
      },
      {
        name: '加拿大',
        value: 80
      },
      {
        name: '俄罗斯',
        value: 39
      },
      {
        name: '中国',
        value: 20
      }
    ]
    let data3 = [
      {
        name: '美国',
        value: 7373
      },
      {
        name: '中国',
        value: 365
      },
      {
        name: '日本',
        value: 221
      },
      // {
      //   name: '亚太地区',
      //   value: 198
      // },
      {
        name: '韩国',
        value: 155
      },
      {
        name: '德国',
        value: 110
      },
      {
        name: '新加坡',
        value: 93
      },
      {
        name: '英国',
        value: 61
      },
      {
        name: '澳大利亚',
        value: 42
      },
      {
        name: '法国',
        value: 40
      },
      {
        name: '巴西',
        value: 30
      },
      {
        name: '荷兰',
        value: 28
      },
      {
        name: '俄罗斯',
        value: 28
      },
      {
        name: '菲律宾',
        value: 26
      },
      {
        name: '加拿大',
        value: 17
      },
      {
        name: '乌克兰',
        value: 13
      },
      {
        name: '爱尔兰',
        value: 12
      },
      {
        name: '土耳其',
        value: 8
      },
      {
        name: '越南',
        value: 6
      },
      {
        name: '印度',
        value: 6
      },
      {
        name: '瑞典',
        value: 6
      },
      {
        name: '波兰',
        value: 5
      },
      {
        name: '比利时',
        value: 5
      },
      {
        name: '丹麦',
        value: 4
      },
      {
        name: '意大利',
        value: 3
      },
      {
        name: '伊朗',
        value: 3
      },
      {
        name: '泰国',
        value: 3
      },
      {
        name: '斯洛伐克',
        value: 3
      },
      {
        name: '摩尔多瓦',
        value: 3
      },
      {
        name: '马来西亚',
        value: 3
      },
      {
        name: '罗马尼亚',
        value: 3
      },
      {
        name: '立陶宛',
        value: 3
      },
      {
        name: '捷克',
        value: 3
      },
      {
        name: '保加利亚',
        value: 3
      },
      {
        name: '印度尼西亚',
        value: 2
      },
      {
        name: '希腊',
        value: 2
      },
      {
        name: '西班牙',
        value: 2
      },
      // {
      //   name: '塞尔维亚',
      //   value: 2
      // },
      {
        name: '瑞士',
        value: 2
      },
      {
        name: '南非',
        value: 2
      },
      {
        name: '墨西哥',
        value: 2
      },
      // {
      //   name: '马恩岛',
      //   value: 2
      // },
      {
        name: '柬埔寨',
        value: 2
      },
      // {
      //   name: '保留地址',
      //   value: 2
      // },
      {
        name: '奥地利',
        value: 2
      },
      {
        name: '爱沙尼亚',
        value: 2
      },
      {
        name: '智利',
        value: 1
      },
      {
        name: '以色列',
        value: 1
      },
      {
        name: '匈牙利',
        value: 1
      },
      {
        name: '突尼斯',
        value: 1
      },
      {
        name: '斯洛文尼亚',
        value: 1
      },
      {
        name: '塞浦路斯',
        value: 1
      },
      {
        name: '葡萄牙',
        value: 1
      },
      {
        name: '挪威',
        value: 1
      },
      {
        name: '卢森堡',
        value: 1
      },
      {
        name: '肯尼亚',
        value: 1
      },
      {
        name: '加蓬',
        value: 1
      },
      // {
      //   name: '共享地址',
      //   value: 1
      // },
      {
        name: '芬兰',
        value: 1
      },
      {
        name: '厄瓜多尔',
        value: 1
      },
      {
        name: '波斯尼亚和黑塞哥维那',
        value: 1
      },
      {
        name: '冰岛',
        value: 1
      },
      {
        name: '阿塞拜疆',
        value: 1
      },
      {
        name: '阿根廷',
        value: 1
      },
      {
        name: '阿富汗',
        value: 1
      },
      {
        name: '阿尔巴尼亚',
        value: 1
      }
    ]
    // this.$refs.mapRef.drawMap(data)
    // this.$refs.mapRef.drawMap(data1)
    // this.$refs.mapRef.drawMap(data2)
    this.$refs.mapRef.drawMap(data3)
  },
  methods: {}
}
</script>

<style lang="scss" scoped>
.world-map1 {
  width: 1440px;
  height: 870px;
  background: url('./img/bigscreenbg.png') no-repeat;
  background-size: 100% 100%;
  .title {
    color: #fff;
    font-size: 20px;
    font-weight: 700;
    margin: 0 auto;
    text-align: center;
    padding-top: 20px;
  }
}
</style>
