package test;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import com.eversec.stark.generic.common.infra.mybatis.plus.SnowflakeIdentifierGenerator;
import org.junit.Test;

/**
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023-07-26 10:18
 */
public class IdUtilTest {

	@Test
	public void getId() throws InterruptedException {
		System.out.println(IdUtil.getSnowflake().getGenerateDateTime(IdUtil.getSnowflakeNextId()));
		Snowflake snowflake = IdUtil.getSnowflake();
		System.out.println(snowflake.getWorkerId(IdUtil.getSnowflakeNextId()));
		System.out.println(snowflake.getDataCenterId(IdUtil.getSnowflakeNextId()));
		System.out.println(snowflake.nextId());
		System.out.println(snowflake.nextId());
		System.out.println(snowflake.nextId());
		System.out.println(snowflake.nextId());
		System.out.println(snowflake.nextId());
		Thread.sleep(1000);
		System.out.println(snowflake.nextId());
		System.out.println(snowflake.nextId());
		System.out.println(snowflake.nextId());
		System.out.println(snowflake.nextId());
	}

}
