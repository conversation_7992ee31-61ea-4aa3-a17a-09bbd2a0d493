package com.eversec.antivpn.log.entity.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.eversec.stark.generic.common.infra.mybatis.plus.BaseDO;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 * <p>
 * 持久化对象，此对象与数据库属性一一对应，被Entity对象继承，扩展属性写在Entity对象中
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-08
 */
@Getter
@Setter
@TableName("e4_dns_log")
@Schema(name = "E4DnsLogPO", description = "")
public class E4DnsLogPO {

    private static final long serialVersionUID = 1L;

    private String version;

    private String comCode;

    private Integer networkBusinessId;

    private Long logId;

    private Long houseId;

    private Long logProvinceId;

    private Long logCityId;

    private Long logCountyId;

    private Integer ipType;

    private String srcIp;

    private Integer srcPort;

    private String dnsServerIp;

    private Integer dnsPort;

    private Integer transactionId;

    private Integer aa;

    private Integer truncation;

    private String domain;

    private String type;

    private Integer replyCode;

    private Integer answersCount;

    private String responseValue;

    private Long trafficType;

    private LocalDateTime accessTime;

    private LocalDateTime timestamp;

    private Long day;

    private Integer hour;
}