<template>
  <span>
    <el-dropdown trigger="click" @command="handleCommand">
      <span class="el-dropdown-link">
        <span>{{ userName }}</span>
        <i class="el-icon-arrow-down el-icon--right" />
      </span>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item
          v-if="globalConfig.showChangePassword"
          command="changePassword"
        >
          修改密码
        </el-dropdown-item>
        <el-dropdown-item
          v-if="globalConfig.showChangeMenuMode"
          command="switchSiteMenu"
        >
          {{ theme.siteMenuMode == 'vertical' ? '顶部菜单' : '左侧菜单' }}
        </el-dropdown-item>
        <el-dropdown-item
          v-if="globalConfig.showLockScreen"
          command="lockScreen"
        >
          锁屏
        </el-dropdown-item>
        <el-dropdown-item
          v-if="globalConfig.showVersionInfo && version"
          command="about"
        >
          版本说明
        </el-dropdown-item>
        <el-dropdown-item divided command="operatorInfo">
          平台配置信息
        </el-dropdown-item>
        <el-dropdown-item command="logout" divided style="color: red">
          退出
        </el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
    <!-- 弹窗, 修改密码 -->
    <change-password
      ref="changePasswordForce"
      :closable="false"
      @onLogout="handleCommand('logout')"
    />
    <change-password ref="changePassword" />
    <about ref="about" />
  </span>
</template>

<script>
import { mapState, mapMutations, mapGetters } from 'vuex'
import api_auth from '@/api/auth'
import { clearLoginInfo } from '@/utils/base'
import { dispatchResize } from '@/utils/screen'
import ChangePassword from './change-password'
import About from './../about'
import antiVpnService from '@/api/antiVpnService'

export default {
  components: {
    ChangePassword,
    About
  },
  data() {
    return {
      globalConfig: window.globalConfig,
      id: null,
      userName: null,
      alertShowing: false,
      alertMessage: null,
      version: localStorage.getItem('currentVersion'),
      comCode: null,
      provinceId: null,
      systemCode: null
    }
  },
  computed: {
    ...mapState(['theme']),
    ...mapGetters('sys-dict', ['dictionaryData']),
    getValue() {
      return (key, val) => {
        if (this.dictionaryData && this.dictionaryData[key])
          return (
            (this.dictionaryData[key] &&
              this.dictionaryData[key].find(e => e.enumKey === val) &&
              this.dictionaryData[key].find(e => e.enumKey === val).enumVal) ||
            ' - '
          )
      }
    }
  },
  watch: {
    $route() {
      if (this.alertShowing) {
        setTimeout(this.showAlert, 0)
      }
    }
  },
  created() {
    this.id = localStorage.getItem('id')
    this.userName = localStorage.getItem('userName')

    if (
      window.globalCache.checkPasswordExpire ||
      window.globalConfig.forcePasswordChange
    ) {
      window.globalCache.checkPasswordExpire = false
      this.checkPasswordExpire()
    }
  },
  mounted() {
    this.getCurrentPlatformForFront()
  },
  methods: {
    ...mapMutations('theme', ['switchSiteMenuMode']),
    // 获取平台配置
    getCurrentPlatformForFront() {
      antiVpnService.getCurrentPlatformForFront().then(res => {
        this.comCode = (res && res.comCode + '') || ' - '
        this.provinceId = (res && res.provinceId + '') || ' - '
        this.systemCode = (res && res.systemCode + '') || ' - '
      })
    },
    handleCommand(command) {
      switch (command) {
        case 'switchSiteMenu':
          this.switchSiteMenuMode()
          dispatchResize()
          break
        case 'changePassword':
          this.$refs.changePassword.init(this.id)
          break
        case 'lockScreen':
          this.$emit('lockScreen')
          break
        case 'about':
          this.$refs.about.init()
          break
        case 'operatorInfo':
          this.$alert(
            `<span>运营商编码: </span>${this.comCode +
              ' - ' +
              this.getValue(
                'ISP_CODE',
                this.comCode
              )}<br /><span>省级区域: </span>${this.getValue(
              'PROVINCE_CODE',
              this.provinceId
            )}<br /><span>业务系统标识: </span>${this.getValue(
              'SYSTEM_CODE',
              this.systemCode
            )}`,
            '平台配置信息',
            {
              dangerouslyUseHTMLString: true,
              confirmButtonText: '确定'
            }
          )
          break
        case 'logout':
          api_auth.logout().then(() => {
            clearLoginInfo()
            this.$router.push({
              name: 'login'
            })
          })
          break
      }
    },
    checkPasswordExpire() {
      api_auth.passwordExpire({ id: this.id }).then(data => {
        if (data.needModify && window.globalConfig.forcePasswordChange) {
          this.alertShowing = true
          this.alertMessage = data.description
          this.showAlert()
        } else if (data.needRemind || data.needModify) {
          this.$confirm(data.description, '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
            closeOnClickModal: false
          })
            .then(() => {
              setTimeout(() => {
                this.$refs.changePassword.init(this.id)
              }, 200)
            })
            .catch(() => {})
        }
      })
    },
    showAlert() {
      this.$alert(this.alertMessage, '提示', {
        confirmButtonText: '立即修改',
        type: 'warning',
        showClose: false
      })
        .then(() => {
          this.alertShowing = false
          setTimeout(() => {
            this.$refs.changePasswordForce.init(this.id)
          }, 200)
        })
        .catch(() => {})
    }
  }
}
</script>
