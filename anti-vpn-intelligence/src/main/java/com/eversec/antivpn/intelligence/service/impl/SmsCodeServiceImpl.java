package com.eversec.antivpn.intelligence.service.impl;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.eversec.antivpn.config.AntiVpnProperties;
import com.eversec.antivpn.intelligence.api.dto.*;
import com.eversec.antivpn.intelligence.entity.SmsCodeRecord;
import com.eversec.antivpn.intelligence.service.ISmsCodeService;
import com.eversec.antivpn.intelligence.service.ISmsService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Arrays;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 短信验证码服务实现类
 * 
 * <AUTHOR>
 * @since 2025-01-12
 */
@Service
@Slf4j
@AllArgsConstructor
public class SmsCodeServiceImpl implements ISmsCodeService {

    private final AntiVpnProperties antiVpnProperties;
    private final ISmsService smsService;

    // 使用内存存储验证码记录，生产环境建议使用Redis
    private final ConcurrentHashMap<String, SmsCodeRecord> codeRecords = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, Integer> dailySendCount = new ConcurrentHashMap<>();

    @Override
    public SmsCodeResponseDTO sendCode(SmsCodeRequestDTO request) {
        AntiVpnProperties.SmsCode smsCodeConfig = antiVpnProperties.getUnicomCloudAbility().getSmsCode();
        
        String phoneNumber = request.getPhoneNumber();
        String businessType = StrUtil.blankToDefault(request.getBusinessType(), "DEFAULT");
        String recordKey = phoneNumber + ":" + businessType;
        String dailyKey = phoneNumber + ":" + LocalDate.now();

        // 检查每日发送次数限制
        Integer todaySendCount = dailySendCount.getOrDefault(dailyKey, 0);
        if (todaySendCount >= smsCodeConfig.getMaxSendTimesPerDay()) {
            return SmsCodeResponseDTO.failure("今日发送次数已达上限，请明日再试");
        }

        // 检查发送间隔
        SmsCodeRecord existingRecord = codeRecords.get(recordKey);
        if (existingRecord != null && existingRecord.getLastSendTime() != null) {
            long secondsSinceLastSend = ChronoUnit.SECONDS.between(existingRecord.getLastSendTime(), LocalDateTime.now());
            if (secondsSinceLastSend < smsCodeConfig.getSendIntervalSeconds()) {
                int remainingSeconds = (int) (smsCodeConfig.getSendIntervalSeconds() - secondsSinceLastSend);
                return SmsCodeResponseDTO.failure("发送过于频繁，请" + remainingSeconds + "秒后再试", remainingSeconds);
            }
        }

        // 生成验证码
        String code = RandomUtil.randomNumbers(smsCodeConfig.getCodeLength());
        
        // 构造短信内容
        String message = smsCodeConfig.getTemplate()
                .replace("{code}", code)
                .replace("{minutes}", smsCodeConfig.getExpireMinutes().toString());

        // 发送短信
        SmsRequestDTO smsRequest = new SmsRequestDTO();
        smsRequest.setSerialNumber(Arrays.asList("86" + phoneNumber));
        smsRequest.setMessage(message);

        SmsResponseDTO smsResponse = smsService.sendSms(smsRequest);
        
        if (!smsResponse.isSuccess()) {
            log.error("短信发送失败，手机号：{}，错误：{}", phoneNumber, smsResponse.getRespDesc());
            return SmsCodeResponseDTO.failure("短信发送失败：" + smsResponse.getRespDesc());
        }

        // 保存验证码记录
        SmsCodeRecord record = new SmsCodeRecord();
        record.setPhoneNumber(phoneNumber);
        record.setCode(code);
        record.setBusinessType(businessType);
        record.setCreateTime(LocalDateTime.now());
        record.setExpireTime(LocalDateTime.now().plusMinutes(smsCodeConfig.getExpireMinutes()));
        record.setUsed(false);
        record.setLastSendTime(LocalDateTime.now());
        
        codeRecords.put(recordKey, record);
        
        // 更新每日发送次数
        dailySendCount.put(dailyKey, todaySendCount + 1);

        log.info("验证码发送成功，手机号：{}，业务类型：{}", phoneNumber, businessType);
        
        return SmsCodeResponseDTO.success("验证码发送成功", smsCodeConfig.getExpireMinutes(), smsCodeConfig.getSendIntervalSeconds());
    }

    @Override
    public SmsCodeResponseDTO verifyCode(SmsCodeVerifyRequestDTO request) {
        String phoneNumber = request.getPhoneNumber();
        String code = request.getCode();
        String businessType = StrUtil.blankToDefault(request.getBusinessType(), "DEFAULT");
        String recordKey = phoneNumber + ":" + businessType;

        SmsCodeRecord record = codeRecords.get(recordKey);
        
        if (record == null) {
            return SmsCodeResponseDTO.failure("验证码不存在或已过期");
        }

        if (record.getUsed()) {
            return SmsCodeResponseDTO.failure("验证码已使用");
        }

        if (record.isExpired()) {
            codeRecords.remove(recordKey);
            return SmsCodeResponseDTO.failure("验证码已过期");
        }

        if (!code.equals(record.getCode())) {
            return SmsCodeResponseDTO.failure("验证码错误");
        }

        // 标记为已使用
        record.setUsed(true);
        record.setUsedTime(LocalDateTime.now());

        log.info("验证码验证成功，手机号：{}，业务类型：{}", phoneNumber, businessType);
        
        return SmsCodeResponseDTO.success("验证码验证成功", null);
    }

    @Override
    public void cleanExpiredCodes() {
        LocalDateTime now = LocalDateTime.now();
        LocalDate today = LocalDate.now();
        
        // 清理过期的验证码记录
        codeRecords.entrySet().removeIf(entry -> {
            SmsCodeRecord record = entry.getValue();
            return record.isExpired() || record.getUsed();
        });
        
        // 清理过期的每日发送次数记录
        dailySendCount.entrySet().removeIf(entry -> {
            String key = entry.getKey();
            String dateStr = key.substring(key.lastIndexOf(':') + 1);
            try {
                LocalDate recordDate = LocalDate.parse(dateStr);
                return recordDate.isBefore(today);
            } catch (Exception e) {
                return true; // 解析失败的记录也删除
            }
        });
        
        log.debug("清理过期验证码记录完成，当前记录数：{}，每日计数记录数：{}", codeRecords.size(), dailySendCount.size());
    }

}
