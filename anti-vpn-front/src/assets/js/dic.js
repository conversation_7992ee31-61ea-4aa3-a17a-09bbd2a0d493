import api from '@/api/rm/dic'

function getDicAll() {
  return new Promise((resolve, reject) => {
    api
      .getDictAllListTree()
      .then(res => {
        let temp = {}
        res.map(e => {
          temp[e.dictType.typeKey] = e.dictList
        })
        resolve(temp)
      })
      .catch(e => reject(false))
  })
}

const getDicKey2Val = (enumKey, dicArr, keyName = 'enumVal') => {
  if (!dicArr) return
  let f = dicArr.find(el => el.enumKey == enumKey)
  if (f) return f[keyName]
  return enumKey
}
export { getDicAll, getDicKey2Val }
