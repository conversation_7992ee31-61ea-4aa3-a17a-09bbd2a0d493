package com.eversec.antivpn.intelligence.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 短信发送响应DTO
 * 
 * <AUTHOR>
 * @since 2025-01-12
 */
@Data
@Schema(name = "SmsResponseDTO", description = "短信发送响应")
public class SmsResponseDTO {

    @Schema(description = "接口返回码", example = "1")
    private String respCode;

    @Schema(description = "接口返回描述", example = "success")
    private String respDesc;

    /**
     * 判断是否发送成功
     * @return true表示成功，false表示失败
     */
    public boolean isSuccess() {
        return "1".equals(respCode);
    }

}
