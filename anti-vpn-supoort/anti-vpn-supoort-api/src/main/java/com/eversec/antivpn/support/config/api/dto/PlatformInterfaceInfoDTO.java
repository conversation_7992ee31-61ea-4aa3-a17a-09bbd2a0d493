package com.eversec.antivpn.support.config.api.dto;

import java.io.Serializable;
import java.time.LocalDateTime;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 平台接口信息DTO
 * </p>
 * <p>
 * api接口数据传输对象（用于save、update接收参数，list、page、find返回对象等）
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-18
 */
@Getter
@Setter
@Schema(name = "PlatformInterfaceInfoDTO", description = "平台接口信息")
public class PlatformInterfaceInfoDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "主键-雪花")
    private Long id;

    @Schema(description = "配置类型。CURRENT:当前系统; PROVINCE: 省平台。平台部署时，存在一条当前系统配置记录。")
    private String configType;

    @Schema(description = "是否生效,false不生效，true生效。是否生效只针对PROVINCE类型记录起作用")
    private Boolean enabled;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "运营商编码。省平台部署：当前平台运营商编码；集团部署：运营商编码")
    private String comCode;

    @Schema(description = "省级区域编号。省平台部署：当前平台省级区域编号；集团部署：省级区域编号")
    private Long provinceId;

    @Schema(description = "省平台业务系统标识。省平台部署：当前省平台业务系统标识；集团部署：省平台业务系统标识")
    private String systemCode;

    @Deprecated
    @Schema(description = "DC消费的目录。省平台部署：DC消费的目录；集团部署：DC消费的目录，每个省平台配置的目录不同，可通过该目录查询省平台信息")
    private String southReceiveDir;

    @Schema(description = "上报ftp地址。例：ftp://localhost:21/report-log/01-11")
    private String northReportFtpAddress;

    @Schema(description = "上报ftp username")
    private String northReportFtpUsername;

    @Schema(description = "上报ftp password")
    private String northReportFtpPassword;

    @Schema(description = "上报加密密钥")
    private String northReportSecretKey;

    @Schema(description = "情报库ftp地址。例：ftp://localhost:21/download-intelligence/01-11")
    private String northIntelligenceFtpAddress;

    @Schema(description = "情报库ftp username")
    private String northIntelligenceFtpUsername;

    @Schema(description = "情报库ftp password")
    private String northIntelligenceFtpPassword;

    @Schema(description = "情报库解密密钥")
    private String northIntelligenceSecretKey;

    @Schema(description = "上报企业状态-网络类型id，多类型|分割")
    private String northCurrentNetworkBusinessIds;

    @Schema(description = "情报库目录。省平台部署：不填写；集团部署：情报库目录，透传部侧情报库文件")
    private String southIntelligenceDir;

    @Schema(description = "集团调用省平台webservice地址，省平台调用CU地址，例如：http://localhost:8080/services")
    private String southWsAddress;

    @Schema(description = "接收解密密钥")
    private String southReceiveSecretKey;

    @Schema(description = "smart 服务地址。")
    private String southSmartAddress;

    @Schema(description = "smart 用户名。")
    private String southSmartUsername;

    @Schema(description = "smart 密码。")
    private String southSmartPassword;

    /**
     * 获取采集类型
     * 当有smart配置无southIntelligenceDir配置时，为smart；当无smart配置有southIntelligenceDir配置时为CU
     * @return
     */
    public String getCollectType(){

        // 默认调用smart
        if (StrUtil.isNotBlank(southSmartAddress)) {
            // 配置了smart接口地址，调用smart
            return "SMART";
        } else if (StrUtil.isNotBlank(southIntelligenceDir) && StrUtil.isNotBlank(southReceiveSecretKey)){
            // 配置了南向情报目录，并且配置了南向接收解密密钥，调用CU
            return "CU";
        } else if (StrUtil.isNotBlank(southIntelligenceDir)) {
            // 配置了南向情报目录，没有配置南向接收加解密密钥，说明不是CU，调用态势感知，态势感知不需要配置加解密密钥
            return "NSSA";
        } else {
            // 不做操作
            return "DEFAULT";

        }
    }


    public SecretKeyDTO toSecretKeyDTO(String secretKey) {
        return JSONUtil.toBean(secretKey, SecretKeyDTO.class);
    }

    @Data
    public static class SecretKeyDTO {

        @Schema(description = "加密偏移量")
        private String aesOffsets;

        @Schema(description = "AES密钥")
        private String aesKey;

        @Schema(description = "用户口令")
        private String messageKey;



    }

}