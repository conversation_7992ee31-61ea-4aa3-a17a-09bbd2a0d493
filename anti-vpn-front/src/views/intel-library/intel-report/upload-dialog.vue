<template>
  <div>
    <el-dialog
      class="dialog-handle"
      :title="title"
      :visible.sync="visible"
      :close-on-click-modal="false"
      :loading="loading"
    >
      <el-form ref="ruleForm" label-width="100px" class="demo-ruleForm">
        <el-form-item :label="`导入取证文件`" prop="">
          <el-upload
            class="upload-demo"
            action=""
            :beforeUpload="beforeAvatarUpload"
            :http-request="handleUpload"
            :on-success="handleproSuccess"
            :on-remove="removeFile"
            :file-list="fileList"
            :limit="1"
            :on-change="changFile"
          >
            <el-button type="primary">选择文件</el-button>
          </el-upload>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="visible = false">取消</el-button>
        <el-button
          type="primary"
          :loading="loading"
          :disabled="!(fileList && fileList.length > 0)"
          @click="submitFun"
        >
          确定
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import antiVpnService from "@/api/antiVpnService";

export default {
  name: "knowledge-base-import",
  components: {},
  props: {
    type: {
      type: String,
      default: () => ""
    }
  },
  data() {
    return {
      loading: false,
      visible: false,
      visibleError: false,
      title: `上传取证文件`,
      fileList: [],
      total: null,
      successList: null,
      failureList: null,
      importErrors: {},
      importErrorBases: {},
      id: null
    };
  },
  computed: {},
  watch: {},
  mounted() {},
  methods: {
    // 获取平台配置

    handleUpload(param) {},
    handleproSuccess(res, file) {},
    openDialog(id) {
      this.fileList = [];
      this.visible = true;
      this.total = null;
      this.successList = null;
      this.failureList = null;
      this.importErrors = {};
      this.importErrorBases = {};
      this.id = id;
    },

    // 上传回调
    changFile(file, fileList) {
      this.fileList = fileList;
    },
    // 上传验证
    beforeAvatarUpload(file) {
      let fileExt = file.name.substring(file.name.lastIndexOf(".") + 1);
      const extension =
        fileExt === "xlsx" ||
        fileExt === "xls" ||
        fileExt === "XLSX" ||
        fileExt === "XLS" ||
        fileExt === "csv" ||
        fileExt === "CSV" ||
        fileExt === 'zip' ||
        fileExt === 'ZIP' ||
        fileExt === 'pcap' ||
        fileExt === 'PCAP';
      if (!extension) {
        this.$message({
          message: "上传文件只能是.XLSX、.XLS格式、.CSV、.ZIP、PCAP!",
          type: "warning"
        });
      }
      return extension;
    },
    // 移除附件
    removeFile(file, fileList) {
      let index = this.fileList.indexOf(file);
      this.fileList.splice(index, 1);
    },
    // 上传
    submitFun() {
      if (this.fileList.length === 0) {
        this.$message({
          message: "请选择需要导入的文件",
          type: "error"
        });
        return;
      }
      this.loading = true;
      let formData = new FormData();
      formData.append("file", this.fileList[0].raw);
      antiVpnService
        .uploadEvidenceFile(formData, this.id)
        .then(res => {
          this.loading = false;
          // if (res.exists) {
          //   this.total =
          //     res.importErrorBases.length || 0 + res.importBases.length || 0
          //   this.successList = res.importBases.length || 0
          //   this.failureList = res.importErrorBases.length || 0
          //   this.$message({
          //     message: `${res.importErrorBases.length || 0}条数据导入失败, ${res
          //       .importBases.length || 0}条数据成功导入`,
          //     type: 'warning'
          //   })
          //   this.importErrors = res.importErrors
          //   this.importErrorBases = res.importErrorBases
          //   this.visibleError = true
          // } else {
          //   this.$message({
          //     message: `${res.importBases.length || 0}条数据成功导入`,
          //     type: 'success'
          //   })
          // }
          this.$message({
            message: `上传取证文件成功`,
            type: "success"
          });
          this.visible = false;
          this.$emit("submitFun");
        })
        .catch(() => {
          this.loading = false;
        });
    }
  }
};
</script>

<style lang="scss" scoped></style>
