<template>
  <el-select
    :value="dicValue"
    :disabled="disabled"
    :placeholder="placeholder ? placeholder : '--请选择--'"
    :clearable="clearable"
    :multiple="multiple"
    :allow-create="allowCreate"
    :filterable="filterable"
    :default-first-option="defaultFirstOption"
    v-bind="$attrs"
    v-on="inputListeners"
  >
    <el-option
      v-for="dic in dicValues"
      :key="dic.id"
      :label="
        dic.enumVal.length > 50 ? dic.enumVal.slice(0, 50) + '...' : dic.enumVal
      "
      :value="dic.enumKey"
      :disabled="childrenDisabled.indexOf(dic.enumKey) !== -1"
    >
      <template
        v-if="
          showAll && dic.enumKey.length <= 8 && /^[0-9]+$/.test(dic.enumKey)
        "
      >
        <span>{{ dic.enumKey }}</span>
        <span>-</span>
        <span>{{ dic.enumVal }}</span>
      </template>
    </el-option>
  </el-select>
</template>

<script>
import { mapGetters } from 'vuex'
export default {
  model: {
    prop: 'dicValue',
    event: 'change'
  },
  props: {
    dicValue: {},
    enumKey: {
      type: String
    },
    allowCreate: {
      type: Boolean,
      default: false
    },
    filterable: {
      type: Boolean,
      default: false
    },
    defaultFirstOption: {
      type: Boolean,
      default: false
    },
    multiple: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    },
    placeholder: {
      type: String,
      default: '请选择'
    },
    clearable: {
      type: Boolean,
      default: true
    },
    // 需要禁用的数据
    childrenDisabled: {
      type: Array,
      default: () => []
    },
    // 需要过滤的数据
    filterList: {
      type: Array,
      default: () => []
    },
    // 显示自定义数据
    showAll: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {}
  },
  computed: {
    ...mapGetters('sys-dict', ['dictionaryData']),
    inputListeners: function() {
      var vm = this
      // `Object.assign` 将所有的对象合并为一个新对象
      return Object.assign(
        {},
        // 我们从父级添加所有的监听器
        this.$listeners,
        // 然后我们添加自定义监听器，
        // 或覆写一些监听器的行为
        {
          // 这里确保组件配合 `v-model` 的工作
          change(value) {
            vm.$emit('change', value)
          }
        }
      )
    },
    dicValues() {
      let value = []
      if (
        this.dictionaryData &&
        Object.keys(this.dictionaryData).length > 0 &&
        this.dictionaryData[this.enumKey]
      ) {
        if (this.filterList.length > 0) {
          value = this.dictionaryData[this.enumKey].filter(e =>
            this.filterList.includes(e.enumKey)
          )
        } else {
          value = this.dictionaryData[this.enumKey]
        }
      }
      return value
    }
  },
  methods: {}
}
</script>

<style lang="scss">
.icon-select-popover {
  max-width: 300px;
}
.icon-select-list {
  max-height: 300px;
  padding: 0;
  margin: -8px 0 0 -8px;
  overflow: auto;
  > .el-button {
    padding: 8px;
    margin: 8px 0 0 8px;
    > span {
      display: inline-block;
      vertical-align: middle;
      width: 18px;
      height: 18px;
      font-size: 18px;
    }
  }
}
</style>
