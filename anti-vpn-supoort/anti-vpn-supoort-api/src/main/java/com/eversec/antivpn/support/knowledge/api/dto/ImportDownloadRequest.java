package com.eversec.antivpn.support.knowledge.api.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <p>
 * 导出返回类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-12
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class ImportDownloadRequest {
    /**
     * 导入失败列表
     */
    private List<Object> importErrorBases;
}