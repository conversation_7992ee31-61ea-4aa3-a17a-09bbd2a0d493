<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eversec.antivpn.intelligence.mapper.VpnIntelligenceMapper">

    <insert id="saveBatchIntelligence" parameterType="java.util.List">
        INSERT INTO vpn_intelligence
            ( id,
             command_id,
             rule_id,
             com_code,
             province_id,
             system_code,
             network_business_id,
             type_id,
             vpn_id,
             vpn_name,
             vpn_domain,
             vpn_ip,
             vpn_url,
             vpn_url_decode,
        vpn_message,
        vpn_message_decode,
        vpn_link,
             content_type,
             vpn_content,
             vpn_port,
             vpn_airport_code,
             vpn_airport_name,
        vpn_software_codes,
        vpn_software_names,
             vpn_country,
             vpn_protocol_code,
             vpn_protocol_eversec_code,
        application_protocol_code,
             time_stamp,
        attach_ment,
             source,
             report_status,
             distribute_status,
             create_user,
             update_user )
--         VALUES ( 179634545099072, 42120000037297, '03', 37, '3', 1, 42120000037297, 'V2RAY', 'nlsdwsz.66666654.xyz', '*************', 999, '*************-2095', '2095', '8', 'proxies.haisto.cn', '美国', '999', '120', '2023-08-13 19:34:55', 'INPUT', 'TO_BE_REPORTED', 'TO_BE_DISTRIBUTED', 'admin', 'admin' )
        VALUES
        <foreach collection="list" index="index" item="vpnIntelligence" separator=",">
            (
                #{vpnIntelligence.id,jdbcType=BIGINT},
                #{vpnIntelligence.commandId,jdbcType=BIGINT},
                #{vpnIntelligence.ruleId,jdbcType=BIGINT},
                #{vpnIntelligence.comCode,jdbcType=VARCHAR},
                #{vpnIntelligence.provinceId,jdbcType=BIGINT},
                #{vpnIntelligence.systemCode,jdbcType=VARCHAR},
                #{vpnIntelligence.networkBusinessId,jdbcType=INTEGER},
                #{vpnIntelligence.typeId,jdbcType=INTEGER},
                #{vpnIntelligence.vpnId,jdbcType=BIGINT},
                #{vpnIntelligence.vpnName,jdbcType=VARCHAR},
                #{vpnIntelligence.vpnDomain,jdbcType=VARCHAR},
                #{vpnIntelligence.vpnIp,jdbcType=VARCHAR},
                #{vpnIntelligence.vpnUrl,jdbcType=VARCHAR},
                #{vpnIntelligence.vpnUrlDecode,jdbcType=VARCHAR},
            #{vpnIntelligence.vpnMessage,jdbcType=VARCHAR},
            #{vpnIntelligence.vpnMessageDecode,jdbcType=VARCHAR},
            #{vpnIntelligence.vpnLink,jdbcType=VARCHAR},
                #{vpnIntelligence.contentType,jdbcType=INTEGER},
                #{vpnIntelligence.vpnContent,jdbcType=VARCHAR},
                #{vpnIntelligence.vpnPort,jdbcType=VARCHAR},
                #{vpnIntelligence.vpnAirportCode,jdbcType=VARCHAR},
                #{vpnIntelligence.vpnAirportName,jdbcType=VARCHAR},
            #{vpnIntelligence.vpnSoftwareCodes,jdbcType=VARCHAR},
            #{vpnIntelligence.vpnSoftwareNames,jdbcType=VARCHAR},
                #{vpnIntelligence.vpnCountry,jdbcType=VARCHAR},
                #{vpnIntelligence.vpnProtocolCode,jdbcType=VARCHAR},
                #{vpnIntelligence.vpnProtocolEversecCode,jdbcType=VARCHAR},
            #{vpnIntelligence.applicationProtocolCode,jdbcType=VARCHAR},
                #{vpnIntelligence.timeStamp,jdbcType=VARCHAR},
            #{vpnIntelligence.attachMent,jdbcType=VARCHAR},
                #{vpnIntelligence.source,jdbcType=VARCHAR},
                #{vpnIntelligence.reportStatus,jdbcType=VARCHAR},
                #{vpnIntelligence.distributeStatus,jdbcType=VARCHAR},
                #{vpnIntelligence.createUser,jdbcType=VARCHAR},
                #{vpnIntelligence.updateUser,jdbcType=VARCHAR}
            )
        </foreach>

    </insert>

    <delete id="deleteByIds">
        delete from vpn_intelligence
               where id in
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

</mapper>
