<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eversec.antivpn.log.mapper.E4DnsLogMapper">
    <resultMap id="MonitorDataVO" type="com.eversec.antivpn.log.mapper.vo.MonitorDataVO">
        <result column="province_id" jdbcType="INTEGER" property="provinceId"/>
        <result column="day" jdbcType="INTEGER" property="day"/>
        <result column="hour" jdbcType="INTEGER" property="hour"/>
        <result column="system_code" jdbcType="VARCHAR" property="systemCode"/>
        <result column="num" jdbcType="BIGINT" property="num"/>
    </resultMap>
    <select id="getSystemProvinceCount" resultMap="MonitorDataVO">
        SELECT
            province_id,
            day,hour,
            system_code,
            countMerge(log_count)  as num
        FROM  e1_comm_log_view
        where day=#{day} and hour in (#{hour},#{hour}-1)
        GROUP BY system_code,province_id,day,hour
        ORDER BY province_id,system_code,day,hour
    </select>
</mapper>
