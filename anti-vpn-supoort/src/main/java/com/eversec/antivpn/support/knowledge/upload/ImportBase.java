package com.eversec.antivpn.support.knowledge.upload;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class ImportBase {

    public static final String[] MUST_FILL = new String[] { "序号", "机场ID", "机场名称", "官网", "注册地", "简介", "价格", "订阅渠道", "付款方式", "发现时间" };

    @Excel(name = "序号")
    public Integer index;
    @Excel(name = "机场ID")
    private String enumKey;
    @Excel(name = "机场名称")
    private String enumVal;
    @Excel(name = "官网")
    private String officialWebsite;

    @Excel(name = "注册地")
    private String registered;

    @Excel(name = "简介")
    private String introduction;

    @Excel(name = "价格")
    private String price;

    @Excel(name = "订阅渠道")
    private String subscriptionChannel;

    @Excel(name = "付款方式")
    private String paymentMethod;

    @Excel(name = "发现时间")
    private String discoveryTime;
//    @Excel(name = "入库时间")
//    private String insert_time;
}
