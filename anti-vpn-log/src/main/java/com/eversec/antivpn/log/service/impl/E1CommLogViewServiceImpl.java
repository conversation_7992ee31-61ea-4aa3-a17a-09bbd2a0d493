package com.eversec.antivpn.log.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.eversec.antivpn.emnus.ScreenTypeEnum;
import com.eversec.antivpn.log.enums.CKPartSql;
import com.eversec.antivpn.log.api.dto.base.AggCountDTO;
import com.eversec.antivpn.log.constants.CKColumn;
import com.eversec.antivpn.log.entity.E1CommLogView;
import com.eversec.antivpn.log.mapper.E1CommLogViewMapper;
import com.eversec.antivpn.log.mapper.vo.BaseSearchVO;
import com.eversec.antivpn.log.mapper.vo.TypeCountVO;
import com.eversec.antivpn.log.service.IE1CommLogViewService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-17
 */
@Service
@Slf4j
public class E1CommLogViewServiceImpl extends ServiceImpl<E1CommLogViewMapper, E1CommLogView> implements IE1CommLogViewService {
    @Resource
    private E1CommLogViewMapper e1CommLogViewMapper;

    @Override
    public AggCountDTO timeRangeAndTodayCount(ScreenTypeEnum screenTypeEnum) {
        AggCountDTO result = new AggCountDTO();
        BaseSearchVO baseSearchVO = new BaseSearchVO();
        baseSearchVO.setTodayTimePartition(ScreenTypeEnum.getTimeCKPartition(ScreenTypeEnum.TODAY));
        Long todayCount = e1CommLogViewMapper.countNum(baseSearchVO);
        result.setTodayCount(todayCount);
        if (screenTypeEnum == ScreenTypeEnum.TODAY) {
            result.setDateRangeCount(todayCount);
        } else {
            baseSearchVO.clear();
            baseSearchVO.setScreenTypeEnumCode(screenTypeEnum.getCode());
            baseSearchVO.setStartTimePartition(ScreenTypeEnum.getTimeCKPartition(screenTypeEnum));
            //baseSearchVO.setEndTimePartition(ScreenTypeEnum.getTimeCKPartition(ScreenTypeEnum.TODAY));
            Long dateRangeCount = e1CommLogViewMapper.countNum(baseSearchVO);
            result.setDateRangeCount(dateRangeCount);
        }
        return result;
    }

    @Override
    public List<TypeCountVO> vpnNameAggregate(ScreenTypeEnum screenTypeEnum) {
        BaseSearchVO baseSearchVO = new BaseSearchVO();
        baseSearchVO.setStartTimePartition(ScreenTypeEnum.getTimeCKPartition(screenTypeEnum));
        return e1CommLogViewMapper.getVpnNameAggregate(baseSearchVO);
    }

}
