<template>
  <section class="title_box">
    <div class="box_tit">
      <span :class="{ active: curI == 0 }" @click="navClick(0, '应用层协议')"
        >应用层协议占比TOP10</span
      >
      |
      <span :class="{ active: curI == 1 }" @click="navClick(1, '传输层协议')"
        >传输层协议占比</span
      >
    </div>
    <div class="box_con">
      <Pie2
        style="height: 250px"
        ref="pieRef"
        v-loading="loading"
        element-loading-background="rgba(0, 0, 0, 0.2)"
      />
    </div>
  </section>
</template>

<script>
import api from "@/api/rm/dic";
import { getDicAll, getDicKey2Val } from "@/assets/js/dic.js";
import overviewApi from "./overviewApi.js";
import Pie2 from "../common/Pie2.vue";
export default {
  components: { Pie2 },
  // props: {
  //   title: {
  //     type: String,
  //     default: ''
  //   }
  // },
  data() {
    return {
      curI: 0,
      title: "",
      dicAll: null,
      searchTime: {},
      loading: false
    };
  },
  methods: {
    init(searchTime, dicAll) {
      this.searchTime = searchTime;
      this.navClick(this.curI, "应用层协议");
    },
    async fn0() {
      let opt = await api.getDictList("APPLICATION_LAYER_PROTOCOL");
      overviewApi
        .applicationProtocolTypeCount(this.searchTime)
        .then(res => {
          let d = (res || []).filter(el => el.key);
          d = d.map((el, i) => {
            return {
              ...el,
              name: getDicKey2Val(el.key, opt),
              value: el.num
            };
          });
          console.log("rig1-应用层占比", res, d);

          this.$refs.pieRef.init(this.title, "占比", d);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    async fn1() {
      let opt = await api.getDictList("TRANSPORT_PROTOCOL");
      overviewApi
        .protocolTypeAggregate(this.searchTime)
        .then(res => {
          let d = (res || []).filter(el => el.key);
          d = d.map((el, i) => {
            return {
              ...el,
              name: getDicKey2Val(el.key, opt),
              value: el.num
            };
          });
          // this.chuanshuOpt = publicPieFn(d);
          console.log("rig1-传输层协议占比", res, d);

          this.$refs.pieRef.init(this.title, "占比", d);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    navClick(i, tit = "") {
      this.curI = i;
      // 数据切换
      this.title = tit;
      this.loading = true;
      this["fn" + i]();
    }
  }
};
</script>

<style scoped lang="scss">
.title_box {
  .box_tit {
    color: #a5acb4;
    font-size: 16px;
    padding: 0 39px;
    line-height: 38px;
    height: 37px;
    background: url(../overview/img/tit_bg.png) no-repeat;
    span {
      cursor: pointer;
      &.active {
        color: #fff;
      }
    }
  }
  .box_con {
    // height: 250px;
  }
}
</style>
