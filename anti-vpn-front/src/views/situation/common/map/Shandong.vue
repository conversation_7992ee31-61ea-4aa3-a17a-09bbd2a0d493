<template>
  <div class="mapbox">
    <div class="mapbg"></div>
    <e-charts
      map="shandong"
      key="shandong"
      type="map"
      class="chinamap"
      empty-type="2"
      empty-color="#e4fdff"
      :options="options"
      @click="mapClick"
    ></e-charts>
  </div>
</template>

<script>
import { worldSecurity } from "@/assets/mapStatic.js";

export default {
  name: "MapWorldNote",
  props: {
    mapD: {
      default: () => [],
      type: Array
    },
    myTooltip: {
      default: String,
      type: ''
    },
  },
  watch: {
    mapD: {
      handler(val) {
        this.field = val;
        this.drawMap(val);
      }
      // immediate: true
    }
  },
  data() {
    return {
      options: {}
    };
  },
  mounted() {
    // this.drawMap();
  },
  methods: {
    mapClick(e) {
      console.log("地图点击", e);
      // if (e.data) {
      //   // 有数据
      //   this.$emit("updateDetail", e.data);
      // } else {
      //   // 无数据
      //   this.$emit("closeDetail");
      // }
    },
    getTooltip(d) {
      // console.log('getTooltip:', d, eval('`' + this.myTooltip + '`'))
      return this.myTooltip ? eval('`' + this.myTooltip + '`') : d.name;
      // return `
      //   <div class="tooltipbox">
      //     <div class="provincename">${d.name} </div>

      //     <div class="tooltip-item">
      //       <div class="item-name">今日上报：</div>
      //       <div class="item-value color_num">${d.todayNum}</div>
      //     </div>
      //     <div class="tooltip-item">
      //       <div class="item-name">总上报：</div>
      //       <div class="item-value color_num2"> ${d.value}</div>
      //     </div>
      //   </div>`;
    },
    drawMap(data = []) {
      let _this = this;
      console.log("drawMap-省", data);
      // data = [
      //   { name: "济南市", value: 1000 },
      //   { name: "青岛市", value: 900 },
      //   { name: "淄博市", value: 500 },
      //   { name: "枣庄市", value: 400 },
      //   { name: "东营市", value: 200 },
      //   { name: "莱芜市", value: 100 },
      // ];

      let max = Math.max.apply(
        null,
        data.map(v => v.value)
      );
      let maxH = 1.3; // [0,8]
      data = data.map((el, i) => {
        return {
          ...el,
          height: (el.value / max) * maxH, // 画柱状图用
          idx: i + 1
        };
      });

      var scatterData = data.map(item => {
        // console.log(111, item.name, worldSecurity,worldSecurity[item.name])
        return [
          worldSecurity[item.name][0],
          worldSecurity[item.name][1] + item.height
        ];
      });
      var scatterData2 = data.map(item => {
        return {
          name: item.name,
          value: worldSecurity[item.name]
        };
      });
      // var scatterData3 = data.map(item => {
      //   return worldSecurity[item.name].concat(item.name);
      // });

      var lineData = data.map(item => {
        return {
          coords: [
            worldSecurity[item.name],
            [
              worldSecurity[item.name][0],
              worldSecurity[item.name][1] + item.height
            ]
          ]
        };
      });

      let series_new = [
        {
          // 地图和颜色映射
          type: "map",
          map: "shandong",
          geoIndex: 0,
          zlevel: 2,
          label: {
            normal: {
              show: true,
              position: "right",
              formatter: "{b}"
            }
          },
          data: data
        },
        // 画柱状图
        {
          type: "lines",
          zlevel: 5,
          effect: {
            show: false,
            period: 4, //箭头指向速度，值越小速度越快
            trailLength: 0.02, //特效尾迹长度[0,1]值越大，尾迹越长重
            symbol: "arrow", //箭头图标
            symbolSize: 5 //图标大小
          },
          lineStyle: {
            width: 13, //尾迹线条宽度
            color: {
              type: "linear",
              x: 0,
              y: 0,
              x2: 1,
              y2: 0,
              colorStops: [
                {
                  offset: 0,
                  color: "#F09B0A" // 0% 处的颜色
                },
                {
                  offset: 0.2,
                  color: "#F09B0A" // 0% 处的颜色
                },
                {
                  offset: 0.5,
                  color: "#FEF03B" // 0% 处的颜色
                },
                {
                  offset: 0.7,
                  color: "#FEF03B" // 0% 处的颜色
                },
                {
                  offset: 1,
                  color: "#F09B0A" // 100% 处的颜色
                }
              ],
              global: false // 缺省为 false
            },
            opacity: 1, //尾迹线条透明度
            curveness: 0 //尾迹线条曲直度
          },
          label: {
            show: 0,
            position: "end",
            formatter: "245"
          },
          silent: true,
          data: lineData
        },
        // 柱形数量显示(圆柱顶)
        {
          type: "scatter",
          coordinateSystem: "geo",
          geoIndex: 0,
          zlevel: 5,
          label: {
            show: !0,
            position: "top",
            formatter: function (params) {
              if (data[params.dataIndex].value > 10000) {
                return Math.round(data[params.dataIndex].value / 10000) + "万";
              } else {
                return data[params.dataIndex].value;
              }
            },
            padding: [4, 8, 3],
            borderRadius: 5,
            // backgroundColor: "rgba(2,20,34, .8)",
            // borderColor: "#57cfce",
            backgroundColor: "#003F5E",
            borderColor: "#67F0EF",

            borderWidth: 1,
            color: "#67F0EF"
          },
          symbol: "circle",
          symbolSize: [13, 7],
          itemStyle: {
            color: "#FEF03B",
            opacity: 1
          },
          silent: true,
          data: scatterData
        },
        // 图中圆点设置(底)
        {
          type: "scatter",
          coordinateSystem: "geo",
          geoIndex: 0,
          zlevel: 5,
          symbol: "circle",
          symbolSize: [13, 7],
          itemStyle: {
            color: "#F09B0A",
            opacity: 1,
            shadowColor: "#000",
            shadowBlur: 5,
            shadowOffsetY: 2
          },
          silent: true,
          label: {
            normal: {
              show: true,
              offset: [0, 13],
              fontSize: 11,
              textBorderColor: "transparent",
              formatter: function(params) {
                return params.name;
              }
            }
          },
          data: scatterData2
        }
        // {
        //   type: "effectScatter",
        //   coordinateSystem: "geo",
        //   geoIndex: 0,
        //   symbol: "circle",
        //   symbolSize: 4,
        //   showEffectOn: "render",
        //   rippleEffect: {
        //     brushType: "fill",
        //     scale: 10
        //   },
        //   hoverAnimation: true,
        //   label: {
        //     formatter: p => p.data[2],
        //     position: "right",
        //     color: "#fff",
        //     fontSize: 14,
        //     distance: 10,
        //     show: !0
        //   },
        //   itemStyle: {
        //     color: "#FEF134"
        //   },
        //   zlevel: 6,
        //   data: scatterData3
        // }
      ];

      var option01 = {
        backgroundColor: "transparent",
        // tooltip: {
        //   show: true,
        //   trigger: "item",
        //   formatter: function(params) {
        //     if (params.seriesType == "map") {
        //       if (typeof params.value == "number" && isNaN(params.value))
        //         params.value = 0;
        //       return params.name + "：" + params.value || 0;
        //     }
        //   }
        // },
        tooltip: {
          show: true,
          trigger: "item",
          backgroundColor: "none",
          extraCssText: "border:0; box-shadow: none;",
          formatter: function(params) {
            let d = params.data;
            // console.log(params);
            if (d) {
              return _this.getTooltip(d);
            }
          }
        },
        geo: [
          {
            // 坐标层
            map: "shandong",
            zoom: 1.36,
            aspectScale: 1.2,
            roam: false,
            layoutCenter: ["50%", "49%"],
            layoutSize: 620,
            zlevel: 1,
            label: {
              normal: {
                show: false,
                color: "#fff"
              },
              emphasis: {
                show: false
              }
            },
            itemStyle: {
              normal: {
                // areaColor: "#12235c",
                borderColor: "#7cb7d5",
                areaColor: "transparent"
                // borderColor: "#36bcc6",
                // opacity: 0.4
              },
              emphasis: {
                // areaColor: "#8cb2ff"
                areaColor: "rgba(31,96,190, .75)"
              }
            },
            regions: [
              {
                name: "南海诸岛",
                value: 0,
                itemStyle: {
                  normal: {
                    opacity: 0,
                    label: {
                      show: false
                    }
                  }
                }
              }
            ]
          }
        ],
        series: series_new
      };
      // console.log("---options-series", series_new);
      this.options = option01;
      // myChart01.setOption(option01);
    }
  }
};
</script>

<style lang="scss" scoped>
.mapbox {
  width: 920px;
  height: 580px;
  background: url(../img/map/bg.png) no-repeat  center center;
  position: relative;
  margin-top: -30px;
  margin-left: auto;
  margin-right: auto;
  .echarts-panel {
    position: absolute;
    z-index: 2;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
  }
  .mapbg {
    background: url(../img/map/shandong.png) no-repeat  -69px -36px;
    width: 950px;
    height: 580px;
    position: relative;
    z-index: 2;
    margin: 0 auto;
  }
}

::v-deep .tooltipbox {
  background: url(../img/pop_bg.png) no-repeat;
  background-size: 100% 100%;
  height: 100px;
  width: 154px;
  font-size: 12px;
  padding: 7px 14px;
  .provincename {
    font-weight: bold;
  }
  .tooltip-item {
    display: flex;
    line-height: 22px;
    position: relative;

    .item-name {
      color: #fff;
      width: 60px;
    }
    .item-value {
      color: #41c6fe;
    }
    .color_num {
      color: #41c7fc;
    }
    .color_num2 {
      color: #ffa849;
    }
  }
}
</style>
