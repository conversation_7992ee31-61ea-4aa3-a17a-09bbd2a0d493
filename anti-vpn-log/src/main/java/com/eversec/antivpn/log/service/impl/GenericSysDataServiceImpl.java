package com.eversec.antivpn.log.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eversec.antivpn.config.enums.AntiVpnDataDictTypeEnum;
import com.eversec.antivpn.log.entity.po.GenericSysDataDict;
import com.eversec.antivpn.log.mapper.SysDataDictMapper;
import com.eversec.antivpn.log.service.GenericSysDataDictService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

;


/**
 * <p>
 * 本地mysql-字典服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-12
 */
@Service
@Slf4j
public class GenericSysDataServiceImpl extends ServiceImpl<SysDataDictMapper, GenericSysDataDict> implements GenericSysDataDictService {

    @Resource
    private SysDataDictMapper sysDataDictMapper;

    @Override
    public Map<String, String> getSystemNameMap() {
        LambdaQueryWrapper<GenericSysDataDict> lambdaQueryWrapper = new LambdaQueryWrapper();
        lambdaQueryWrapper.eq(GenericSysDataDict::getTypeKey, AntiVpnDataDictTypeEnum.SYSTEM_CODE.name());
        List<GenericSysDataDict> genericSysDataDicts = sysDataDictMapper.selectList(lambdaQueryWrapper);
        Map<String, String> map = CollectionUtils.isEmpty(genericSysDataDicts) ? new HashMap<String, String>() : genericSysDataDicts.stream()
            .collect(Collectors.toMap(GenericSysDataDict::getEnumKey, GenericSysDataDict::getEnumVal));
        return map;
    }

    @Override
    public List<GenericSysDataDict> getSoftWareInfoByCode(List<String> codes) {
        LambdaQueryWrapper<GenericSysDataDict> lambdaQueryWrapper = new LambdaQueryWrapper();
        lambdaQueryWrapper.eq(GenericSysDataDict::getTypeKey,AntiVpnDataDictTypeEnum.SOFTWARE_INFORMATION.name())
                .in(GenericSysDataDict::getEnumKey,codes);
        List<GenericSysDataDict> genericSysDataDicts = sysDataDictMapper.selectList(lambdaQueryWrapper);
        return genericSysDataDicts;
    }

    @Override
    public List<GenericSysDataDict> getDictList(AntiVpnDataDictTypeEnum antiVpnDataDictTypeEnum) {
        LambdaQueryWrapper<GenericSysDataDict> lambdaQueryWrapper = new LambdaQueryWrapper();
        lambdaQueryWrapper.eq(GenericSysDataDict::getTypeKey,antiVpnDataDictTypeEnum.name());
        List<GenericSysDataDict> genericSysDataDicts = sysDataDictMapper.selectList(lambdaQueryWrapper);
        return genericSysDataDicts;
    }
}
