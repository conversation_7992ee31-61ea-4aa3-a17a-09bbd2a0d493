### 重新下发到smart
GET {{base}}/intelligence/vpnIntelligence/redistributeToSmart
userid: 1
username: admin


### 分页查询
GET {{base}}/intelligence/vpnIntelligence/page?current=1&vpnId=4312000001
userid: 1
username: admin

### 根据条件删除
DELETE {{base}}/intelligence/vpnIntelligence/deleteByCondition?vpnId=43120000010772
userid: 1
username: admin

### report 上报
POST {{base}}/intelligence/vpnIntelligence/report/1
userid: 1
username: admin
Content-Type: application/json

[168717732282402, 168717732282434]


### 下载导入情报模板
GET {{base}}/intelligence/vpnIntelligence/downloadTemplate
userid: 1
username: admin
Content-Type: application/json


### 导入
POST {{base}}/intelligence/vpnIntelligence/import/03/37/3
userid: 1
username: admin
Cookie: _ga_F0DQ6TTSVW=GS1.2.1690165304.1.0.1690165304.60.0.0; _ga=GA1.1.998109005.1665722205; _ga_YFKNQX5E65=GS1.1.1698805307.417.1.1698806661.0.0.0; JSESSIONID.61cd3fa6=node0v39zt0zahbdw8embfhc3001p1026.node0; SESSION=d8ca0e6c-e31c-462f-b0da-73b985ffb063
Accept: application/json, text/plain
Content-Type: multipart/form-data; boundary=WebAppBoundary
Accept-Encoding: gzip, deflate
Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6
Connection: keep-alive

--WebAppBoundary
Content-Disposition: form-data; name="file"; filename="report_intelligence_test.csv"

< 情报信息导入模版_20231016.csv

--WebAppBoundary



### 上报取证文件
POST {{base}}/intelligence/vpnIntelligence/uploadAttachMent/183061299959456
userid: 1
username: admin
Accept: application/json, text/plain
Content-Type: multipart/form-data; boundary=WebAppBoundary
Accept-Encoding: gzip, deflate
Connection: keep-alive

--WebAppBoundary
Content-Disposition: form-data; name="file"; filename="import_machine_learning.zip"

< import_machine_learning.zip

--WebAppBoundary



### 导入到测试环境
POST {{base}}/intelligence/vpnIntelligence/import/03/37/3
userid: 1
username: admin
Content-Type: multipart/form-data; boundary=WebAppBoundary

--WebAppBoundary
Content-Disposition: form-data; name="file"; filename="Desktop.zip"

< d:\\Desktop.zip

--WebAppBoundary



### 导入到测试环境
POST {{base}}/intelligence/vpnIntelligence/import/03/37/3
userid: 1
username: admin
Content-Type: multipart/form-data; boundary=WebAppBoundary

--WebAppBoundary
Content-Disposition: form-data; name="file"; filename="Desktop.csv"

< d:\\a.csv

--WebAppBoundary



### 验证PUT记录请求和返回参数
PUT {{base}}/intelligence/vpnIntelligence/updateById
Content-Type: application/json
userid: 1
username: admin
Content-Type: application/json

{
  "commandId": 33333
}

###
PUT {{base}}/intelligence/vpnIntelligence/updateById
Content-Type: application/json
userid: 1
username: admin
Content-Type: application/json

{"id":189566678006272,"version":"1.0","commandId":23441467469290,"ruleId":43120000009000,"comCode":"03","provinceId":37,"systemCode":null,"networkBusinessId":null,"typeId":2,"vpnId":43120000009000,"vpnName":"SHADOWSOCKS","vpnDomain":null,"vpnIp":"*************:8080","vpnUrl":null,"vpnUrlDecode":null,"vpnMessage":null,"vpnMessageDecode":null,"vpnLink":"https://hvbes.themiydaqgsxw.top/api/v1/sub_info/client/subscribe","contentType":2,"vpnContent":"*************:8080","vpnPort":null,"vpnAirportCode":"37","vpnCountry":"美国","vpnSoftwareCodeList":["42","44"],"vpnProtocolCode":"999","vpnProtocolEversecCode":"120","applicationProtocolCode":null,"timeStamp":"2023-12-12 04:00:00","source":"INPUT","reportStatus":"TO_BE_REPORTED","reportDatetime":null,"distributeStatus":"TO_BE_DISTRIBUTED","distributeDatetime":null,"createDatetime":"2023-12-14T05:00:36.000+00:00","attachMent":"43120000009000.zip"}
