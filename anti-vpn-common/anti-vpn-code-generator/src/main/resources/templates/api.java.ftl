package ${apiPackage};

import ${apiDtoPackage}.${apiDtoName};

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eversec.framework.feignboot.conf.EversecFeignConfiguration;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;

import org.springdoc.api.annotations.ParameterObject;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
* ${table.comment!} Api接口
*
* 模块对外暴露的api
*
* @FeignClient 注解参数解释
* value: 服务提供者提供的服务名
* url: 开发、测试环境调试无法通过服务名调用，填写服务对外暴露的地址。该参数有值 value则不生效，该参数为空时使用value值
* path: 服务前缀，实现类 @RequestMapping 也使用该值
* contextId: 全局唯一，默认为包名加类名
* configuration:
*          EversecFeignDecoderConfiguration: 自动解包，异常传递。
*          EversecFeignLogConfiguration: 记录请求日志。默认开启日志，可通过如下参数控制是否记录日志
*                  eversec.feign.request-log=true
*                  eversec.feign.result-log=true
*
*/

@Validated
@Tag(name = "${table.comment!}")
@FeignClient(value = "anti-vpn-service",
        path = ${apiName}.PATH,
        url = "<#noparse>${</#noparse>app.anti-vpn.service-url:<#noparse>}</#noparse>",
        contextId = "${apiPackage}.${apiName}",
        configuration = { EversecFeignConfiguration.class })
public interface ${apiName} {

    String PATH = "<#if package.ModuleName?? && package.ModuleName != "">/${package.ModuleName}</#if>/<#if controllerMappingHyphenStyle>${controllerMappingHyphen}<#else>${table.entityPath}</#if>";

    @Operation(summary = "根据id查询")
    @GetMapping("/getById/{id}")
    ${apiDtoName} getById(@PathVariable(value = "id") Long id);

    @Operation(summary = "查询-所有")
    @GetMapping("/listAll")
    List<${apiDtoName}> listAll();

    /**
     * @param paramDto 查询参数
     * @param pageInfo 分页参数，用到其中的size和current
     */
    @Operation(summary = "查询-分页")
    @GetMapping("/page")
    Page<${apiDtoName}> page(@ModelAttribute @ParameterObject ${apiDtoName} paramDto,
                             @ModelAttribute @ParameterObject Page<${apiDtoName}> pageInfo);

    @Operation(summary = "新增")
    @PostMapping("/save")
    void save(@RequestBody @Valid final ${apiDtoName} paramDto);

    @Operation(summary = "根据id修改")
    @PutMapping("/updateById")
    void updateById(@RequestBody @Valid final ${apiDtoName} paramDto);

    @Operation(summary = "根据id批量删除，逗号分割")
    @DeleteMapping("/deleteByIds/{ids}")
    void deleteByIds(@PathVariable(value = "ids") List<Long> ids);

}





