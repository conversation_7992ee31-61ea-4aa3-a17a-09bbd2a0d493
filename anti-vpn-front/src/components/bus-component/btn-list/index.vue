<template>
  <article class="btn-module" :class="paClassName">
    <template v-for="(items, index) in btnList">
      <el-link
        v-if="
          items.shape === 'link' &&
            !hideList.includes(items.hideName) &&
            (items.isAuth || items.isAuth === undefined)
        "
        :key="index"
        :href="items.href"
        :type="items.type"
        :underline="items.underline"
        :disabled="items.disabled || disabledList.includes(items.disabledName)"
        :icon="items.icon"
        :class="[items.class, index > 0 ? 'is-ml-10' : '']"
        @click="cliBtn(items)"
      >
        {{ items.label }}
      </el-link>
      <slot
        v-else-if="
          items.shape === 'slot' &&
            !items.hide &&
            (items.isAuth || items.isAuth === undefined)
        "
        :name="items.slotName"
        :class="items.class"
        :item="items"
      />
      <el-button
        v-else-if="
          items.shape !== 'slot' &&
            items.shape !== 'link' &&
            (items.isAuth || items.isAuth === undefined) &&
            !hideList.includes(items.hideName)
        "
        :key="index"
        :type="items.type"
        :icon="items.icon"
        :size="items.size || 'mini'"
        :loading="loading"
        :plain="items.plain"
        :round="items.round"
        :circle="items.circle"
        :disabled="items.disabled || disabledList.includes(items.disabledName)"
        :class="[items.class, index > 0 ? 'is-ml-10' : '']"
        @click="cliBtn(items)"
      >
        {{ items.label }}
      </el-button>
    </template>
  </article>
</template>

<script>
export default {
  name: 'BtnList',
  components: {},
  props: {
    btnList: {
      type: Array,
      default: () => []
    },
    paClassName: {
      type: String,
      default: () => 'section'
    },
    loading: {
      type: Boolean,
      default: () => false
    },
    disabledList: {
      type: Array,
      default: () => []
    },
    hideList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {}
  },
  mounted() {},
  methods: {
    // btn按钮点击事件
    cliBtn(items) {
      this.$emit('callbackEvent', items)
    }
  }
}
</script>

<style lang="scss" scoped>
.btn-module {
  //.el-link:nth-child(n + 2) {
  //
  //}
}
</style>
