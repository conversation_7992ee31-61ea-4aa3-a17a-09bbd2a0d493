version: '3'
services:
  anti-vpn-service:
    image: harbor-bj.eversaas.cn/harbor-anti-vpn/anti-vpn-service:10.0.0-beta.x86
    ports:
      - 32021:8080
    volumes:
      - tmp:/tmp
      - logs:/app/logs
    environment:
      - JAVA_TOOL_OPTIONS=-XX:MetaspaceSize=1G -XX:MaxMetaspaceSize=1G -Xms3G -Xmx3G -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/app/logs/dump.hprof -XX:+PrintGCDetails -Xloggc:/app/logs/gc.log
      - TZ=Asia/Shanghai
      - EUREKA_ADDRESS=http://**************:21000/eureka
      - eureka.instance.prefer-ip-address=true
      - eureka.instance.ip-address=**************
      - eureka.instance.non-secure-port=32021

      - IGNORED_INTERFACE=eth[1-2]
      - DATABASE_HOST=***************
      - DATABASE_PORT=3306
      - DATABASE_NAME=anti_vpn
      - DATABASE_USERNAME=root
      - DATABASE_PASSWORD=r9xh*yH*DcEFQE
      - REDIS_HOST=***************
      - REDIS_PORT=6379
      - REDIS_PASSWORD=r9xh*yH*DcEFQE
      - spring.datasource.dynamic.datasource.clickhouse.url=**********************************************
      - app.anti-vpn.deploy-place=group
      - app.anti-vpn.start-province-search.openStatus=false

    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "10"
    healthcheck:
      test: [ "CMD-SHELL", "curl -sS localhost:8080 || exit 1" ]
      interval: 30s
      timeout: 5s
      retries: 40
    deploy:
      replicas: 1
      restart_policy:
        condition: any
        delay: 30s
        window: 120s
      resources:
        limits:
          cpus: '2'
          memory: 4G

  anti-vpn-front:
    image: harbor-bj.eversaas.cn/harbor-anti-vpn/anti-vpn-front:10.0.0-beta.x86
    ports:
      - 32020:80
      - 32443:443
    volumes:
      - logs:/var/log/nginx
    environment:
      - TZ=Asia/Shanghai
      - GATEWAY_ADDRESS=**************:20000
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "10"
    healthcheck:
      test: [ "CMD-SHELL", "curl -sS localhost:80 || exit 1" ]
      interval: 30s
      timeout: 10s
      retries: 10
    deploy:
      replicas: 1
      restart_policy:
        condition: any
        delay: 30s
        window: 120s
      resources:
        limits:
          cpus: '0.2'
          memory: 200M

  evercm-logs:
    image: harbor-bj.eversaas.cn/harbor-anti-vpn/evercm-logs:6.4.1-alpha.x86
    ports:
      - 32024:8080
    volumes:
      - tmp:/tmp
      - logs:/app/logs
    environment:
      - JAVA_TOOL_OPTIONS=-XX:MetaspaceSize=1G -XX:MaxMetaspaceSize=1G -Xms6G -Xmx6G -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/app/logs/dump.hprof -XX:+PrintGCDetails -Xloggc:/app/logs/gc.log
      - TZ=Asia/Shanghai
      - server.port=8080
      - IGNORED_INTERFACE=eth[1-2]
      - eureka.instance.prefer-ip-address=true
      - eureka.instance.ip-address=**************
      - eureka.instance.non-secure-port=32024

      - spring.datasource.primary.jdbc-url=******************************************************************************************************************************************************************************************************************
      - spring.datasource.primary.username=root
      - spring.datasource.primary.password=r9xh*yH*DcEFQE
      - spring.datasource.evernssa.jdbc-url=******************************************************************************************************************************************************************************************************************
      - spring.datasource.evernssa.username=root
      - spring.datasource.evernssa.password=r9xh*yH*DcEFQE
      - spring.datasource.ops.jdbc-url=********************************************************************************************************************************************************************************************************************************
      - spring.datasource.ops.username=root
      - spring.datasource.ops.password=r9xh*yH*DcEFQE
      - clickhouse.datasource.url=**********************************************
      - clickhouse.datasource.driver=ru.yandex.clickhouse.ClickHouseDriver
      - clickhouse.datasource.user=default
      - spring.redis.host=***************
      - spring.redis.port=6379
      - spring.redis.password=r9xh*yH*DcEFQE
      - eversec.systemdb.datasource-url=***************************************************************************************************************************************************************************************************************
      - eversec.systemdb.datasource-username=root
      - eversec.systemdb.datasource-password=r9xh*yH*DcEFQE
      - eureka.client.serviceUrl.defaultZone=http://**************:21000/eureka
      - spring.application.name=EVERCM-LOGS
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "10"
    healthcheck:
      test: [ "CMD-SHELL", "curl -sS localhost:8080 || exit 1" ]
      interval: 30s
      timeout: 5s
      retries: 40
    deploy:
      replicas: 1
      restart_policy:
        condition: any
        delay: 30s
        window: 120s
      resources:
        limits:
          cpus: '2'
          memory: 4G

  evercm-front:
    image: harbor-bj.eversaas.cn/harbor-anti-vpn/evercm-front:6.4.1-alpha.x86
    ports:
      - 32025:80
    volumes:
      - logs:/var/log/nginx
    environment:
      - TZ=Asia/Shanghai
      - GATEWAY_HOST=**************
      - GATEWAY_PORT=20000
      - INTERFACE_HOST=evercm-logs
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "10"
    healthcheck:
      test: [ "CMD-SHELL", "curl -sS localhost:80 || exit 1" ]
      interval: 30s
      timeout: 10s
      retries: 10
    deploy:
      replicas: 1
      restart_policy:
        condition: any
        delay: 30s
        window: 120s
      resources:
        limits:
          cpus: '0.2'
          memory: 200M

  security-tech-generic:
    image: harbor-bj.eversaas.cn/harbor-anti-vpn/security-tech-generic:1.1.0-rc.x86
    ports:
      - 32023:8080
    volumes:
      - tmp:/tmp
      - logs:/app/logs
    environment:
      - JAVA_TOOL_OPTIONS=-XX:MetaspaceSize=1G -XX:MaxMetaspaceSize=1G -Xms3G -Xmx3G -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/app/logs/dump.hprof -XX:+PrintGCDetails -Xloggc:/app/logs/gc.log
      - TZ=Asia/Shanghai
      - IGNORED_INTERFACE=eth[1-2]
      - EUREKA_ADDRESS=http://**************:21000/eureka
      - eureka.instance.prefer-ip-address=true
      - eureka.instance.ip-address=**************
      - eureka.instance.non-secure-port=32023

      # 关闭minio
      - spring.storage.dynamic.enabled=false
      - DATABASE_HOST=***************
      - DATABASE_PORT=3306
      - DATABASE_NAME=anti_vpn
      - DATABASE_USERNAME=root
      - DATABASE_PASSWORD=r9xh*yH*DcEFQE
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "10"
    healthcheck:
      test: [ "CMD-SHELL", "curl -sS localhost:8080 || exit 1" ]
      interval: 30s
      timeout: 5s
      retries: 40
    deploy:
      replicas: 1
      restart_policy:
        condition: any
        delay: 30s
        window: 120s
      resources:
        limits:
          cpus: '2'
          memory: 4G

volumes:
  tmp:
  logs:
