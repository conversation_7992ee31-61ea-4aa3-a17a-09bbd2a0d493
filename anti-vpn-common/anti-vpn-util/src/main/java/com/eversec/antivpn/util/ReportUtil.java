package com.eversec.antivpn.util;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.eversec.antivpn.emnus.AntiVpnReportCodeEnum;
import lombok.extern.slf4j.Slf4j;

import javax.xml.crypto.Data;
import java.io.File;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.Clock;
import java.time.LocalDateTime;
import java.time.temporal.ChronoField;
import java.util.Date;

/**
 * 上报工具类
 * 数据上报流程中上报的数据包括跨境通信监测日志、跨境通信机器学习监测日志、跨境通信情报库信息（企业上报）、DNS解析日志、跨境通信情报和监测模型及样本信息、系统活动状态等。部侧系统为每个省企业侧系统创建一个根目录，为便于描述，下文用cross_home表示该目录，企业侧系统负责维护自己的根目录。
 * 上报文件存放路径对应的上报数据类型代码对应表1中的1-6，存放路径为：/cross_home/上报数据类型代码/上报日期/。日期采用yyyy-MM-dd的格式编写。文件以省级区域编号(2位)，见附录F.4节+运营商标识(2位)+生成时间（用1970年1月1日到文件生成时的微秒数表示）+随机数(4位).csv命名，其中：运营商标识可以用10-电信，11-移动，12-联通，13-广电，99-其他，例如：121016444433333330010001.csv。企业侧先以“xxx.csv.tmp”形式命名，表示文件在上传中，如“121016444433333330010001.csv.tmp”。数据上报完成后去除.tmp后缀，如：“121016444433333330010001.csv”。
 * 每个根目录下除了表1中所列的数据上报目录以外，还应有一个名为999的目录，用来存放部侧系统生成的上报文件处理结果。上报文件的处理结果文件存放路径为：/cross_home/999/上报日期/数据类型代码-对应的文件原名-处理结果代码。部侧系统生成数据上报处理结果为UTF-8编码的纯文本文件，文件内容为处理结果的必要描述，文件原名不含后缀（如，“上报数据类型代码-上报数据文件名-处理结果代码”形式）。其中，数据类型代码见表1，处理结果代码见表2。
 *
 */
@Slf4j
public class ReportUtil {


    /**
     * 获取上报目录
     * 上报文件存放路径对应的上报数据类型代码对应表1中的1-6，存放路径为：/cross_home/上报数据类型代码/上报日期/。日期采用yyyy-MM-dd的格式编写。
     * @param southReceiveDir
     * @param reportCodeEnum
     * @return
     */
    public static String getReportDirPath(String southReceiveDir, AntiVpnReportCodeEnum reportCodeEnum) {
        Path path = Paths.get(southReceiveDir, String.valueOf(reportCodeEnum.getCode()), DateUtil.format(new Date(), "yyyy-MM-dd"));
        FileUtil.mkParentDirs(path);
        FileUtil.mkdir(path.toFile());
        return path.toString();
    }

    /**
     * 获取上报文件名
     * 文件以省级区域编号(2位)，见附录F.4节+运营商标识(2位)+生成时间（用1970年1月1日到文件生成时的微秒数表示）+随机数(4位).csv命名，其中：运营商标识可以用10-电信，11-移动，12-联通，13-广电，99-其他，例如：121016444433333330010001.csv。企业侧先以“xxx.csv.tmp”形式命名，表示文件在上传中，如“121016444433333330010001.csv.tmp”。数据上报完成后去除.tmp后缀，如：“121016444433333330010001.csv”。
     * @return
     */
    private static String getReportFileName(Long provinceId, String comCode, Integer randomNum) {
        String fileName = StrUtil.format("{}{}{}{}.csv", provinceId, comCode, System.currentTimeMillis(), getRandomStr(randomNum));
        return fileName;
    }

    /**
     * 获取上报文件名
     * 文件以省级区域编号(2位)，见附录F.4节+运营商标识(2位)+生成时间（用1970年1月1日到文件生成时的微秒数表示）+随机数(4位).csv命名，其中：运营商标识可以用10-电信，11-移动，12-联通，13-广电，99-其他，例如：121016444433333330010001.csv。企业侧先以“xxx.csv.tmp”形式命名，表示文件在上传中，如“121016444433333330010001.csv.tmp”。数据上报完成后去除.tmp后缀，如：“121016444433333330010001.csv”。
     * @return
     */
    public static String getReportZipFileName(Long provinceId, String comCode, Integer randomNum) {
        String fileName = StrUtil.format("{}{}{}{}.zip", provinceId, comCode, System.currentTimeMillis(), getRandomStr(randomNum));
        return fileName;
    }

    private static String getRandomStr(Integer randomNum) {
        String randomStr;
        if (randomNum == null) {
            randomNum = RandomUtil.randomInt(0, 9999999);
        }
        randomStr = String.valueOf(10000000 + randomNum);
        return StrUtil.sub(randomStr, randomStr.length() - 7, randomStr.length());
    }

    public static void main(String[] args) {
        System.out.println(getRandomStr(1));
        System.out.println(getRandomStr(123456));
        System.out.println(getRandomStr(1234567));

    }

    /**
     * 获取上报文件
     * @param southReceiveDir
     * @param reportCodeEnum
     * @param provinceId
     * @param comCode
     * @return
     */
    public static File getReportFile(String southReceiveDir, AntiVpnReportCodeEnum reportCodeEnum, Long provinceId, String comCode, Integer random) {
        String reportDirPath = getReportDirPath(southReceiveDir, reportCodeEnum);
        String reportFileName = getReportFileName(provinceId, comCode, random);
        Path path = Paths.get(reportDirPath, reportFileName);
        log.info("上报{}文件: {}", reportCodeEnum.getDesc(), reportFileName);
        return path.toFile();
    }
    public static File getReportZipFile(String southReceiveDir, AntiVpnReportCodeEnum reportCodeEnum, Long provinceId, String comCode, Integer random) {
        String reportDirPath = getReportDirPath(southReceiveDir, reportCodeEnum);
        String reportFileName = getReportZipFileName(provinceId, comCode, random);
        Path path = Paths.get(reportDirPath, reportFileName);
        log.info("上报{}文件: {}", reportCodeEnum.getDesc(), reportFileName);
        return path.toFile();
    }




    /**
     * 获取当前文件的临时文件
     * @return
     */
    public static File getTempFile(File sourceFile) {
        return new File(sourceFile.getAbsolutePath() + ".tmp");
    }
}
