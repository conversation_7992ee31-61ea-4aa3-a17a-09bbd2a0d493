# 部署docker、portainer、开启swarm

参考 https://doc.weixin.qq.com/doc/w3_ABcASQZdAA4ze8rqXwoQhSzY6QTRz?scode=ANYAPwcwAA4ZXz03CDABcASQZdAA4

# 通过portainer部署业务服务

1. 修改 [anti-vpn-template.yaml](anti-vpn-template.yaml) 中的配置

2. 修改目录映射

   服务器创建 /app/data/south_receive_dir 目录


# 部署newup


# 修改 anti_vpn表中数据 platform_interface_info

1. platform_interface_info集团和省平台都需要修改CURRENT类型记录
    省平台部署需要填充字段：
        south_receive_dir：E3、E6数据上报给DC
        south_intelligence_dir: CU全量情报地址
        south_ws_address: CU webservice地址
        south_receive_secret_key: CU 的webservice key
        south_smart_address: smart地址，和cu配一个就行
        south_smart_username:
        south_smart_password:

2. 省平台修改PROVINCE类型记录

3. 导入知识库、情报库数据

4. 验证日志是否有数据，无数据需要找smart、cu、dc排查

5. 省端验证状态是否上报到集团

6. gateway加白名单
   WHITE_LIST=/anti-vpn-service/services/IDCWebService/idcCommand


# 用户名密码
```shell
portal
vpn
vR^V-HO(Dh*ip2GA

portainer
admin
admin!@3#1234

```
