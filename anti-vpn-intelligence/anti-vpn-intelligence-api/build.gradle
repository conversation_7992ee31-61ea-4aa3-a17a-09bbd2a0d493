plugins {
    id 'java-library'
}

dependencies {
    // webservice
    implementation 'javax.jws:javax.jws-api:1.1'
    implementation 'org.springframework.boot:spring-boot-starter-web'
    // 分页接口
    implementation "com.baomidou:mybatis-plus-extension:${mybatisPlusVersion}"
    compileOnly "org.springdoc:springdoc-openapi-ui:${springdocOpenapiVersion}"
    implementation "com.eversec.framework:eversec-feignboot:${eversecFeignbootVersion}"

    test {
        useTestNG()
    }
}
