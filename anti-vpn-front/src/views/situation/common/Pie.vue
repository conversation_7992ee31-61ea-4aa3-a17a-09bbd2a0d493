<template>
  <div class="chartbox">
    <e-charts
      :class="pieD.length ? 'piebg' : ''"
      class="chartH"
      type="pie"
      :options="pieOpt"
      emptyColor="#fff"
    ></e-charts>
    <ul class="chartUl">
      <li v-for="(item, i) in pieD" :key="item.name">
        <span class="dian" :style="{ background: color[i] }"> </span>
        <span class="name" :title="item.name">{{ item.name }}</span>
        <span class="percent">{{ item.percent }}%</span>
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: ""
    },
    color: {
      type: Array,
      default: () => [
        "#fe7b69",
        "#ffd459",
        "#fff33a",
        "#ccf176",
        "#7ae15f",
        "#69edca",
        "#1ca4ff",
        "#2a84ff",
        "#77a4ff",
        "#a477ff"
      ] // 从红开始
    },
    pieData: {
      default: () => [
        // { name: "域名", value: 500 },
        // { name: "URL", value: 1000 },
        // { name: "IP", value: 1000 },
        // { name: "AI模型", value: 1000 },
        // { name: "VPN协议", value: 10 }
      ],
      type: Array
    }
  },
  data() {
    return {
      pieOpt: null,
      // color: ["#2884ff", "#1ba5fe", "#68eec9", "#7ae15e", "#fcf94c","#fdd35a","#fe7b69", "#b52be2", "#6646ee", "#9442f1"],
      pieD: []
    };
  },
  watch: {
    pieData: {
      handler(val) {
        this.pieD = val;
        this.init();
      },
      immediate: true,
      deep: true
    }
  },
  mounted() {
    // this.init();
  },
  methods: {
    init() {
      let color = this.color;
      let total = this.pieD.reduce((n, cur) => n + cur.value, 0);
      this.pieD = this.pieD.map((el, i) => {
        return {
          ...el,
          percent: ((el.value * 100) / total).toFixed(2)
        };
      });
      if (this.pieD.length == 0) {
        this.pieOpt = null;
        return;
      }

      // this.pieD = [
      //   { name: "域名", value: 500 },
      //   { name: "URL", value: 1000 },
      //   { name: "IP", value: 1000 },
      //   { name: "AI模型", value: 1000 },
      //   { name: "VPN协议", value: 10 },
      //   { name: "域名", value: 500 },
      //   { name: "URL", value: 1000 },
      //   { name: "IP", value: 1000 },
      //   { name: "AI模型", value: 1000 },
      //   { name: "VPN协议", value: 10 },
      // ],
      this.pieOpt = {
        color,
        tooltip: {
          show: true,
          trigger: "item",
          // backgroundColor: "none",
          // extraCssText: "border:0; box-shadow: none;",
          formatter: function(p) {
            let d = p.data;
            // console.log('aa', p, p.color)
            //  style="background:#082243; border: 1px solid #3b4356; padding: 0 10px; min-width: 90px;"
            return `
              <div><span style="background:${p.color}; display:inline-block; width:7px;height:7px;border-radius:50%;margin-right:6px"> </span>
                <span style="color:${p.color}">${d.name}</span>
                ${d.value}
                <div>${d.percent}%</div>
              </div>`;
          }
        },
        legend: {
          show: false,
          bottom: "5%",
          left: "center",
          icon: "circle",
          itemWidth: 7,
          itemHeight: 7,
          textStyle: {
            color: "#fff"
          }
        },
        series: [
          {
            name: "",
            type: "pie",
            minAngle: 6,
            radius: ["40%", "66%"],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: "center"
            },
            emphasis: {
              label: {
                show: false,
                fontSize: 30,
                fontWeight: "bold"
              }
            },
            labelLine: {
              show: false
            },
            itemStyle: {
              borderRadius: 0,
              borderColor: "transprent",
              borderWidth: 1
            },
            data: this.pieD
            // [
            //   { value: 1048, name: "Search Engine" },
            //   { value: 735, name: "Direct" },
            //   { value: 580, name: "Email" },
            //   { value: 484, name: "Union Ads" },
            //   { value: 300, name: "Video Ads" }
            // ]
          }
        ]
      };
    }
  }
};
</script>

<style scoped lang="scss">
.chartbox {
  // height: 500px;
  .piebg {
    background: url(./img/pie_bg.png) no-repeat center 34px;
  }
  .chartH {
    height: 270px;
  }
  .chartUl {
    display: flex;
    flex-wrap: wrap;
    margin-top: -10px;
    li {
      color: #fff;
      font-size: 12px;
      background: #1e2432;
      height: 24px;
      line-height: 24px;
      clear: both;
      padding: 0 10px;
      margin-bottom: 5px;
      width: calc(50% - 8px);
      display: flex;
      align-items: center;
      &:nth-of-type(2n-1) {
        margin-right: 16px;
      }
      .dian {
        display: inline-block;
        width: 7px;
        height: 7px;
        border-radius: 50%;
        margin-right: 6px;
      }
      .name {
        width: 150px;
        overflow: hidden;
        display: block;
      }
      .percent {
        // color: #a7e7ff;
        width: 60px;
        text-align: right;
      }
    }
  }
}
</style>
