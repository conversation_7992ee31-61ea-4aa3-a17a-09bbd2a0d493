package com.eversec.antivpn.support.config.entity;

import com.eversec.antivpn.support.config.entity.po.PlatformInterfaceInfoPO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 平台接口信息
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-17
 */
@Getter
@Setter
@Schema(name = "PlatformInterfaceInfo", description = "平台接口信息")
public class PlatformInterfaceInfo extends PlatformInterfaceInfoPO implements java.io.Serializable {

    private static final long serialVersionUID = 1L;

    /*
     * 扩展字段，数据库中不存在的字段，数据库中不存在的字段需加 @TableField(exist = false)
     * 如字典翻译、子对象、子对象列表等
     */
//    @com.baomidou.mybatisplus.annotation.TableField(exist = false)
//    private String xxx;

}