package com.eversec.antivpn.intelligence.service.impl;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.text.csv.CsvUtil;
import cn.hutool.core.text.csv.CsvWriter;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ZipUtil;
import cn.hutool.crypto.digest.MD5;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eversec.antivpn.config.AntiVpnProperties;
import com.eversec.antivpn.config.enums.AntiVpnBusinessExceptionEnum;
import com.eversec.antivpn.config.enums.AntiVpnFileBizTypeEnum;
import com.eversec.antivpn.emnus.AntiVpnReportCodeEnum;
import com.eversec.antivpn.intelligence.api.dto.VpnIntelligenceCommandReportDTO;
import com.eversec.antivpn.intelligence.api.dto.VpnMachineLearningCodeDictDTO;
import com.eversec.antivpn.intelligence.api.enums.IntelligenceCommandReportStatusEnum;
import com.eversec.antivpn.intelligence.dto.VpnIntelligenceImportDTO;
import com.eversec.antivpn.intelligence.dto.VpnMachineLearningCodeDictImportDTO;
import com.eversec.antivpn.intelligence.dto.VpnMachineLearningCodeDictQueryConditionDTO;
import com.eversec.antivpn.intelligence.entity.VpnIntelligence;
import com.eversec.antivpn.intelligence.entity.VpnIntelligenceCommand;
import com.eversec.antivpn.intelligence.entity.VpnMachineLearningCodeDict;
import com.eversec.antivpn.intelligence.entity.po.VpnIntelligencePO;
import com.eversec.antivpn.intelligence.factory.VpnMachineLearningCodeDictFactory;
import com.eversec.antivpn.intelligence.mapper.VpnMachineLearningCodeDictMapper;
import com.eversec.antivpn.intelligence.service.IVpnIntelligenceService;
import com.eversec.antivpn.intelligence.service.IVpnMachineLearningCodeDictService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eversec.antivpn.support.config.api.PlatformInterfaceInfoApi;
import com.eversec.antivpn.support.config.api.dto.PlatformInterfaceInfoDTO;
import com.eversec.antivpn.util.ReportUtil;
import com.eversec.framework.webboot.common.exception.BusinessException;
import com.eversec.stark.common.utils.generator.IdUtil;
import com.eversec.stark.generic.common.util.ByteArrayMultipartFile;
import com.eversec.stark.generic.common.util.FileFeignUtil;
import com.eversec.stark.generic.file.api.CommonFileApi;
import com.eversec.stark.generic.file.api.args.UploadArgs;
import com.eversec.stark.generic.file.dto.CommonFileIdDTO;
import com.eversec.stark.generic.file.dto.CommonFileInfoDTO;
import com.eversec.stark.generic.file.types.FileContentDispositionTypeEnum;
import feign.Response;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-21
 */
@Service
@Slf4j
@AllArgsConstructor
public class VpnMachineLearningCodeDictServiceImpl extends ServiceImpl<VpnMachineLearningCodeDictMapper, VpnMachineLearningCodeDict> implements IVpnMachineLearningCodeDictService {
	private final PlatformInterfaceInfoApi platformInterfaceInfoApi;

	private final AntiVpnProperties antiVpnProperties;

	private final CommonFileApi commonFileApi;

	private final VpnMachineLearningCodeDictFactory vpnMachineLearningCodeDictFactory;


	private IVpnMachineLearningCodeDictService getVpnMachineLearningCodeDictService() {
		// 解决循环依赖
		return SpringUtil.getBean(IVpnMachineLearningCodeDictService.class);
	}

	@Override
	public Page<VpnMachineLearningCodeDict> page(VpnMachineLearningCodeDictQueryConditionDTO paramDto, Page<VpnMachineLearningCodeDict> pageInfo) {
		QueryWrapper<VpnMachineLearningCodeDict> vpnIntelligenceQueryWrapper = paramDto.autoWrapper();
		LambdaQueryWrapper<VpnMachineLearningCodeDict> vpnIntelligenceLambdaQueryWrapper = vpnIntelligenceQueryWrapper.lambda().orderByDesc(VpnMachineLearningCodeDict::getId);
		Page<VpnMachineLearningCodeDict> page = this.page(pageInfo, vpnIntelligenceLambdaQueryWrapper);
		return page;
	}

	public static void main(String[] args) {

	}

	/**
	 * 保存文件到本地
	 * @param tmpModuleFilePath
	 * @param childFile
	 * @param moduleFileMd5
	 */
	private void saveFileToLocal(Path tmpModuleFilePath, String childFile, String moduleFileMd5) {
		Path moduleFilePath = Paths.get(antiVpnProperties.getFileDataStorageDir(), antiVpnProperties.getBusinessDir().getMachineLearningModuleDir(), moduleFileMd5);
		if (!moduleFilePath.toFile().exists()) {
			FileUtil.mkParentDirs(moduleFilePath);
			FileUtil.copy(tmpModuleFilePath, moduleFilePath);
		}
	}

	/**
	 * 保存文件到minio
	 */
	@SneakyThrows
	private void saveFileToMinio(Path moduleFilePath, String childFile, String moduleFileMd5, Map<String, Long> modelFileIdMap) {
		// 查询文件是否已经存在，不存在再保存
		List<CommonFileInfoDTO> commonFileInfoDTOS = commonFileApi.listByBizKey(AntiVpnFileBizTypeEnum.MACHINE_LEARNING_MODULE_FILE.name(), moduleFileMd5);

		if (CollectionUtil.isEmpty(commonFileInfoDTOS)) {
			// 上传文件
			UploadArgs uploadArgs = UploadArgs.builder()
					.fileName(childFile)
					.fileSize(moduleFilePath.toFile().length())
					.md5(moduleFileMd5)
					.bizKey(moduleFileMd5)
					.bizType(AntiVpnFileBizTypeEnum.MACHINE_LEARNING_MODULE_FILE.name())
					.description(null)
					.build();
			MultipartFile byteArrayMultipartFile = ByteArrayMultipartFile.createMultipart(childFile, FileUtil.getInputStream(moduleFilePath.toFile()));
			CommonFileIdDTO upload = commonFileApi.upload(byteArrayMultipartFile, uploadArgs);
			modelFileIdMap.put(childFile, upload.getId());
		} else {
			CommonFileInfoDTO commonFileInfoDTO = commonFileInfoDTOS.get(0);
			modelFileIdMap.put(childFile, commonFileInfoDTO.getId());
		}
	}

	/**
	 * 1. 解析 zpi文件
	 * 2. 上传样本和模板文件到文件服务器
	 * 3. 填充机器学习样本模板数据、入库，单独一个事务
	 * 4. 调用上报接口
	 * @param comCode
	 * @param provinceId
	 * @param systemCode
	 * @param file
	 */
	@SneakyThrows
	@Override
	public void importVpnMachineLearningCodeDict(String comCode, Long provinceId, String systemCode, Integer networkBusinessId, MultipartFile file) {
		Path tmpPath = Paths.get(FileUtil.getTmpDir().getAbsolutePath(), "anti-vpn", "importVpnMachineLearningCodeDict", DateUtil.format(new Date(), "yyyyMMddHHmmSS"));
		File tempFile = FileUtil.createTempFile();
		file.transferTo(tempFile);
		ZipUtil.unzip(tempFile, tmpPath.toFile());
		List<String> files = FileUtil.listFileNames(tmpPath.toString());
		String excelFile = null;
		Map<String, String> moduleFileMd5Map = new HashMap<>();
		Map<String, Long> modelFileIdMap = new HashMap<>();

		for (String childFile : files) {
			String extName = FileUtil.extName(childFile);
			if ("xlsx".equalsIgnoreCase(extName) || "xls".equalsIgnoreCase(extName) ) {
				excelFile = childFile;
			} else {
				Path moduleFilePath = Paths.get(tmpPath.toString(), childFile);
				String moduleFileMd5 = MD5.create().digestHex(moduleFilePath.toFile()).toUpperCase();
				moduleFileMd5Map.put(childFile, moduleFileMd5);
				if (antiVpnProperties.getFileStorageType() == AntiVpnProperties.FileStorageTypeEnum.MINIO) {
					saveFileToMinio(moduleFilePath, childFile, moduleFileMd5, modelFileIdMap);
				} else if (antiVpnProperties.getFileStorageType() == AntiVpnProperties.FileStorageTypeEnum.LOCAL) {
					 saveFileToLocal(moduleFilePath, childFile, moduleFileMd5);
				}
			}
		}

		if (StrUtil.isBlank(excelFile)) {
			// 压缩包中不存在xlsx、xls文件，请检查文件内容
			throw new BusinessException(AntiVpnBusinessExceptionEnum.CODE_10011);
		}
		List<VpnMachineLearningCodeDictImportDTO> importDTOS = ExcelImportUtil.importExcel(Paths.get(tmpPath.toString(), excelFile).toFile(), VpnMachineLearningCodeDictImportDTO.class, new ImportParams() {{
			this.setHeadRows(1);
			// 设置验证支持
			this.setNeedVerfiy(false);
			this.setImportFields(VpnMachineLearningCodeDictImportDTO.MUST_FILL);
		}});

		List<VpnMachineLearningCodeDict> entityList = new ArrayList<>();
		for (VpnMachineLearningCodeDictImportDTO importDTO : importDTOS) {
			entityList.add(vpnMachineLearningCodeDictFactory.importDtoToEntity(importDTO, comCode, provinceId, systemCode, networkBusinessId,
					moduleFileMd5Map.get(importDTO.getModeFileName()),
					FileUtil.extName(importDTO.getModeFileName()),
					modelFileIdMap.get(importDTO.getModeFileName())));
		}
		getVpnMachineLearningCodeDictService().saveOrUpdateEntity(entityList);


	}

	@Override
	public String saveOrUpdateEntity(List<VpnMachineLearningCodeDict> entityList) {
		if (CollectionUtil.isEmpty(entityList)) {
			return "";
		}

		switch (antiVpnProperties.getDeployPlace()) {
			case GROUP:
				break;
			case PROVINCE:
			case SYSTEM:
//				// 验证数据是否和当前系统配置一致，不一致不可保存
//				PlatformInterfaceInfoDTO currentPlatform = platformInterfaceInfoApi.getCurrentPlatform();
//				for (VpnMachineLearningCodeDict entity : entityList) {
//					if (!entity.getComCode().equals(currentPlatform.getComCode())
//							|| !entity.getProvinceId().equals(currentPlatform.getProvinceId())
//							|| !entity.getSystemCode().equals(currentPlatform.getSystemCode())) {
//						throw new BusinessException(AntiVpnBusinessExceptionEnum.CODE_10010);
//					}
//				}
				break;
		}

		// 判断是否存在重复数据，重复数据进行覆盖操作
		List<String> modelCodeList = entityList.stream().map(VpnMachineLearningCodeDict::getModelCode).collect(Collectors.toList());
		LambdaQueryWrapper<VpnMachineLearningCodeDict> vpnIntelligenceLambdaQueryWrapper = new LambdaQueryWrapper<>();
		vpnIntelligenceLambdaQueryWrapper.in(VpnMachineLearningCodeDict::getModelCode, modelCodeList);
		List<VpnMachineLearningCodeDict> alreadyExistVpnIntelligenceList = this.list(vpnIntelligenceLambdaQueryWrapper);
		List<String> alreadyExistModuleCodeList = alreadyExistVpnIntelligenceList.stream().map(VpnMachineLearningCodeDict::getModelCode).collect(Collectors.toList());
		List<Long> alreadyExistIdList = alreadyExistVpnIntelligenceList.stream().map(VpnMachineLearningCodeDict::getId).collect(Collectors.toList());
		// 先删除历史重复数据
		this.removeByIds(alreadyExistIdList);
		// 保存新数据
		this.saveBatch(entityList, 1000);
		if (CollectionUtil.isNotEmpty(alreadyExistModuleCodeList)) {
			return "这些情报已经存在，进行了更新操作：" + alreadyExistModuleCodeList;
		} else {
			return "操作成功";
		}

	}

	@Override
	public File getModuleFile(String fileName) {
		Path moduleFilePath = Paths.get(antiVpnProperties.getFileDataStorageDir(), antiVpnProperties.getBusinessDir().getMachineLearningModuleDir(), fileName);
		return moduleFilePath.toFile();
	}

	@Override
	public void report(List<Long> ids, Integer fileType) {
		List<VpnMachineLearningCodeDict> vpnMachineLearningCodeDicts = this.listByIds(ids);
		if (CollectionUtil.isEmpty(vpnMachineLearningCodeDicts)) {
			return;
		}
		PlatformInterfaceInfoDTO currentPlatform = platformInterfaceInfoApi.getCurrentPlatform();

		Set<String> fileNameSet = vpnMachineLearningCodeDicts.stream().map(VpnMachineLearningCodeDict::getContentFileName).collect(Collectors.toSet());

		Map<String, String> contentFileNamePathMap = new HashMap<>();
		if (antiVpnProperties.getFileStorageType() == AntiVpnProperties.FileStorageTypeEnum.MINIO) {

			int i = 0;
			for (String fileName : fileNameSet) {
				i ++;
				// 2.2 下载模板和样例文件并压缩上报
				List<CommonFileInfoDTO> commonFileInfoDTOS = commonFileApi.listByBizKey(AntiVpnFileBizTypeEnum.MACHINE_LEARNING_MODULE_FILE.name(), fileName);
				if (CollectionUtil.isNotEmpty(commonFileInfoDTOS)) {
					Response response = commonFileApi.downloadFeign(commonFileInfoDTOS.get(0).getId());
					String reportFileDir = ReportUtil.getReportDirPath(antiVpnProperties.getReportDataDir(), AntiVpnReportCodeEnum.VPN_MACHINE_LEARNING_MODULE);
					Path contentFileNamePath = Paths.get(reportFileDir, commonFileInfoDTOS.get(0).getBizKey());
					// 写入ContentFileName文件(md5文件)
					FileFeignUtil.writeFromResponse(response, contentFileNamePath.toFile());
					// 生成zip文件
					File reportZipFile = ReportUtil.getReportZipFile(antiVpnProperties.getReportDataDir(), AntiVpnReportCodeEnum.VPN_MACHINE_LEARNING_MODULE,
							vpnMachineLearningCodeDicts.get(0).getProvinceId(), vpnMachineLearningCodeDicts.get(0).getComCode(), i);
					File zip = ZipUtil.zip(contentFileNamePath.toFile());
					FileUtil.copy(zip, reportZipFile, true);
					contentFileNamePathMap.put(commonFileInfoDTOS.get(0).getBizKey(), reportZipFile.getAbsolutePath());
				}
			}
		} else if (antiVpnProperties.getFileStorageType() == AntiVpnProperties.FileStorageTypeEnum.LOCAL) {
			log.info("压缩、复制本地文件：{}", fileNameSet);
			int i = 0;
			for (String fileName : fileNameSet) {
				i ++;
				// fileName 为ContentFileName
				// 判断本地文件是否存在
				Path moduleFilePath = Paths.get(antiVpnProperties.getFileDataStorageDir(), antiVpnProperties.getBusinessDir().getMachineLearningModuleDir(), fileName);
				Path moduleFilePathZip = Paths.get(moduleFilePath + ".zip");
				log.info("开始处理模型/样本文件：{}", moduleFilePath);
				if (moduleFilePath.toFile().exists()) {
					/// 压缩文件
					// 判断文件是否存在
					if (!moduleFilePathZip.toFile().exists()) {
						File zip = ZipUtil.zip(moduleFilePath.toFile());
					}
					// 生成zip文件
					File reportZipFile = ReportUtil.getReportZipFile(antiVpnProperties.getReportDataDir(), AntiVpnReportCodeEnum.VPN_MACHINE_LEARNING_MODULE,
							vpnMachineLearningCodeDicts.get(0).getProvinceId(), vpnMachineLearningCodeDicts.get(0).getComCode(), i);
					log.info("开始生成并复制模型/样本zip文件: {}", reportZipFile);

					File reportZipFileTmp = Paths.get(reportZipFile.getPath() + ".tmp").toFile();
					FileUtil.mkParentDirs(reportZipFile);
					if (!reportZipFile.exists()) {
						// 复制为临时文件再移动
						FileUtil.copy(moduleFilePathZip.toFile(), reportZipFileTmp, true);
						FileUtil.move(reportZipFileTmp, reportZipFile, true);
						contentFileNamePathMap.put(fileName, reportZipFile.getAbsolutePath());
					}
				}
			}
		}


		// 2.1 生成csv
		File reportFile = ReportUtil.getReportFile(antiVpnProperties.getReportDataDir(), AntiVpnReportCodeEnum.VPN_MACHINE_LEARNING_MODULE,
				currentPlatform.getProvinceId(), currentPlatform.getComCode(), null);
		log.info("开始生成模型/样本csv文件: {}", reportFile);
		File reportTempFile = ReportUtil.getTempFile(reportFile);
		try (CsvWriter writer = CsvUtil.getWriter(reportTempFile, StandardCharsets.UTF_8)) {
			List<String> csvLineList = new ArrayList<>();
			for (VpnMachineLearningCodeDict entity : vpnMachineLearningCodeDicts) {
				if (StrUtil.isBlank(entity.getContentFileName())) {
					continue;
				}
				String zipFile = contentFileNamePathMap.get(entity.getContentFileName());
				if (StrUtil.isBlank(zipFile)) {
					continue;
				}
				String csvLine = entity.toCsvLine(new File(zipFile).getName(), fileType);
				if (StrUtil.isNotBlank(csvLine)) {
					csvLineList.add(csvLine);
				}
			}
			writer.write(csvLineList);
			writer.flush();
		}

		FileUtil.move(reportTempFile, reportFile, true);


	}

}
