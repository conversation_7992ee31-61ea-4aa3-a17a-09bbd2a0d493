export default {
  // vpn服务商-显示数据集
  airport: {
    query: [
      [
        {
          val: 'enumKey',
          type: 'input',
          clearable: true,
          funName: 'changeItem',
          className: 'is-one-fifth is-pr-10',
          placeholder: '请输入vpn服务商ID'
        },
        {
          val: 'enumVal',
          type: 'input',
          clearable: true,
          funName: 'changeItem',
          className: 'is-one-fifth',
          placeholder: '请输入vpn服务商名称'
        }
      ]
    ],
    queryKey: ['enumKey', 'enumVal'],
    tableLabel: [
      {
        val: 'enumKey',
        label: 'vpn服务商ID',
        width: 120
      },
      {
        val: 'enumVal',
        label: 'vpn服务商名称',
        width: 120
      },
      {
        val: 'officialWebsite',
        label: '官方网站',
        width: 120
      },
      {
        val: 'registered',
        label: '注册地',
        width: 120
      },
      {
        val: 'price',
        label: '价格',
        width: 120
      },
      {
        val: 'subscriptionChannel',
        label: '订阅渠道',
        width: 120
      },
      {
        val: 'paymentMethod',
        label: '付款方式',
        width: 120
      },
      {
        val: 'discoveryTime',
        label: '发现时间',
        width: 180
      },
      {
        val: 'createDatetimeStr',
        label: '入库时间',
        width: 180
      },
      {
        val: 'introduction',
        label: '简介',
        showOverflowTooltip: true,
        width: 200
      },
      {
        label: '操作',
        type: 'slot',
        slotName: 'handle',
        fixed: 'right',
        width: '120'
      }
    ],
    dialogFormLabel: [
      [
        {
          label: 'vpn服务商ID',
          val: 'enumKey',
          type: 'input',
          maxlength: 255,
          limit: true,
          specialRules: 'nullCheck'
        }
      ],
      [
        {
          label: 'vpn服务商名称',
          val: 'enumVal',
          type: 'input',
          maxlength: 255,
          limit: true,
          specialRules: 'nullCheck'
        }
      ],
      [
        {
          label: '官方网站',
          val: 'officialWebsite',
          type: 'input',
          maxlength: 255,
          limit: true,
          specialRules: 'nullCheck'
        }
      ],
      [
        {
          label: '注册地',
          val: 'registered',
          type: 'input',
          maxlength: 255,
          limit: true,
          specialRules: 'nullCheck'
        }
      ],
      [
        {
          label: '价格',
          val: 'price',
          type: 'input',
          maxlength: 255,
          limit: true,
          specialRules: 'nullCheck'
        }
      ],
      [
        {
          label: '订阅渠道',
          val: 'subscriptionChannel',
          type: 'input',
          maxlength: 255,
          limit: true,
          specialRules: 'nullCheck'
        }
      ],
      [
        {
          label: '付款方式',
          val: 'paymentMethod',
          type: 'input',
          maxlength: 255,
          limit: true,
          specialRules: 'nullCheck'
        }
      ],
      [
        {
          label: '发现时间',
          val: 'discoveryTime',
          type: 'date-time',
          specialRules: 'selectCheck'
        }
      ],
      [
        {
          label: '简介',
          val: 'introduction',
          type: 'textarea',
          maxlength: 255,
          limit: true,
          specialRules: 'nullCheck'
        }
      ]
    ],
    keyVal: 'vpn服务商',
    getListFunName: 'getAirportInfo',
    addFunName: 'saveAirport',
    updateFunName: 'updateAirport',
    deleteFunName: 'deleteAirport',
    downloadTemplateFunName: 'downloadAirportTemplate',
    importTemplateFunName: 'importTemplateAirport',
    importerrorFunName: 'importerrorAirport'
  },
  // 协议-显示数据集
  protocol: {
    query: [
      [
        {
          val: 'enumKey2',
          type: 'input',
          clearable: true,
          funName: 'changeItem',
          className: 'is-one-fifth is-pr-10',
          placeholder: '请输入协议ID'
        },
        {
          val: 'enumVal',
          type: 'input',
          clearable: true,
          funName: 'changeItem',
          className: 'is-one-fifth is-pr-10',
          placeholder: '请输入协议名称'
        },
        {
          val: 'enumKey',
          type: 'input',
          clearable: true,
          funName: 'changeItem',
          className: 'is-one-fifth',
          placeholder: '请输入恒安协议编码'
        }
      ]
    ],
    queryKey: ['enumKey', 'enumVal', 'enumKey2'],
    tableLabel: [
      {
        val: 'enumKey2',
        label: '协议ID',
        width: 120
      },
      {
        val: 'enumKey',
        label: '恒安协议编码',
        width: 120
      },
      {
        val: 'enumVal',
        label: '协议名称',
        width: 120
      },
      {
        val: 'supportSoftware',
        label: '支持软件',
        width: 120
      },
      {
        val: 'encryptSystem',
        label: '加密体系',
        width: 120
      },
      {
        val: 'transportProtocol',
        label: '传输协议',
        width: 120
      },
      {
        val: 'camouflageProtocol',
        label: '可伪装网络协议',
        width: 120
      },
      {
        val: 'osiModel',
        label: 'OSI模型',
        width: 120
      },
      {
        val: 'discoveryTime',
        label: '发现时间',
        width: 180
      },
      {
        val: 'createDatetimeStr',
        label: '入库时间',
        width: 180
      },
      {
        label: '操作',
        type: 'slot',
        slotName: 'handle',
        fixed: 'right',
        width: 120
      }
    ],
    dialogFormLabel: [
      [
        {
          val: 'enumKey2',
          label: '协议ID',
          type: 'input',
          maxlength: 255,
          limit: true,
          specialRules: 'nullCheck'
        }
      ],
      [
        {
          val: 'enumKey',
          label: '恒安协议编码',
          type: 'input',
          maxlength: 255,
          limit: true,
          specialRules: 'nullCheck'
        }
      ],
      [
        {
          val: 'enumVal',
          label: '协议名称',
          type: 'input',
          maxlength: 255,
          limit: true,
          specialRules: 'nullCheck'
        }
      ],
      [
        {
          val: 'supportSoftware',
          label: '支持软件',
          type: 'input',
          maxlength: 255,
          limit: true,
          specialRules: 'nullCheck'
        }
      ],
      [
        {
          val: 'encryptSystem',
          label: '加密体系',
          type: 'input',
          maxlength: 255,
          limit: true,
          specialRules: 'nullCheck'
        }
      ],
      [
        {
          val: 'transportProtocol',
          label: '传输协议',
          type: 'input',
          maxlength: 255,
          limit: true,
          specialRules: 'nullCheck'
        }
      ],
      [
        {
          val: 'camouflageProtocol',
          label: '可伪装网络协议',
          type: 'input',
          maxlength: 255,
          limit: true,
          specialRules: 'nullCheck'
        }
      ],
      [
        {
          val: 'osiModel',
          label: 'OSI模型',
          type: 'input',
          maxlength: 255,
          limit: true,
          specialRules: 'nullCheck'
        }
      ],
      [
        {
          label: '发现时间',
          val: 'discoveryTime',
          type: 'date-time',
          specialRules: 'selectCheck'
        }
      ]
    ],
    keyVal: '协议',
    getListFunName: 'getProtocolInfo',
    addFunName: 'saveProtocol',
    updateFunName: 'updateProtocol',
    deleteFunName: 'deleteProtocol',
    downloadTemplateFunName: 'downloadProtocolTemplate',
    importTemplateFunName: 'importTemplateProtocol',
    importerrorFunName: 'importerrorProtocol'
  },
  // 软件-显示数据集
  software: {
    query: [
      [
        {
          val: 'enumKey',
          type: 'input',
          clearable: true,
          funName: 'changeItem',
          className: 'is-one-fifth is-pr-10',
          placeholder: '请输入软件'
        },
        {
          val: 'enumVal',
          type: 'input',
          clearable: true,
          funName: 'changeItem',
          className: 'is-one-fifth',
          placeholder: '请输入软件名称'
        }
      ]
    ],
    queryKey: ['enumKey', 'enumVal'],
    tableLabel: [
      {
        val: 'enumKey',
        label: '软件',
        width: 120
      },
      {
        val: 'enumVal',
        label: '软件名称',
        width: 120
      },
      {
        val: 'logo',
        label: 'logo',
        type: 'slot',
        slotName: 'img',
        width: 130
      },
      {
        val: 'supportSystem',
        label: '支持系统',
        width: 120
      },
      {
        val: 'supportProtocol',
        label: '支持协议',
        width: 120
      },
      {
        val: 'issuer',
        label: '开发者信息',
        width: 120
      },
      {
        val: 'authType',
        label: '授权类型',
        width: 120
      },
      {
        val: 'downloadWebsite',
        label: '下载站点',
        width: 120
      },
      {
        val: 'discoveryTime',
        label: '发现时间',
        width: 180
      },
      {
        val: 'createDatetimeStr',
        label: '入库时间',
        width: 180
      },
      {
        val: 'introduction',
        label: '简介',
        showOverflowTooltip: true,
        width: 200
      },
      {
        label: '操作',
        type: 'slot',
        slotName: 'handle',
        fixed: 'right',
        width: '120'
      }
    ],
    dialogFormLabel: [
      [
        {
          val: 'enumKey',
          label: '软件',
          type: 'input',
          maxlength: 255,
          limit: true,
          specialRules: 'nullCheck'
        }
      ],
      [
        {
          val: 'enumVal',
          label: '软件名称',
          type: 'input',
          maxlength: 255,
          limit: true,
          specialRules: 'nullCheck'
        }
      ],
      [
        {
          val: 'logo',
          label: 'logo',
          type: 'slot',
          slotName: 'imageUpload'
        }
      ],
      [
        {
          val: 'supportSystem',
          label: '支持系统',
          type: 'input',
          maxlength: 255,
          limit: true,
          specialRules: 'nullCheck'
        }
      ],
      [
        {
          val: 'supportProtocol',
          label: '支持协议',
          type: 'input',
          maxlength: 255,
          limit: true,
          specialRules: 'nullCheck'
        }
      ],
      [
        {
          val: 'issuer',
          label: '开发者信息',
          type: 'input',
          maxlength: 255,
          limit: true,
          specialRules: 'nullCheck'
        }
      ],
      [
        {
          val: 'authType',
          label: '授权类型',
          type: 'input',
          maxlength: 255,
          limit: true,
          specialRules: 'nullCheck'
        }
      ],
      [
        {
          val: 'downloadWebsite',
          label: '下载站点',
          type: 'input',
          maxlength: 255,
          limit: true,
          specialRules: 'nullCheck'
        }
      ],
      [
        {
          val: 'discoveryTime',
          label: '发现时间',
          type: 'date-time',
          specialRules: 'selectCheck'
        }
      ],
      [
        {
          val: 'introduction',
          label: '简介',
          type: 'textarea',
          maxlength: 255,
          limit: true,
          specialRules: 'nullCheck'
        }
      ]
    ],
    keyVal: '软件',
    getListFunName: 'getSoftwareInfo',
    addFunName: 'saveSoftware',
    updateFunName: 'updateSoftware',
    deleteFunName: 'deleteSoftware',
    downloadTemplateFunName: 'downloadSoftwareTemplate',
    importTemplateFunName: 'importTemplateSoftware',
    importerrorFunName: 'importerrorSoftware'
  }
}
