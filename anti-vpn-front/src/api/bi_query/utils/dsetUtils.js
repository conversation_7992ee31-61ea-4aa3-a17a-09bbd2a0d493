import api_dset from "../api/dataset";

const loadingSet = new Set();       // 请求池，避免短时间内重复请求
const callbackPool = {};            // 回调池
/**
 * 获取数据集详情
 * @param {*} dsetId
 * @param {*} callback
 * @param {*} error
 */
export function getDsetDetail(dsetId, callback, error) {
  const key = dsetId;
  if (loadingSet.has(key)) {
    // 如果正在请求，加入回调池
    if (!callbackPool[key]) callbackPool[key] = [];
    callbackPool[key].push({callback, error});
    return;
  }
  loadingSet.add(key);

  api_dset
    .getDetail(dsetId)
    .then(data => {
      loadingSet.delete(key);
      if (callbackPool[key]) {
        // 回调池触发回调 callback
        callbackPool[key].forEach(o => o.callback(data));
        delete callbackPool[key];
      }

      callback(data);
    })
    .catch(() => {
      if (error) error();
      loadingSet.delete(key);
      if (callbackPool[key]) {
        // 回调池触发回调 error
        callbackPool[key].forEach(o => o.error());
        delete callbackPool[key];
      }
    });
}
