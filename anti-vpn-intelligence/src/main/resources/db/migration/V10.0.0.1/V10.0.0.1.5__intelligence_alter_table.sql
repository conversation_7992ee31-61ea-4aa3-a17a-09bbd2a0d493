
ALTER TABLE `vpn_machine_learning_code_dict`
    add COLUMN `version` varchar(20) NOT NULL DEFAULT '1.0' COMMENT '跨境通信情报库版本格式：主版本号.副版本号，本规范主版本号为1，副版本号为0，即1.0。',
    add COLUMN `com_code` varchar(18) default NULL COMMENT '运营商编码，见附录F.2',
    add COLUMN `province_id` bigint(2) default  NULL COMMENT '省级区域编号，见附录F.4',
    add COLUMN `system_code` varchar(100) default NULL COMMENT '省平台业务系统标识',
    add COLUMN `network_business_id` int(8) DEFAULT NULL COMMENT '监测网络类型code，见附录F.3。企业侧上报时必填。部侧下发时可选填，当部侧下发为空时表示所有网络类型生效',
    add COLUMN `file_type` int(8) DEFAULT NULL COMMENT '文件类型: 1:机器学习监测模型； 2:样本文件(格式类型只为PCAP包)。',
    add COLUMN `file_name` varchar(100) default NULL COMMENT '压缩包文件名称。机器学习监测模型及样本压缩包文件名称，具体命名及上报方法见7.2.3章节',
    add COLUMN `content_file_name` varchar(100) default NULL COMMENT '压缩包文件包含内容文件名称。机器学习监测模型及样本文件文件包含内容文件名称，具体命名及上报方法见7.2.3章节',
    add COLUMN `content_filesuffix` varchar(100) default NULL COMMENT '压缩包文件包含内容文件格式。压缩包文件包含内容文件格式，例：.pcap',
    add COLUMN `time_stamp` varchar(19) NOT NULL COMMENT '模型及样本生成时间。模型及样本生成时间，采用yyyy-MM-dd HH:mm:ss格式。'
    after id;


