package com.eversec.antivpn.log.service.impl;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.eversec.antivpn.emnus.ScreenTypeEnum;
import com.eversec.antivpn.log.entity.po.GenericSysDataDict;
import com.eversec.antivpn.log.mapper.*;
import com.eversec.antivpn.log.mapper.vo.*;
import com.eversec.antivpn.log.service.GenericSysDataDictService;
import com.eversec.antivpn.log.service.UserOverallService;
import com.eversec.antivpn.log.util.*;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/8/17 16:44
 **/
@Service
public class UserOverallServiceImpl implements UserOverallService {
    @Resource
    private E1CommLogUserViewMapper e1CommLogUserViewMapper;

    @Resource
    private E2MachineLearningLogViewMapper e2MachineLearningLogViewMapper;

    @Resource
    private E1CommLogViewMapper e1CommLogViewMapper;

    @Resource
    private SearchParamUtil searchParamUtil;

    @Resource
    private E1CommLogMapper e1CommLogMapper;

    @Resource
    private LogVpnIntelligenceMapper logVpnIntelligenceMapper;

    @Resource
    private GenericSysDataDictService genericSysDataDictService;

    @Resource
    private MapDataCleanUtil mapDataCleanUtil;

    @Override
    public Long getVpnUserNum(ScreenTypeEnum screenTypeEnum) {
        BaseSearchVO baseSearchVO = getBaseSearchVO(screenTypeEnum);
        Long e1User = e1CommLogUserViewMapper.getVpnUserNum(baseSearchVO);
        Long e2User = e2MachineLearningLogViewMapper.getVpnUserNum(baseSearchVO);
        return e1User + e2User;
    }

    @Override
    public Long getUserHaveTelPhoneNum(ScreenTypeEnum screenTypeEnum) {
        BaseSearchVO baseSearchVO = getBaseSearchVO(screenTypeEnum);
        Long userCount = e1CommLogUserViewMapper.getUserHaveTelPhoneNum(baseSearchVO);
        return userCount;
    }

    @Override
    public Integer getUserLocationNum(ScreenTypeEnum screenTypeEnum) {
        BaseSearchVO baseSearchVO = searchParamUtil.getBaseSearchVO();
        baseSearchVO.setStartTimePartition(ScreenTypeEnum.getTimeCKPartition(screenTypeEnum));
        List<Long> e1SrcProvinceIdList = e1CommLogUserViewMapper.getUserLocation(baseSearchVO);
        List<Long> e2SrcProvinceIdList = e2MachineLearningLogViewMapper.getUserLocation(baseSearchVO);
        List<Long> mergeList = AggregateDataUtil.mergeLongList(e1SrcProvinceIdList, e2SrcProvinceIdList);
        return mergeList.size();
    }

    @Override
    public Integer getDestNum(ScreenTypeEnum screenTypeEnum) {
        BaseSearchVO baseSearchVO = getBaseSearchVO(screenTypeEnum);
        List<String> e1DestCountryIdList = e1CommLogUserViewMapper.getDestCountry(baseSearchVO);
        List<String> e2DestCountryIdList = e2MachineLearningLogViewMapper.getDestCountry(baseSearchVO);
        List<String> mergeList = AggregateDataUtil.mergeStringList(e1DestCountryIdList, e2DestCountryIdList);
        return mergeList.size();
    }

    @Override
    public Long getCrossBorderTotalNum(ScreenTypeEnum screenTypeEnum) {
        BaseSearchVO baseSearchVO = getBaseSearchVO(screenTypeEnum);
        Long e1Count = e1CommLogUserViewMapper.countNum(baseSearchVO);
        Long e2Count = e2MachineLearningLogViewMapper.countNum(baseSearchVO);
        return e1Count + e2Count;
    }

    @Override
    public List<TypeCountVO> getUserLocationTop10(ScreenTypeEnum screenTypeEnum) {
        List<TypeCountVO> result;
        BaseSearchVO baseSearchVO = searchParamUtil.getBaseSearchVO();
        baseSearchVO.setStartTimePartition(ScreenTypeEnum.getTimeCKPartition(screenTypeEnum));
        List<TypeCountVO> e1List = e1CommLogUserViewMapper.getLocationAggregate(baseSearchVO);
        List<TypeCountVO> e2List = e2MachineLearningLogViewMapper.getLocationAggregate(baseSearchVO);
        e1List.addAll(e2List);
        List<TypeCountVO> dealList = AggregateDataUtil.mergeTypeCountVO(e1List);
        SortUtil.sortTypeCountVODesc(dealList);
        if (dealList.size() > 10) {
            result = dealList.subList(0, 10);
        } else {
            result = dealList;
        }
        return result;
    }

    @Override
    public List<TypeCountVO> getUserDestTop10(ScreenTypeEnum screenTypeEnum) {
        List<TypeCountVO> result;
        BaseSearchVO baseSearchVO = getBaseSearchVO(screenTypeEnum);
        baseSearchVO.setStartTimePartition(ScreenTypeEnum.getTimeCKPartition(screenTypeEnum));
        List<TypeCountVO> e1List = e1CommLogUserViewMapper.getUserDest(baseSearchVO);
        List<TypeCountVO> e2List = e2MachineLearningLogViewMapper.getUserDest(baseSearchVO);
        List<TypeCountVO> dealList = AggregateDataUtil.mergeTypeCountVOSameKeyString(e1List, e2List);
        SortUtil.sortTypeCountVODesc(dealList);
        if (dealList.size() > 10) {
            result = dealList.subList(0, 10);
        } else {
            result = dealList;
        }
        return result;
    }

    @Override
    public List<UserDateHistogramVO> getUserCrossBorderCommDateHistogram(ScreenTypeEnum screenTypeEnum) {
        BaseSearchVO baseSearchVO = getBaseSearchVO(screenTypeEnum);
        baseSearchVO.setScreenTypeEnumCode(screenTypeEnum.getCode());
        baseSearchVO.setEndTimePartition(ScreenTypeEnum.getTimeCKPartition(ScreenTypeEnum.TODAY));
        List<TypeCountVO> e1List = e1CommLogViewMapper.getDateHistogram(baseSearchVO);
        List<TypeCountVO> e2List = e2MachineLearningLogViewMapper.getDateHistogram(baseSearchVO);
        List<UserDateHistogramVO> dealList = AggregateDataUtil.mergeTypeCountVOKeyTime(e1List, e2List);
        return dealList;
    }

    @Override
    public List<TypeCountVO> getUserDateHistogram(ScreenTypeEnum screenTypeEnum) {
        BaseSearchVO baseSearchVO = getBaseSearchVO(screenTypeEnum);
        baseSearchVO.setScreenTypeEnumCode(screenTypeEnum.getCode());
        baseSearchVO.setEndTimePartition(ScreenTypeEnum.getTimeCKPartition(ScreenTypeEnum.TODAY));
        List<TypeCountVO> e1DataList = e1CommLogUserViewMapper.getUserDateHistogram(baseSearchVO);
        List<TypeCountVO> e2DataList = e2MachineLearningLogViewMapper.getUserDateHistogram(baseSearchVO);
        e1DataList.addAll(e2DataList);
        List<TypeCountVO> resultList = AggregateDataUtil.mergeTypeCountVO(e1DataList);
        ErrorDealUtil.removeErrorData(resultList);
        return resultList;
    }


    @Override
    public List<TypeCountVO> getUserCrossBorderCommTimesTop10(ScreenTypeEnum screenTypeEnum) {
        List<TypeCountVO> result;
        BaseSearchVO baseSearchVO = getBaseSearchVO(screenTypeEnum);
        List<TypeCountVO> e1DataList = e1CommLogUserViewMapper.userCountLogNum(baseSearchVO);
        List<TypeCountVO> e2DataList = e2MachineLearningLogViewMapper.userCountLogNum(baseSearchVO);
        e1DataList.addAll(e2DataList);
        List<TypeCountVO> resultList = AggregateDataUtil.mergeTypeCountVO(e1DataList);
        SortUtil.sortTypeCountVODesc(resultList);
        if (resultList.size() <= 10) {
            result = resultList;
        } else {
            result = resultList.subList(0, 11);
        }
        changeUserToIpForE1AndE2(result, screenTypeEnum);
        return result;
    }

    private void changeUserToIpForE1AndE2(List<TypeCountVO> dataList, ScreenTypeEnum screenTypeEnum) {
        BaseSearchVO baseSearchVO = searchParamUtil.getBaseSearchVOWithTimePartition(screenTypeEnum);
        Map<String, TypeCountVO> map = new HashMap<>();
        List<String> users = new ArrayList<>();
        int dealDataNum = setUserIPFromE1Data(dataList, baseSearchVO, map, users);
        //所有数据来自于E1，E2无需查询
        if (dealDataNum == dataList.size()) return;
        setUserIPFromE2Data(baseSearchVO, map);
    }

    private int setUserIPFromE1Data(List<TypeCountVO> dataList, BaseSearchVO baseSearchVO, Map<String, TypeCountVO> map, List<String> users) {
        dataList.forEach(vo -> {
            if (StringUtils.isBlank(vo.getKey())) return;
            users.add(vo.getKey());
            map.put(vo.getKey(), vo);
        });
        if (users.isEmpty() || users == null) return 0;
        baseSearchVO.setStrIds(users);
        List<UserViewVO> userViewVOS = e1CommLogUserViewMapper.getUserAndSrcIp(baseSearchVO);
        userViewVOS.forEach(vo -> {
            map.get(vo.getUser()).setKey(vo.getSrcIp());
        });
        return userViewVOS.size();
    }

    private void setUserIPFromE2Data(BaseSearchVO baseSearchVO, Map<String, TypeCountVO> map) {
        if (baseSearchVO.getStrIds() == null || baseSearchVO.getStrIds().isEmpty()) return;
        List<UserViewVO> userViewVOS = e2MachineLearningLogViewMapper.getUserAndSrcIp(baseSearchVO);
        userViewVOS.forEach(vo -> {
            map.get(vo.getUser()).setKey(vo.getSrcIp());
        });
    }

    @Override
    public List<TypeCountVO> getUserCrossBorderCommResourceNodeTop10(ScreenTypeEnum screenTypeEnum) {
        BaseSearchVO baseSearchVO = searchParamUtil.getBaseSearchVOWithTimePartition(screenTypeEnum);
        List<TypeCountVO> typeCountVOList = e1CommLogUserViewMapper.getUserCrossBorderCommResourceNodeTop(baseSearchVO);
        List<String> users = new ArrayList<>();
        Map<String, TypeCountVO> map = new HashMap<>();
        setUserIPFromE1Data(typeCountVOList, baseSearchVO, map, users);
        return typeCountVOList;
    }

    @Override
    public List<TypeCountVO> getMap(ScreenTypeEnum screenTypeEnum) {
        BaseSearchVO baseSearchVO = searchParamUtil.getBaseSearchVOWithTimePartition(screenTypeEnum);
        List<TypeCountVO> e1DataList = e1CommLogUserViewMapper.getLocationAggregate(baseSearchVO);
        List<TypeCountVO> e2DataList = e2MachineLearningLogViewMapper.getLocationAggregate(baseSearchVO);
        e1DataList.addAll(e2DataList);
        List<TypeCountVO> resultList = AggregateDataUtil.mergeTypeCountVO(e1DataList);
        //增加今天数据处理
        if (screenTypeEnum != ScreenTypeEnum.TODAY) {
            addTodayNum(resultList);
        } else {
            resultList.forEach(typeCountVO -> {
                typeCountVO.setTodayNum(typeCountVO.getNum());
            });
        }
        removeErrorData(resultList);
        SortUtil.sortTypeCountVODesc(resultList);
        mapDataCleanUtil.cleanCityOutOfTheProvince(resultList);
        return resultList;
    }


    private void removeErrorData(List<TypeCountVO> data) {
        Iterator<TypeCountVO> iterator = data.iterator();
        while (iterator.hasNext()) {
            TypeCountVO vo = iterator.next();
            if (vo == null) {
                iterator.remove();
                continue;
            }
            if (vo.getKey() == null) {
                iterator.remove();
            } else if ("99".equals(vo.getKey())) {
                iterator.remove();
            }
        }
    }

    @Override
    public List<UserLogPageVO> getUserLogPageList() {
        Long todayPartition = ScreenTypeEnum.getTimeCKPartition(ScreenTypeEnum.TODAY);
        List<UserLogPageVO> list = e1CommLogMapper.getUserLogPageList(todayPartition, null);
        List<String> vpnAirportCodeList = new ArrayList<>();
        List<String> vpnSoftwareCodeList = new ArrayList<>();
        List<Long> vpnIdList = new ArrayList<>();
        list.forEach(vo -> {
            if (!StringUtils.isBlank(vo.getVpnAirportCode()) && !"".equals(vo.getVpnAirportCode())) {
                vpnAirportCodeList.add(vo.getVpnAirportCode());
            }
            if (!StringUtils.isBlank(vo.getVpnSoftwareCode()) && !"".equals(vo.getVpnSoftwareCode())) {
                vpnSoftwareCodeList.add(vo.getVpnSoftwareCode());
            }
            if (vo.getVpnId() != null) {
                vpnIdList.add(vo.getVpnId());
            }
        });
        setAirportCodeName(vpnAirportCodeList, list);
        setSoftWareInfo(vpnSoftwareCodeList, list);
        setVpnIP(vpnIdList, list);
        return decorateData(list);
    }

    private List<UserLogPageVO> decorateData(List<UserLogPageVO> data) {
        if (data == null || data.isEmpty()) return data;
        List<UserLogPageVO> head = new ArrayList<>();
        List<UserLogPageVO> tail = new ArrayList<>();
        data.forEach(vo ->{
            if (vo.getVpnAirportName() != null || vo.getVpnSoftwareName() != null){
                head.add(vo);
            }{
                tail.add(vo);
            }
        });
        head.addAll(tail);
        return head;
    }

    private void setVpnIP(List<Long> vpnIdList, List<UserLogPageVO> voList) {
        if (vpnIdList.isEmpty()) return;
        List<VpnBaseVO> vpnBaseVOList = logVpnIntelligenceMapper.getVpnIPByVpnId(vpnIdList);
        Map<Long, String> map = new HashMap<>();
        vpnBaseVOList.forEach(vo -> {
            map.put(vo.getVpnId(), vo.getVpnIp());
        });
        if (map.isEmpty()) return;
        voList.forEach(vo -> {
            if (map.containsKey(vo.getVpnId())) vo.setResourceIp(map.get(vo.getVpnId()));
        });
    }

    private void setAirportCodeName(List<String> codes, List<UserLogPageVO> voList) {
        if (codes.isEmpty()) return;
        List<DictVO> dictVOList = logVpnIntelligenceMapper.getVpnAirportCodeAndName(codes);
        Map<String, String> map = new HashMap<>();
        dictVOList.forEach(vo -> {
            map.put(vo.getVpnAirportCode(), vo.getVpnAirportName());
        });
        if (map.isEmpty()) return;
        voList.forEach(vo -> {
            if (map.containsKey(vo.getVpnAirportCode())) {
                vo.setVpnAirportName(map.get(vo.getVpnAirportCode()));
            }
        });
    }

    private void setSoftWareInfo(List<String> originCodesArray, List<UserLogPageVO> list) {
        if (originCodesArray.isEmpty()) return;
        List<String> codes = new ArrayList<>();
        Map<String, JSONArray> codeMap = new HashMap<>();
        originCodesArray.forEach(str -> {
            if (str == null || "".equals(str)) return;
            try {
                JSONArray jsonArray = JSONArray.parse(str);
                codeMap.put(str, jsonArray);
                for (int i = 0; i < jsonArray.size(); i++) {
                    codes.add(jsonArray.getString(i));
                }
            } catch (Exception e) {

            }
        });
        List<GenericSysDataDict> dictList = genericSysDataDictService.getSoftWareInfoByCode(codes);
        Map<String, GenericSysDataDict> map = new HashMap<>();
        dictList.forEach(dict -> {
            map.put(dict.getEnumKey(), dict);
        });
        list.forEach(userLogPageVO -> {
            JSONArray array = codeMap.get(userLogPageVO.getVpnSoftwareCode());
            if (array == null || array.size() == 0) return;
            for (int i = 0; i < array.size(); i++) {
                GenericSysDataDict dict = map.get(array.getString(i));
                if (dict == null) continue;
                if (userLogPageVO.getVpnSoftwareName() == null) {
                    userLogPageVO.setVpnSoftwareName(dict.getEnumVal());
                } else {
                    String softName = userLogPageVO.getVpnSoftwareName() + "," + dict.getEnumVal();
                    userLogPageVO.setVpnSoftwareName(softName);
                }

                String evex = dict.getEnumValExtend();
                if (StringUtils.isBlank(evex)) continue;
                JSONObject jsonObject = JSONObject.parseObject(evex);
                if (userLogPageVO.getVpnSoftwareProtocol() == null) {
                    userLogPageVO.setVpnSoftwareProtocol(jsonObject.getString("supportProtocol"));
                } else {
                    String protocol = userLogPageVO.getVpnSoftwareProtocol() + "," + jsonObject.getString("supportProtocol");
                    userLogPageVO.setVpnSoftwareProtocol(protocol);
                }
            }
        });
    }


    private void addTodayNum(List<TypeCountVO> list) {
        BaseSearchVO baseSearchVO = searchParamUtil.getBaseSearchVOWithTimePartition(ScreenTypeEnum.TODAY);
        List<TypeCountVO> e1DataList = e1CommLogUserViewMapper.getLocationAggregate(baseSearchVO);
        List<TypeCountVO> e2DataList = e2MachineLearningLogViewMapper.getLocationAggregate(baseSearchVO);
        e1DataList.addAll(e2DataList);
        List<TypeCountVO> todayDataList = AggregateDataUtil.mergeTypeCountVO(e1DataList);
        AggregateDataUtil.mergeAllAndTodayTypeCountVO(list, todayDataList);
    }

    private BaseSearchVO getBaseSearchVO(ScreenTypeEnum screenTypeEnum) {
        BaseSearchVO baseSearchVO = new BaseSearchVO();
        if (screenTypeEnum == ScreenTypeEnum.TODAY) {
            baseSearchVO.setTodayTimePartition(ScreenTypeEnum.getTimeCKPartition(screenTypeEnum));
        } else {
            baseSearchVO.setStartTimePartition(ScreenTypeEnum.getTimeCKPartition(screenTypeEnum));
        }
        baseSearchVO.setEndTimePartition(ScreenTypeEnum.getTimeCKPartition(ScreenTypeEnum.TODAY));
        return baseSearchVO;
    }


}
