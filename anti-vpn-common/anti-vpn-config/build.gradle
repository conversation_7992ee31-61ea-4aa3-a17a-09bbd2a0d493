plugins {
    id 'java-library'
}
dependencies {
    api "com.eversec.framework:eversec-webboot:${eversecWebbootVersion}"
    implementation project(":anti-vpn-common:anti-vpn-util")
    implementation 'org.springframework.boot:spring-boot'
    implementation 'org.springframework.boot:spring-boot-starter'
    implementation 'org.springframework.boot:spring-boot-starter-data-redis'
    implementation "cn.hutool:hutool-all:${hutoolVersion}"
    annotationProcessor "org.springframework.boot:spring-boot-configuration-processor:${springBootVersion}"
}
