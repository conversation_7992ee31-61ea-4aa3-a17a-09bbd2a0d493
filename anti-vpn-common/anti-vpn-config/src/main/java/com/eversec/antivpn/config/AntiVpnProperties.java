package com.eversec.antivpn.config;

import com.eversec.antivpn.config.enums.AntiVpnDeployPlaceEnum;
import javax.annotation.PostConstruct;

import com.eversec.antivpn.config.enums.CollectTypeEnum;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;

/**
 * anti-vpn配置
 *
 * 所有配置都在此文件
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-11-08 18:36
 */
@Data
@Configuration
@ConfigurationProperties("app.anti-vpn")
public class AntiVpnProperties {

	/**
	 * 部署地，默认省平台
	 */
	private AntiVpnDeployPlaceEnum deployPlace = AntiVpnDeployPlaceEnum.SYSTEM;

	/**
	 * 上报企业状态频率，毫秒.默认每10分钟上报一次
	 */
	private Long reportStatusDelay = 600_000L;

	/**
	 * 服务地址
	 */
	private String serviceUrl;

	/**
	 * 上报数据目录。docker内部目录，需要映射到容器外，再通过newiup上传到DC消费目录
	 */
	private String reportDataDir = "/app/data/south_receive_dir";

	/**
	 * 文件存储目录。
	 */
	private String fileDataStorageDir = "/app/data/storage";

	/**
	 * 文件存储模式
	 */
	private FileStorageTypeEnum fileStorageType = FileStorageTypeEnum.LOCAL;



	private Integer aiModelAccuracy;
	/**
	 * 临时文件目录
	 */
	private BusinessDir businessDir = new BusinessDir();

	/**
	 * VPN 大屏开启省-所属市日志查询
	 */
	private startProvinceSearch startProvinceSearch = new startProvinceSearch();

	/**
	 * 联通云网能力开放平台配置
	 */
	private UnicomCloudAbility unicomCloudAbility = new UnicomCloudAbility();

	@PostConstruct
	public void init() {
	}

	@Data
	public static class BusinessDir {

		/**
		 * 下发smart 规则时生成上传文件的地址
		 */
		private String smartRule = "SmartRule";
		/**
		 * 下发到态势感知 规则时生成上传文件的地址
		 */
		private String nssaRule = "NssaRule";

		/**
		 * 机器学习模型目录，实际使用时需要加fileDataStorageDir前缀
		 */
		private String machineLearningModuleDir = "MachineLearningModule";

		/**
		 * 情报取证文件目录，实际使用时需要加fileDataStorageDir前缀
		 */
		private String intelligenceAttachMentDir = "IntelligenceAttachMent";

		/**
		 * 指令下发目录
		 */
		private String commandDistributeDir = "CommandDistribute_cross_info_home";


	}

	/**
	 * VPN 大屏开启省-所属市日志查询
	 */
	@Data
	public static class startProvinceSearch {

		/**
		 * 开启省端查询
		 */
		private Boolean openStatus = false;

		/**
		 * 开启省端行政编码
		 */
		private Long provinceId ;

		/**
		 * 开启省端行政名称
		 */
		private String provinceName = "";

	}

	/**
	 * 联通云网能力开放平台配置
	 */
	@Data
	public static class UnicomCloudAbility {

		/**
		 * 是否启用联通云网能力开放平台短信功能
		 */
		private Boolean enabled = false;

		/**
		 * 客户端ID，由云网能力开放平台分配
		 */
		private String clientId;

		/**
		 * 客户端密钥，由云网能力开放平台分配
		 */
		private String clientSecret;

		/**
		 * 网关地址，根据环境配置
		 * 测试环境：http://172.26.63.140:8180/api/v1
		 * 生产环境：http://172.26.63.188:8180/api/v1
		 */
		private String gatewayUrl;

		/**
		 * 短信发送接口URL
		 */
		private String smsApiUrl = "publicService/psOther/1.0/aaa/message/triggerSendSms";

		/**
		 * 验证码配置
		 */
		private SmsCode smsCode = new SmsCode();

	}

	/**
	 * 短信验证码配置
	 */
	@Data
	public static class SmsCode {

		/**
		 * 验证码有效期（分钟），默认5分钟
		 */
		private Integer expireMinutes = 5;

		/**
		 * 验证码长度，默认4位
		 */
		private Integer codeLength = 4;

		/**
		 * 同一手机号发送间隔（秒），默认60秒
		 */
		private Integer sendIntervalSeconds = 60;

		/**
		 * 每日同一手机号最大发送次数，默认10次
		 */
		private Integer maxSendTimesPerDay = 10;

		/**
		 * 短信模板，{code}会被替换为验证码，{minutes}会被替换为有效期分钟数
		 */
		private String template = "您的验证码是{code}，在{minutes}分钟内有效。如非本人操作请忽略本短信。";

	}

	/**
	 * 文件存储方式
	 */
	public static enum FileStorageTypeEnum {
		MINIO,
		LOCAL,

	}

}
