# mkdir -p /data/mysql

version: '3'
services:
  minio:
    image: harbor-bj.eversaas.cn/public/minio:RELEASE.2022-05-26T05-48-41Z
    ports:
      - "39000:9000"
      - "39001:9001"
    volumes:
      - /data/minio:/data
      - /etc/localtime:/etc/localtime
    environment:
      - TZ=Asia/Shanghai
      - MINIO_SERVER_URL=http://minio:9000
      - MINIO_ROOT_USER=eversec
      - MINIO_ROOT_PASSWORD=eversec123456
    networks:
      - net
    command: server /data --address ':9000' --console-address ':9001'

    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3
    deploy:
      restart_policy:
        condition: any
        delay: 30s
      resources:
        limits:
          cpus: '1'
          memory: 1G

networks:
  net:

volumes:
  logs: