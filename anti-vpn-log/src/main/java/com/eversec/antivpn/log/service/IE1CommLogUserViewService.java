package com.eversec.antivpn.log.service;

import com.eversec.antivpn.emnus.ScreenTypeEnum;
import com.eversec.antivpn.log.api.dto.base.AggCountDTO;
import com.eversec.antivpn.log.entity.E1CommLog;
import com.eversec.antivpn.log.entity.E1CommLogUserView;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;

/**
 * <p>
 *  E1 e1_comm_log_user_view服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-18
 */
public interface IE1CommLogUserViewService extends IService<E1CommLogUserView> {

    AggCountDTO countNum(ScreenTypeEnum screenTypeEnum);

    /**
     * 根据查询分区获取所有E1数据
     * @param screenTypeEnum
     * @return
     */
    List<E1CommLogUserView> getE1LogsByPartition(ScreenTypeEnum screenTypeEnum);

    /**
     * 根据查询分区获取所有E1数据软件信息
     * @param screenTypeEnum
     * @return
     */
    List<String> getE1LogBySoftWareByPartition(ScreenTypeEnum screenTypeEnum);


    /**
     * 软件分组日志数量TOPO10
     * @param screenTypeEnum
     * @return
     */
    List<E1CommLogUserView> getLogNumBySofaWareTop10(ScreenTypeEnum screenTypeEnum);

    /**
     * 软件分组用户数量TOPO10
     * @param screenTypeEnum
     * @return
     */
    List<E1CommLogUserView> getLogNumByUserTop10(ScreenTypeEnum screenTypeEnum);

    /**
     * 用户量TOP10的软件提供服务次数趋势图
     * @param screenTypeEnum
     * @return
     */
    List<E1CommLogUserView> getLogNumBySofaWareAndDayTop10(ScreenTypeEnum screenTypeEnum);
}
