package com.eversec.antivpn.support.knowledge.upload;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Getter;
import lombok.Setter;

/**
 * 软件信息导入类
 * <AUTHOR>
 */
@Getter
@Setter
public class ImportSoftware {

    public static final String[] MUST_FILL = new String[] { "序号", "软件ID", "软件名称", "logo", "支持系统", "支持协议", "开发者信息", "授权类型", "下载地址", "简介", "发现时间" };

    @Excel(name = "序号")
    public Integer index;

    @Excel(name = "软件ID")
    private String enumKey;
    @Excel(name = "软件名称")
    public String enumVal;

//    @Excel(name = "logo",type = 2,savePath = "/temp")
    @Excel(name = "logo",type = 2)
//    private byte[] logo;
    private String logo;

    @Excel(name = "支持系统")
    private String supportSystem;

    @Excel(name = "支持协议")
    private String supportProtocol;

    @Excel(name = "开发者信息")
    private String issuer;

    @Excel(name = "授权类型")
    private String authType;

    @Excel(name = "下载地址")
    private String downloadWebsite;

    @Excel(name = "简介")
    private String introduction;

    @Excel(name = "发现时间")
    private String discoveryTime;
//    @Excel(name = "入库时间")
//    private String insert_time;
}
