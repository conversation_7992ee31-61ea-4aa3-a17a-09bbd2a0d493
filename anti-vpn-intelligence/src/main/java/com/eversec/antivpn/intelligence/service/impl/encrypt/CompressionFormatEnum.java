package com.eversec.antivpn.intelligence.service.impl.encrypt;

import lombok.Getter;

/**
 * 加密算法枚举
 *
 * 对称加密算法。
 * 0：不进行加密，明文传输；
 * 1：AES加密算法。
 * 加密密钥由部侧系统与企业侧系统事先配置确定，长度至少为20字节，最多32字节。
 * 企业侧系统应根据部侧系统的要求完成加密算法的具体实现。企业侧系统至少应支持采用CBC模式、PKCS7Padding补码方式实现AES加密算法，并可根据部侧系统的要求设置AES密钥长度、加密偏移量等参数。
 *
 */
@Getter
public enum CompressionFormatEnum {
	NO_COMPRESSION(0, "无压缩"),
	ZIP(1, "Zip压缩格式"),
	GZ(2, "gz压缩格式"),
	;

	CompressionFormatEnum(int code, String name) {
		this.code = code;
		this.name = name;
	}

	private int code;
	private String name;

}
