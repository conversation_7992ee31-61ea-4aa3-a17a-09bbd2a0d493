import { nameMap, worldSecurity } from '@/assets/mapStatic.js'

const getXY = name => {
  let [x, y] = worldSecurity[name]
  if (x < -19.1) x += 355 // 重点!!! 世界中国地图坐标位置转化
  // return { name: name, value: [x, y]};
  return [x, y]
}

export function mapOpt2(data = []) {
  let max = Math.max.apply(
    null,
    data.map(v => v.value)
  )
  let maxH = 8 // [0,8]
  data = data.map((el, i) => {
    return {
      ...el,
      height: (el.value / max) * maxH, // 画柱状图用
      idx: i + 1
    }
  })

  var scatterData = data.map(item => {
    let [x, y] = getXY(item.name)
    return [x, y + item.height]
  })
  var scatterData2 = data.map(item => {
    return {
      name: item.name,
      value: getXY(item.name)
    }
    return getXY(item.name)
  })

  var lineData = data.map(item => {
    let [x, y] = getXY(item.name)
    return {
      coords: [
        [x, y],
        [x, y + item.height]
      ]
    }
  })

  let series = [
    {
      // 地图和颜色映射
      type: 'map',
      map: '世界中国',
      geoIndex: 0,
      zlevel: 20,
      data: data
    },

    // 画柱状图
    {
      type: 'lines',
      zlevel: 5,
      effect: {
        show: false,
        period: 4, //箭头指向速度，值越小速度越快
        trailLength: 0.02, //特效尾迹长度[0,1]值越大，尾迹越长重
        symbol: 'arrow', //箭头图标
        symbolSize: 5 //图标大小
      },
      lineStyle: {
        width: 13, //尾迹线条宽度
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 1,
          y2: 0,
          colorStops: [
            {
              offset: 0,
              color: '#F09B0A' // 0% 处的颜色
            },
            {
              offset: 0.2,
              color: '#F09B0A' // 0% 处的颜色
            },
            {
              offset: 0.5,
              color: '#FEF03B' // 0% 处的颜色
            },
            {
              offset: 0.7,
              color: '#FEF03B' // 0% 处的颜色
            },
            {
              offset: 1,
              color: '#F09B0A' // 100% 处的颜色
            }
          ],
          global: false // 缺省为 false
        },
        opacity: 1, //尾迹线条透明度
        curveness: 0 //尾迹线条曲直度
      },
      label: {
        show: 0,
        position: 'end',
        formatter: '245'
      },
      silent: true,
      data: lineData
    },
    // 柱形数量显示(圆柱顶)
    {
      type: 'scatter',
      coordinateSystem: 'geo',
      geoIndex: 0,
      zlevel: 5,
      label: {
        show: !0,
        position: 'top',
        formatter: params => data[params.dataIndex].value,
        padding: [4, 8],
        backgroundColor: 'rgba(2,20,34, .8)',
        borderRadius: 5,
        borderColor: '#57cfce',
        // borderColor: "#67F0EF",
        borderWidth: 1,
        color: '#fff',
        fontSize: 12
      },
      symbol: 'circle',
      symbolSize: [13, 7],
      itemStyle: {
        color: '#FEF03B',
        opacity: 1
      },
      silent: true,
      data: scatterData
    },
    // 图中圆点设置(底)
    {
      type: 'scatter',
      coordinateSystem: 'geo',
      geoIndex: 0,
      zlevel: 5,
      symbol: 'circle',
      symbolSize: [13, 7],
      itemStyle: {
        color: '#F09B0A',
        opacity: 1,
        shadowColor: '#000',
        shadowBlur: 5,
        shadowOffsetY: 2
      },
      silent: true,
      label: {
        normal: {
          show: true,
          offset: [0, 13],
          fontSize: 11,
          textBorderColor: 'transparent',
          formatter: function(params) {
            return params.name
          }
        }
      },
      data: scatterData2
    }
  ]
  return {
    backgroundColor: 'transparent',
    tooltip: {
      show: true,
      trigger: 'item',
      backgroundColor: 'none',
      extraCssText: 'border:0; box-shadow: none;',
      formatter: function(params) {
        let value = 0
        let value2 = 0

        let d = params.data
        if (d) {
          value = d.value
          value2 = d.value2
        }
        return `
          <div class="tooltipbox_world">
            <div class="title">${params.name}</div>
<!--            <div class="tooltip-item">VPN服务商数量： <span class="color_blue">${value2}</span></div>-->
            <div class="tooltip-item">跨境通道数量： <span class="color_orange">${value}</span></div>
          </div>`
      }
    },
    geo: [
      {
        map: '世界中国',
        roam: false,
        zoom: 1.3,
        aspectScale: 0.8,
        layoutCenter: ['50%', '50%'],
        zlevel: 1,
        nameMap: nameMap,
        label: {
          normal: {
            show: false
          },
          emphasis: {
            show: false
          }
        },
        itemStyle: {
          normal: {
            areaColor: 'transparent',
            borderColor: '#4bcee2',
            opacity: 0.9
          },
          emphasis: {
            areaColor: 'rgba(255, 0, 0, .6)',
            borderColor: '#51a5c9'
          }
        },
        regions: [
          {
            // 默认选中中国（红色显示）
            name: '中国',
            // selected: true,
            value: 0,
            itemStyle: {
              normal: {
                // areaColor: 'rgba(255, 0, 0, .8)',
                borderColor: '#fff', // 省份界线颜色
                label: {
                  show: true
                }
              }
            }
          }
        ]
      }
    ],
    series: series
  }
}

export function mapOpt(data = [], dianImg, numBg) {
  data = data || [
    {
      name: '中国',
      value: 100,
      value2: 100
    },
    {
      name: '美国',
      value: 200,
      value2: 100
    },
    {
      name: '日本',
      value: 500,
      value2: 100
    },
    {
      name: '韩国',
      value: 5000,
      value2: 100
    }
  ]

  let series = [
    {
      // 地图和颜色映射
      type: 'map',
      map: '世界中国',
      geoIndex: 0,
      zlevel: 20,
      data: data
    },

    {
      // 点-图标
      name: '点',
      type: 'scatter',
      coordinateSystem: 'geo',
      // symbol: "circle",
      symbol: 'image://' + dianImg,
      symbolSize: 60,
      zlevel: 1,
      label: {
        normal: {
          show: true,
          offset: [0, 33],
          fontSize: 11,
          textBorderColor: 'transparent',
          formatter: function(params) {
            return params.name
          }
        }
      },
      tooltip: {
        show: false
      },
      data: data.map(function(dataItem) {
        let [x, y] = worldSecurity[dataItem.name]
        if (x < -19.1) x += 355 // 重点!!! 世界中国地图坐标位置转化
        return {
          name: dataItem.name,
          value: [x, y + 3.8].concat(dataItem.value)
        }
      })
    },

    {
      // 点-数量
      name: '点',
      type: 'scatter',
      coordinateSystem: 'geo',
      // symbol: "rect",
      symbol: 'image://' + numBg,
      symbolSize: [100, 38],
      // symbolSize: [67, 20],
      zlevel: 11,
      label: {
        normal: {
          show: true,
          offset: [0, 1],
          width: 100,
          align: 'center',
          position: 'inside',
          fontSize: 12,
          color: '#fff',
          lineHeight: 16,
          // textBorderType: "solid",
          // textBorderColor: "#58d0cf",
          formatter: function(params) {
            let f = data.find(el => el.name == params.name)
            if (f) return `服务商: ${f.value}\n资源节点: ${f.value2}`
            return params.value[2]
          }
        }
      },
      tooltip: {
        trigger: 'item',
        // show: false,
        formatter: function(params) {
          var num = params.value[2]
          return params.name + ': ' + num
        }
      },
      data: data.map(function(dataItem) {
        let [x, y] = worldSecurity[dataItem.name]
        if (x < -19.1) x += 355 // 重点!!! 世界中国地图坐标位置转化
        return {
          name: dataItem.name,
          value: [x, y + 16].concat(dataItem.value)
          // value: [x, y + 10].concat(dataItem.value)
        }
      })
    }
  ]
  return {
    backgroundColor: 'transparent',
    tooltip: {
      show: true,
      trigger: 'item',
      backgroundColor: 'none',
      extraCssText: 'border:0; box-shadow: none;',
      formatter: function(params) {
        let value = 0
        let value2 = 0

        let d = params.data
        if (d) {
          value = d.value
          value2 = d.value2
        }
        return `
          <div class="tooltipbox_world">
            <div class="title">${params.name}</div>
            <div class="tooltip-item">VPN服务商数量： <span class="color_blue">${value}</span></div>
            <div class="tooltip-item">VPN资源节点数量： <span class="color_orange">${value2}</span></div>
          </div>`
      }
    },
    geo: [
      {
        map: '世界中国',
        roam: false,
        zoom: 1.3,
        aspectScale: 0.8,
        layoutCenter: ['50%', '50%'],
        zlevel: 1,
        nameMap: nameMap,
        label: {
          normal: {
            show: false
          },
          emphasis: {
            show: false
          }
        },
        itemStyle: {
          normal: {
            areaColor: 'transparent',
            borderColor: '#4bcee2',
            opacity: 0.9
          },
          emphasis: {
            areaColor: 'rgba(255, 0, 0, .6)',
            borderColor: '#51a5c9'
          }
        },
        regions: [
          {
            // 默认选中中国（红色显示）
            name: '中国',
            selected: true,
            value: 0,
            itemStyle: {
              normal: {
                areaColor: 'rgba(255, 0, 0, .8)',
                borderColor: '#fff', // 省份界线颜色
                label: {
                  show: true
                }
              }
            }
          }
        ]
      }
    ],
    series: series
  }
}
