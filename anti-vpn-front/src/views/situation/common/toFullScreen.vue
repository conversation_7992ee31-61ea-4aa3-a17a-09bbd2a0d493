<template>
  <div @click="scrrenChange()" class="icon2big">
    <svg-icon :type="svgType"></svg-icon>
  </div>
</template>

<script>
import { doFullScreen } from "@/utils/screen";
const isElement = el => {
  return typeof el === "object" && el.nodeType === 1;
};

export default {
  props: {
    // 绑定元素
    bind: {
      type: [String, HTMLElement, Function],
      default: "" // 默认为空 代表 document.documentElement
    }
  },
  computed: {
    doc() {
      return document.documentElement || document.body;
    }
  },
  data() {
    return {
      svgType: "fullscreen"
    };
  },
  methods: {
    scrrenChange(val) {
      const el = this.bind
        ? isElement(this.bind)
          ? this.bind
          : document.querySelector(this.bind)
        : this.doc;
      console.log('放大的元素', this.bind, el);
      this.$emit('screenType', this.svgType)
      doFullScreen(this.svgType != "fullscreen", el);
      this.svgType =
        this.svgType == "fullscreen" ? "outfullscreen" : "fullscreen"; 
    }
  }
};
</script>

<style scoped lang="scss">
.icon2big {
  cursor: pointer;
  position: absolute;
  top: 31px;
  right: 20px;
  border: 1px solid #028fc7;
  width: 36px;
  height: 36px;
  line-height: 36px;
  background: #0b1b35;
  text-align: center;
  svg {
    color: #4dbbf6;
    font-size: 22px;
  }
}
</style>
