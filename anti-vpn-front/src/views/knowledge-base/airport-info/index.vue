<template>
  <div class="airport-info">
    <componentsCRUD v-if="type" :type="type" />
  </div>
</template>

<script>
export default {
  name: 'airport-info',
  components: {
    componentsCRUD: () => import('../components')
  },
  data() {
    return {
      type: ''
    }
  },
  computed: {},
  watch: {},
  mounted() {
    this.type = 'airport'
  },
  methods: {}
}
</script>

<style lang="scss" scoped>
.airport-info {
  height: 100%;
}
</style>
