package com.eversec.antivpn.support.knowledge.upload;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Getter;
import lombok.Setter;

/**
 * 协议信息导入类
 * <AUTHOR>
 */
@Getter
@Setter
public class ImportProtocol {

    public static final String[] MUST_FILL = new String[] { "序号", "协议ID", "eversec协议编码","协议名称", "软件支持", "加密体系", "传输协议", "可伪装网络协议", "OSI模型", "发现时间" };

    @Excel(name = "序号")
    public Integer index;

    @Excel(name = "协议ID")
    private String enumKey2;

    @Excel(name = "eversec协议编码")
    private String enumKey;

    @Excel(name = "协议名称")
    public String enumVal;

    @Excel(name = "软件支持")
    private String supportSoftware;

    @Excel(name = "加密体系")
    private String encryptSystem;

    @Excel(name = "传输协议")
    private String transportProtocol;

    @Excel(name = "可伪装网络协议")
    private String camouflageProtocol;

    @Excel(name = "OSI模型")
    private String osiModel;

    @Excel(name = "发现时间")
    private String discoveryTime;
//    @Excel(name = "入库时间")
//    private String insert_time;
}
