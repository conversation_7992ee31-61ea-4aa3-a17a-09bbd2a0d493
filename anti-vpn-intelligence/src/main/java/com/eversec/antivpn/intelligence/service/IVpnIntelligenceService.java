package com.eversec.antivpn.intelligence.service;

import cn.hutool.core.lang.Pair;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eversec.antivpn.emnus.ScreenTypeEnum;
import com.eversec.antivpn.intelligence.api.dto.VpnIntelligenceDTO;
import com.eversec.antivpn.intelligence.api.enums.IntelligenceCommandOperationTypeEnum;
import com.eversec.antivpn.intelligence.dto.AggCountDTO;
import com.eversec.antivpn.intelligence.dto.VpnIntelligenceQueryConditionDTO;
import com.eversec.antivpn.intelligence.entity.VpnIntelligence;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * vpn情报 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-10
 */
public interface IVpnIntelligenceService extends IService<VpnIntelligence> {

	/**
	 * 页面分页查询
	 * @param paramDto
	 * @param pageInfo
	 * @return
	 */
    Page<VpnIntelligence> page(VpnIntelligenceQueryConditionDTO paramDto, Page<VpnIntelligence> pageInfo);

	/**
	 * 页面保存
	 * @param paramDto
	 */
    void save(VpnIntelligenceDTO paramDto);

	/**
	 * 批量保存或者修改
	 * 保存时校验是否为当前省份数据，存在非当前省份数据进行报错。
	 * 保存时校验是否已经存在ruleId和vpnId重复的数据，如果存在重复数据进行覆盖，并提示给前端
	 * @param vpnIntelligenceList
	 */
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	String saveOrUpdateEntity(List<VpnIntelligence> vpnIntelligenceList);

	/**
	 * 页面更新
	 * @param paramDto
	 */
    void updateById(VpnIntelligenceDTO paramDto);

	/**
	 * 上报情报到部侧
	 * @param idList
	 */
    void reportVpnIntelligence(ArrayList<Long> idList, Integer networkBusinessId);

	/**
	 * 导入情报库
	 * @param comCode
	 * @param provinceId
	 * @param file
	 */
	void importVpnIntelligence(String comCode, Long provinceId, String systemCode, MultipartFile file);

	/**
	 * 调用smart和CU
	 * 全量
	 */
	void callSmartCU(String comCode, Long provinceId, String systemCode, List<VpnIntelligence> vpnIntelligenceList, IntelligenceCommandOperationTypeEnum operationTypeEnum);

	Path redistributeToNssa();
	/**
	 * 下发到smart
	 *
	 * @return left: tzRandomKey; right: snortRandomKey
	 */
	Path callSmart();

	void callCU(String comCode, Long provinceId, String systemCode, List<VpnIntelligence> vpnIntelligenceList, IntelligenceCommandOperationTypeEnum operationTypeEnum);

	/**
	 * 调用smart和CU
	 * 增量
	 * @param vpnIntelligenceList
	 */
	void increaseCallSmartCU(List<VpnIntelligence> vpnIntelligenceList, IntelligenceCommandOperationTypeEnum operationTypeEnum);

	AggCountDTO reportVpnIntelligence(ScreenTypeEnum screenTypeEnum);

	AggCountDTO totalCount(ScreenTypeEnum screenTypeEnum);

	/**
	 * 上报取证文件
	 * @param id
	 * @param file
	 */
    void uploadAttachMent(Long id, MultipartFile file);

    File getAttachMent(String fileName);

	/**
	 * 根据条件删除
	 * @param paramDto
	 */
    void deleteByCondition(VpnIntelligenceQueryConditionDTO paramDto);
}
