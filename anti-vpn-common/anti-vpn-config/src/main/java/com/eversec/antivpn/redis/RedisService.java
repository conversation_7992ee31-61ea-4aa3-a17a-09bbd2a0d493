package com.eversec.antivpn.redis;

import cn.hutool.core.util.ObjectUtil;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;

/**
 * Redis 缓存Service
 *
 * <AUTHOR>
 */
public abstract class RedisService {

    private static final Logger LOGGER = LoggerFactory.getLogger(RedisService.class);

    @Value("${spring.application.name}")
    private String applicationName;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 检查Key是否存在
     *
     * @param cacheKey 缓存key
     * @return 存在 true 不存在 false
     */
    protected Boolean existsCacheKey(String cacheKey) {
        return redisTemplate.hasKey(cacheKey);
    }

    /**
     * 添加缓存
     *
     * @param key   key
     * @param value value
     */
    protected void set(String key, Object value) {
        LOGGER.info("[ RedisCacheService ] 添加 Redis 缓存 Key :[{}] , Value: [{}]", key, value);
        redisTemplate.opsForValue().set(key, value);
    }

    /**
     * 添加缓存（失效时间）
     *
     * @param key      key
     * @param value    value
     * @param duration 时效
     * @param timeUnit 时间类型
     */
    protected void set(String key, Object value, Long duration, TimeUnit timeUnit) {
        LOGGER.info("[ RedisCacheService ] 添加 Redis 缓存 Key :[{}] , Value: [{}] , Duration:[{}] , TimeUnit: [{}]", key, value, duration, timeUnit);
        redisTemplate.opsForValue().set(key, value, duration, timeUnit);
    }


    /**
     * 获取缓存值
     *
     * @param key key
     * @return 返回值 Object
     */
    protected Object get(String key) {
        LOGGER.info("[ RedisCacheService ] 获取 Redis 缓存 Key :[{}] ", key);
        return redisTemplate.opsForValue().get(key);
    }

    /**
     * 获取缓存值，缓存值为空返回默认值
     *
     * @param key          key
     * @param defaultValue 默认值
     * @return 缓存值 Object
     */
    protected Object getOrDefault(String key, Object defaultValue) {
        Object cacheValue = this.get(key);
        if (ObjectUtil.isNotEmpty(cacheValue)) {
            LOGGER.info("[ RedisCacheService ] 获取 Redis 缓存 Key :[{}] ", key);
            return cacheValue;
        }
        LOGGER.info("[ RedisCacheService ] 获取 Redis 缓存 Key :[{}] Value 不存在返回默认值", key);
        return defaultValue;
    }

    /**
     * key 的模糊查询
     *
     * @param keyPattern 参数
     * @return 匹配到的key所有的key
     */
    protected Set<String> keysLike(String keyPattern) {
        return this.redisTemplate.keys(keyPattern);
    }

    /**
     * 删除缓存
     *
     * @param key key
     */
    protected void removeKey(String key) {
        LOGGER.info("[ RedisCacheService ] 删除 Redis 缓存 Key :[{}] ", key);
        redisTemplate.delete(key);
    }

    /**
     * Redis 原子性自增方法
     *
     * @param key          缓存key
     * @param incrementNum 自增数量
     */
    protected Long incr(String key, Long incrementNum) {
        LOGGER.info("[ RedisCacheService ] 自增 Redis 缓存 Key :[{}] 增加数量:[{}]", key, incrementNum);
        return redisTemplate.opsForValue().increment(key, incrementNum);
    }

    /**
     * Redis 原子性自减方法
     *
     * @param key          缓存key
     * @param incrementNum 自减数量
     */
    protected Long decrement(String key, Long incrementNum) {
        LOGGER.info("[ RedisCacheService ] 自减 Redis 缓存 Key :[{}] 自减数量:[{}]", key, incrementNum);
        return redisTemplate.opsForValue().decrement(key, incrementNum);
    }

    /**
     * Redis List 模型 推送
     *
     * @param key   缓存key
     * @param value value
     */
    protected void push(String key, Object value) {
        redisTemplate.opsForList().rightPush(key, value);
    }

    /**
     * Redis List 获取
     *
     * @param key key
     * @return obj
     */
    protected Object pollRight(String key) {
        return redisTemplate.opsForList().rightPop(key);
    }

    /**
     * Redis ZSet 添加
     *
     * @param key        统一标识
     * @param primaryKey 唯一标识
     * @param score      分数
     */
    protected void zsetAdd(String key, String primaryKey, Double score) {
        redisTemplate.opsForZSet().add(key, primaryKey, score);
    }


    /**
     * Redis ZSet 累加
     * @param key key
     * @param primaryKey 唯一值
     * @param score 累加分数
     */
    protected Double zsetIncrScore(String key, String primaryKey, Double score){
        return redisTemplate.opsForZSet().incrementScore(key, primaryKey, score);
    }


    /**
     * Redis ZSet 指定成员Rank 排行
     *
     * @param key        统一标识
     * @param primaryKey 唯一标识
     */
    protected Long zsetRank(String key, String primaryKey) {
        return redisTemplate.opsForZSet().rank(key, primaryKey);
    }

    /**
     * Redis zrevRank 指定成员Rank 排行 按照分数从大到小排序
     *
     * @param key        统一标识
     * @param primaryKey 唯一标识
     */
    protected Long zrevRank(String key, String primaryKey) {
        return redisTemplate.opsForZSet().reverseRank(key, primaryKey);
    }

    /**
     * 获取分数
     *
     * @param key        统一标识
     * @param primaryKey 唯一标识
     * @return
     */
    protected Double zsetRankScore(String key, String primaryKey) {
        return redisTemplate.opsForZSet().score(key, primaryKey);
    }

    /**
     * Redis ZSet 移除
     *
     * @param key        统一标识
     * @param primaryKey 唯一标识
     */
    protected void zsetRemove(String key, String primaryKey) {
        redisTemplate.opsForZSet().remove(key, primaryKey);
    }

    /**
     * 获取排序集合大小
     * @param key
     */
    protected Long zsetCard(String key){
       return redisTemplate.opsForZSet().zCard(key);
    }



    /**
     * 获取rank 情况Reverse 从大到小
     *
     * @param key 统一标识
     * @return Set<Object>
     */
    protected Set<Object> zsetListReverse(String key, Integer startIndex, Integer endIndex) {
        return redisTemplate.opsForZSet().reverseRange(key, startIndex, endIndex);
    }

    /**
     * Set 模型添加数据
     *
     * @param key   统一标识
     * @param value 值
     * @return Long Set长度
     */
    protected Long setAdd(String key, Object value) {
        return redisTemplate.opsForSet().add(key, value);
    }

    /**
     * Set 模式 检查给定的元素是否在变量中
     *
     * @param key   缓存Key
     * @param value value
     * @return 存在 true  不存在 false
     */
    protected Boolean setIsMember(String key, Object value) {
        return redisTemplate.opsForSet().isMember(key, value);
    }

    /**
     * Set 模式  获取变量中的值
     *
     * @param key 缓存Key
     * @return 所有值
     */
    protected Set<Object> setMembers(String key) {
        return redisTemplate.opsForSet().members(key);
    }


    /**
     * 包装缓存Key
     *
     * @param prefix     缓存前缀
     * @param primaryKey 唯一值
     * @return 缓存Key （string）
     */
    protected String wrapperKey(String function,String prefix, Object primaryKey) {
        StringBuilder directory = new StringBuilder();
        directory.append(function).append(RedisConstants.REDIS_CACHE_DELIMITER)
                .append(this.applicationName)
                .append(RedisConstants.REDIS_CACHE_DELIMITER)
                .append(prefix).append(RedisConstants.REDIS_CACHE_DELIMITER)
                .append(primaryKey);
        LOGGER.info("[ RedisCacheService ] 生成缓存Key:[{}]", directory.toString());
        return directory.toString();
    }

}
