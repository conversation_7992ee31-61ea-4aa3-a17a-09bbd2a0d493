package com.eversec.antivpn.log.mapper;

import com.eversec.antivpn.log.mapper.vo.*;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;
import java.util.Map;


@Mapper
public interface LogVpnIntelligenceMapper {
    Long selectVpnIntelligenceCount();
    List<Map<String,String>> selectVpnIdAndVpnAirportCode();

    List<DictVO> getVpnAirportCodeAndName(List<String> ids);

    List<VpnBaseVO> getVpnIPByVpnId(List<Long> vpnIds);

    List<TypeCountVO> getCountryResourceNodeNumTop10();

    List<VpnServicePageVO> getVpnServicePage(Integer page);

    List<VpnMapAggVO>  getCountryResourceNodeAgg();

    Long getVpnServiceCount();

    List<TypeCountVO>  getVpnServiceResourceNodeNum(List<String> vpnAirportCodes);

    List<PlatformInterfaceVO> getPlatformInterfaceInfo();

    PlatformInterfaceVO getCurrentPlatformInterfaceInfo();
}
