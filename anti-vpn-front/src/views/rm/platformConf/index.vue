<template>
  <div class="platformConf" v-loading="loading">
    <div class="is-overflow-hidden is-mb-10">
      <el-button
        class="is-float-right is-ml-10"
        type="danger"
        :disabled="!(activeList && activeList.length > 0)"
        @click="batchDelete"
      >
        批量删除
      </el-button>
      <el-button class="is-float-right" type="primary" @click="addFun">
        新增
      </el-button>
      <el-button class="is-float-right" type="primary" @click="getDataList">
        刷新
      </el-button>
    </div>
    <!-- table -->
    <publicTable
      :tableLabel="tableLabel"
      :tableData="dataList"
      ref="table"
      height="100%"
      :options="{
        highlightCurrentRow: false,
        isString: true,
        checkbox: true,
        border: false
      }"
      @handleSelectionChange="handleSelectionChange"
    >
      <template v-slot:switch="{ data }">
        <el-switch
          v-model="data.row['enabled']"
          @change="changeSwitch(data.row)"
        />
      </template>
      <template v-slot:handle="{ data }">
        <el-link
          class="is-ml-10 is-float-left"
          :underline="false"
          type="primary"
          @click="editBtn(data.row)"
        >
          编辑
        </el-link>
        <el-link
          class="is-ml-10 is-float-left"
          :underline="false"
          type="danger"
          @click="deleteBtn(data.row.id)"
        >
          删除
        </el-link>
      </template>
    </publicTable>
    <detailDialog ref="detailDialog" @dataFormSubmit="getDataList" />
  </div>
</template>

<script>
import antiVpnService from '@/api/antiVpnService'

export default {
  name: 'platformConf',
  components: {
    detailDialog: () => import('./detail-dialog')
  },
  data() {
    return {
      loading: false,
      tableLabel: [
        {
          label: '名称',
          val: 'name',
          width: 140
        },
        {
          label: '配置类型',
          val: 'configType',
          width: 120
        },
        {
          label: '启用状态',
          val: 'enabled',
          type: 'slot',
          slotName: 'switch',
          width: 120
        },
        {
          label: '运营商编码',
          val: 'comCode',
          type: 'dic-val',
          enumKey: 'ISP_CODE',
          width: 120
        },
        {
          label: '省级区域编号',
          val: 'provinceId',
          type: 'dic-val',
          enumKey: 'PROVINCE_CODE',
          width: 120
        },
        {
          label: '省平台业务系统标识',
          val: 'systemCode',
          type: 'dic-val',
          enumKey: 'SYSTEM_CODE',
          width: 160
        },
        {
          label: 'DC消费的目录',
          val: 'southReceiveDir',
          width: 140
        },
        {
          label: '上报ftp地址',
          val: 'northReportFtpAddress',
          width: 140
        },
        {
          label: '上报ftp',
          val: 'northReportFtpUsername',
          width: 140
        },
        {
          label: '上报ftp',
          val: 'northReportFtpPassword',
          width: 140
        },
        {
          label: '上报加密密钥',
          val: 'northReportSecretKey',
          width: 140
        },
        {
          label: '情报库ftp地址',
          val: 'northIntelligenceFtpAddress',
          width: 140
        },
        {
          label: '情报库ftp',
          val: 'northIntelligenceFtpUsername',
          width: 140
        },
        {
          label: '情报库ftp',
          val: 'northIntelligenceFtpPassword',
          width: 140
        },
        {
          label: '情报库解密密钥',
          val: 'northIntelligenceSecretKey',
          width: 140
        },
        {
          label: '上报企业状态',
          val: 'northCurrentNetworkBusinessIds',
          width: 140
        },
        {
          label: '情报库目录',
          val: 'southIntelligenceDir',
          width: 140
        },
        {
          label: '集团调用省平台webservice地址',
          val: 'southWsAddress',
          width: 220
        },
        {
          label: '接收解密密钥',
          val: 'southReceiveSecretKey',
          width: 140
        },
        {
          label: 'smart服务地址',
          val: 'southSmartAddress',
          width: 140
        },
        {
          label: 'smart用户名',
          val: 'southSmartUsername',
          width: 140
        },
        {
          label: 'smart密码',
          val: 'southSmartPassword',
          width: 140
        },
        {
          label: '操作',
          type: 'slot',
          slotName: 'handle',
          fixed: 'right',
          headerAlign: 'center',
          width: 120
        }
      ],
      dataList: [],
      activeList: [],
      param: null
    }
  },
  computed: {},
  watch: {},
  mounted() {
    this.getDataList()
  },
  methods: {
    // 列表查询
    getDataList() {
      this.loading = true
      antiVpnService
        .getListAll()
        .then(res => {
          this.dataList = res || []
          this.loading = false
        })
        .catch(() => {
          this.loading = false
        })
    },
    // 选中框
    handleSelectionChange(val) {
      this.activeList = val
    },
    // 删除
    deleteBtn(id) {
      this.$confirm('此操作将删除该数据, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.loading = true
          antiVpnService
            .platformInterfaceInfoDeleteByIds(id)
            .then(res => {
              this.loading = false
              this.$message({
                type: 'success',
                message: '删除成功!'
              })
              this.getDataList()
            })
            .catch(() => {
              this.loading = false
            })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    // 批量删除
    batchDelete() {
      this.$confirm('此操作将删除已选数据, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.loading = true
          antiVpnService
            .platformInterfaceInfoDeleteByIds(this.activeList.map(e => e.id))
            .then(res => {
              this.loading = false
              this.$message({
                type: 'success',
                message: '删除成功!'
              })
              this.getDataList()
            })
            .catch(() => {
              this.loading = false
            })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    // 编辑
    editBtn(item) {
      this.$refs.detailDialog.openDialog(item)
    },
    // 新增
    addFun() {
      this.$refs.detailDialog.openDialog(null)
    },
    changeSwitch(e) {
      this.loading = true
      antiVpnService
        .platformInterfaceInfoUpdateById(e)
        .then(res => {
          this.$message({
            message: '修改成功',
            type: 'success'
          })
          this.loading = false
          this.getDataList()
        })
        .catch(() => {
          this.loading = false
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.platformConf {
  height: 100%;
  ::v-deep .public-table {
    height: calc(100% - 40px);
  }
}
</style>
