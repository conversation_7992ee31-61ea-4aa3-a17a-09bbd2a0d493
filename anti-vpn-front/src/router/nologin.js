import api_auth from "@/api/auth";
import RSAUtils from "@/utils/rsaUtil";
export function noLogin(authFun) {
  console.log("noLogin");
  api_auth
    .getKeyPair()
    .then(resData => {
      const publicKey = RSAUtils.getKeyPair(
        resData.exponent,
        "",
        resData.modulus
      );
      const o = {
        userName: window.globalConfig.noLogin.userName, //账号
        password: RSAUtils.encryptedString(
          publicKey,
          window.globalConfig.noLogin.password //密码
        ),
        // code: this.formData.code,
        appKey: window.globalConfig.appKey
      };
      api_auth
        .login(o)
        .then(data => {
          // 登录成功后，清空原来的身份信息
          // clearLoginInfo();
          // window.globalCache.checkPasswordExpire = true;
          window.globalConfig.showUserOpt = false;
          authFun && authFun();
        })
        .catch(() => {
          authFun && authFun();
        });
    })
    .catch((error) => {
      console.log('111',error);
    });
}
