<template>
  <el-dialog
    class="dialog-handle"
    :title="title"
    :visible.sync="visible"
    :close-on-click-modal="false"
    :loading="loading"
  >
    <!-- form -->
    <publicForm
      ref="publicForm"
      :formDataLabel="formDataLabel"
      :labelWidth="'130px'"
      :form-data="formData"
    >
      <template v-slot:imageUpload="{ data }">
        <el-upload
          v-if="visible"
          action
          ref="uploadFile"
          :limit="1"
          :on-change="changeFile"
          accept="image/*"
          list-type="picture-card"
          :on-remove="handleRemove"
          :auto-upload="false"
          :file-list="formData.logo"
        >
          <i slot="default" class="el-icon-plus" />
          <div slot="tip" class="el-upload__tip">
            上传文件大小不能超过 5MB!
          </div>
        </el-upload>
      </template>
    </publicForm>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" :loading="loading" @click="dataFormSubmit">
        确定
      </el-button>
    </span>
  </el-dialog>
</template>

<script>
import cloneDeep from 'lodash/cloneDeep'
import antiVpnService from '@/api/antiVpnService'
import config from '../components/config'

export default {
  name: 'knowledge-base-dialog',
  components: {},
  props: {
    type: {
      type: String,
      default: () => ''
    }
  },
  data() {
    return {
      loading: false,
      visible: false,
      title: '',
      formDataLabel: config[this.type].dialogFormLabel, // form树
      formData: {}
    }
  },
  computed: {},
  watch: {},
  mounted() {},
  methods: {
    // 打开dialog
    openDialog(e) {
      if (e) {
        this.formData = cloneDeep(e)
        if (e.logo) {
          let imageType = this.getImageType(e.logo)
          this.$set(this.formData, 'logo', [
            { name: '图片.' + imageType, url: e.logo, imgPath: true }
          ])
        }
        this.title = `修改${config[this.type].keyVal}信息`
      } else {
        this.title = `新增${config[this.type].keyVal}信息`
        this.formData = {}
      }
      this.visible = true
      this.$nextTick(() => {
        this.$refs.publicForm.$refs.form.clearValidate()
      })
    },
    // 提交钱数据验证以及格式转换
    dataFormSubmit() {
      this.$refs.publicForm.$refs.form.validate(valid => {
        if (valid) {
          if (
            this.type === 'software' &&
            !(this.formData.logo && this.formData.logo.length > 0)
          ) {
            this.$message({
              message: '请上传logo',
              type: 'warning'
            })
            return
          }
          this.loading = true
          let param = {
            ...this.formData,
            createUser: localStorage.getItem('userName'),
            updateUser: localStorage.getItem('userName')
          }
          if (
            this.formData.logo &&
            this.formData.logo[0] &&
            (this.formData.logo[0].raw || this.formData.logo[0].url)
          ) {
            this.getBase64Url(
              this.formData.logo[0].raw || this.formData.logo[0].url,
              base64Url => {
                param.logo = base64Url
                this.submit(param)
              }
            )
          } else {
            this.submit(param)
          }
        }
      })
    },
    // 提交表单
    submit(param) {
      antiVpnService[
        this.formData.id
          ? config[this.type].updateFunName
          : config[this.type].addFunName
      ](param)
        .then(res => {
          this.$message({
            message: this.formData.id ? '修改成功' : '新增成功',
            type: 'success'
          })
          this.loading = false
          this.visible = false
          this.$emit('dataFormSubmit')
        })
        .catch(() => {
          this.loading = false
        })
    },
    // 添加图片成功
    changeFile(file, fileList) {
      const isLt2M = file.size / 1024 / 1024 < 5
      if (!isLt2M) this.$message.error('上传文件大小不能超过 5MB!')
      if (isLt2M) {
        this.formData.logo = fileList
      } else {
        fileList.splice(-1, 1)
      }
    },
    // 移除文件
    handleRemove(file, fileList) {
      this.formData.logo = fileList
    },
    // 图片转换Base64
    getBase64Url(fileRaw, callback) {
      try {
        let reader = new FileReader()
        reader.readAsDataURL(fileRaw)
        reader.onload = function(e) {
          let base64Str = e.target.result
          callback(base64Str)
        }
      } catch (e) {
        callback(fileRaw)
      }
    },
    // 解析图片
    getImageType(base64Url) {
      let reg = /^data:image\/(\w+)\;base64.+/
      let type = ''
      base64Url.replace(reg, function(_, $1) {
        type = $1
      })
      return type
    }
  }
}
</script>

<style lang="scss" scoped></style>
