package com.eversec.antivpn.intelligence.api.dto;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.XmlUtil;
import cn.hutool.json.JSONUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.w3c.dom.Document;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.File;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 跨境通信情报库指令下发。解密后
 */
@Getter
@Setter
@Schema(name = "VpnIntelligenceCommandDistributeDTO", description = "跨境通信情报库指令下发。解密后")
@Slf4j
public class VpnIntelligenceCommandDistributeDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    // 指令内容

    @NotEmpty
    @Schema(description = "跨境通信情报库版本格式：主版本号.副版本号，本规范主版本号为1，副版本号为0，即1.0。")
    private String version;

    @NotNull
    @Schema(description = "跨境通信情报库下发执行指令ID。当部侧下发时由部侧系统控制惟一；当企业侧系统上报时企业侧内惟一。")
    private Long commandId;


    @Schema(description = "指令来源。填写提出指令的单位，建议命名如下：\n" +
            "部XX（如：部网安）；\n" +
            "省XX（如：省管局）；\n" +
            "集团XX（如：集团反诈）；\n" +
            "企业XX（如：企业信安）等。")
    private String source;

    @NotNull
    @Schema(description = "运营商编码，见附录F.2")
    private String comCode;

    @NotNull
    @Schema(description = "省级区域编号，见附录F.4")
    private Long provinceId;

    @Schema(description = "监测网络类型code，见附录F.3。企业侧上报时必填。部侧下发时可选填，当部侧下发为空时表示所有网络类型生效")
    private Integer networkBusinessId;

    @Schema(description = "取证文件名称,当isUploadFile为0时必填。上报方法参见7.2.2章节。")
    private String attachMent;

    @Schema(description = "创建时间,生成该文件的时间")
    private String timeStamp;

    @NotNull
    @Schema(description = "操作类型,对该记录的操作类型，包括： 0-增量情报下发； 1-上报； 2-删除； 3-全量情报下发； 4-模型下发； 5-样本下发。")
    private Integer operationType;

    private List<VpnIntelligenceIncreaseDistributeDTO> comintData;

    
    public String toDistributeXml() {

        Map<String, Object> document = new HashMap<>();
        document.put("crossBorderVpnInfoData", this);
        String commandXml = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>" + JSONUtil.toXmlStr(JSONUtil.parseObj(document));
        log.info("commandXml:" + commandXml);

        return commandXml;
    }

    public static void main(String[] args) {
        XmlUtil.writeObjectAsXml(new File("test.xml"), new ArrayList<Integer>());
    }
    @Getter
    @Setter
    public static class VpnIntelligenceIncreaseDistributeDTO {
        // 情报库内容
        @Schema(description = "情报类型code： 1-VPN服务地址,例:提供VPN下载、注册、登录、购买、登出等各类访问地址和链接； 2-VPN 跨境服务地址,例：海外域名通道或海外IP通道地址； 3-模型研判； 4-样本库； 999-其他。 当操作类型为0、1、2时必填，当操作类型为3、4、5时选填。 ")
        private Integer typeId;

        @Schema(description = "情报库ID，当部侧系统下发时必填；企业侧系统上报、全量库下发时可不填写")
        private Long vpnId;

        @Schema(description = "Vpn名称或文件名称.当操作类型为0、1、2时填写VPN名称，当操作类型为3时填写的是全量跨境通信情报库文件名；当操作类型为4、5时填写的是模型或样本文件名，多个用|线分隔； 当填写Vpn名称时，优先使用中文名称，没有中文名称时使用英文名称")
        private String vpnName;

        @Schema(description = "域名。VPN活动所使用的域名地址。")
        private String vpnDomain;

        @Schema(description = "IP地址,VPN活动所使用的IP地址；IPv4使用点分表示法，IPv6使用冒分小写0位压缩表示法。")
        private String vpnIp;

        @Schema(description = "URL地址,VPN活动所使用的URL地址，采用base64编码后的URL。")
        private String vpnUrl;

        @Schema(description = "取证链接,包含VMess、VLESS、Shadowsocks、Socks、Trojan等链接地址。企业侧系统上报时根据实际情况填写。")
        private String vpnLink;

    }

}
