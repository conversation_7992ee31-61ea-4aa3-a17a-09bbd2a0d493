package com.eversec.antivpn.intelligence.api.enums;


import lombok.Getter;

/**
 * 指令操作类型
 * 和字典的 INTELLIGENCE_COMMAND_OPERATION_TYPE 对应
 */
@Getter
public enum IntelligenceCommandOperationTypeEnum {

    TEMP(-1, "临时"),
    INCREASE(0, "增量情报下发"),
    REPORT(1, "上报"),
    DELETE(2, "删除"),
    FULL(3, 1, "全量情报下发"),
    MODEL(4, 2, "模型下发"),
    SAMPLE(5, 3, "样本下发"),
    VPN_TOOL(6, "代理/VPN工具"),
    IMPORT(7, "页面导入"),

    ;


    IntelligenceCommandOperationTypeEnum(int code, String name){
        this.code = code;
        this.name = name;
    }
    IntelligenceCommandOperationTypeEnum(int code, int distributeCode, String name){
        this.code = code;
        this.name = name;
        this.distributeCode = distributeCode;
    }

    private int code;
    private String name;
    // 下发类型代码，下载全量升级包文件时用
    private int distributeCode;

}
