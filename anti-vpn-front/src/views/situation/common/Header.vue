<template>
  <div class="header_bigScreen">
    <!-- header -->
    <ul class="nav">
      <li
        v-for="(item, i) in navD"
        :key="item.name"
        :class="[item.bg, item.value === activeTab ? 'active' : '']"
        @click="navClick(item)"
      >
        {{ item.name }}
      </li>
    </ul>
    <TopTime
      :isShow_dataPicker="false"
      @timeChange="timeChange"
      :initCurI="initCurI"
      :timeOpt="timeOpt"
    ></TopTime>
    <toFullScreen bind="#bigPage" @screenType="screenType"></toFullScreen>
  </div>
</template>

<script>
import api from "@/api/rm/dic";
import { getDicAll, getDicKey2Val } from "@/assets/js/dic.js";
import sysConf from "@/api/rm/sysConf";
import TopTime from "./TopTime.vue";
import toFullScreen from "./toFullScreen.vue";

import httpRequest from "@/utils/httpRequest.js";
const BaseServer = "/anti-vpn-service";

export default {
  components: { toFullScreen, TopTime },
  props: {
    activeTab: {
      type: String,
      default: "overview"
    }
  },
  computed: {},
  data() {
    return {
      currentTab: "overview",
      curI: 0,
      navD: [
        {
          name: "总体态势",
          bg: "nav2",
          path: "/situation/overview",
          value: "overview"
        },
        {
          name: "VPN服务商态势",
          bg: "nav3",
          path: "/situation/vpn",
          value: "vpn"
        },
        {
          name: "用户态势",
          bg: "nav2",
          path: "/situation/user",
          value: "user"
        },
        { name: "AI模型态势", bg: "nav2", path: "/situation/ai", value: "ai" },
        {
          name: "跨境使用软件态势",
          bg: "nav3",
          path: "/situation/cross",
          value: "cross"
        }
      ],
      initCurI: 0,
      timeOpt: [],
    };
  },

  mounted() {
    // 配置管理/系统配置管理 动态设置默认时间区间
    sysConf.getByKey("bigScreenTime").then(res => {
      let idx = 0;
      if (res.valueType == "jsonType") {
        // let v = eval ("(" + res.itemValue + ")");
        let v = res.itemValue && JSON.parse(res.itemValue);
        console.log("时间默认设置：", v);
        idx = v.defaultIndex || 0;
        this.timeOpt = v.timeOpt;
      }
      this.initCurI = idx;
    });
    // this.initCurI = window.globalConfig.situation.bigScreenCurI;
  },
  methods: {
    screenType(screenType) {
      console.log("全屏状态", screenType);
    },
    async timeChange(screenType, ft) {
      let provinceOpt = await api.getDictList("PROVINCE_CODE");

      this.getArea().then(res => {
        localStorage.setItem("isProvince", res.openStatus);
        let provinceName = "";

        if (res.openStatus) {
          provinceName = getDicKey2Val(res.provinceId, provinceOpt, "enumVal2"); // shandong
        }
        localStorage.setItem("provinceName", provinceName);

        // console.log("地区--", provinceName, res);
      });
      this.$emit("update", screenType, ft);
    },
    getName2() {},
    getArea(o) {
      return httpRequest({
        // url: BaseServer + `/log/aiScreen/startProvince`, // 原来
        url: BaseServer + `/config/platformInterfaceInfo/startProvince`,
        method: "get"
      });
    },
    navClick(item) {
      // this.currentTab = item.value;
      // this.curI = i;
      this.$emit("update:activeTab", item.value);
      this.$emit("tab-click", item.value);
      // this.$router.push({ path: item.path }).catch(err => {
      //   console.error("err", err);
      // });
    }
  }
};
</script>

<style scoped lang="scss">
.header_bigScreen {
  position: relative;
  height: 81px;
  background: url(./img/top.png) no-repeat;
  background-size: auto 119px;
  height: 100px;
  padding: 30px 0 0 567px;
  min-width: 1910px;
  .time_top_box {
    position: absolute;
    top: 36px;
    right: 80px;
  }

  .nav {
    display: flex;

    > li {
      width: 184px;
      height: 46px;
      line-height: 46px;
      text-align: center;
      color: #a7b1bd;
      cursor: pointer;
      &:not(:first-child) {
        margin-left: -47px;
      }
      &.nav2 {
        background: url(./img/nav2.png) no-repeat;
        width: 184px;
        &.active {
          background: url(./img/nav2_active.png) no-repeat;
          color: #fff;
        }
      }
      &.nav3 {
        background: url(./img/nav3.png) no-repeat;
        width: 234px;
        &.active {
          background: url(./img/nav3_active.png) no-repeat;
          color: #fff;
        }
      }
    }
  }
}
</style>
