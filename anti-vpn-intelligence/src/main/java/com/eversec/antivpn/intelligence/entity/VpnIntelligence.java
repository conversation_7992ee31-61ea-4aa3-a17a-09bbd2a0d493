package com.eversec.antivpn.intelligence.entity;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.StrUtil;
import com.eversec.antivpn.intelligence.api.enums.IntelligenceContentTypeEnum;
import com.eversec.antivpn.intelligence.api.enums.IntelligenceSourceEnum;
import com.eversec.antivpn.intelligence.entity.po.VpnIntelligencePO;
import com.eversec.antivpn.intelligence.feign.smart.dto.SmartRuleSourceTypeEnum;
import com.eversec.antivpn.intelligence.feign.smart.dto.SmartRuleTypeEnum;
import com.eversec.stark.generic.common.types.ContentDispositionTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * vpn情报
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-10
 */
@Getter
@Setter
@Schema(name = "VpnIntelligence", description = "vpn情报")
public class VpnIntelligence extends VpnIntelligencePO implements java.io.Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 规则大类
     */
    public static final String RULE_CLASS = "43";

    /**
     * 规则小类
     */
    public static final String RULE_SUB_CLASS = "999";

    public static void main(String[] args) {
        VpnIntelligence vpnIntelligence = new VpnIntelligence();
        vpnIntelligence.setContentType(2);
        vpnIntelligence.setSource("FULL");

        vpnIntelligence.setVpnIp("*********");
        System.out.println(vpnIntelligence.tzRuleLine());

        vpnIntelligence.setVpnIp("*********:2222");
        System.out.println(vpnIntelligence.tzRuleLine());

        vpnIntelligence.setVpnIp("*********:2222#3333");
        System.out.println(vpnIntelligence.tzRuleLine());

    }

    /*
     * 扩展字段，数据库中不存在的字段，数据库中不存在的字段需加 @TableField(exist = false)
     * 如字典翻译、子对象、子对象列表等
     */
//    @com.baomidou.mybatisplus.annotation.TableField(exist = false)
//    private String xxx;

    /**
     *
     * 99101000563635|2|1|3|99|101|2||0|0|test.com/test||eyJydWxlX3NvdXJjZSI6IjIiLCJkYl90eXBlIjoxLCJ3YXJuX2xldmVsIjoyLCJhY2N1cmFjeSI6MywiZGVzY3JpcHRpb24iOiLop4TliJnmj4/ov7AiLCJtYWxld2FyZV9uYW1lIjoi55eF5q+S5ZCN56ewIiwibWFsZXdhcmVfdHlwZSI6IjIifQ==
     * @return
     */
    public String tzRuleLine() {
        /*
         * https://doc.weixin.qq.com/doc/w3_AM4A_AZJAP8MS0NgS1ARWKquMs4HX?scode=ANYAPwcwAA4UUm8ynYABcASQZdAA4
         */

        int ruleType;
        String ruleContent;
        IntelligenceContentTypeEnum contentType = EnumUtil.getBy(IntelligenceContentTypeEnum::getCode, getContentType());
        if (contentType == null) {
            contentType = IntelligenceContentTypeEnum.OTHER;
        }

        switch (contentType) {
            case DOMAIN:
                ruleType =SmartRuleTypeEnum.DOMAIN.getCode();
                ruleContent = getVpnDomain();
                break;
            case IP:
                /*
                VPN活动所使用的IP地址及端口,多个端口间用#分隔，如果端口为空则表示监测所有端口,具体填报形式如下:
                **********；
                **********:3334；
                **********:3334#3335#3338。
                 */
                String vpnIp = getVpnIp();
                if (StrUtil.isBlank(vpnIp)) {
                    vpnIp = "";
                }
                // 当包含多个端口，只保留ip部分
                if (vpnIp.contains(":") && vpnIp.contains("#")) {
                    // **********:3334#3335#3338
                    vpnIp = vpnIp.split(":")[0];
                }

                if (vpnIp.contains(":")) {
                    // **********:3334

                    ruleType =SmartRuleTypeEnum.IP_PORT.getCode();
                    String[] ipPort = vpnIp.split(":");
                    ruleContent = ipPort[0] + "-" + ipPort[1];
                } else {
                    // **********
                    ruleType =SmartRuleTypeEnum.IP.getCode();
                    ruleContent = vpnIp;
                }

                break;
            case URL:
                ruleType =SmartRuleTypeEnum.URL.getCode();
                ruleContent = getVpnUrlDecode();
                break;
            case VPN_PROTOCOL:
                ruleType =SmartRuleTypeEnum.VPN_PROTOCOL.getCode();
                ruleContent = getVpnProtocolEversecCode();
                break;
            case OTHER:
                // 判断
                if (StrUtil.isNotBlank(getVpnPort())) {
                    ruleType =SmartRuleTypeEnum.IP_PORT.getCode();
                    ruleContent = getVpnIp() + "-" + getVpnPort();
                } else {
                    // 不支持的情况，直接返回
                    return "";
                }
                break;
            default:
                // 不支持的情况，直接返回
                return "";
        }

        int ruleSource;
        IntelligenceSourceEnum sourceEnum = EnumUtil.getBy(IntelligenceSourceEnum::name, getSource());
        switch (sourceEnum) {
            case FULL:
            case INCREASE:
                ruleSource = SmartRuleSourceTypeEnum.工信部.getCode();
                break;
            case INPUT:
                ruleSource = SmartRuleSourceTypeEnum.eversec_跨境VPN.getCode();
                break;
            case REPORT:
                ruleSource = SmartRuleSourceTypeEnum.省公司.getCode();
                break;
            default:
                return "";

        }

        ArrayList<Object> lineItem = new ArrayList<>();
        String ruleId = String.valueOf(getRuleId());
        String ruleClass = RULE_CLASS;
        String ruleSubClass = RULE_SUB_CLASS;
        if (ruleId.length() == 14) {
            ruleClass = ruleId.substring(0, 2);
            ruleSubClass = ruleId.substring(2, 5);
        }
        lineItem.add(getRuleId());
        lineItem.add(2);
        lineItem.add(ruleType);
        lineItem.add(3);
        lineItem.add(ruleClass);
        lineItem.add(ruleSubClass);
        lineItem.add(ruleSource);
        lineItem.add("");
        lineItem.add("");
        lineItem.add("");
        lineItem.add(ruleContent);
        lineItem.add(getVpnName());
        lineItem.add(Base64.encode("{}"));

        return CollectionUtil.join(lineItem, "|");
    }

    /**
     * alert tcp *************** any -> any any (msg:"http zip.pcap"; content:"GET"; nocase; logto:"b.pcap"; sid: 3102080000000001; rev:8;)
     * @return
     */
    public String snortEvtLine() {
        return getVpnMessage();
    }

}