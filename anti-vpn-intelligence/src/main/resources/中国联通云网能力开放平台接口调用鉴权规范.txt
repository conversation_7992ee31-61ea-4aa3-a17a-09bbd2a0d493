中国联通云网能力开放平台系统
接口调用鉴权规范V3.0

版本迭代记录
序号	版本号	迭代内容	修订清单	发布时间
1	1.0	新建		2018.12.04
2	2.0	新增	补充了sign签名的调发	2019.07.17
3	2.1	新增	完善了sign签名的描述	2019.08.30
4	2.2	新增	新增测试接口调用说明	2019.10.21
5	2.3	新增	新增短信、邮件接口调用说明	2019.10.21
6	2.4	修改	完善签名鉴权算法	2019.10.22
7	2.5	修改	签名鉴权算法部分版本改为2.0，时间戳添加毫秒	2019.12.06
8	2.6	修改	更新短信、邮件接口地址	2019.12.10
9	2.6.1	修改	更新邮件接口入参及示例	2020.04.15
10	2.7	新增	新增短信验证码发送接口	2020.04.16
11	3.0	新增	增加SDK使用说明	2020.12.30


目  录
1、概述	1
1.1 编制目的	1
1.2 适用范围	1
1.3 术语缩略语	1
2. 技术实现规范	2
2.1 能力平台说明	2
2.2 通信机制与协议说明	2
2.3 平台gateway地址	2
2.4 动态令牌鉴权调用方式	3
2.5 签名鉴权调用方式	4
3. 能力平台接口开放	8
3.1 测试调用接口	8
3.1.1 接口请求说明	8
3.1.2 SDK调用示例	8
3.1.3 输入参数	8
3.1.4 输入参数说明	8
3.1.5 输出参数	8
3.1.6 输出参数说明	9
3.1.7 业务规则及约束	9



1、概述
1.1 编制目的
统一标准和规范：进行服务化标准约束，对外提供统一的标准和规范，指导各子门户改造，易于服务能力的开放管理和积累沉淀；
服务开放：开放基础能力，实现各业务能力共享，服务开放标准、安全，易于共享。
指导云联网和云组网以及其他需要接入的第三方开发的应用如何使用平台。
1.2 适用范围
本规范适用于中国联通云网能力开放平台，说明能力调用的接口规范。
1.3 术语缩略语


2. 技术实现规范
2.1 能力平台说明
云网能力开放平台为各系统提供统一的能力调用。
如有需要通过平台开放的能力，请联系平台管理员并按要求提供接口文档。
2.2 通信机制与协议说明
各系统与能力平台通信，数据组织格式采用JSON格式。
REST协议+JSON：能力平台的标准协议类型，使用平台标准的鉴权方式，建议采用这种协议。
REST调用地址格式为：http://host:port/子系统名称/{版本号}/{业务模块}/{能力名称}
例如DCI调用订单轮询接口的URL为：http://xxxx:xxxx/dci/v1/order/pollingOrder
2.3 平台gateway地址
现网生产环境
廊坊资源池地址：************:8180
郑州资源池DCN地址：************:8180
郑州资源池内部地址：*************:8180
现网测试环境
廊坊资源池地址：***********:29988
郑州资源池DCN地址：************:8180
郑州资源池内部地址：*************:8180
开发测试环境
gateway地址：**************:21111

支持动态令牌鉴权和签名鉴权两种调用方式。动态令牌鉴权方式即在调用能力前需要先获取动态令牌的方式，签名鉴权方式不需要获取动态令牌，只需要在调用能力时携带数字签名。
2.4 动态令牌鉴权调用方式
平台支持动态令牌方式调用能力，调用方式说明如下：
1.需要从云网能力开放平台调用能力的应用或系统，首先需要申请clientId和clientSecret。申请方式为线下发邮件给平台管理人员，由管理人员在能力平台生成clientId和clientSecret。
2.获得clientId和clientSecret后，调用能力前需要先从平台获取动态票据。
获取方式：HTTP GET
调用URL：http://gatewayAdress/api/v1/token
输入参数：
序号	参数名称	参数描述	数据类型	是否必填	备注
1	clientId	各系统申请的，测试AWDTAPLTTY5XGGF5	String	是
2	timestamp	时间戳yyyyMMddHHmmssSSS	String	是
3	sign	签名	String	是
输入参数说明：Sign的生成规则为SHA256(clientId+clientSecret+timestamp)。

输出参数：
序号	参数名称	参数描述	数据类型	是否必填	备注
1	code	0失败1成功	String	是
2	desc	结果描述	String	是
3	abilityCT	能力平台动态票据	String	是
4	expire	失效时间	Number	是	几小时后失效
3.使用动态票据调用平台开放的能力。
获取动态令牌后，再使用动态令牌调用能力。调用时需要在HTTP请求头添加鉴权参数CB-Ability-Authorization和业务参数CB-Ability-Business，说明如下：
1)鉴权参数 CB-Ability-Authorization
HTTP消息头加入JSON字符串格式的鉴权信息参数，例如CB-Ability-Authorization:{"version":"v1","clientId":"TFREQSW","timestamp":"201811151211111","abilityCT":"ET#!@AVEAEGHYEH"}。
字段解释如下：
version：鉴权算法版本，动态令牌鉴权方式为v1，通过扩展此字段平台支持多种鉴权算法；	（必填）
clientId：向平台管理员申请的clientId/clientSecret；	（必填）
timestamp：请求时间戳，格式yyyyMMddHHmmssSSS（GMT+8北京时间），平台拒绝与自身服务器时间偏差大于10分钟的请求；	（必填）
abilityCT：上一步获取的动态令牌；	（必填）
2)业务参数 CB-Ability-Business
HTTP消息头加入JSON字符串格式的业务信息参数，例如CB-Ability-Business:{"id":"123449439","parent":"559190918"}。
字段解释如下：
id：业务编号id，基于uuid生成不重复的字符串；	（必填）
parent：父级业务编号；	（可选）

2.5 签名鉴权调用方式
通过签名鉴权方式调用能力时，也需要在HTTP请求头添加鉴权参数CB-Ability-Authorization和业务参数CB-Ability-Business。但签名鉴权方式使用的鉴权参数CB-Ability-Authorization与动态令牌调用方式不同，说明如下：
HTTP消息头加入JSON字符串格式的鉴权信息参数，例如CB-Ability-Authorization:{"version":"v2", "clientId":"xxxx", "timestamp":"20190101235959000", "sign":"xxxxxxxxxxxx"}
字段解释如下：
version：鉴权算法版本，签名鉴权方式为v2；	（必填）
clientId：向平台管理员申请的clientId/clientSecret；	（必填）
timestamp：请求时间戳，格式yyyyMMddHHmmssSSS（GMT+8北京时间），平台拒绝与自身服务器时间偏差大于10分钟的请求；	（必填）
sign：签名鉴权字符串，采用SHA-256签名算法对请求中的敏感数据进行签名获得；	（必填）
sign签名使用的字符串参数说明如下：
SHA256(HttpMethod+请求url+请求url参数+请求消息体+clientSec+timestamp);
HttpMethod：为大写英文字母，可选值为 GET/POST/PUT/DELETE/PATCH；
请求url：去除ip端口以及参数之后的url字符串；
请求url参数：对于url参数按字母进行排序，删除=&等连接字符，直接对键值进行拼接形成的字符串。如：?a=a1 &q=q1&d=d1，首先按字母排序a=a1&d=d1&q=q1，然后删除=&，最后形成的字符串为 aa1dd1qq1；
请求消息体：HTTP请求消息体；
clientSec：向平台管理员申请的clientId/clientSecret；
timestamp：要与请求头CB-Ability-Authorization中的时间戳保持一致。

示例如下：
请求url：
POST http://**************:21111/api/v1/groupSdw/userManagerment/1.0.0/api/jzjk/unifiedAcct/sendSMSCaptchaCode/v0?a=a1 &q=q1&d=d1
Body:{ "userName":"admin",	"password":"admin"}

拼接原始string：
POSTapi/v1/groupSdw/userManagerment/1.0.0/api/jzjk/unifiedAcct/sendSMSCaptchaCode/v0aa1dd1qq1{"userName":"admin","password":"admin"}TEST70H5KU9AKT13MKPFGKNOZU4BP0YZRAANGBG3883LHB79ETQ9CLYVMZCD5XFS20190101235959000

1)SHA256加密原始String后为xxxxxxxxxxxx
2)请求头放入
CB-Ability-Authorization:
{"clientId":"C-TESTJ2Q4JQ6VWRZP", "version":"v2", "timestamp":"20190101235959000", "sign":"xxxxxxxxxxxx"}

附录：SHA256 java实现逻辑
public static String getSHA256StrJava(String str) throws Exception {
MessageDigestmessageDigest;
String encodeStr = "";
messageDigest = MessageDigest.getInstance("SHA-256");
messageDigest.update(str.getBytes("UTF-8"));
encodeStr = byte2Hex(messageDigest.digest());
    return encodeStr;
}

private static String byte2Hex(byte[] bytes) {
StringBufferstringBuffer = new StringBuffer();
String temp = null;
    for (int i = 0; i<bytes.length; i++) {
        temp = Integer.toHexString(bytes[i] &0xFF);
        if (temp.length() == 1) {
stringBuffer.append("0");
}
stringBuffer.append(temp);
}
return stringBuffer.toString();
}

示例的clientId和clientSec分别为：
clientId= C-TESTJ2Q4JQ6VWRZP
clientSec= TEST70H5KU9AKT13MKPFGKNOZU4BP0YZRAANGBG3883LHB79ETQ9CLYVMZCD5XFS

2.6 错误码说明
错误类型	错误码	消息含义
missing_parameter	9800	"missing required arguments"
invalid_parameter	9801	"invalid parameters"
invalid_ability	9802	"invalid ability"
invalid_client	9803	"invalid client"
invalid_perm	9804	"bad permission"
timeout	9805	"use ability timeout"
max_ratelimit	9806	"exceed limits"
max_timeout_times	9807	"exceed max timeout count"
invalid_shutdown	9808	"temporarily unavailable"
invalid_baseurl	9809	"invalid prefix url"
unknown	9810	"unknown error"
req_late	9811	"request too late"
invalid_token	9812	"invalid token"
invalid_ability_api	9813	"invalid ability api"
illegal_req	9814	“sign is illegal”
black_white_limit	9815	"black and white limit"

2.7 SDK使用说明
SDK当前版本为cb-ability-sdk-1.4.3
在SDK中提供了封装好的AbilityGet,AbilityPost,AbilityPut,AbilityDelete，分别用于对应到HTTP请求中的GET, POST, PUT,DELETE方法
再调用前通过AbilityApi获取对应的操作实例，如下：
AbilityPost post = AbilityApi.post(
				clientid,
				clientsecret,
				gatewayaddress
			);

AbilityApi提供了get/post/put/delete四个获取操作实例的方法，分别返回AbilityGet/AbilityPost/AbilityPut/AbilityDelete操作实例
其中clientid和clientsecret由云网能力开放平台管理员分配。
gatewayaddress为能力平台网关的地址和端口，调用方可以根据调用的环境自行配置传入即可，如：http://************:8180/api/v1

获取到操作实例后，即可进行能力调用，调用前首先设置能力的信息
post.setApiUrl(apiUrl);
post.setContent(postContent);
其中setApiUrl为设置要调用的能力api的url
setContent只有在post,put,delete方法时可用，用于设置http的请求实体

如果特殊请求调用时需要设置HTTP头信息则可以使用putHead方法实现，如下：
post.putHead("key", "value");
也可以自行构造Map<String,String>后通过putAllHead方法一次性设置，如下：
Map<String,String> heads = new HashMap();
heads.put("key1", "value1");
heads.put("key2", "value2");
post.putAllHead(heads);

设置完成后，既可以调用：
String result = post.send();
最终调用能力，调用能力的结果将直接返回

其中sdk会抛出AbilityException异常，使用方可以捕获该异常，此异常在调用能力出现问题或者参数等设置错误时抛出，以下为抛出的异常会包含的错误码：
 错误将直接通过AbilityException抛出，通过异常中可以拿到MESSAGE和CODE
 错误码继承自网关使用98XX系列，SDK使用9850开始的错误码
 其中，9850-9859为sdk基础API错误，9860-9869为HTTP操作错误
 9850：SDK不支持的HTTP方法
 9851：获取动态令牌失败
 9852：通过URL获取CONNECTION发生了IO错误，具体信息将在message中体现
 9853：通过URL获取CONNECTION发生了其他错误，具体信息将在message中体现
 9854：在给CONNECTION设置HTTP方法时发生了错误，可能是使用了错误的HTTP方法或者链接被重用
 9855：调用能力平台时发生了IO错误，具体信息将在message中体现
 9856：调用能力平台时发生了其他错误，具体信息将在message中体现
 9857：刷新TOKEN时发生了其他错误，具体信息将在message中体现
 9858：刷新token失败，超过重试次数
 9860：GET方法不支持设置请求实体


3. 能力平台接口开放
3.1 邮件发送接口
3.1.1 接口请求说明
API名称	邮件发送接口
郑州测试环境API地址	http://*************:8180/api/v1/citc/inx/1.0/aaa/emails/sendEmail
郑州生产环境API地址	http://*************:8180/api/v1/citc/inx/1.0/aaa/emails/sendEmail
HTTP请求方式	HTTP-POST
格式	JSON
API鉴权类型	默认鉴权类型
所属系统	智慧云网
发起方	各子系统
落地方	智慧云网
3.1.2 SDK调用示例
郑州测试
/** * 通过POST方式调用能力
 * 支持GET/POST/PUT/DELETE
 * 方便通过AbilityApi.get/post/put/delete实现
 * 调用完成后会直接将结果通过String返回
 */
String result = AbilityApi.post(
          "您使用的clientId", //由云网能力开放平台分配的密钥对，用于签名鉴权
          "您使用的clientSecret", //由云网能力开放平台分配的密钥对，用于签名鉴权
          "http://*************:8180/api/v1")  //能力平台网关地址
          .setApiUrl("citc/inx/1.0/aaa/emails/sendEmail") //要调用的能力URL
          .setContent(body) // body为String类型，要发送的HTTP CONTENT , 仅在POST/PUT/DELETE时支持
          .send(); //发起调用
郑州生产
/** * 通过POST方式调用能力
 * 支持GET/POST/PUT/DELETE
 * 方便通过AbilityApi.get/post/put/delete实现
 * 调用完成后会直接将结果通过String返回
 */
String result = AbilityApi.post(
          "您使用的clientId", //由云网能力开放平台分配的密钥对，用于签名鉴权
          "您使用的clientSecret", //由云网能力开放平台分配的密钥对，用于签名鉴权
          "http://*************:8180/api/v1")  //能力平台网关地址
          .setApiUrl("citc/inx/1.0/aaa/emails/sendEmail") //要调用的能力URL
          .setContent(body) // body为String类型，要发送的HTTP CONTENT , 仅在POST/PUT/DELETE时支持
          .send(); //发起调用
3.1.3 输入参数
序号	参数名称	参数描述	数据类型	是否必填	参数约束	备注
1	bcc	密送	array	否
	cc	抄送	array	否
	subject	邮件主题	String	否
	emailTemplate	邮件模板名称	String	是
	files	邮件附件路径	array	否		可以通过文件上传接口，先上传附件文件,将返回的路径地址作为参数
	from	发件人	String	否		不传则使用默认的
	to	收件人	array	是
	param	对应模板中的参数	map<String,Object>	否		模板中变量的映射参数，如：
{“username”:”yangjj”}

3.1.4 输入参数示例
{
  "bcc": [
"string"
],
  "cc": [
"string"
],
  "subject": "string",
  "emailTemplate": "string",
  "files": [
    "string"
  ],
  "from": "string",
  "param": {},
  "to": [
    "string"
  ]
}

3.1.5 输出参数
序号	参数名称	参数描述	数据类型	是否必填	备注
1	respCode	标识	String	是	1111 成功
0000 失败
2	respDesc	描述	String		描述
3	data	返回数据	Object		返回数据

3.1.6 输出参数示例
{“code”:”1111”,  ”desc”:”success” , ”data”: null}

3.2 短信发送接口（联通号码）
3.2.1 接口请求说明
API名称	短信发送接口
郑州测试环境API地址	http://*************:8180/api/v1/publicService/psOther/1.0/aaa/message/triggerSendSms
郑州生产环境API地址	http://*************:8180/api/v1/publicService/psOther/1.0/aaa/message/triggerSendSms
HTTP请求方式	HTTP-POST
格式	JSON
API鉴权类型	默认鉴权类型
所属系统	智慧云网
发起方	外部系统
落地方	智慧云网

注：该接口只支持联通号码
3.2.2 SDK调用示例
郑州测试
/** * 通过POST方式调用能力
 * 支持GET/POST/PUT/DELETE
 * 方便通过AbilityApi.get/post/put/delete实现
 * 调用完成后会直接将结果通过String返回
 */
String result = AbilityApi.post(
          "您使用的clientId", //由云网能力开放平台分配的密钥对，用于签名鉴权
          "您使用的clientSecret", //由云网能力开放平台分配的密钥对，用于签名鉴权
          "http://*************:8180/api/v1")  //能力平台网关地址
          .setApiUrl("publicService/psOther/1.0/aaa/message/triggerSendSms") //要调用的能力URL
          .setContent(body) // body为String类型，要发送的HTTP CONTENT , 仅在POST/PUT/DELETE时支持
          .send(); //发起调用
郑州生产
/** * 通过POST方式调用能力
 * 支持GET/POST/PUT/DELETE
 * 方便通过AbilityApi.get/post/put/delete实现
 * 调用完成后会直接将结果通过String返回
 */
String result = AbilityApi.post(
          "您使用的clientId", //由云网能力开放平台分配的密钥对，用于签名鉴权
          "您使用的clientSecret", //由云网能力开放平台分配的密钥对，用于签名鉴权
          "http://*************:8180/api/v1")  //能力平台网关地址
          .setApiUrl("publicService/psOther/1.0/aaa/message/triggerSendSms") //要调用的能力URL
          .setContent(body) // body为String类型，要发送的HTTP CONTENT , 仅在POST/PUT/DELETE时支持
          .send(); //发起调用
3.2.3 输入参数
节点名称	父节点名称	约束	类型	长度	说明
serialNumber		1	Array	1	接收短信号码(号码要以86开头)
message		1	String	255	短信内容

3.2.4 输入参数示例
{
	"serialNumber": ["86186xxxxxxxx"],
	"message": "您的验证码是xxxx，在5分钟内有效。如非本人操作请忽略本短信。"
}
3.2.5 输出参数
节点名称	父节点名称	约束	类型	长度	说明
respCode		1	String	1	接口返回码
respDesc		1	String	255	接口返回描述
3.2.6 输出参数示例
{"respCode":"1","respDesc":"success"}

