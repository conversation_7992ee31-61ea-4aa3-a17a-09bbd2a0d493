<template>
  <div class="listbox">
    <ul class="listUl">
      <li v-for="(item, i) in listD" :key="'us' + i">
        <div class="li_top">
          <p class="color_gray">{{ item.name }}</p>
          <p class="huanbi">
            <span class="num"> {{ item.percent }}%</span>
            <!-- <i class="triangle up"></i> -->
          </p>
        </div>
        <div class="li_top li_bot ft16">
          <p class="ip">资源 {{ item.subName }}</p>
          <p class="color_blue ft16">
            {{ item.value | num2Ft }}
          </p>
        </div>
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  props: {
    list: {
      default: () => [],
      type: Array
    }
  },
  watch: {
    list: {
      handler(v) {
        this.listD = v;
      }
    },
  },
  data() {
    return {
      listD: [
        // {
        //   name: "SpeedCAT 闪电猫机场",
        //   subName: "***********",
        //   value: 100,
        //   percent: "20%"
        // },
        // {
        //   name: "SpeedCAT 闪电猫机场",
        //   subName: "***********",
        //   value: 100,
        //   percent: "20%"
        // },
        // {
        //   name: "SpeedCAT 闪电猫机场",
        //   subName: "***********",
        //   value: 100,
        //   percent: "20%"
        // },
        // {
        //   name: "SpeedCAT 闪电猫机场",
        //   subName: "***********",
        //   value: 100,
        //   percent: "20%"
        // },
      ]
    };
  }
};
</script>

<style lang="scss" scoped>
.listbox {
  margin-top: 15px;
  color: #fffeff;
  font-size: 14px;
  line-height: 28px;
  .ft16 {
    font-size: 16px;
  }
  .color_blue {
    color: #a6e7fb;
  }
  .color_gray {
    color: #8b9094;
  }
  .num {
    font-size: 16px;
  }

  .triangle {
    width: 0;
    height: 0;
    display: inline-block;
    border: 6px solid;
    border-color: #ff716f transparent transparent;
  }
  .listUl {
    height: 276px;
    overflow-y: auto;
    > li {
      height: 64px;
      padding: 2px 21px;
      &:not(:last-child) {
        margin-bottom: 5px;
      }
      background: url(./img/kuang3.png) no-repeat;
      &:nth-of-type(2n) {
        background: url(./img/kuang4.png) no-repeat;
        .color_blue {
          color: #238ede;
        }
      }
      .li_top {
        display: flex;
        justify-content: space-between;
      }
      .ip {
        color: #fff;
      }
    }
  }

  .huanbi {
    position: relative;
    width: 100px;
    text-align: right;
    // padding-right: 20px;
    color: #a0a9b2;
    .triangle {
      position: absolute;
      top: 11px;
      right: 0;
      transform-origin: center 3px;
      &.down {
        transform: rotateZ(0deg);
      }
      &.up {
        transform: rotateZ(180deg);
      }
    }
  }
}
</style>
