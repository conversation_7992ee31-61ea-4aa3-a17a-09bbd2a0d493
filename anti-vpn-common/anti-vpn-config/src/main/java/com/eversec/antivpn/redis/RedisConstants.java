package com.eversec.antivpn.redis;


/**
 * RedisCacheConstants
 *
 * <AUTHOR>
 * @date 2021/7/9 15:08
 * @since 1.0.0
 */
public class RedisConstants {

    /**
     * Redis 缓存默认分隔符，":"符号在redis中代表目录分隔符
     */
    public static final String REDIS_CACHE_DELIMITER=":";
    /**
     * 代表redis 缓存操作
     */
    public static final String REDIS_CACHE_FUNCTION= "cache";
    /**
     * 代表redis锁操作
     */
    public static final String REDIS_LOCK_FUNCTION= "lock";
}
