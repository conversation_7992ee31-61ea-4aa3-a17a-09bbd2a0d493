package com.eversec.antivpn.log.service;

import com.eversec.antivpn.emnus.ScreenTypeEnum;
import com.eversec.antivpn.log.mapper.vo.UserDateHistogramVO;
import com.eversec.antivpn.log.mapper.vo.TypeCountVO;
import com.eversec.antivpn.log.mapper.vo.UserLogPageVO;

import java.util.List;

public interface UserOverallService {

    Long getVpnUserNum(ScreenTypeEnum screenTypeEnum);
    Long getUserHaveTelPhoneNum(ScreenTypeEnum screenTypeEnum);
    Integer getUserLocationNum(ScreenTypeEnum screenTypeEnum);
    Integer getDestNum(ScreenTypeEnum screenTypeEnum);
    Long getCrossBorderTotalNum(ScreenTypeEnum screenTypeEnum);
    List<TypeCountVO> getUserLocationTop10(ScreenTypeEnum screenTypeEnum);
    List<TypeCountVO> getUserDestTop10(ScreenTypeEnum screenTypeEnum);
    List<UserDateHistogramVO> getUserCrossBorderCommDateHistogram(ScreenTypeEnum screenTypeEnum);
    List<TypeCountVO> getUserDateHistogram(ScreenTypeEnum screenTypeEnum);
    List<TypeCountVO> getUserCrossBorderCommTimesTop10(ScreenTypeEnum screenTypeEnum);
    List<TypeCountVO> getUserCrossBorderCommResourceNodeTop10(ScreenTypeEnum screenTypeEnum);
    List<TypeCountVO> getMap(ScreenTypeEnum screenTypeEnum);
    List<UserLogPageVO> getUserLogPageList();
}
