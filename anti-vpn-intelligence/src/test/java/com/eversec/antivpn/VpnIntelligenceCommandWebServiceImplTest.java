package com.eversec.antivpn;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.XmlUtil;
import cn.hutool.json.JSONUtil;
import com.eversec.antivpn.intelligence.api.VpnIntelligenceCommandWebService;
import com.eversec.antivpn.intelligence.api.dto.VpnIntelligenceCommandDistributeDTO;
import com.eversec.antivpn.intelligence.api.dto.VpnIntelligenceCommandDistributeResponseDTO;
import com.eversec.antivpn.intelligence.api.enums.IntelligenceCommandOperationTypeEnum;
import io.swagger.models.Xml;
import lombok.SneakyThrows;
import org.apache.cxf.binding.soap.interceptor.EndpointSelectionInterceptor;
import org.apache.cxf.interceptor.Fault;
import org.apache.cxf.interceptor.Interceptor;
import org.apache.cxf.interceptor.LoggingInInterceptor;
import org.apache.cxf.interceptor.LoggingOutInterceptor;
import org.apache.cxf.jaxws.JaxWsProxyFactoryBean;
import org.apache.cxf.jaxws.endpoint.dynamic.JaxWsDynamicClientFactory;
import org.apache.cxf.jaxws.spring.JaxWsProxyFactoryBeanDefinitionParser;
import org.apache.cxf.message.Message;
import org.apache.cxf.service.model.BindingOperationInfo;
import org.testng.annotations.Test;

import javax.xml.namespace.QName;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class VpnIntelligenceCommandWebServiceImplTest {

    LoggingInInterceptor ipt = new LoggingInInterceptor();
    LoggingOutInterceptor opt = new LoggingOutInterceptor();

    {
        ipt.setPrettyLogging(true);
        opt.setPrettyLogging(true);
    }

    private Object[] params = {"03",99L, "2SpZDOcRM23FS3MQMyc8", "MTBhZWZjYTdlNmUzMzQzYzk2MTQ1MWY5ZmUyM2Q2NTU=",
            "haSKpe2lfkbg2kmJbh8stO1qrh7hC1bfIGXjJgq5185ek0g4T8skKPBOWezhG/O/GjL830lHiUgpM12soqPQU2FJ0Atq9gbYp1jt1IRgcUX0bZbwbKe/QLSJrUpmXNhlCjKgWs9jXnw7k6BP+FFfDQzE2D8PTEIeVlToVVmx+Da9QcjLUQMYWWLezasw+mz8pruPZl/9Wm36XAThexXkMyegCIWIqOqrN1ukMKyl2m+KusWvgypOgqY3jqRWG15GJzX9NfGgJQAAJOUbhzK9xZTl39nxht2ou2MqglG/RhfY1vWqG2sNgbmPYb+kJd9aVv4U3ZtxBP2YXKijXLpQtyBaXKSX/3QPjKbo9fxBekjCeSRpH0RhtT+/wy9u7WZy1cuGpJds+l9XFJFK+LFB3vx5QtJWyeFJy4Toed4Iig92SdAayz++jy4nskF04j0uvl3nh2PlIVkAMOKknZJhUDwuXnuDZLDQlTTx991YHQWtdcdDzzO6Psiu+DGfnr7+M8R2k1q1za5xgslZIll9147SFsT/rShb3egBATdb3CJerbyTGRZ+BvkI2D+FxTcHktV5vKByMQh0hSY+2FNC0F1KrMXRraVGc9WDicW9yRDPtvDr80VaI4HSe0PrItXEoqDVLL+swfPwpktkZ4181lAkaSQpTycvizcdvb7eByUQLPxfBBC+NXQL4xV5Lt1e8dUgi1bIS8bSSGKFn6VdwdcqBusrE1opJWaZa8/cp/fvtwhJrE0zRL3AAk93pR6DVv1r63B8+ad645t7qE/nvMzMZqhOLKRhqM5CigYSY05iHVNOqQBx5gMbvWsoJsCUAxjpDINK+O1pcRcRW50FjnChSxtXGhkykbraHmDMoo9+xWENyhy6x5cZJVQtkAzimJQNeLeN2DTQapkINR/qgc8Y3Eb510c5gmzmJ2Dx5UblJnwSgsgwYj2ufW45FF5FRUqSm05q6LI1q5R7u1HPvg==",
            "Y2NhZGVhMTgwMTliNTc3OTgwMDQ4ODk5ZTg0NGIwN2E=",1, 1, 1,
            1, "1.0"};


    @SneakyThrows
    @Test
    public void testDistribute4() {
        String address = "http://anti-vpn-service.anti-vpn.beijing.everdevcloud.com/services/IDCWebService/idcCommand?wsdl&systemCode=2";
        address = "http://s.beijing.everdevcloud.com:31730/service/anti-vpn-service/services/IDCWebService/idcCommand?wsdl";

        JaxWsDynamicClientFactory dcf = JaxWsDynamicClientFactory.newInstance();
        org.apache.cxf.endpoint.Client client = dcf.createClient(address);

        client.getInInterceptors().add(ipt);
        client.getOutInterceptors().add(opt);

//        EndpointSelectionInterceptor interceptor = new EndpointSelectionInterceptor();
//        client.getOutInterceptors().add()


        Object[]  objects = client.invoke("idc_command_province", "03",99L, "2SpZDOcRM23FS3MQMyc8", "MTBhZWZjYTdlNmUzMzQzYzk2MTQ1MWY5ZmUyM2Q2NTU=",
                "haSKpe2lfkbg2kmJbh8stO1qrh7hC1bfIGXjJgq5185ek0g4T8skKPBOWezhG/O/GjL830lHiUgpM12soqPQU2FJ0Atq9gbYp1jt1IRgcUX0bZbwbKe/QLSJrUpmXNhlCjKgWs9jXnw7k6BP+FFfDQzE2D8PTEIeVlToVVmx+Da9QcjLUQMYWWLezasw+mz8pruPZl/9Wm36XAThexXkMyegCIWIqOqrN1ukMKyl2m+KusWvgypOgqY3jqRWG15GJzX9NfGgJQAAJOUbhzK9xZTl39nxht2ou2MqglG/RhfY1vWqG2sNgbmPYb+kJd9aVv4U3ZtxBP2YXKijXLpQtyBaXKSX/3QPjKbo9fxBekjCeSRpH0RhtT+/wy9u7WZy1cuGpJds+l9XFJFK+LFB3vx5QtJWyeFJy4Toed4Iig92SdAayz++jy4nskF04j0uvl3nh2PlIVkAMOKknZJhUDwuXnuDZLDQlTTx991YHQWtdcdDzzO6Psiu+DGfnr7+M8R2k1q1za5xgslZIll9147SFsT/rShb3egBATdb3CJerbyTGRZ+BvkI2D+FxTcHktV5vKByMQh0hSY+2FNC0F1KrMXRraVGc9WDicW9yRDPtvDr80VaI4HSe0PrItXEoqDVLL+swfPwpktkZ4181lAkaSQpTycvizcdvb7eByUQLPxfBBC+NXQL4xV5Lt1e8dUgi1bIS8bSSGKFn6VdwdcqBusrE1opJWaZa8/cp/fvtwhJrE0zRL3AAk93pR6DVv1r63B8+ad645t7qE/nvMzMZqhOLKRhqM5CigYSY05iHVNOqQBx5gMbvWsoJsCUAxjpDINK+O1pcRcRW50FjnChSxtXGhkykbraHmDMoo9+xWENyhy6x5cZJVQtkAzimJQNeLeN2DTQapkINR/qgc8Y3Eb510c5gmzmJ2Dx5UblJnwSgsgwYj2ufW45FF5FRUqSm05q6LI1q5R7u1HPvg==",
                "Y2NhZGVhMTgwMTliNTc3OTgwMDQ4ODk5ZTg0NGIwN2E=",1, 1, 1,
                1, "1.0");

        String re = objects[0].toString();
        System.out.println(re);

    }




    @Test
    public void testDistribute3() {
        /**
         *
         * IdcCommandParam(comCode=02, provinceId=99, randVal=2SfCcAbaTzb2uhsncKuE, pwdHash=MjY3Mjc1N2IzZDhjOWI1YTk4OTg1NzVkYTg5NmZiNTg=,
         * command=CMPznHhkYz2IIwXeGTvXHlOPVxYMUNUpyOp9jXy0noSnm775IcgNkjsL28vmQMqJvrnJKfIiscPpZGsnJwTc2I1YsnGkczQv2Rhhk/MP2bl+uqkLX/9kfgCXiep7SP2xfBGwB4T/TOFpouBWdhgodUjBk1juEic05HcfmRMmxH66rMKG3hOqQPSkpPprvvXqTchG94ljfv6A+zPF7/bcT5a/dxeL1be3FdZZa1bOqDwlnQlMndkvsVK7tEaXmfdd3HmaOXYZdrTzl07s8IcFi1iXJ2FCwqbu31IRhzO5zOX2EUf+xB82ywEeu/wacJ5VmIj7uhdeOmZO2tv8Zbpt1wOOOG3Mk/aRQmnfe9uVU1oD6q7mhLHUbXnt+WojJDj++URu4OHiuo5gZSnL2LcM7yq/tUaAqkcZX6rhRdry7J8r7J1yLvXXhe6g5aoSSq8HhcxQKV/FGQ1kL/IPkf8s/RVUJ3FWwdDTyGefEzSxOLI=,
         * commandHash=MDdlNmU2NDJiY2E0YWQzNTczMjI4NTMwZmRjOGRhODM=,
         * commandType=1, encryptAlgorithm=1, hashAlgorithm=1, compressionFormat=1, commandVersion=1.0)
         * */
        String address = "http://anti-vpn-service.anti-vpn.beijing.everdevcloud.com/services/IDCWebService/idcCommand?wsdl";
        // 代理工厂
        JaxWsProxyFactoryBean jaxWsProxyFactoryBean = new JaxWsProxyFactoryBean();
        // 设置代理地址
        jaxWsProxyFactoryBean.setAddress(address);
        // 设置接口类型
        jaxWsProxyFactoryBean.setServiceClass(VpnIntelligenceCommandWebService.class);
        jaxWsProxyFactoryBean.setServiceName(new QName("http://server.chatgpt.com", "cGptCommand"));
//        jaxWsProxyFactoryBean.setEndpointName(new QName("http://server.chatgpt.com", "cGptCommand"));
        // 创建一个代理接口实现
        VpnIntelligenceCommandWebService webService = (VpnIntelligenceCommandWebService) jaxWsProxyFactoryBean.create();
        // 调用代理接口的方法调用并返回结果
        String userName = webService.idcCommandProvince("01", 99L, "2SfCcAbaTzb2uhsncKuE",
                "MjY3Mjc1N2IzZDhjOWI1YTk4OTg1NzVkYTg5NmZiNTg=",
                "CMPznHhkYz2IIwXeGTvXHlOPVxYMUNUpyOp9jXy0noSnm775IcgNkjsL28vmQMqJvrnJKfIiscPpZGsnJwTc2I1YsnGkczQv2Rhhk/MP2bl+uqkLX/9kfgCXiep7SP2xfBGwB4T/TOFpouBWdhgodUjBk1juEic05HcfmRMmxH66rMKG3hOqQPSkpPprvvXqTchG94ljfv6A+zPF7/bcT5a/dxeL1be3FdZZa1bOqDwlnQlMndkvsVK7tEaXmfdd3HmaOXYZdrTzl07s8IcFi1iXJ2FCwqbu31IRhzO5zOX2EUf+xB82ywEeu/wacJ5VmIj7uhdeOmZO2tv8Zbpt1wOOOG3Mk/aRQmnfe9uVU1oD6q7mhLHUbXnt+WojJDj++URu4OHiuo5gZSnL2LcM7yq/tUaAqkcZX6rhRdry7J8r7J1yLvXXhe6g5aoSSq8HhcxQKV/FGQ1kL/IPkf8s/RVUJ3FWwdDTyGefEzSxOLI=",
                "MDdlNmU2NDJiY2E0YWQzNTczMjI4NTMwZmRjOGRhODM=",
                1, 1, 1, 1, "1.0");

        System.out.println(JSONUtil.toJsonPrettyStr(userName));
    }


    @Test
    public void testDistribute2() {
        /**
         *
         * IdcCommandParam(comCode=02, provinceId=99, randVal=2SfCcAbaTzb2uhsncKuE, pwdHash=MjY3Mjc1N2IzZDhjOWI1YTk4OTg1NzVkYTg5NmZiNTg=,
         * command=CMPznHhkYz2IIwXeGTvXHlOPVxYMUNUpyOp9jXy0noSnm775IcgNkjsL28vmQMqJvrnJKfIiscPpZGsnJwTc2I1YsnGkczQv2Rhhk/MP2bl+uqkLX/9kfgCXiep7SP2xfBGwB4T/TOFpouBWdhgodUjBk1juEic05HcfmRMmxH66rMKG3hOqQPSkpPprvvXqTchG94ljfv6A+zPF7/bcT5a/dxeL1be3FdZZa1bOqDwlnQlMndkvsVK7tEaXmfdd3HmaOXYZdrTzl07s8IcFi1iXJ2FCwqbu31IRhzO5zOX2EUf+xB82ywEeu/wacJ5VmIj7uhdeOmZO2tv8Zbpt1wOOOG3Mk/aRQmnfe9uVU1oD6q7mhLHUbXnt+WojJDj++URu4OHiuo5gZSnL2LcM7yq/tUaAqkcZX6rhRdry7J8r7J1yLvXXhe6g5aoSSq8HhcxQKV/FGQ1kL/IPkf8s/RVUJ3FWwdDTyGefEzSxOLI=,
         * commandHash=MDdlNmU2NDJiY2E0YWQzNTczMjI4NTMwZmRjOGRhODM=,
         * commandType=1, encryptAlgorithm=1, hashAlgorithm=1, compressionFormat=1, commandVersion=1.0)
         * */
        String address = "http://localhost:32020/service/anti-vpn-service/services/IDCWebService/idcCommand?wsdl";
        // 代理工厂
        JaxWsProxyFactoryBean jaxWsProxyFactoryBean = new JaxWsProxyFactoryBean();
        // 设置代理地址
        jaxWsProxyFactoryBean.setAddress(address);
        // 设置接口类型
        jaxWsProxyFactoryBean.setServiceClass(VpnIntelligenceCommandWebService.class);
        System.out.println(JSONUtil.toJsonPrettyStr(jaxWsProxyFactoryBean.getProperties()));

        // 创建一个代理接口实现
        VpnIntelligenceCommandWebService webService = (VpnIntelligenceCommandWebService) jaxWsProxyFactoryBean.create();
        // 调用代理接口的方法调用并返回结果
        String userName = webService.idcCommandProvince("01", 99L, "2SfCcAbaTzb2uhsncKuE",
                "MjY3Mjc1N2IzZDhjOWI1YTk4OTg1NzVkYTg5NmZiNTg=",
                "CMPznHhkYz2IIwXeGTvXHlOPVxYMUNUpyOp9jXy0noSnm775IcgNkjsL28vmQMqJvrnJKfIiscPpZGsnJwTc2I1YsnGkczQv2Rhhk/MP2bl+uqkLX/9kfgCXiep7SP2xfBGwB4T/TOFpouBWdhgodUjBk1juEic05HcfmRMmxH66rMKG3hOqQPSkpPprvvXqTchG94ljfv6A+zPF7/bcT5a/dxeL1be3FdZZa1bOqDwlnQlMndkvsVK7tEaXmfdd3HmaOXYZdrTzl07s8IcFi1iXJ2FCwqbu31IRhzO5zOX2EUf+xB82ywEeu/wacJ5VmIj7uhdeOmZO2tv8Zbpt1wOOOG3Mk/aRQmnfe9uVU1oD6q7mhLHUbXnt+WojJDj++URu4OHiuo5gZSnL2LcM7yq/tUaAqkcZX6rhRdry7J8r7J1yLvXXhe6g5aoSSq8HhcxQKV/FGQ1kL/IPkf8s/RVUJ3FWwdDTyGefEzSxOLI=",
                "MDdlNmU2NDJiY2E0YWQzNTczMjI4NTMwZmRjOGRhODM=",
                1, 1, 1, 1, "1.0");

        System.out.println(JSONUtil.toJsonPrettyStr(userName));
    }


    @Test
    public void testDistributeFull() {
        /**
         *
         * IdcCommandParam(comCode=02, provinceId=99, randVal=2SfCcAbaTzb2uhsncKuE, pwdHash=MjY3Mjc1N2IzZDhjOWI1YTk4OTg1NzVkYTg5NmZiNTg=,
         * command=CMPznHhkYz2IIwXeGTvXHlOPVxYMUNUpyOp9jXy0noSnm775IcgNkjsL28vmQMqJvrnJKfIiscPpZGsnJwTc2I1YsnGkczQv2Rhhk/MP2bl+uqkLX/9kfgCXiep7SP2xfBGwB4T/TOFpouBWdhgodUjBk1juEic05HcfmRMmxH66rMKG3hOqQPSkpPprvvXqTchG94ljfv6A+zPF7/bcT5a/dxeL1be3FdZZa1bOqDwlnQlMndkvsVK7tEaXmfdd3HmaOXYZdrTzl07s8IcFi1iXJ2FCwqbu31IRhzO5zOX2EUf+xB82ywEeu/wacJ5VmIj7uhdeOmZO2tv8Zbpt1wOOOG3Mk/aRQmnfe9uVU1oD6q7mhLHUbXnt+WojJDj++URu4OHiuo5gZSnL2LcM7yq/tUaAqkcZX6rhRdry7J8r7J1yLvXXhe6g5aoSSq8HhcxQKV/FGQ1kL/IPkf8s/RVUJ3FWwdDTyGefEzSxOLI=,
         * commandHash=MDdlNmU2NDJiY2E0YWQzNTczMjI4NTMwZmRjOGRhODM=,
         * commandType=1, encryptAlgorithm=1, hashAlgorithm=1, compressionFormat=1, commandVersion=1.0)
         * */
        String address = "http://localhost:8080/services/IDCWebService/idcCommand?wsdl&systemCode-test=2";
        // 代理工厂
        JaxWsProxyFactoryBean jaxWsProxyFactoryBean = new JaxWsProxyFactoryBean();
        // 设置代理地址
        jaxWsProxyFactoryBean.setAddress(address);
        // 设置接口类型
        jaxWsProxyFactoryBean.setServiceClass(VpnIntelligenceCommandWebService.class);
        System.out.println(JSONUtil.toJsonPrettyStr(jaxWsProxyFactoryBean.getProperties()));

        // 创建一个代理接口实现
        VpnIntelligenceCommandWebService webService = (VpnIntelligenceCommandWebService) jaxWsProxyFactoryBean.create();
        // 调用代理接口的方法调用并返回结果
        String userName = webService.idcCommandProvince("01", 99L, "2SfCcAbaTzb2uhsncKuE",
                "MjY3Mjc1N2IzZDhjOWI1YTk4OTg1NzVkYTg5NmZiNTg=",
                "CMPznHhkYz2IIwXeGTvXHlOPVxYMUNUpyOp9jXy0noSnm775IcgNkjsL28vmQMqJvrnJKfIiscPpZGsnJwTc2I1YsnGkczQv2Rhhk/MP2bl+uqkLX/9kfgCXiep7SP2xfBGwB4T/TOFpouBWdhgodUjBk1juEic05HcfmRMmxH66rMKG3hOqQPSkpPprvvXqTchG94ljfv6A+zPF7/bcT5a/dxeL1be3FdZZa1bOqDwlnQlMndkvsVK7tEaXmfdd3HmaOXYZdrTzl07s8IcFi1iXJ2FCwqbu31IRhzO5zOX2EUf+xB82ywEeu/wacJ5VmIj7uhdeOmZO2tv8Zbpt1wOOOG3Mk/aRQmnfe9uVU1oD6q7mhLHUbXnt+WojJDj++URu4OHiuo5gZSnL2LcM7yq/tUaAqkcZX6rhRdry7J8r7J1yLvXXhe6g5aoSSq8HhcxQKV/FGQ1kL/IPkf8s/RVUJ3FWwdDTyGefEzSxOLI=",
                "MDdlNmU2NDJiY2E0YWQzNTczMjI4NTMwZmRjOGRhODM=",
                1, 1, 1, 1, "1.0");

        System.out.println(JSONUtil.toJsonPrettyStr(userName));
    }

    @Test
    public void testDistribute() {
        /**
         * comCode=01, provinceId=99, randVal=cZb5eGnGUk9ulrZdFNzv, pwdHash=MTBjOWVjNDBlMGJmNWQ0ZDg5ODkzNTM2Y2QxZGI4MDk=,
         * command=j9XeoxBUucZUS0qdpTdkoSmgyxnOEQrXn1NnvClasEgXd/xNM5ptqwgpF+W+fRG8pyiNZ0qDlitw6BGe7y7LMuWNDbqwuj18xY4bYRAPs0sXS/jNCQqqkSYlLlQvaIv7KK+LE64mEvWqrLGNZlqIfIIme6J5wAEFUSFNACqWRQz/SJQrxznMVNjNKFxEufOVQe3eKRWmfCQ7bqpGGM+UiVaklrGHHaC+aYTjHMYV7Y2KYd2nZfVLqS5JBmNvJeJPidkweExSEuuu6ockNRr8g6x+sLpejVlulXnA+Y8o7ZVIT8VD13B6MBOhyvDp5Rg5jXmT64xMb8h9n5fGotyWzqB2bf1nAXcOBQxgIhS7oH5oFKY24IwULW5mOhFz9HaWecvxzMwMIXef0k7zwpVFqOlqodF15Psx0j0mns6rzeCW1IuJXtCdQmDK1RPHOtTmQOVRlzxO77L6WjeVxb71FBjJu2aNJChj4gxebwrtmT+lfA/rJrfiG5yTe7bxu0F7AtPY6/CJIKoNg/og3mG5xQxFUxVnde3kZlvHHmcQnUqcvRnsf/rIP3kCqrfZi8fK0OGPvxYCVvAInNZbXTirvm+xlcFVGfhKWbXRDJSNQPzeeg/x3FUteybWbWw03M3szrjm2FYd5hdf7whZ349oUbKeETZaBrCh3NkuNELbdjFhdmVQyeGSWvxD9dgY0fBF8o/DpmWid/OHyidLcS9pXmkS0p0G6VTdB2tGqS9IL1OJQWf750tW0KHycbSp/t3BuQjqr2KDSwP/0zObinlSsXuZILE5stFGrP5Rf4KVcmVe7ITs9QqgpOjTBmWu1AZgO9aJ81aClPfM8y/iFtThxF5ls5EB4gJrO0IbBNNh2IB9g1nAB5LQGW6J0z7j9S2Lq0Sbs41P+5Y6Iatt0PHWGdS5pRjYOW/0UpGaHMXfRTrC76X96EYW/ht/saiSHcBLAqmXRWh9C/kiz7Sij9c6WA==,
         * commandHash=OWJmYjFmNWE4NGZhNDE1ZDlmZGViZGY5MmEwMjczZTM=, commandType=1, encryptAlgorithm=1, hashAlgorithm=1, compressionFormat=1, commandVersion=1.0)
         */
        String address = "http://localhost:8080/services/IDCWebService/idcCommand?wsdl&systemCode-test=2";
        // 代理工厂
        JaxWsProxyFactoryBean jaxWsProxyFactoryBean = new JaxWsProxyFactoryBean();
        // 设置代理地址
        jaxWsProxyFactoryBean.setAddress(address);
        // 设置接口类型
        jaxWsProxyFactoryBean.setServiceClass(VpnIntelligenceCommandWebService.class);
        // 创建一个代理接口实现
        VpnIntelligenceCommandWebService webService = (VpnIntelligenceCommandWebService) jaxWsProxyFactoryBean.create();
        // 调用代理接口的方法调用并返回结果
        String userName = webService.idcCommandProvince("01", 99L, "cZb5eGnGUk9ulrZdFNzv", "MTBjOWVjNDBlMGJmNWQ0ZDg5ODkzNTM2Y2QxZGI4MDk=",
                "j9XeoxBUucZUS0qdpTdkoSmgyxnOEQrXn1NnvClasEgXd/xNM5ptqwgpF+W+fRG8pyiNZ0qDlitw6BGe7y7LMuWNDbqwuj18xY4bYRAPs0sXS/jNCQqqkSYlLlQvaIv7KK+LE64mEvWqrLGNZlqIfIIme6J5wAEFUSFNACqWRQz/SJQrxznMVNjNKFxEufOVQe3eKRWmfCQ7bqpGGM+UiVaklrGHHaC+aYTjHMYV7Y2KYd2nZfVLqS5JBmNvJeJPidkweExSEuuu6ockNRr8g6x+sLpejVlulXnA+Y8o7ZVIT8VD13B6MBOhyvDp5Rg5jXmT64xMb8h9n5fGotyWzqB2bf1nAXcOBQxgIhS7oH5oFKY24IwULW5mOhFz9HaWecvxzMwMIXef0k7zwpVFqOlqodF15Psx0j0mns6rzeCW1IuJXtCdQmDK1RPHOtTmQOVRlzxO77L6WjeVxb71FBjJu2aNJChj4gxebwrtmT+lfA/rJrfiG5yTe7bxu0F7AtPY6/CJIKoNg/og3mG5xQxFUxVnde3kZlvHHmcQnUqcvRnsf/rIP3kCqrfZi8fK0OGPvxYCVvAInNZbXTirvm+xlcFVGfhKWbXRDJSNQPzeeg/x3FUteybWbWw03M3szrjm2FYd5hdf7whZ349oUbKeETZaBrCh3NkuNELbdjFhdmVQyeGSWvxD9dgY0fBF8o/DpmWid/OHyidLcS9pXmkS0p0G6VTdB2tGqS9IL1OJQWf750tW0KHycbSp/t3BuQjqr2KDSwP/0zObinlSsXuZILE5stFGrP5Rf4KVcmVe7ITs9QqgpOjTBmWu1AZgO9aJ81aClPfM8y/iFtThxF5ls5EB4gJrO0IbBNNh2IB9g1nAB5LQGW6J0z7j9S2Lq0Sbs41P+5Y6Iatt0PHWGdS5pRjYOW/0UpGaHMXfRTrC76X96EYW/ht/saiSHcBLAqmXRWh9C/kiz7Sij9c6WA=",
                "OWJmYjFmNWE4NGZhNDE1ZDlmZGViZGY5MmEwMjczZTM=",
                1, 1, 1, 1, "1.0");

        System.out.println(JSONUtil.toJsonPrettyStr(userName));
    }


    /**
     * 代理工厂的方式，需要拿到对方的接口地址
     */
    @Test
    public void testWs() {
        // 接口地址
//        String address = "http://localhost:8080/services/IDCWebService/idcCommand?wsdl";
        String address = "http://localhost:8080/services/IDCWebService/idcCommand/2?wsdl&systemCode=2";
        // 代理工厂
        JaxWsProxyFactoryBean jaxWsProxyFactoryBean = new JaxWsProxyFactoryBean();
        // 设置代理地址
        jaxWsProxyFactoryBean.setAddress(address);
        // 设置接口类型
        jaxWsProxyFactoryBean.setServiceClass(VpnIntelligenceCommandWebService.class);
        // 创建一个代理接口实现
        VpnIntelligenceCommandWebService webService = (VpnIntelligenceCommandWebService) jaxWsProxyFactoryBean.create();
        // 调用代理接口的方法调用并返回结果
        String userName = webService.idcCommandProvince("01", 11L, "", "",
                "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",
                "",
                1, 0, 0, 0, "1.0");

        System.out.println(JSONUtil.toJsonPrettyStr(userName));
        /*System.out.println("返回结果: "+userName);*/
    }


    @Test
    public void genericVpnIntelligenceCommandDistributeDTO() {
        List<VpnIntelligenceCommandDistributeDTO> list = new ArrayList<>();
        for (int commandNum = 0; commandNum < 3; commandNum++) {
            List<VpnIntelligenceCommandDistributeDTO.VpnIntelligenceIncreaseDistributeDTO> comintData = new ArrayList<>();
            // 增量
            VpnIntelligenceCommandDistributeDTO dto = new VpnIntelligenceCommandDistributeDTO();
            dto.setVersion("1.0");
            dto.setCommandId(100001L + commandNum);
            dto.setComCode("01");
            dto.setProvinceId(11L);
            dto.setNetworkBusinessId(1);
            dto.setTimeStamp("2023-08-03 16:47:28");
            dto.setOperationType(IntelligenceCommandOperationTypeEnum.INCREASE.getCode());
            dto.setComintData(comintData);
            list.add(dto);
            for (int i = 0; i < 10; i++) {
                VpnIntelligenceCommandDistributeDTO.VpnIntelligenceIncreaseDistributeDTO subDto = new VpnIntelligenceCommandDistributeDTO.VpnIntelligenceIncreaseDistributeDTO();
                subDto.setTypeId(2);
                subDto.setVpnId(new Date().getTime() + i);
                subDto.setVpnName("全球智能线路（VIP）-" + i);
                if (i%4 == 0) {
                    subDto.setVpnDomain("www.v2ray.com");
                }
                if (i%4 == 1) {
                    subDto.setVpnIp("89.12.22." + i);
                }
                if (i%4 == 2) {
                    subDto.setVpnUrl(Base64.encode("https://www.v2ray.com/chapter_02/protocols/vmess.html"));
                }
                if (i%4 == 3) {
                    subDto.setVpnLink("https://www.v2ray.com/chapter_02/protocols/vmess.html");
                }
                comintData.add(subDto);
            }

            Map<String, Object> document = new HashMap<>();
            document.put("crossBorderVpnInfoData", dto);
            String commandXml = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>" + JSONUtil.toXmlStr(JSONUtil.parseObj(document));
            String commandBase64 = Base64.encode(commandXml);
            System.out.println(XmlUtil.parseXml(commandXml));
            System.out.println(commandBase64);

            VpnIntelligenceCommandDistributeDTO vpnIntelligenceCommandDistributeDTO = XmlUtil.xmlToBean(XmlUtil.readXML(commandXml).getDocumentElement(), VpnIntelligenceCommandDistributeDTO.class);
            System.out.println(JSONUtil.toJsonPrettyStr(vpnIntelligenceCommandDistributeDTO));

        }


    }

}
