package com.eversec.antivpn.intelligence.util;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.hash.Hash;
import cn.hutool.core.util.HashUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.ZipUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.digest.DigestAlgorithm;
import cn.hutool.crypto.digest.Digester;
import cn.hutool.crypto.digest.MD5;
import com.eversec.antivpn.config.enums.ResponseCodeEnum;
import com.eversec.antivpn.intelligence.service.impl.encrypt.CompressionFormatEnum;
import com.eversec.antivpn.intelligence.service.impl.encrypt.EncryptAlgorithmEnum;
import com.eversec.antivpn.intelligence.service.impl.encrypt.HashAlgorithmEnum;
import com.eversec.framework.webboot.common.exception.BusinessException;
import com.eversec.stark.generic.common.util.ZipUtils;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;

/**
 * 加密、解密工具
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023-08-03 12:09
 */
@Slf4j
public class EncryptUtil {

	/**
	 * 解密 command
	 *
	 * @param encryptAlgorithm 对称加密算法。
	 *                         0：不进行加密，明文传输；
	 *                         1：AES加密算法。
	 *                         加密密钥由部侧系统与企业侧系统事先配置确定，长度至少为20字节，最多32字节。
	 *                         企业侧系统应根据部侧系统的要求完成加密算法的具体实现。企业侧系统至少应支持采用CBC模式、PKCS7Padding补码方式实现AES加密算法，并可根据部侧系统的要求设置AES密钥长度、加密偏移量等参数。
	 * @param encryptCommand 加密数据
	 * @return 解密后的数据
	 */
	@SneakyThrows
	public static byte[] decryptCommand(EncryptAlgorithmEnum encryptAlgorithm, byte[] encryptCommand, String aesKey, String aesOffsets) {
		log.info("对数据进行解密，encryptAlgorithm: {}, aesKey: {}, aesOffsets: {}", encryptAlgorithm, aesKey, aesOffsets);
		switch (encryptAlgorithm){
			case AES:
				return ByteUtil.decrypt(encryptCommand, aesKey, aesOffsets);
			case CLEAR_TEXT:
				return encryptCommand;
			default:
				throw new BusinessException(ResponseCodeEnum.文件解密失败);
		}
	}

	/**
	 * 加密command
	 * @param encryptAlgorithm
	 * @param aesKey
	 * @param aesOffsets
	 * @return
	 */
	public static byte[] encryptCommand(EncryptAlgorithmEnum encryptAlgorithm, byte[] compressedCommand, String aesKey, String aesOffsets) {
		switch (encryptAlgorithm){
			case AES:
				return ByteUtil.aes(compressedCommand, aesKey, aesOffsets);
			case CLEAR_TEXT:
				return compressedCommand;
		}
		return null;
	}



	/**
	 * 对data进行hash校验
	 * @param hashAlgorithmEnum
	 * @param commandHashBase64
	 */
	public static void checkCommandHash(HashAlgorithmEnum hashAlgorithmEnum, byte[] decryptCommand, String commandHashBase64) {
		log.info("开始对数据进行hash校验，hashAlgorithmEnum: {}, commandHashBase64: {}", hashAlgorithmEnum, commandHashBase64);
		String myCommandHash;
		switch (hashAlgorithmEnum){
			case NO_HASH:
				log.info("该指令无hash，无需校验");
				return;
			case MD5:
				myCommandHash = MD5.create().digestHex(decryptCommand);
				break;
			case SHA_1:
				myCommandHash = new Digester(DigestAlgorithm.SHA1).digestHex(decryptCommand);
				break;
			default:
				throw new UnsupportedOperationException();
		}

		if (!commandHashBase64.equals(Base64.encode(myCommandHash))) {
			throw new BusinessException(ResponseCodeEnum.文件校验失败);
		}
	}

	public static String commandHash(HashAlgorithmEnum hashAlgorithmEnum, byte[] decryptCommand) {
		String result;
		switch (hashAlgorithmEnum){
			case NO_HASH:
				result = "";
			case MD5:
				result = MD5.create().digestHex(decryptCommand);
				break;
			case SHA_1:
				result = new Digester(DigestAlgorithm.SHA1).digestHex(decryptCommand);
				break;
			default:
				throw new UnsupportedOperationException();
		}

		return result;
	}

	@SneakyThrows
	public static String decompressCommand(CompressionFormatEnum compressionFormatEnum, byte[] decryptCommand) {
		log.info("对数据进行解压，compressionFormatEnum: {}", compressionFormatEnum);
		String result = null;
		switch (compressionFormatEnum){
			case NO_COMPRESSION:
				result = new String(decryptCommand);
				break;
			case ZIP:
				try (ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(decryptCommand)){
					Path randomUnzipDir = Paths.get(FileUtil.getTmpDir().getPath(), "unzip-input-stream", RandomUtil.randomString(10));
					FileUtil.mkParentDirs(randomUnzipDir);
					FileUtil.mkdir(randomUnzipDir);
					File unzip = ZipUtil.unzip(byteArrayInputStream, randomUnzipDir.toFile(), Charset.defaultCharset());
					log.info("二进制数据unzip解压到：{}", unzip);
					List<String> xmlFileNames = FileUtil.listFileNames(randomUnzipDir.toString());

					if (CollectionUtil.isNotEmpty(xmlFileNames)) {
						byte[] bytes = FileUtil.readBytes(Paths.get(randomUnzipDir.toString(), xmlFileNames.get(0)));
						result =  new String(bytes);
					}
					break;
				}
			case GZ:
				result = ZipUtil.unGzip(decryptCommand, StandardCharsets.UTF_8.displayName());
				break;
			default:
				result = new String(decryptCommand);
		}
		log.info("解压后的文件内容: {}", result);
		return result;
	}

	@SneakyThrows
	public static byte[] compressCommand(CompressionFormatEnum compressionFormatEnum, String commandXml) {
		switch (compressionFormatEnum){
			case NO_COMPRESSION:
				return commandXml.getBytes(StandardCharsets.UTF_8);
			case ZIP:
				try (ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(commandXml.getBytes(StandardCharsets.UTF_8))){
					File tempFile = FileUtil.createTempFile();
					ZipUtil.zip(tempFile, "command.xml", byteArrayInputStream);
					byte[] bytes = FileUtil.readBytes(tempFile);
					return bytes;
				}
			case GZ:
				return ZipUtil.gzip(commandXml.getBytes(StandardCharsets.UTF_8));
		}
		return commandXml.getBytes(StandardCharsets.UTF_8);
	}

	public static void checkPwdHash(String pwdHash, String pwd, String randVla, HashAlgorithmEnum hashAlgorithmEnum) {
		log.info("开始进行pwdHash校验, pwdHash: {}, pwd: {}, randVla: {}, hashAlgorithmEnum: {}", pwdHash, pwd, randVla, hashAlgorithmEnum);
		if (hashAlgorithmEnum != HashAlgorithmEnum.NO_HASH) {
			String pwdHashForCheck = makePwdHash(pwd, randVla, hashAlgorithmEnum);
			if (!pwdHashForCheck.equals(Base64.decodeStr(pwdHash))) {
				log.info("指令中的pwdHash(decoded)：{}， 根据内容生成的pwdHash：{}", Base64.decodeStr(pwdHash), pwdHashForCheck);
				throw new BusinessException(ResponseCodeEnum.文件解密失败);
			}
		}
	}

	public static String makePwdHash(String pwd, String randVla, HashAlgorithmEnum hashAlgorithmEnum) {
		String pwdHash = pwd + randVla;
		String result = "";
		switch (hashAlgorithmEnum) {
			case NO_HASH:
				result = "";
				break;
			case MD5:
				result = SecureUtil.md5().digestHex(pwdHash.getBytes());
				break;
			case SHA_1:
				result = SecureUtil.sha1().digestHex(pwdHash.getBytes());

				break;
		}
		return result;
	}
}
