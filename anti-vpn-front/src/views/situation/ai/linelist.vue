<template>
  <div class="lineList">
    <!-- <p>日志</p> -->
    <Empty class="empty" v-if="isEmpty" color="#fff" />
    <ul v-else>
      <li v-for="(item, index) in dataList" :key="index">
        <div class="listTitle">
          <div>
            <span
              :style="{ backgroundColor: index == 0 ? '#392c26' : '#273e50' }"
              >{{ index + 1 }}</span
            >
            {{ item.key }}
          </div>
          <div>
            <span
              class="percent"
              :style="{ color: index == 0 ? '#ea9f4c' : '#40b8db' }"
              >{{ item.percent }}%</span
            >
            <span
              class="num"
              :style="{ color: index == 0 ? '#ea9f4c' : '#40b8db' }"
              >{{ item.num | numInit }}</span
            >
          </div>
        </div>
        <div
          class="line"
          :style="{ backgroundColor: index == 0 ? '#392c26' : '#273e50' }"
        >
          <p
            :style="{
              backgroundColor: index == 0 ? '#956a3f' : '#3395b2',
              width: item.percent + '%'
            }"
          >
            <span
              :style="{
                backgroundColor: index == 0 ? '#e29b4d' : '#4bdbff'
              }"
            ></span>
          </p>
        </div>
      </li>
    </ul>
  </div>
</template>

<script>
import Empty from "@/components/empty.vue";

export default {
  props: {
    topList: {
      type: Array
    }
  },
  components: {
    Empty
  },
  watch: {
    topList: {
      handler(val) {
        this.dataList = JSON.parse(JSON.stringify(val));
        this.init();
      },
      immediate: true
    }
  },
  data() {
    return {
      dataList: [],
      isEmpty: false,
      openTwiter: false
    };
  },

  filters: {
    numInit(n) {
      let numFt = n;
      console.log(n);
      if (n >= 10000 && n < 100000000) {
        n = (n / 10000).toFixed(2);
        numFt = n + "万";
      } else if (n >= 100000000) {
        n = (n / 100000000).toFixed(2);
        numFt = n + "亿";
      }
      return numFt;
    }
  },

  methods: {
    init() {
      let total = this.dataList.reduce((n, cur) => n + cur.num, 0);
      this.dataList.length == 0
        ? (this.isEmpty = true)
        : (this.isEmpty = false);
      this.dataList.sort((a, b) => {
        return b.num - a.num;
      });
      this.dataList.map(item => {
        item.percent = ((item.num / total) * 100).toFixed(2);
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.lineList {
  margin-top: 20px;
  > p {
    text-align: right;
    color: #fff;
    margin-bottom: 5px;
  }
  .empty {
    height: 483px;
    margin: 0;
    display: flex;
    justify-content: center;
    flex-direction: column;
  }
  ul {
    height: 484px;
    overflow: auto;
  }
  li {
    border: 1px solid #18264d;
    box-sizing: border-box;
    height: 43px;
    margin-bottom: 6px;
    padding: 5px;
    &:last-child {
      margin-bottom: 0;
    }
    .listTitle {
      display: flex;
      justify-content: space-between;
      color: #fff;
      font-size: 14px;
      margin-bottom: 5px;
      > div {
        &:first-child {
          span {
            display: inline-block;
            background-color: #273e50;
            width: 30px;
            height: 24px;
            text-align: center;
            line-height: 24px;
          }
        }
        &:last-child {
          color: #fff;
          .percent {
            margin-right: 25px;
          }
        }
      }
    }
    .line {
      width: 100%;
      height: 2px;
      position: relative;
      p {
        position: absolute;
        left: 0;
        top: 0;
        height: 2px;
        span {
          position: absolute;
          right: 0px;
          top: 0px;
          display: block;
          height: 100%;
          width: 8px;
        }
      }
    }
  }
}
</style>
