package com.eversec.antivpn;

import com.eversec.antivpn.intelligence.api.dto.VpnIntelligenceCommandDistributeDTO;
import com.eversec.antivpn.intelligence.entity.VpnIntelligence;
import com.eversec.antivpn.intelligence.service.IVpnIntelligenceCommandService;
import com.eversec.antivpn.intelligence.service.IVpnIntelligenceService;
import com.eversec.antivpn.intelligence.service.impl.VpnIntelligenceCommandServiceImpl;
import com.eversec.antivpn.support.config.api.PlatformInterfaceInfoApi;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.testng.AbstractTestNGSpringContextTests;
import org.testng.annotations.Test;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023-08-01 13:40
 */
@SpringBootTest
public class VpnIntelligenceCommandServiceImplTest extends AbstractTestNGSpringContextTests {

	@Autowired
	private IVpnIntelligenceCommandService vpnIntelligenceCommandService;
	@MockBean
	private IVpnIntelligenceService iVpnIntelligenceService;
	@MockBean
	private PlatformInterfaceInfoApi platformInterfaceInfoApi;

	@Test
	public void parseVpnIntelligenceByZip() {
		VpnIntelligenceCommandServiceImpl vpnIntelligenceCommandServiceImpl = (VpnIntelligenceCommandServiceImpl)vpnIntelligenceCommandService;
		List<VpnIntelligence> vpnIntelligenceList = vpnIntelligenceCommandServiceImpl.parseVpnIntelligenceByZip(new VpnIntelligenceCommandDistributeDTO(), "D:\\code\\anti-vpn\\anti-vpn\\anti-vpn-intelligence\\src\\test\\resources\\command\\test", "1");
		System.out.println(vpnIntelligenceList);
	}


}
