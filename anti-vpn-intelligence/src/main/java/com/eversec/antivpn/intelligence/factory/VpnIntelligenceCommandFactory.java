package com.eversec.antivpn.intelligence.factory;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.eversec.antivpn.intelligence.api.dto.VpnIntelligenceCommandDistributeDTO;
import com.eversec.antivpn.intelligence.api.dto.VpnIntelligenceCommandDistributeRequestDTO;
import com.eversec.antivpn.intelligence.api.dto.VpnIntelligenceCommandReportDTO;
import com.eversec.antivpn.intelligence.api.dto.VpnIntelligenceIrcsCommandDistributeRequestDTO;
import com.eversec.antivpn.intelligence.api.enums.*;
import com.eversec.antivpn.intelligence.entity.VpnIntelligence;
import com.eversec.antivpn.intelligence.entity.VpnIntelligenceCommand;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Date;

@Component
public class VpnIntelligenceCommandFactory {

    public VpnIntelligenceCommand importIntelligence(String comCode, Long provinceId, String systemCode) {

        VpnIntelligenceCommand vpnIntelligenceCommand = new VpnIntelligenceCommand();
        vpnIntelligenceCommand.setSystemCode(systemCode);
        vpnIntelligenceCommand.setComCode(comCode);
        vpnIntelligenceCommand.setProvinceId(provinceId);
        vpnIntelligenceCommand.setCommandProcessStatus(IntelligenceCommandProcessStatusEnum.RECEIVE.getDesc());
        vpnIntelligenceCommand.setReportStatus(IntelligenceCommandReportStatusEnum.REPORTED.name());
        vpnIntelligenceCommand.setReportDatetime(LocalDateTime.now());
        vpnIntelligenceCommand.setDistributeStatus(IntelligenceCommandDistributeStatusEnum.TO_BE_DISTRIBUTED.name());
        vpnIntelligenceCommand.setSource(IntelligenceSourceEnum.INPUT.name());
        vpnIntelligenceCommand.setCommandId(RandomUtil.randomLong(10000000000000L, 100000000000000L));
        vpnIntelligenceCommand.setTimeStamp(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        vpnIntelligenceCommand.setOperationType(IntelligenceCommandOperationTypeEnum.IMPORT.getCode());
        return vpnIntelligenceCommand;
    }


    public VpnIntelligenceCommand distributeRequestDtoToIntelligenceCommandEntity(VpnIntelligenceIrcsCommandDistributeRequestDTO dto, String comCode) {
        String jsonPrettyStr = JSONUtil.toJsonPrettyStr(dto);

        VpnIntelligenceCommand vpnIntelligenceCommand = new VpnIntelligenceCommand();
        vpnIntelligenceCommand.setSystemCode(dto.getIrcsId());
        vpnIntelligenceCommand.setComCode(comCode);
        vpnIntelligenceCommand.setProvinceId(0L);
        vpnIntelligenceCommand.setCommandProcessStatus(IntelligenceCommandProcessStatusEnum.RECEIVE.getDesc());
        vpnIntelligenceCommand.setDistributeRequestParams(jsonPrettyStr);
        vpnIntelligenceCommand.setReportStatus(IntelligenceCommandReportStatusEnum.REPORTED.name());
        vpnIntelligenceCommand.setReportDatetime(LocalDateTime.now());
        vpnIntelligenceCommand.setDistributeStatus(IntelligenceCommandDistributeStatusEnum.TO_BE_DISTRIBUTED.name());
        vpnIntelligenceCommand.setSource(IntelligenceSourceEnum.INPUT.name());
        vpnIntelligenceCommand.setCommandId(0L);
        vpnIntelligenceCommand.setTimeStamp(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        vpnIntelligenceCommand.setOperationType(IntelligenceCommandOperationTypeEnum.TEMP.getCode());
        return vpnIntelligenceCommand;
    }

    public VpnIntelligenceCommand distributeRequestDtoToIntelligenceCommandEntity(VpnIntelligenceCommandDistributeRequestDTO dto, String systemCode) {
        String jsonPrettyStr = JSONUtil.toJsonPrettyStr(dto);

        VpnIntelligenceCommand vpnIntelligenceCommand = new VpnIntelligenceCommand();
        vpnIntelligenceCommand.setSystemCode(systemCode);
        vpnIntelligenceCommand.setComCode(dto.getComCode());
        vpnIntelligenceCommand.setProvinceId(dto.getProvinceId());
        vpnIntelligenceCommand.setCommandProcessStatus(IntelligenceCommandProcessStatusEnum.RECEIVE.getDesc());
        vpnIntelligenceCommand.setDistributeRequestParams(jsonPrettyStr);
        vpnIntelligenceCommand.setReportStatus(IntelligenceCommandReportStatusEnum.REPORTED.name());
        vpnIntelligenceCommand.setReportDatetime(LocalDateTime.now());
        vpnIntelligenceCommand.setDistributeStatus(IntelligenceCommandDistributeStatusEnum.TO_BE_DISTRIBUTED.name());
        vpnIntelligenceCommand.setSource(IntelligenceSourceEnum.INPUT.name());
        vpnIntelligenceCommand.setCommandId(0L);
        vpnIntelligenceCommand.setTimeStamp(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        vpnIntelligenceCommand.setOperationType(IntelligenceCommandOperationTypeEnum.TEMP.getCode());
        return vpnIntelligenceCommand;
    }

    /**
     * 指令下发对象转为指令对象
     */
    public VpnIntelligenceCommand distributeDtoToIntelligenceCommandEntity(VpnIntelligenceCommand dbVpnIntelligenceCommand, VpnIntelligenceCommandDistributeDTO dto, String systemCode) {
        VpnIntelligenceCommand vpnIntelligenceCommand = new VpnIntelligenceCommand();
        BeanUtil.copyProperties(dto, vpnIntelligenceCommand);
        vpnIntelligenceCommand.setVpnSource(dto.getSource());
        vpnIntelligenceCommand.setDistributeRequestParams(dbVpnIntelligenceCommand.getDistributeRequestParams());
        vpnIntelligenceCommand.setId(dbVpnIntelligenceCommand.getId());
        vpnIntelligenceCommand.setCommandProcessStatus(IntelligenceCommandProcessStatusEnum.RECEIVE.getDesc());


        vpnIntelligenceCommand.setSystemCode(systemCode);

        vpnIntelligenceCommand.setReportStatus(IntelligenceCommandReportStatusEnum.REPORTED.name());
        vpnIntelligenceCommand.setReportDatetime(LocalDateTime.now());
        vpnIntelligenceCommand.setDistributeStatus(IntelligenceCommandDistributeStatusEnum.TO_BE_DISTRIBUTED.name());
        return vpnIntelligenceCommand;
    }

    /**
     * 上报时生成对象
     * @return
     */
    public VpnIntelligenceCommand createVpnIntelligenceCommand(String comCode, Long provinceId, String systemCode, Long commandId, Integer networkBusinessId) {
        VpnIntelligenceCommand vpnIntelligenceCommand = new VpnIntelligenceCommand();
        vpnIntelligenceCommand.setCommandId(commandId);
        vpnIntelligenceCommand.setVersion("1.0");
        vpnIntelligenceCommand.setComCode(comCode);
        vpnIntelligenceCommand.setProvinceId(provinceId);
        vpnIntelligenceCommand.setSystemCode(systemCode);
        vpnIntelligenceCommand.setNetworkBusinessId(networkBusinessId);
        vpnIntelligenceCommand.setFileName(null);
        vpnIntelligenceCommand.setIsUploadFile(1);
        vpnIntelligenceCommand.setAttachMent(null);
        vpnIntelligenceCommand.setTimeStamp(LocalDateTimeUtil.format(LocalDateTime.now(), "yyyy-MM-dd HH:mm:ss"));
        vpnIntelligenceCommand.setOperationType(IntelligenceCommandOperationTypeEnum.REPORT.getCode());
        vpnIntelligenceCommand.setSource(IntelligenceSourceEnum.INPUT.name());
        vpnIntelligenceCommand.setReportStatus(IntelligenceCommandReportStatusEnum.REPORTED.name());
        vpnIntelligenceCommand.setReportDatetime(LocalDateTime.now());
        vpnIntelligenceCommand.setDistributeStatus(IntelligenceCommandDistributeStatusEnum.TO_BE_DISTRIBUTED.name());
        vpnIntelligenceCommand.setDistributeDatetime(LocalDateTime.now());
        return vpnIntelligenceCommand;
    }

    public VpnIntelligenceCommandReportDTO createReportDto(VpnIntelligenceCommand vpnIntelligenceCommand, VpnIntelligence vpnIntelligence, String attachMent) {
        VpnIntelligenceCommandReportDTO reportDTO = new VpnIntelligenceCommandReportDTO();
        reportDTO.setVersion(vpnIntelligenceCommand.getVersion());
        reportDTO.setCommandId(vpnIntelligenceCommand.getCommandId());
        reportDTO.setComCode(vpnIntelligenceCommand.getComCode());
        reportDTO.setProvinceId(vpnIntelligenceCommand.getProvinceId());
        reportDTO.setNetworkBusinessId(vpnIntelligenceCommand.getNetworkBusinessId());
        reportDTO.setTypeId(vpnIntelligence.getTypeId());
        reportDTO.setVpnId(vpnIntelligence.getVpnId());
        reportDTO.setVpnName(vpnIntelligence.getVpnName());
        reportDTO.setVpnDomain(vpnIntelligence.getVpnDomain());
        reportDTO.setVpnIp(vpnIntelligence.getVpnIp());
        reportDTO.setVpnUrl(vpnIntelligence.getVpnUrl());
        reportDTO.setVpnLink(vpnIntelligence.getVpnLink());
        if (StrUtil.isNotBlank(attachMent)) {
            reportDTO.setIsUploadFile(0);
            reportDTO.setAttachMent(attachMent);
        } else {
            reportDTO.setIsUploadFile(1);
            reportDTO.setAttachMent("");
        }
        reportDTO.setOperationType(vpnIntelligenceCommand.getOperationType());
        reportDTO.setTimeStamp(vpnIntelligenceCommand.getTimeStamp());
        return reportDTO;

    }

    public VpnIntelligenceCommandDistributeDTO createDistributeDto() {
        VpnIntelligenceCommandDistributeDTO vpnIntelligenceCommandDistributeDTO = new VpnIntelligenceCommandDistributeDTO();
        // TODO: 2023-08-21
        return vpnIntelligenceCommandDistributeDTO;
    }

}
