<template>
  <section class="content">
    <div class="con_left">
      <TopNum :numList="numList"></TopNum>
      <div class="leftbox mt20">
        <div class="leftbox_l">
          <Mybox title="访问者所在地区分布TOP10">
            <AreaTop10
              v-loading="area_loading"
              element-loading-background="rgba(0, 0, 0, 0.2)"
              :lists="area_list"
              style="height:188px"
            />
          </Mybox>
          <Mybox class="mt10" title="访问者的目的地区分布TOP10">
            <AreaTop10
              v-loading="areaDesc_loading"
              element-loading-background="rgba(0, 0, 0, 0.2)"
              :lists="areaDesc_list"
              style="height:188px"
            />
          </Mybox>
          <Mybox class="mt10" title="用户跨境通信时间分布图">
            <e-charts
              style="height:260px"
              type="line"
              :options="lineOpt"
              emptyColor="#fff"
            ></e-charts>
          </Mybox>
        </div>

        <div class="leftbox_mid">
          <section style="height:580px;">
            <MapChina ref="mapRef" :mapD="mapD" v-if="!isProvince" />
            <MapShandong
              :mapD="mapD"
              :myTooltip="myTooltip"
              v-if="isProvince && provinceName == 'shandong'"
            />
          </section>

          <Mybox2 style="margin-top:-38px;" title="用户跨境访问日志">
            <mid2
              :tableD="tableD"
              v-loading="table_loading"
              element-loading-background="rgba(0, 0, 0, 0.2)"
            />
          </Mybox2>
        </div>
      </div>
    </div>
    <div class="con_rig">
      <Mybox title="单用户跨境通信次数 TOP10">
        <AreaTop10
          v-loading="vpn_loading"
          element-loading-background="rgba(0, 0, 0, 0.2)"
          style="height:255px"
          :lists="vpn_list.slice(0, 10)"
        />
      </Mybox>
      <Mybox class="mt20" title="单用户跨境通信使用资源节点总量TOP10">
        <AreaTop10
          v-loading="vpnnode_loading"
          element-loading-background="rgba(0, 0, 0, 0.2)"
          :lists="vpnnode_list.slice(0, 10)"
        />
      </Mybox>
      <Mybox class="mt20" title="跨境通信用户量趋势图">
        <e-charts
          style="height:280px"
          type="line"
          :options="lineMidOpt"
          v-loading="lineMid_loading"
          element-loading-background="rgba(0, 0, 0, 0.2)"
          emptyColor="#fff"
        ></e-charts>
      </Mybox>
    </div>
  </section>
</template>

<script>
import api from "@/api/rm/dic";
import { getDicAll, getDicKey2Val } from "@/assets/js/dic.js";
import { formatTime } from "@/utils/time";
import { lineBase, linesFn, publicPieFn } from "../echartOption";
import Header from "../common/Header.vue";
import Mybox from "../common/Mybox.vue";
import Mybox2 from "../common/Mybox2.vue";
import Pie2 from "../common/Pie2.vue";
import AreaTop10 from "../common/AreaTop10.vue";
import MapChina from "./MapChinaBar.vue";
import TopNum from "./TopNum.vue";
import mid2 from "./mid2.vue";
import userApi from "./userApi.js";
import MapShandong from "../common/map/Shandong.vue";

export default {
  components: {
    Header,
    Mybox,
    Mybox2,
    Pie2,
    MapChina,
    AreaTop10,
    TopNum,
    mid2,
    MapShandong
  },
  computed: {
    isProvince() {
      // return false;
      return localStorage.isProvince == 'true';
    },
    
    provinceName() {
      return localStorage.provinceName;
    }
  },
  data() {
    return {
      searchTime: {
        startTime: "",
        endTime: ""
      },
      bigPage: null,
      map_loading: false,
      mapD: [],
      myTooltip:
        '<div class="tooltipbox" style="height: 110px;width: 190px;">' +
        '<div class="provincename">${d.name} </div>' +
        '<div class="tooltip-item">' +
        '<div class="item-name" style="min-width: 106px;">排名：</div>' +
        '<div class="item-value color_num">${d.idx}</div>' +
        "</div>" +
        '<div class="tooltip-item">' +
        '<div class="item-name" style="min-width: 106px;">跨境通信用户：</div>' +
        '<div class="item-value color_num">${d.value}</div>' +
        "</div>" +
        '<div class="tooltip-item">' +
        '<div class="item-name" style="min-width: 106px;">今日跨境通信用户：</div>' +
        '<div class="item-value color_num2"> ${d.todayNum}</div>' +
        "</div>" +
        "</div>",
      numList: [
        {
          name: "跨境通信用户数",
          num: 0,
          unit: "人",
          icon: require("./img/icon1.png")
        },
        {
          name: "有手机号码的用户",
          num: 0,
          unit: "人",
          icon: require("./img/icon2.png")
        },
        {
          name: "访问者地区数量",
          num: 0,
          icon: require("./img/icon3.png")
        },
        {
          name: "目的地地区数量",
          num: 0,
          icon: require("./img/icon4.png")
        },
        {
          name: "总跨境通信次数",
          num: 0,
          icon: require("./img/icon5.png")
        }
      ],
      provinceOpt: [],
      cityOpt: [],
      areaOpt: [],
      lineOpt: null,
      lineMidOpt: null,
      lineMid_loading: false,
      net_loading: false,
      area_list: [],
      area_loading: false,
      areaDesc_list: [],
      areaDesc_loading: false,
      vpn_list: [],
      vpn_loading: false,
      vpnnode_list: [],
      vpnnode_loading: false,
      tableD: [],
      table_loading: false
    };
  },
  async created() {
    this.$emit("onUpdateMain", {
      pageContent: {
        padding: 0,
        background: "#F7F8FC"
      }
    });
  },
  async mounted() {},
  methods: {
    async update(screenType, ft, isGroup = true) {
      if (screenType) {
        this.searchTime = {
          screenTypeEnum: screenType
        };
      }
      this.getTopNum();

      // 字典
      this.provinceOpt = await api.getDictList("PROVINCE_CODE");
      if (this.isProvince) {
        this.cityOpt = await api.getDictList("CITY_CODE");
      }
      this.areaOpt = this.isProvince ? this.cityOpt : this.provinceOpt;

      this.left();

      this.mapFn();
      this.mid2();

      // 右侧
      this.rig();
      this.rig3();
    },
    getTopNum() {
      userApi.vpnUserNum(this.searchTime).then(res => {
        console.log("num1--", res);
        this.numList[0].num = res;
      });
      userApi.userHaveTelPhoneNum(this.searchTime).then(res => {
        console.log("num2--", res);
        this.numList[1].num = res;
      });
      userApi.userProvinceNum(this.searchTime).then(res => {
        console.log("num3--", res);
        this.numList[2].num = res;
      });
      userApi.destProvinceNum(this.searchTime).then(res => {
        console.log("num4--", res);
        this.numList[3].num = res;
      });
      userApi.crossBorderTotalNum(this.searchTime).then(res => {
        console.log("num5--", res);
        this.numList[4].num = res;
      });
    },
    async left() {
      this.area_loading = true;
      userApi
        .userProvinceTop10(this.searchTime)
        .then(res => {
          console.log("left1--", res);
          let d = (res || []).filter(el => {
            let nameFt = this.getDicKey2Val(el.key, this.areaOpt); // 匹配不上的不返回
            el.name = nameFt;
            return nameFt;
          })
          let total = d.reduce((n, cur) => (n += cur.num), 0);
          this.area_list = d.map(el => {
            return {
              ...el,
              // name: getDicKey2Val(el.key, this.areaOpt),
              value: el.num,
              percent: el.percent || ((el.num * 100) / total).toFixed(0) + "%"
            };
          });
        })
        .finally(el => (this.area_loading = false));

      // this.areaDesc_list = [
      //   { name: "北京市", value: 2000, percent: "20%" },
      //   { name: "河北省", value: 1000, percent: "20%" },
      //   { name: "河南省", value: 1000, percent: "20%" },
      //   { name: "江苏省", value: 1000, percent: "20%" },
      //   { name: "黑龙江", value: 1000, percent: "20%" }
      // ];
      this.areaDesc_loading = true;
      userApi
        .userDestTop10(this.searchTime)
        .then(res => {
          console.log("left2--", res);
          let d = (res || []);
          // let d = (res || []).filter(el => {
          //   let nameFt = this.getDicKey2Val(el.key, this.areaOpt); // 匹配不上的不返回
          //   el.name = nameFt;
          //   return nameFt;
          // })

          let total = d.reduce((n, cur) => (n += cur.num), 0);
          this.areaDesc_list = d.map(el => {
            return {
              ...el,
              name: getDicKey2Val(el.key, this.areaOpt),
              value: el.num,
              percent: el.percent || ((el.num * 100) / total).toFixed(0) + "%"
            };
          });
          console.log("left2--2", this.areaDesc_list);
        })
        .finally(el => (this.areaDesc_loading = false));

      // let d = [
      //   { name: "08-13", value: 100 },
      //   { name: "08-14", value: 200 },
      //   { name: "08-15", value: 300 },
      //   { name: "08-16", value: 130 },
      //   { name: "08-17", value: 180 }
      // ];
      // this.lineOpt = lineBase(d);

      // this.areaDesc_loading = true;
      userApi.userCrossBorderCommDateHistogram(this.searchTime).then(res => {
        console.log("left3--", res);
        let d = (res || []).filter(el => el.key); // 过滤空数据
        d = d.map(el => {
          let name = el.key;
          if (el.key.length > 7) {
            name = `${el.key.slice(4, 6)}-${el.key.slice(6, 8)}`;
          }
          return {
            ...el,
            name,
            value: el.num
          };
        });
        this.lineOpt = lineBase(d);
      });
      // .finally(el => this.areaDesc_loading = false);
    },
    rig() {
      this.vpn_loading = true;
      userApi
        .userCrossBorderCommTimesTop10(this.searchTime)
        .then(res => {
          console.log("rig1--", res);
          let d = (res || []).filter(el => el.key); // 过滤空数据
          let total = d.reduce((n, cur) => (n += cur.num), 0);
          this.vpn_list = d.map(el => {
            return {
              ...el,
              name: el.key,
              // name: getDicKey2Val(el.key, this.areaOpt),
              value: el.num,
              percent: el.percent || ((el.num * 100) / total).toFixed(0) + "%"
            };
          });
        })
        .finally(el => (this.vpn_loading = false));

      this.vpnnode_loading = true;
      userApi
        .userCrossBorderCommResourceNodeTop10(this.searchTime)
        .then(res => {
          console.log("rig2--", res);
          let d = (res || []).filter(el => el.key);
          let total = d.reduce((n, cur) => (n += cur.num), 0);
          this.vpnnode_list = d.map(el => {
            return {
              ...el,
              name: el.key,
              // name: getDicKey2Val(el.key, this.areaOpt),
              value: el.num,
              percent: el.percent || ((el.num * 100) / total).toFixed(0) + "%"
            };
          });
        })
        .finally(el => (this.vpnnode_loading = false));
    },
    getDicKey2Val(enumKey, dicArr, keyName = "enumVal") {
      if (!dicArr) return;
      let f = dicArr.find(el => el.enumKey == enumKey);
      if (f) return f[keyName];
      return false;
    },
    getNum(n) {
      let numFt = n;
      if (n >= 10000 && n < 100000000) {
        n = (n / 10000).toFixed(2);
        numFt = n + "万";
      } else if (n >= 100000000) {
        n = (n / 100000000).toFixed(2);
        numFt = n + "亿";
      }
      return numFt;
    },
    mapFn() {
      // this.$refs.mapRef && this.$refs.mapRef.drawMap();
      // this.mapD = [
      //   { name: "江苏", value: 1000 },
      //   { name: "黑龙江", value: 900 },
      //   { name: "吉林", value: 500 },
      //   { name: "青海", value: 400 },
      //   { name: "云南", value: 200 }
      // ];
      this.map_loading = true;
      userApi
        .map(this.searchTime)
        .then(res => {
          console.log("map--", this.isProvince, res);
          let d = (res || []).filter(el => {
            let nameFt = this.getDicKey2Val(el.key, this.areaOpt); // 匹配不上的不返回
            el.name = nameFt;
            return nameFt;
          });

          this.mapD = d.map((el, i) => {
            return {
              ...el,
              // name: getDicKey2Val(el.key, opt),
              value: el.num,
              numFt: this.getNum(el.num),
              todayNumFt: this.getNum(el.todayNum),
              idx: i + 1
            };
          });
        })
        .finally(el => (this.map_loading = false));
    },
    rig3() {
      // let obj = {
      //   name: ["跨境通信用户量"],
      //   xData: ["08-13", "08-14", "08-15", "08-16", "08-17", "08-18", "08-19"],
      //   lineData: [
      //     [10, 55, 17, 20, 30, 25, 100]
      //     // [20, 25, 27, 20, 40, 35]
      //   ]
      // };
      // let color = ["254,79,110"];
      // this.lineMidOpt = linesFn(obj, color, false);

      this.lineMid_loading = true;
      userApi
        .userDateHistogram(this.searchTime)
        .then(res => {
          console.log("rig3--", res);
          let d = (res || []).filter(el => el.key); // 过滤空数据
          d = d.map(el => {
            let name = el.key;
            if (el.key.length > 7) {
              name = `${el.key.slice(4, 6)}-${el.key.slice(6, 8)}`;
            }
            return {
              ...el,
              name,
              value: el.num
            };
          });
          this.lineMidOpt = lineBase(d);
        })
        .finally(el => (this.lineMid_loading = false));
    },
    mid2() {
      this.table_loading = true;
      userApi
        .userCrossBorderLogPage(this.searchTime)
        .then(res => {
          console.log("mid2--", res);
          this.tableD = (res || []).map(el => {
            return {
              ...el,
              time: formatTime(el.accessDateTime),
              province: getDicKey2Val(el.provinceId, this.provinceOpt)
            };
          });
        })
        .finally(el => (this.table_loading = false));
    }
  }
};
</script>

<style lang="scss" scoped>
.content {
  display: flex;
  // min-height: 700px;
  padding: 0 30px;
  width: 100%;
  justify-content: space-between;
  .con_left {
    width: 1400px;
    .leftbox {
      display: flex;
      justify-content: space-between;
      .leftbox_l {
        width: 440px;
      }
      .leftbox_mid {
        width: calc(100% - 460px);
      }
    }
  }
  .con_rig {
    width: 440px;

    .areaList {
      height: 220px;
    }
  }
}
</style>
