plugins {
    id 'java'
    id 'maven-publish'
    id 'io.spring.dependency-management' version '1.0.15.RELEASE'
    id 'com.google.cloud.tools.jib' version '3.3.1'
    id 'com.gorylenko.gradle-git-properties' version '2.4.0'
}

allprojects {
    group 'com.eversec.antivpn'
    version '1.0.0-SNAPSHOT'

    apply plugin: "java"
    apply plugin: 'maven-publish'
    apply plugin: "io.spring.dependency-management"


    sourceCompatibility = JavaVersion.VERSION_1_8
    targetCompatibility = JavaVersion.VERSION_1_8

    tasks.withType(JavaCompile) {
        options.encoding = "UTF-8"
    }

    buildscript {
        ext {
            set('springCloudVersion', "2021.0.5")
            set('springBootVersion', "2.7.6")
            set('springCloudAlibabaVersion', "2021.0.5.0")

            // 覆盖 springboot parent 中的 flyway 版本，flyway8不支持mysql5.7
            set("flyway.version", "7.15.0")
            set("lombokVersion", "1.18.28")
            set("mybatisPlusVersion", "*******")
            set("mybatisPlusDatasourceVersion", "3.6.1")
            set("mysqlConnectorVersion", "8.0.33")
            set("clickhouseConnectorVersion", "0.3.2-patch7")
            set("p6spyVersion", "3.9.1")
            set("guavaVersion", "32.1.1-jre")
            set("hutoolVersion", "5.8.20")
            set("springdocOpenapiVersion", "1.6.8")
            set("knife4jSpringdocVersion", "3.0.3")
            set("easyMinioVersion", "1.0.2")
//            set("webserviceCxfVersion", "3.5.6")
            set("webserviceCxfVersion", "3.5.7")
//            set("webserviceCxfVersion", "3.6.2")
//            set("webserviceCxfVersion", "4.0.3")

            set("eversecWebbootVersion", "3.1.8")
            set("eversecFeignbootVersion", "1.5.5")
            set("eversecDynamicFlyway", "1.0.1")

            set("genericVersion", "1.1.1")
            set("starkCommonVersion", "1.0.4-SNAPSHOT")
            set("ebpSystemVersion","2.1.0-release")

            set("cloudCoreVersion", "1.5.5")

        }
        repositories {
            maven { name "Alibaba"; url 'https://maven.aliyun.com/repository/public' }
            maven {
                allowInsecureProtocol = true
                url 'http://192.168.205.11:8282/repository/public/'
            }
            maven { name "M2"; url 'https://plugins.gradle.org/m2/' }
        }
    }


    dependencies {
        annotationProcessor "org.projectlombok:lombok:${lombokVersion}"
        compileOnly "org.projectlombok:lombok:${lombokVersion}"
        testImplementation "org.projectlombok:lombok:${lombokVersion}"
        testAnnotationProcessor "org.projectlombok:lombok:${lombokVersion}"

    }


    repositories {
        maven { name "Alibaba"; url 'https://maven.aliyun.com/repository/public' }
        maven {
            allowInsecureProtocol = true
            url 'http://192.168.205.11:8282/repository/public/'
        }
        maven { name "M2"; url 'https://plugins.gradle.org/m2/' }
    }



    dependencyManagement {
        imports {
            mavenBom "org.springframework.cloud:spring-cloud-dependencies:${springCloudVersion}"
            mavenBom "org.springframework.boot:spring-boot-parent:${springBootVersion}"
            mavenBom "org.springframework.boot:spring-boot-dependencies:${springBootVersion}"

            mavenBom "org.projectlombok:lombok:${lombokVersion}"
        }
        resolutionStrategy {
            cacheChangingModulesFor 0, 'seconds'
        }
    }

    // 打包sourcesJar任务
    task sourcesJar(type: Jar, dependsOn: classes) {
        classifier = 'sources'
        from sourceSets.main.allSource
    }
    tasks.withType(JavaCompile) {
        options.encoding = "UTF-8"
    }
    publishing {
        // 发布
        publications {
            mavenJava(MavenPublication) {
                //指定group/artifact/version信息
                groupId project.group
                artifactId project.name
                version project.version
                // components.java jar包
                // components.web war包
                from components.java

                // 增加 sourcesJar、javadocJar 任务
                artifact sourcesJar
            }
        }

        repositories {
            maven {
                name "snapshots"
                allowInsecureProtocol = true
                //默认发布到Maven Nexus私服的发行库
                def nexusUrl ="http://192.168.205.11:8282/repository/releases/"
                //如果为快照版本发布到Maven Nexus私服的快照库
                if(version.endsWith("-SNAPSHOT")) {
                    nexusUrl ="http://192.168.205.11:8282/repository/snapshots/"
                }
                url nexusUrl

                // 凭证
                credentials {
                    username 'admin' // 仓库发布用户名
                    password 'admin123' // 仓库发布用户密码
                }
            }
        }
    }

}
