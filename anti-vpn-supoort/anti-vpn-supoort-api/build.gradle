dependencies {
    implementation 'org.springframework.boot:spring-boot-starter-web'
    // 分页接口
    implementation "com.baomidou:mybatis-plus-extension:${mybatisPlusVersion}"
    compileOnly "org.springdoc:springdoc-openapi-ui:${springdocOpenapiVersion}"
    implementation "com.eversec.framework:eversec-feignboot:${eversecFeignbootVersion}"
    //知识库分页返回
    implementation "com.eversec.cloud:cloud-core:${cloudCoreVersion}"
    test {
        useTestNG()
    }
}
