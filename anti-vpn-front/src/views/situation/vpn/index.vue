<template>
  <section class="content">
    <div class="con_left">
      <div class="leftbox">
        <div class="leftbox_l">
          <LeftNum ref="leftNum" />
          <Mybox title="用户使用通道热度 TOP10" style="margin-top: 10px">
            <Left2 :list="list_l2" />
          </Mybox>
          <Mybox
            title="近7日服务商及资源节点活跃量趋势"
            style="margin-top: 23px"
          >
            <e-charts
              style="height:260px"
              type="line"
              :options="lineOpt"
              emptyColor="#fff"
            ></e-charts>
          </Mybox>
        </div>

        <div class="leftbox_mid">
          <MapWorld ref="mapRef" />

          <Mybox2 class="mid2" title="最新发现的VPN服务商信息">
            <mid2 ref="mid2Ref" />
          </Mybox2>
        </div>
      </div>
    </div>
    <div class="con_rig">
      <Mybox title="所在地区VPN服务商数量 TOP10">
        <Pie2 ref="rig1Ref" />
      </Mybox>
      <Mybox title="所在地区VPN服务资源节点数量 TOP10">
        <Pie2 ref="rig2Ref" />
      </Mybox>
      <Mybox title="VPN服务商用户量TOP10">
        <Pie2 ref="rig3Ref" style="height:290px" />
      </Mybox>
    </div>
  </section>
</template>

<script>
import { linesFn } from "../echartOption";
import Header from "../common/Header.vue";
import Mybox from "../common/Mybox.vue";
import Mybox2 from "../common/Mybox2.vue";
import Pie2 from "../common/Pie2.vue";
import MapWorld from "./MapWorld.vue";
import LeftNum from "./LeftNum.vue";
import Left2 from "./Left2.vue";
import mid2 from "./mid2.vue";
import vpnApi from "./vpnApi.js";

export default {
  components: { Header, Mybox, Mybox2, Pie2, MapWorld, LeftNum, Left2, mid2 },
  data() {
    return {
      searchTime: {
        startTime: "",
        endTime: ""
      },
      bigPage: null,
      dicAll: {},
      mapD: [],
      lineOpt: null,
      list_l2: [],
      bigStyle: {}
    };
  },
  computed: {},
  watch: {},
  async created() {
    this.$emit("onUpdateMain", {
      pageContent: {
        padding: 0,
        background: "#F7F8FC"
      }
    });
  },
  methods: {
    getNum(n) {
      let numFt = n;
      if (n >= 10000 && n < 100000000) {
        n = (n / 10000).toFixed(2);
        numFt = n + "万";
      } else if (n >= 100000000) {
        n = (n / 100000000).toFixed(2);
        numFt = n + "亿";
      }
      return numFt;
    },
    async update(screenType, ft) {
      if (screenType) {
        this.searchTime = {
          screenTypeEnum: screenType
        };
      }

      this.$refs.leftNum.getNum(this.searchTime);

      this.$refs.mid2Ref.init(this.searchTime);
      vpnApi.map(this.searchTime).then(res => {
        console.log("map-", res);
        let d = (res || []).map(el => {
          return {
            ...el,
            name: el.key,
            value: el.resourceNodeNum,
            value2: el.vpnServiceNum,
            valueFt: this.getNum(el.resourceNodeNum),
            value2Ft: this.getNum(el.vpnServiceNum)
          };
        });
        this.$refs.mapRef.drawMap(d);
      });

      this.left2();
      this.left3();

      this.rig();
    },
    rig() {
      // 右侧
      // let d1 = [
      //   { name: "美国", value: 1500 },
      //   { name: "日本", value: 1000 },
      //   { name: "中国", value: 1000 },
      //   { name: "韩国", value: 800 },
      //   { name: "意大利", value: 500 },
      //   { name: "英国", value: 300 }
      // ];
      vpnApi.countryVpnServiceNumTop10(this.searchTime).then(res => {
        console.log("rig1-", res);
        let d = (res || []).filter(el => el.key); // 过滤空数据
        d = d.map(el => {
          return {
            ...el,
            name: el.key,
            value: el.vpnServiceNum || 0
          };
        });
        this.$refs.rig1Ref.init("VPN服务商", "占比", d);
      });
      vpnApi.countryResourceNodeNumTop10(this.searchTime).then(res => {
        console.log("rig2-", res);
        let d = (res || []).filter(el => el.key); // 过滤空
        d = d.map(el => {
          return {
            ...el,
            name: el.key,
            value: el.num || 0
          };
        });
        this.$refs.rig2Ref.init("VPN服务资源", "节点占比", d);
      });

      // let d2 = [
      //   { name: "有配套软件", value: 1500 },
      //   { name: "只有第三方软件", value: 1000 }
      // ];
      // this.$refs.rig3Ref.init("", "", d2);
      vpnApi.vpnVpnServicePersonNumTop10(this.searchTime).then(res => {
        console.log("rig3-", res);
        let d = (res || []).filter(el => el.key); // 过滤空
        let d2 = d.map(el => {
          return {
            ...el,
            name: el.key,
            value: el.num
          };
        });
        this.$refs.rig3Ref.init("", "", d2);
      });
    },
    left2() {
      vpnApi.usedResourceNodeTop10(this.searchTime).then(res => {
        console.log("left2-", res);
        if (res) {
          // let d = (res || []).filter(el => el.vpnServiceName); // 过滤空数据
          let d=res
          let total = d.reduce((n, cur) => (n += cur.personNum), 0);
          this.list_l2 = d
            .map(el => {
              return {
                ...el,
                name: el.vpnServiceName,
                subName: el.resourceNodeName,
                value: el.personNum,
                percent: ((el.personNum * 100) / total).toFixed(0)
              };
            })
            .slice(0, 10);
          console.log("left-sss", this.list_l2);
        }
      });
    },
    left3() {
      // let obj = {
      // name: ["服务商", "资源节点"],
      // xData: ["08-13", "08-14", "08-15", "08-16", "08-17", "08-18"],
      // lineData: [
      //   [10, 15, 17, 20, 30, 25],
      //   [20, 25, 27, 20, 40, 35]
      // ]
      // };
      // this.lineOpt = linesFn(obj);

      vpnApi
        .vpnServiceAndResourceNodeDateHistogram(this.searchTime)
        .then(res => {
          console.log("left3-", res);
          let d1 = [];
          let d2 = [];
          if (res) {
            let xData = res.map(el => {
              d1.push(el.vpnServiceNum);
              d2.push(el.resourceNodeNum);
              let name = el.key;
              if (el.key.length > 7) {
                name = `${el.key.slice(4, 6)}-${el.key.slice(6, 8)}`;
              }
              return name;
            });
            let obj = {
              name: ["服务商", "资源节点"],
              xData,
              lineData: [d1, d2]
            };
            this.lineOpt = linesFn(obj);
          }
        });
    }
  }
};
</script>

<style lang="scss" scoped>
@import "../common/bigScreen.scss";
.pagebox {
  background: url("../common/img/bigscreenbg.png") no-repeat;
}
.content {
  display: flex;
  // min-height: 700px;
  padding: 0 30px;
  width: 100%;
  justify-content: space-between;
  .con_left {
    width: 1400px;
    .leftbox {
      display: flex;
      justify-content: space-between;
      .leftbox_l {
        width: 440px;
      }
      .leftbox_mid {
        width: calc(100% - 440px);
        .mid2 {
          margin: 4px 0 0 20px;
        }
      }
    }
  }
  .con_rig {
    width: 440px;
  }
}
</style>
