<template>
  <section class="title_box2">
    <div class="box_tit">
      {{ title }}
    </div>
    <div class="box_con">
      <slot />
    </div>
  </section>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: ""
    }
  },
  data() {
    return {};
  },
  methods: {
  }
};
</script>

<style scoped lang="scss">
.title_box2 {
  min-width: 910px;
  margin-left: auto;
  margin-right: auto;
  color: #efedf2;
  font-size: 14px;
  .box_tit {
    color: #fff;
    font-size: 16px;
    padding: 0 40px;
    line-height: 40px;
    height: 37px;
    background: url(./img/tit2_bg.png) no-repeat;
    background-size: 100% 100%;
    position: relative;
  }
  .box_con {
    height: 260px;
    // padding-top: 10px;
  }
}
</style>
