package com.eversec.antivpn.log.aop;

import com.eversec.antivpn.log.service.ScreenRedisService;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/9/8 11:16
 **/

@Aspect
@Component
@Slf4j
public class RedisCacheAop {

    @Resource
    private ScreenRedisService screenRedisService;

    @Value("${app.cacheEnabled}")
    private Boolean cacheEnabled;

    @Around("@annotation(com.eversec.antivpn.log.aop.RedisCache)")
    public Object around(ProceedingJoinPoint joinPoint) {
        String key = getKey(joinPoint);
        return getObjectFromCache(joinPoint, key);
    }

    @Around("@annotation(com.eversec.antivpn.log.aop.RedisCacheOnlyMethodKey)")
    public Object aroundOnlyMethodKey(ProceedingJoinPoint joinPoint) {
        String key = getKeyOnlyClassAndMethod(joinPoint);
        return getObjectFromCache(joinPoint, key);
    }

    private Object getObjectFromCache(ProceedingJoinPoint joinPoint, String key) {
        Object value = screenRedisService.getScreenResult(key);
        if(cacheEnabled == false) {
            try {
                return joinPoint.proceed();
            } catch (Throwable throwable) {
                throwable.printStackTrace();
            }
        }
        log.info("进行缓存查询："+key);
        try {
            if (value == null) {
                value = joinPoint.proceed();
                screenRedisService.setScreenResult(key,value);
            }
        } catch (Throwable e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        }
        return value;
    }

    private String getKey(ProceedingJoinPoint joinPoint) {
            String className = joinPoint.getSignature().getDeclaringTypeName();
            String methodName = joinPoint.getSignature().getName();
            Object[] args = joinPoint.getArgs();
            String key;
            if(args==null || args.length==0 ){
                key = className + ":" + methodName;
            }else {
                Object arg0 = args[0];
                key = className + ":" + methodName + ":" + arg0;
            }
        return key;
    }

    private String getKeyOnlyClassAndMethod(ProceedingJoinPoint joinPoint) {
        String className = joinPoint.getSignature().getDeclaringTypeName();
        String methodName = joinPoint.getSignature().getName();
        return className + ":" + methodName;
    }

}
