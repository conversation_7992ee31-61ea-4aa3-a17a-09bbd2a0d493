package com.eversec.antivpn.log.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.eversec.antivpn.config.enums.AntiVpnDataDictTypeEnum;
import com.eversec.antivpn.emnus.ScreenTypeEnum;
import com.eversec.antivpn.log.api.dto.TypeCountDTO;
import com.eversec.antivpn.log.api.dto.base.DateHistogramDTO;
import com.eversec.antivpn.log.constants.CKColumn;
import com.eversec.antivpn.log.entity.E1CommLogUserView;
import com.eversec.antivpn.log.entity.po.GenericSysDataDict;
import com.eversec.antivpn.log.service.GenericSysDataDictService;
import com.eversec.antivpn.log.service.IE1CommLogUserViewService;
import com.eversec.antivpn.log.service.SoftWareCommLogService;
import com.eversec.antivpn.log.util.CalculateUtil;
import com.eversec.stark.generic.sys.api.SysDataDictApi;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-12
 */
@Service
@Slf4j
public class SoftWareCommLogServiceImpl implements SoftWareCommLogService {

    @Resource
    private IE1CommLogUserViewService service;

    @Autowired
    private SysDataDictApi sysDataDictApi;

    @Resource
    private GenericSysDataDictService genericSysDataDictService;

    public Map<ScreenTypeEnum, List<String>> e1LogsByPartitionMap = new HashMap<>();


    /**
     * 根据查询 今日、周、月、至今时间分区获取E1数据
     *
     * @param screenTypeEnum
     * @return 软件id 去重
     */
    public Set<String> getSoftWareIdsByPartitionDistinct(ScreenTypeEnum screenTypeEnum) {
        Set<String> softWareIds = new HashSet<>();
        //1、获取E1数据
        List<String> e1LogsByPartitionList = service.getE1LogBySoftWareByPartition(screenTypeEnum);
        e1LogsByPartitionList.forEach(softwareCode -> {
            // vpn_software_codes ["code1", "code2"]
            if (StringUtils.isNotBlank(softwareCode)) {
                JSONArray array = JSONUtil.parseArray(softwareCode);
                array.forEach(softWare -> {
                    softWareIds.add((String) softWare);
                });

            }
        });

        return softWareIds;
    }


    /**
     * 根据查询 今日、周、月、至今时间分区获取E1数据
     *
     * @param screenTypeEnum
     * @return 软件id 未去重
     */
    public List<String> getSoftWareIdsByPartition(ScreenTypeEnum screenTypeEnum) {
        List<String> softWareIds = new ArrayList<>();
        //1、获取E1数据
        List<String> e1LogsByPartitionList = service.getE1LogBySoftWareByPartition(screenTypeEnum);
        e1LogsByPartitionList.forEach(vpnSoftwareCode -> {
            // vpn_software_codes ["code1", "code2"]
            if (StringUtils.isNotBlank(vpnSoftwareCode)) {
                JSONArray array = JSONUtil.parseArray(vpnSoftwareCode);
                array.forEach(softWare -> {
                    softWareIds.add((String) softWare);
                });

            }
        });

        return softWareIds;
    }

    @Override
    public TypeCountDTO getDateSoftWareSum() {
        TypeCountDTO typeCountDTO = new TypeCountDTO();
        //查询数据库
        long num = getSoftWareSumNum();
        typeCountDTO.setNum(num);
        return typeCountDTO;
    }

    /**
     * 获取软件总量
     * @return
     */
    private long getSoftWareSumNum() {
        LambdaQueryWrapper<GenericSysDataDict> lambdaQueryWrapper = new LambdaQueryWrapper();
        lambdaQueryWrapper.eq(GenericSysDataDict::getTypeKey, AntiVpnDataDictTypeEnum.SOFTWARE_INFORMATION.name()).eq(GenericSysDataDict::getDeleted,0);
        return genericSysDataDictService.count(lambdaQueryWrapper);
    }

    @Override
    public TypeCountDTO last7Days() {
        TypeCountDTO typeCountDTO = new TypeCountDTO();
        //1.获取软件总量
        Set<String> currentIds = getSoftWareIdsByPartitionDistinct(ScreenTypeEnum.THISWEEK);
        //近7天数量
        typeCountDTO.setNum((long) currentIds.size());
        //环比
        Set<String> Twoweek = getSoftWareIdsByPartitionDistinct(ScreenTypeEnum.THISWEEK_BEFORE);
//        环比的计算公式=（本期数-上期数）/上期数×100%
        double searchRate = CalculateUtil.myEcyclePercent(Long.valueOf(currentIds.size()), Long.valueOf(currentIds.size()));
        typeCountDTO.setPercent(searchRate);
        return typeCountDTO;
    }

    @Override
    public List<TypeCountDTO> getLogNumByUserTop10(ScreenTypeEnum screenTypeEnum) {
        List<TypeCountDTO> result = new ArrayList<>(10);
        //1.查询E1 软件用户TOPO10
        List<E1CommLogUserView> e1SoftWare = service.getLogNumByUserTop10(screenTypeEnum);
        if (CollectionUtil.isEmpty(e1SoftWare)) {
            return result;
        }
        Map<String, GenericSysDataDict> sysDataDictMap = getSoftWareMap(e1SoftWare);
        //返回数据
        for (E1CommLogUserView software : e1SoftWare) {
            TypeCountDTO dto = new TypeCountDTO();
            GenericSysDataDict sysDataDict = sysDataDictMap.get(software.getVpnSoftwareCode());
            dto.setKey(ObjectUtil.isNotEmpty(sysDataDict) ? sysDataDict.getEnumVal() : "");
            dto.setNum(software.getNum());
            result.add(dto);
        }
        return result;
    }


    @Override
    public List<TypeCountDTO> getLogNumBySofaWareTop10(ScreenTypeEnum screenTypeEnum) {
        List<TypeCountDTO> result = new ArrayList<>(10);
        //1.查询E1 软件用户TOPO10
        List<E1CommLogUserView> e1SoftWare = service.getLogNumBySofaWareTop10(screenTypeEnum);
        if (CollectionUtil.isEmpty(e1SoftWare)) {
            return result;
        }
        Map<String, GenericSysDataDict> sysDataDictMap = getSoftWareMap(e1SoftWare);
        for (E1CommLogUserView software : e1SoftWare) {
            TypeCountDTO dto = new TypeCountDTO();
            GenericSysDataDict sysDataDict = sysDataDictMap.get(software.getVpnSoftwareCode());
            dto.setKey(ObjectUtil.isNotEmpty(sysDataDict) ? sysDataDict.getEnumVal() : "");
            dto.setNum(software.getNum());
            result.add(dto);
        }
        return result;
    }

    /**
     * 根据E1 视图数据获取软件map
     *
     * @param e1SoftWare
     * @return
     */
    private Map<String, GenericSysDataDict> getSoftWareMap(List<E1CommLogUserView> e1SoftWare) {
        List<String> softwareList = e1SoftWare.stream().filter(o -> StringUtils.isNotBlank(o.getVpnSoftwareCode())).map(E1CommLogUserView::getVpnSoftwareCode)
            .distinct().collect(Collectors.toList());
        //mock 软件ID 集合
        //2.查询软件库list回填软件名称
        LambdaQueryWrapper<GenericSysDataDict> lambdaQueryWrapper = new LambdaQueryWrapper();
        lambdaQueryWrapper.eq(GenericSysDataDict::getTypeKey, AntiVpnDataDictTypeEnum.SOFTWARE_INFORMATION.name());
        lambdaQueryWrapper.in(GenericSysDataDict::getEnumKey, softwareList);
        List<GenericSysDataDict> sysDataDicts = genericSysDataDictService.list(lambdaQueryWrapper);
        //返回数据
        return CollectionUtils.isEmpty(sysDataDicts) ? new HashMap<String, GenericSysDataDict>() : sysDataDicts.stream().collect(Collectors.toMap(GenericSysDataDict::getEnumKey, a -> a, (k1, k2) -> k1));
    }

    @Override
    public List<DateHistogramDTO> getLogNumBySofaWareAndDayTop10(ScreenTypeEnum screenTypeEnum) {
        List<DateHistogramDTO> result = new ArrayList<>(10);
        //1.查询E1 软件用户TOPO10
        List<E1CommLogUserView> e1SoftWare = service.getLogNumBySofaWareAndDayTop10(screenTypeEnum);
        if (CollectionUtil.isEmpty(e1SoftWare)) {
            return result;
        }
        Map<String, GenericSysDataDict> sysDataDictMap = getSoftWareMap(e1SoftWare);
        //返回数据
        for (E1CommLogUserView software : e1SoftWare) {
            DateHistogramDTO dto = new DateHistogramDTO();
            GenericSysDataDict sysDataDict = sysDataDictMap.get(software.getVpnSoftwareCode());
            //软件简介
            if(ObjectUtil.isNotEmpty(sysDataDict)){
                String enumValExtend = sysDataDict.getEnumValExtend();
                JSONObject jSONObject = new JSONObject(enumValExtend);
                dto.setKey2(ObjectUtil.isNotEmpty(jSONObject) ? jSONObject.getStr("introduction") : "");
            }
//            "key" :”clash3”, 软件名称
            dto.setKey(ObjectUtil.isNotEmpty(sysDataDict) ? sysDataDict.getEnumVal() : "");
            //数量
            //'e1Num": 10，  x轴用户量 num
            //'e2Num": 10    y轴日志量 log_count
            dto.setE1Num(software.getNum());
            dto.setE2Num(software.getLogCount());
            result.add(dto);
        }

        return result;
    }

    @Override
    public List<DateHistogramDTO> getUserNumDevelop(ScreenTypeEnum screenTypeEnum) {
        List<DateHistogramDTO> result = new ArrayList<>(10);
        //1.查询E1 软件用户TOPO10
        List<E1CommLogUserView> e1SoftWare = service.getLogNumByUserTop10(screenTypeEnum);
        if (CollectionUtil.isEmpty(e1SoftWare)) {
            return result;
        }
        Map<String, GenericSysDataDict> sysDataDictMap = getSoftWareMap(e1SoftWare);
        //返回数据
        for (E1CommLogUserView software : e1SoftWare) {
            DateHistogramDTO dto = new DateHistogramDTO();
            GenericSysDataDict sysDataDict = sysDataDictMap.get(software.getVpnSoftwareCode());
            if(ObjectUtil.isNotEmpty(sysDataDict)){
                //软件名称
                dto.setKey(ObjectUtil.isNotEmpty(sysDataDict) ? sysDataDict.getEnumVal() : "");
                //开发者
                String enumValExtend = sysDataDict.getEnumValExtend();
                JSONObject jSONObject = new JSONObject(enumValExtend);
                dto.setKey(ObjectUtil.isNotEmpty(sysDataDict) ? sysDataDict.getEnumVal() : "");
                dto.setKey2(ObjectUtil.isNotEmpty(jSONObject) ? jSONObject.getStr("issuer") : "");
                dto.setE1Num(software.getNum());
                result.add(dto);
            }
        }
        return result;
    }

    @Override
    public List<TypeCountDTO> getsoftWareProtocolTopo10(ScreenTypeEnum screenTypeEnum) {
        List<TypeCountDTO> result = new ArrayList<>(10);
        QueryWrapper<GenericSysDataDict> queryWrapper = new QueryWrapper();
//        queryWrapper.select("json_extract( enum_val_extend, '$.supportProtocol' ) AS enumVal, count(*)  num");
        queryWrapper.select("REPLACE(json_extract( enum_val_extend, '$.supportProtocol' ) , '\"', '') AS enumVal, count(*)  num");
        if (getDictJSonQueryWrapper(result, queryWrapper,true)) {
            //获取软件总量，计算百分比
            long softWareNum = getSoftWareSumNum();
            //排序
            List<TypeCountDTO> result2 =  result.stream().sorted(Comparator.comparing(TypeCountDTO::getNum).reversed()).collect(Collectors.toList());
            result2.forEach(sofeWare->{
                Long num = sofeWare.getNum();
                double searchRate = CalculateUtil.myPercent1(num, Long.valueOf(softWareNum));
                sofeWare.setPercent(searchRate);
            });
            return result2;
        }
        return result;
    }

    @Override
    public List<TypeCountDTO> getSoftwareLicence(ScreenTypeEnum screenTypeEnum) {
        List<TypeCountDTO> result = new ArrayList<>();
        QueryWrapper<GenericSysDataDict> queryWrapper = new QueryWrapper();
        queryWrapper.select("REPLACE(json_extract( enum_val_extend, '$.authType' ) , '\"', '') AS enumVal, count(*)  num");

        if (getDictJSonQueryWrapper(result, queryWrapper,false)) {
            return result;
        }
        return result;
    }

    @Override
    public List<TypeCountDTO> getsoftWareSystemTopo10(ScreenTypeEnum screenTypeEnum) {
        List<TypeCountDTO> result = new ArrayList<>(10);
        QueryWrapper<GenericSysDataDict> queryWrapper = new QueryWrapper();
        queryWrapper.select("REPLACE(json_extract( enum_val_extend, '$.supportSystem' ) , '\"', '') AS enumVal, count(*)  num");

        if (getDictJSonQueryWrapper(result, queryWrapper,true)) {
            //获取软件总量，计算百分比
            //排序
            List<TypeCountDTO> result2 = result.stream().sorted(Comparator.comparing(TypeCountDTO::getNum).reversed()).collect(Collectors.toList());
            long softWareNum = getSoftWareSumNum();
            result2.forEach(sofeWare->{
                Long num = sofeWare.getNum();
                double searchRate = CalculateUtil.myPercent1(num, Long.valueOf(softWareNum));
                sofeWare.setPercent(searchRate);
            });
            return result2;
        }
        return result;
    }

    /**
     * 获取知识库-软件库json字段聚合
     * @param result
     * @param queryWrapper
     * @return
     */
    private boolean getDictJSonQueryWrapper(List<TypeCountDTO> result, QueryWrapper<GenericSysDataDict> queryWrapper,Boolean limitStatus) {
        boolean flag = false;
        Map<String,Object> resultMap = new HashMap<>();
        queryWrapper.eq("type_key",AntiVpnDataDictTypeEnum.SOFTWARE_INFORMATION.name());
        queryWrapper.eq("deleted",0);
        queryWrapper.groupBy(CKColumn.ENUMVAL);
        queryWrapper.orderByDesc("num");
        if(limitStatus){
            queryWrapper.last("limit 10");
        }
        List<GenericSysDataDict> sysDataDicts = genericSysDataDictService.list(queryWrapper);

        if (CollectionUtil.isEmpty(sysDataDicts)) {
            return flag;
        }
        sysDataDicts.forEach(dict->{
            String enumVal = dict.getEnumVal();
            if( enumVal.contains("、")){
                String[] arrKey = enumVal.split("、");
                for (int i =0; i<arrKey.length; i++){
                    String key = arrKey[i];

                    if(ObjectUtil.isNotEmpty(resultMap.get(key))){
                        TypeCountDTO typeCountDTO = (TypeCountDTO) resultMap.get(key);
                        long sum = typeCountDTO.getNum() + dict.getNum();
                        typeCountDTO.setNum(sum);
                        resultMap.put(key,typeCountDTO);
                        continue;
                    }
                    TypeCountDTO dto = new TypeCountDTO();
                    dto.setKey(key);
                    dto.setNum(dict.getNum());
                    resultMap.put(key,dto);

                }

            }else if( enumVal.contains(",")){
                String[] arrKey = enumVal.split(",");
                for (int i =0; i<arrKey.length; i++){
                    String key = arrKey[i];

                    if(ObjectUtil.isNotEmpty(resultMap.get(key))){
                        TypeCountDTO typeCountDTO = (TypeCountDTO) resultMap.get(key);
                        long sum = typeCountDTO.getNum() + dict.getNum();
                        typeCountDTO.setNum(sum);
                        resultMap.put(key,typeCountDTO);
                        continue;
                    }
                    TypeCountDTO dto = new TypeCountDTO();
                    dto.setKey(key);
                    dto.setNum(dict.getNum());
                    resultMap.put(key,dto);
                }

            } else{

                if(ObjectUtil.isNotEmpty(resultMap.get(dict.getEnumVal()))){
                    TypeCountDTO typeCountDTO = (TypeCountDTO) resultMap.get(dict.getEnumVal());
                    typeCountDTO.setNum(typeCountDTO.getNum()+dict.getNum());
                    resultMap.put(dict.getEnumVal(),typeCountDTO);
                }else{
                    TypeCountDTO dto = new TypeCountDTO();
                    dto.setKey(dict.getEnumVal());
                    dto.setNum(dict.getNum());
                    resultMap.put(dict.getEnumVal(),dto);
                }

            }

        });
        Iterator<Object> valueIterator = resultMap.values().iterator();
        while (valueIterator.hasNext()) {
            TypeCountDTO value = (TypeCountDTO) valueIterator.next();
             result.add(value);
            flag = true;
        }
        return flag;
    }
}
