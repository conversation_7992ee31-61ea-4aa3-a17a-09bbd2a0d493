package com.eversec.antivpn.log.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.func.Func1;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.eversec.antivpn.emnus.ScreenTypeEnum;
import com.eversec.antivpn.log.api.dto.TypeCountDTO;
import com.eversec.antivpn.log.api.dto.base.AggCountDTO;
import com.eversec.antivpn.log.api.dto.base.DateHistogramDTO;
import com.eversec.antivpn.log.constants.CKColumn;
import com.eversec.antivpn.log.constants.CommonField;
import com.eversec.antivpn.log.entity.E2MachineLearningLog;
import com.eversec.antivpn.log.mapper.E2MachineLearningLogMapper;
import com.eversec.antivpn.log.service.IE2MachineLearningLogService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eversec.antivpn.log.util.CKPartitionUtil;
import java.math.BigDecimal;
import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.apache.commons.lang.ObjectUtils;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-08
 */
@Service
@Slf4j
public class E2MachineLearningLogServiceImpl extends ServiceImpl<E2MachineLearningLogMapper, E2MachineLearningLog> implements IE2MachineLearningLogService {

    @Resource
    private E2MachineLearningLogMapper e2MachineLearningLogMapper;

    @Override
    public AggCountDTO countNum(ScreenTypeEnum screenTypeEnum) {
        AggCountDTO aggCountDTO = new AggCountDTO();
        Long todayTimeCKPartition =  ScreenTypeEnum.getTimeCKPartition(ScreenTypeEnum.TODAY);
        LambdaQueryWrapper<E2MachineLearningLog> lambdaQueryWrapper = new LambdaQueryWrapper();
        lambdaQueryWrapper.ge(E2MachineLearningLog::getDay,todayTimeCKPartition);
        Long todayCount = e2MachineLearningLogMapper.selectCount(lambdaQueryWrapper);
        aggCountDTO.setTodayCount(todayCount);
        if(ScreenTypeEnum.TODAY == screenTypeEnum){
            aggCountDTO.setDateRangeCount(todayCount);
        }else{
            lambdaQueryWrapper.clear();
            Long startTimeCKPartition = ScreenTypeEnum.getTimeCKPartition(screenTypeEnum);
            lambdaQueryWrapper.ge(startTimeCKPartition!=null,E2MachineLearningLog::getDay,startTimeCKPartition)
                    .le(E2MachineLearningLog::getDay,todayTimeCKPartition);
            Long dateRangeCount = e2MachineLearningLogMapper.selectCount(lambdaQueryWrapper);
            aggCountDTO.setDateRangeCount(dateRangeCount);
        }
        return aggCountDTO;
    }


    public AggCountDTO countNumByIPAndProvinceId(ScreenTypeEnum screenTypeEnum, String ip) {
        //今天
        AggCountDTO aggCountDTO = new AggCountDTO();
        Long todayTimeCKPartition =  ScreenTypeEnum.getTimeCKPartition(ScreenTypeEnum.TODAY);
        QueryWrapper<E2MachineLearningLog> lambdaQueryWrapper = new QueryWrapper();
        lambdaQueryWrapper.select(" model_name as modelName,sum(log_count)  as num");
        lambdaQueryWrapper.eq(CKColumn.DAY,todayTimeCKPartition);
        lambdaQueryWrapper.eq(CKColumn.TRAFFIC_TYPE,CommonField.TRAFFIC_TYPE);
        if(CommonField.SRC_IP.equals(ip)){
//            lambdaQueryWrapper.groupBy(E2MachineLearningLog::getSrcProvinceId,E2MachineLearningLog::getSrcProvinceId);
//            lambdaQueryWrapper.groupBy(E2MachineLearningLog::getSrcProvinceId);
            lambdaQueryWrapper.groupBy(CKColumn.SRC_PROVINCE_ID);
        }
        if(CommonField.DEST_IP.equals(ip)){
//            lambdaQueryWrapper.groupBy(E2MachineLearningLog::getDstCountry);
            lambdaQueryWrapper.groupBy(CKColumn.DEST_COUNTRY);
        }
        Long todayCount = e2MachineLearningLogMapper.selectCount(lambdaQueryWrapper);
        aggCountDTO.setTodayCount(todayCount);
        //获取总量
        LambdaQueryWrapper<E2MachineLearningLog> lambdaQuery = new LambdaQueryWrapper();
        Long startTimeCKPartition = ScreenTypeEnum.getTimeCKPartition(ScreenTypeEnum.SOFAR);
        lambdaQuery.ge(E2MachineLearningLog::getDay,startTimeCKPartition)
            .le(E2MachineLearningLog::getDay,todayTimeCKPartition);
        if(CommonField.SRC_IP.equals(ip)){
            lambdaQuery.groupBy(E2MachineLearningLog::getSrcProvinceId);
        }
        if(CommonField.DEST_IP.equals(ip)){
            lambdaQuery.groupBy(E2MachineLearningLog::getDstCountry);
        }
        Long dateRangeCount = e2MachineLearningLogMapper.selectCount(lambdaQuery);
        aggCountDTO.setDateRangeCount(dateRangeCount);
        return aggCountDTO;
    }

    @Override
    public Long countSum(ScreenTypeEnum screenTypeEnum) {
        //获取总量
        QueryWrapper<E2MachineLearningLog> lambdaQueryWrapper = new QueryWrapper();
        setScreenTimeByEnums(screenTypeEnum,lambdaQueryWrapper);
        Long dateRangeCount = e2MachineLearningLogMapper.selectCount(lambdaQueryWrapper);
        return dateRangeCount;
    }

    @Override
    public List<TypeCountDTO> getDatedDiscernTopo10(ScreenTypeEnum screenTypeEnum) {
        List<TypeCountDTO> result = new ArrayList<>(10);
        QueryWrapper<E2MachineLearningLog> lambdaQueryWrapper = new QueryWrapper<>();
        lambdaQueryWrapper.select(" model_name as modelName, model_code as modelCode, sum(log_count)  as num");

        setScreenTimeByEnums(screenTypeEnum, lambdaQueryWrapper);
        lambdaQueryWrapper.groupBy(CKColumn.MODEL_CODE);
        lambdaQueryWrapper.last("limit 10");

        List<E2MachineLearningLog> e2MachineLearningLogs = e2MachineLearningLogMapper.selectList(lambdaQueryWrapper);

        if(CollectionUtil.isEmpty(e2MachineLearningLogs)){
            return  result;
        }
        //获取E2日志总量
        Long countSum = countSum(screenTypeEnum);
        e2MachineLearningLogs.stream().forEach(e2->{
            TypeCountDTO dto = new TypeCountDTO();
            dto.setKey(e2.getModelName());
            dto.setNum(e2.getNum());
            dto.setPercent(myPercent(e2.getNum(),countSum));
        });
        return  result;
    }

    @Override
    public List<TypeCountDTO> getLogListByProtocolType(ScreenTypeEnum screenTypeEnum) {
        List<TypeCountDTO> result = new ArrayList<>();
        QueryWrapper<E2MachineLearningLog> lambdaQueryWrapper = new QueryWrapper<>();
        lambdaQueryWrapper.select(" protocol_type  as protocolType,sum(log_count)  as num");

        setScreenTimeByEnums(screenTypeEnum, lambdaQueryWrapper);
        lambdaQueryWrapper.groupBy(CKColumn.PROTOCOL_TYPE);
        List<E2MachineLearningLog> e2MachineLearningLogs = e2MachineLearningLogMapper.selectList(lambdaQueryWrapper);

        if(CollectionUtil.isEmpty(e2MachineLearningLogs)){
            return  result;
        }
        //获取E2日志总量
        Long countSum = countSum(screenTypeEnum);
        e2MachineLearningLogs.stream().forEach(e2->{
            TypeCountDTO dto = new TypeCountDTO();
            dto.setKey(CommonField.PROTOCOL_TYPE_TCP.equals(e2.getProtocolType()) ? CommonField.PROTOCOL_TYPE_TCP_STR : CommonField.PROTOCOL_TYPE_UDP_STR);
            dto.setNum(e2.getNum());
            dto.setPercent(myPercent(e2.getNum(),countSum));
        });
        return result;
    }

    @Override
    public List<TypeCountDTO> getAILogByYearORmonthORDayORWeek(ScreenTypeEnum screenTypeEnum) {
        List<TypeCountDTO> result = new ArrayList<>();
        QueryWrapper<E2MachineLearningLog> lambdaQueryWrapper = new QueryWrapper<>();

        setScreenTimeGroupByEnums(screenTypeEnum, lambdaQueryWrapper);

        List<E2MachineLearningLog> e2MachineLearningLogs = e2MachineLearningLogMapper.selectList(lambdaQueryWrapper);

        if(CollectionUtil.isEmpty(e2MachineLearningLogs)){
            return  result;
        }
        e2MachineLearningLogs.stream().forEach(e2->{
            TypeCountDTO dto = new TypeCountDTO();
            dto.setKey(String.valueOf(e2.getTimes()));
            dto.setNum(e2.getNum());
            result.add(dto);
        });
        return  result;
    }

    @Override
    public List<DateHistogramDTO> getLogNumByRateList(ScreenTypeEnum screenTypeEnum) {
        //1.获取E2查询条件
        List<DateHistogramDTO> result = new ArrayList<>(10);
        QueryWrapper<E2MachineLearningLog> lambdaQueryWrapper = new QueryWrapper<>();
        lambdaQueryWrapper.select(" model_name as modelName,sum(log_count)  as num ,day");

        setScreenTimeByEnums(screenTypeEnum, lambdaQueryWrapper);
        lambdaQueryWrapper.last("limit 10000");

        List<E2MachineLearningLog> e2MachineLearningLogs = e2MachineLearningLogMapper.selectList(lambdaQueryWrapper);
        if(CollectionUtil.isEmpty(e2MachineLearningLogs)){
            return  result;
        }
        e2MachineLearningLogs.stream().forEach(e2->{
            DateHistogramDTO dto = new DateHistogramDTO();
            dto.setKey(e2.getModelName());
            dto.setE1Num(e2.getDay());
            dto.setE2Num(e2.getNum());
        });
        return  result;
    }

    @Override
    public List<DateHistogramDTO> getLogNumByRateDistribute(ScreenTypeEnum screenTypeEnum) {
        //1.获取E2查询条件
        List<DateHistogramDTO> result = new ArrayList<>();
        QueryWrapper<E2MachineLearningLog> lambdaQueryWrapper = new QueryWrapper<>();
        lambdaQueryWrapper.select(" rate,sum(log_count)  as num ");

        setScreenTimeByEnums(screenTypeEnum, lambdaQueryWrapper);
        lambdaQueryWrapper.groupBy(CKColumn.RATE);
//        lambdaQueryWrapper.last("limit 10000");

        List<E2MachineLearningLog> e2MachineLearningLogs = e2MachineLearningLogMapper.selectList(lambdaQueryWrapper);
        if(CollectionUtil.isEmpty(e2MachineLearningLogs)){
            return  result;
        }
        e2MachineLearningLogs.stream().forEach(e2->{
            DateHistogramDTO dto = new DateHistogramDTO();
            dto.setKey(String.valueOf(e2.getRate()));
            dto.setE1Num(e2.getNum());
        });
        return  result;
    }

    @Override
    public List<TypeCountDTO> getLogNumByProvinceId(ScreenTypeEnum screenTypeEnum) {
        //1.获取E2查询条件
        List<TypeCountDTO> result = new ArrayList<>(10);
        QueryWrapper<E2MachineLearningLog> lambdaQueryWrapper = new QueryWrapper<>();
        lambdaQueryWrapper.select(" province_id as provinceId ,sum(log_count)  as num ");

        setScreenTimeByEnums(screenTypeEnum, lambdaQueryWrapper);
        lambdaQueryWrapper.groupBy(CKColumn.PROVINCE_ID);
        lambdaQueryWrapper.apply(" order by num desc");
        lambdaQueryWrapper.last("limit 10");

        List<E2MachineLearningLog> e2MachineLearningLogs = e2MachineLearningLogMapper.selectList(lambdaQueryWrapper);
        if(CollectionUtil.isEmpty(e2MachineLearningLogs)){
            return  result;
        }
        e2MachineLearningLogs.stream().forEach(e2->{
            DateHistogramDTO dto = new DateHistogramDTO();
            dto.setKey(e2.getLogProvinceId() != null ? e2.getLogProvinceId().toString():"");
            dto.setE1Num(e2.getNum());
        });
        return  result;
    }

    @Override
    public List<TypeCountDTO> getChinaMapByProvinceId(ScreenTypeEnum screenTypeEnum) {
        List<TypeCountDTO> result = new ArrayList<>(10);
        //1.获取E2查询条件
        QueryWrapper<E2MachineLearningLog> lambdaQueryWrapper = new QueryWrapper<>();
        lambdaQueryWrapper.select(" province_id as provinceId,sum(log_count)  as num ,day ");

        setScreenTimeByEnums(screenTypeEnum, lambdaQueryWrapper);
        lambdaQueryWrapper.groupBy(CKColumn.PROVINCE_ID);
        lambdaQueryWrapper.apply(" order by num desc");
//        lambdaQueryWrapper.last("limit 10000");

        List<E2MachineLearningLog> e2MachineLearningLogs = e2MachineLearningLogMapper.selectList(lambdaQueryWrapper);
        if(CollectionUtil.isEmpty(e2MachineLearningLogs)){
            return  result;
        }
        //获取E2日志总量
//        Long countSum = countSum(screenTypeEnum);
        e2MachineLearningLogs.stream().forEach(e2->{
            TypeCountDTO dto = new TypeCountDTO();
            dto.setKey(e2.getLogProvinceId() != null ? e2.getLogProvinceId().toString():"");
            dto.setNum(e2.getNum());//获取各省的日志总量
            long toDaySum = e2MachineLearningLogs.stream().filter(o -> o.getDay().equals(ScreenTypeEnum.getTimeCKPartition(ScreenTypeEnum.TODAY)) && o.getProvinceId() == e2.getProvinceId()).mapToLong(E2MachineLearningLog::getNum).sum();
            dto.setTodayNum(ObjectUtil.isEmpty(toDaySum)? 0L:toDaySum);//今天总量
        });
        return  result;
    }

    /**
     * 根据查询枚举设置查询条件时间
     * @param screenTypeEnum
     * @param lambdaQueryWrapper
     */
    private void setScreenTimeByEnums(ScreenTypeEnum screenTypeEnum, QueryWrapper<E2MachineLearningLog> lambdaQueryWrapper) {
        switch (screenTypeEnum) {

            case TODAY:
                lambdaQueryWrapper.eq(CKColumn.DAY,ScreenTypeEnum.getTimeCKPartition(ScreenTypeEnum.TODAY));
                break;
            case THISWEEK:
                //上周今天（往前7天）
                lambdaQueryWrapper.ge(CKColumn.DAY,ScreenTypeEnum.getTimeCKPartition(ScreenTypeEnum.THISWEEK));
                lambdaQueryWrapper.le(CKColumn.DAY,ScreenTypeEnum.getTimeCKPartition(ScreenTypeEnum.TODAY));
                break;
            case THISMONTH:
                lambdaQueryWrapper.ge(CKColumn.DAY,ScreenTypeEnum.getTimeCKPartition(ScreenTypeEnum.THISMONTH));
                lambdaQueryWrapper.le(CKColumn.DAY,ScreenTypeEnum.getTimeCKPartition(ScreenTypeEnum.TODAY));
                break;
            case SOFAR:
                lambdaQueryWrapper.ge(CKColumn.DAY,ScreenTypeEnum.getTimeCKPartition(ScreenTypeEnum.SOFAR));
                lambdaQueryWrapper.le(CKColumn.DAY,ScreenTypeEnum.getTimeCKPartition(ScreenTypeEnum.TODAY));
                break;
        }
    }

    /**
     * 根据查询枚举设置查询条件时间
     *  查询条件：今天-> 按小时、近一周-> 按天、月-> 按周、至今-> 按月
     * @param screenTypeEnum
     * @param lambdaQueryWrapper
     */
    private void setScreenTimeGroupByEnums(ScreenTypeEnum screenTypeEnum, QueryWrapper<E2MachineLearningLog> lambdaQueryWrapper) {
        switch (screenTypeEnum) {

            case TODAY:
                lambdaQueryWrapper.select(" hour as times ,sum(log_id)  as num");
                lambdaQueryWrapper.eq(CKColumn.DAY,ScreenTypeEnum.getTimeCKPartition(ScreenTypeEnum.TODAY));
                lambdaQueryWrapper.groupBy(CKColumn.HOUR);
                break;
            case THISWEEK:
                //上周今天（往前7天）
                lambdaQueryWrapper.select(" day as times,sum(log_id)  as num");
                lambdaQueryWrapper.ge(CKColumn.DAY,ScreenTypeEnum.getTimeCKPartition(ScreenTypeEnum.THISWEEK));
                lambdaQueryWrapper.le(CKColumn.DAY,ScreenTypeEnum.getTimeCKPartition(ScreenTypeEnum.TODAY));
                lambdaQueryWrapper.groupBy(CKColumn.DAY);
                break;
            case THISMONTH:
                lambdaQueryWrapper.select(" week as times,sum(log_id)  as num");
                lambdaQueryWrapper.ge(CKColumn.DAY,ScreenTypeEnum.getTimeCKPartition(ScreenTypeEnum.THISMONTH));
                lambdaQueryWrapper.le(CKColumn.DAY,ScreenTypeEnum.getTimeCKPartition(ScreenTypeEnum.TODAY));
                lambdaQueryWrapper.groupBy(CKColumn.WEEK);
                break;
            case SOFAR:
                lambdaQueryWrapper.select(" month as times,sum(log_id)  as num");
                lambdaQueryWrapper.le(CKColumn.DAY,ScreenTypeEnum.getTimeCKPartition(ScreenTypeEnum.TODAY));
                lambdaQueryWrapper.groupBy(CKColumn.MONTH);
                break;
        }
    }


    private long getTodayCount(){
        Integer today =  CKPartitionUtil.getTodayCKPartition();
        LambdaQueryWrapper<E2MachineLearningLog> lambdaQueryWrapper = new LambdaQueryWrapper();
        lambdaQueryWrapper.eq(E2MachineLearningLog::getDay,today);
        return e2MachineLearningLogMapper.selectCount(lambdaQueryWrapper);
    }

    /**
     * 计算百分比
     *
     * @param count 数量
     * @param total 总数
     * @return
     */
    private static Double myPercent(long  count, long  total) {
        if (total == 0L) {
            return Double.valueOf(0);
        }

        BigDecimal ecount = new BigDecimal(count);
        BigDecimal totalCount = new BigDecimal(total);
        BigDecimal divide = ecount.divide(totalCount,2, BigDecimal.ROUND_HALF_UP);
        double searchRate = divide.multiply(new BigDecimal(100)).doubleValue();

        return searchRate;
    }
}
