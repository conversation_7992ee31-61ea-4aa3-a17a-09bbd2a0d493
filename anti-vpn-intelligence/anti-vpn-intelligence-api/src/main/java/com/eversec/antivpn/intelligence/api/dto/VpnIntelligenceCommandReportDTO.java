package com.eversec.antivpn.intelligence.api.dto;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.StrUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 跨境通信情报库指令上报
 */
@Getter
@Setter
@Schema(name = "VpnIntelligenceCommandReportDTO", description = "跨境通信情报库指令上报")
public class VpnIntelligenceCommandReportDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    // 指令内容

    @NotEmpty
    @Schema(description = "跨境通信情报库版本格式：主版本号.副版本号，本规范主版本号为1，副版本号为0，即1.0。")
    private String version;

    @NotNull
    @Schema(description = "跨境通信情报库下发执行指令ID。当部侧下发时由部侧系统控制惟一；当企业侧系统上报时企业侧内惟一。")
    private Long commandId;

    @NotNull
    @Schema(description = "运营商编码，见附录F.2")
    private String comCode;

    @NotNull
    @Schema(description = "省级区域编号，见附录F.4")
    private Long provinceId;

    @Schema(description = "监测网络类型code，见附录F.3。企业侧上报时必填。部侧下发时可选填，当部侧下发为空时表示所有网络类型生效")
    private Integer networkBusinessId;

    // 情报库内容
    @Schema(description = "情报类型code： 1-VPN服务地址,例:提供VPN下载、注册、登录、购买、登出等各类访问地址和链接； 2-VPN 跨境服务地址,例：海外域名通道或海外IP通道地址； 3-模型研判； 4-样本库； 999-其他。 当操作类型为0、1、2时必填，当操作类型为3、4、5时选填。 ")
    private Integer typeId;

    @Schema(description = "情报库ID，当部侧系统下发时必填；企业侧系统上报、全量库下发时可不填写")
    private Long vpnId;

    @Schema(description = "Vpn名称或文件名称.当操作类型为0、1、2时填写VPN名称，当操作类型为3时填写的是全量跨境通信情报库文件名；当操作类型为4、5时填写的是模型或样本文件名，多个用|线分隔； 当填写Vpn名称时，优先使用中文名称，没有中文名称时使用英文名称")
    private String vpnName;

    @Schema(description = "域名。VPN活动所使用的域名地址。")
    private String vpnDomain;

    @Schema(description = "IP地址,VPN活动所使用的IP地址；IPv4使用点分表示法，IPv6使用冒分小写0位压缩表示法。")
    private String vpnIp;

    @Schema(description = "URL地址,VPN活动所使用的URL地址，采用base64编码后的URL。")
    private String vpnUrl;

    @Schema(description = "取证链接,包含VMess、VLESS、Shadowsocks、Socks、Trojan等链接地址。企业侧系统上报时根据实际情况填写。")
    private String vpnLink;
    /// 情报库内容

    @Schema(description = "是否上报取证文件：\n" + "0：上报（企业默认上报）；\n" + "1：不上报。\n" + "企业侧系统上报时要求上报")
    private Integer isUploadFile;

    @Schema(description = "取证文件名称,当isUploadFile为0时必填。上报方法参见7.2.2章节。")
    private String attachMent;

    @Schema(description = "操作类型,对该记录的操作类型，包括： 0-增量情报下发； 1-上报； 2-删除； 3-全量情报下发； 4-模型下发； 5-样本下发。")
    private Integer operationType;

    @Schema(description = "创建时间,生成该文件的时间")
    private String timeStamp;


    /**
     * AntiVpnReportCodeEnum.VPN_INTELLIGENCE.getHeader()
     *
     * 1	version	版本号	必填	字符串	8	跨境通信情报库版本格式：主版本号.副版本号，本规范主版本号为1，副版本号为0，即1.0。
     * 2	commandId	执行指令ID	必填	长整型	8	上报执行指令ID，当企业侧系统上报时企业侧内惟一。
     * 3	comCode	运营商编码	必填	字符串	18	运营商编码，见附录F.2节。
     * 4	provinceId	省级区域编号	必填	长整型	2	省级区域编号，见附录F.4节。
     * 5	networkBusinessId	网络类型	必填	整型	/	监测网络类型，见附录F.3节。
     * 6	typeId	情报类型	必填	整型	/	情报类型：
     * 1-VPN服务地址,例:提供VPN下载、注册、登录、购买、登出等各类访问地址和链接；
     * 2-VPN 跨境服务地址,例：海外域名通道或海外IP通道地址；
     * 3-模型研判；
     * 4-样本库；
     * 999-其他。
     * 7	vpnId	情报库ID	选填	长整型	/	情报库ID，企业侧系统上报时可不填写。
     * 8	vpnName	Vpn名称或文件名称	必填	字符串	256	当操作类型为1、2时填写VPN名称，多个用|线分隔；
     * 当填写Vpn名称时，优先使用中文名称，没有中文名称时使用英文名称。
     * 9	vpnDomain	域名	选填	字符串	128	VPN活动所使用的域名地址。
     * 10	vpnIp	IP地址	选填	字符串	512	VPN活动所使用的IP地址及端口,多个端口间用#分隔，如果端口为空则表示监测所有端口,具体填报形式如下: **********；
     * **********:3334；
     * **********:3334#3335#3338。
     * 11	vpnUrl	URL地址	选填	字符串	1024	VPN活动所使用的URL地址，采用base64编码后的URL。
     * 12	vpnLink	取证链接	选填	字符串	1024	取证链接，包含VMess、VLESS、Shadowsocks、Socks、Trojan等链接地址。企业侧系统上报时根据实际情况填写。
     * 13	isUploadFile	是否上报取证文件	必填	整型	/	是否上报取证文件：
     * 0：上报（企业默认上报）；
     * 1：不上报。
     * 企业侧系统上报时要求上报。
     * 14	attachMent	取证文件名称	选填	字符串	256	当isUploadFile为0时必填。上报方法参见7.2.2章节。
     * 15	operationType	操作类型	必填	整型	/	对该记录的操作类型，包括：
     * 1-上报；
     * 2-删除；
     * 16	timeStamp	情报生成时间	必填	字符串	19	情报生成时间，采用yyyy-MM-dd HH:mm:ss格式。
     * 备注:当操作类型为1、2时，每项情报“vpnDomain”、“vpnIp”、“vpnUrl”三者必须有一项不为空。
     *
     * @return
     */
    public String toCsvLine() {
        List<String> fields = new ArrayList<>();
        fields.add(version);
        fields.add(nullToEmpty(commandId));
        fields.add(comCode);
        fields.add(nullToEmpty(provinceId));
        fields.add(nullToEmpty(networkBusinessId));
        fields.add(nullToEmpty(typeId));
        fields.add(nullToEmpty(vpnId));
        fields.add(nullToEmpty(vpnName));
        fields.add(nullToEmpty(vpnDomain));
        fields.add(nullToEmpty(vpnIp));
        if (vpnUrl == null) {
            fields.add("");
        } else {
            fields.add(Base64.encode(vpnUrl));
        }
        fields.add(nullToEmpty(vpnLink));
        fields.add(nullToEmpty(isUploadFile));
        fields.add(nullToEmpty(attachMent));
        fields.add(nullToEmpty(operationType));
        fields.add(nullToEmpty(timeStamp));
        return StrUtil.join(",", fields);
    }

    private String nullToEmpty(Object value) {
        if (value == null) {
            return "";
        } else {
            return String.valueOf(value);
        }
    }

}
