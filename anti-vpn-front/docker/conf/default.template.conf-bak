server {
    listen       80;
    listen       8080;
    listen       443 ssl;

    ssl_certificate /app/https/ssl_cert.pem;  # 指定证书的位置，绝对路径
    ssl_certificate_key /app/https/ssl_private.key;
    ssl_session_timeout 5m;
    ssl_protocols TLSv1 TLSv1.1 TLSv1.2; #按照这个协议配置
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:HIGH:!aNULL:!MD5:!RC4:!DHE;#按照这个套件配置
    ssl_prefer_server_ciphers on;

    # 扩展配置
    ${DEFAULT_CONF_SERVER_EXPAND}

    # error page
    error_page 400 401 403 405 413  500 502 503 504  /50x.html;
    location = /50x.html {
        root   /usr/local/openresty/nginx/html;
    }
    error_page  404              /404.html;
    location = /404.html {
        root   /usr/local/openresty/nginx/html;
    }


    # 静态页
    location / {
        proxy_pass  http://127.0.0.1:${NODE_PORT}/;
        index  index.html index.htm;
        add_header  Cache-Control max-age=86400;
        proxy_set_header    X-Forwarded-Proto $scheme;

        # 扩展配置
        ${DEFAULT_CONF_LOCATION_EXPAND}
        # 静态页扩展配置
        ${DEFAULT_CONF_LOCATION_STATIC_EXPAND}

    }

    # 首页不缓存
    location = /index.html {
        proxy_pass  http://127.0.0.1:${NODE_PORT}/;
        add_header Cache-Control "no-cache, no-store";

        # 扩展配置
        ${DEFAULT_CONF_LOCATION_EXPAND}
        # 静态页扩展配置
        ${DEFAULT_CONF_LOCATION_STATIC_EXPAND}
    }

    # gateway
    location ^~ /service/ {
        proxy_pass  http://${GATEWAY_ADDRESS}/;

        proxy_buffering         ${DEFAULT_CONF_LOCATION_SERVICE_PROXY_BUFFERING};
        proxy_redirect          ${DEFAULT_CONF_LOCATION_SERVICE_PROXY_REDIRECT};
        client_max_body_size    ${DEFAULT_CONF_LOCATION_SERVICE_CLIENT_MAX_BODY_SIZE};

        proxy_set_header    Host $host;
        proxy_set_header    X-Real-IP $remote_addr;
        proxy_set_header    X-Forwarded-Host $remote_addr;
        proxy_set_header    X-Forwarded-Server $remote_addr;
        proxy_set_header    X-Forwarded-For  $proxy_add_x_forwarded_for;

        # 扩展配置
        ${DEFAULT_CONF_LOCATION_EXPAND}
        # 网关扩展配置
        ${DEFAULT_CONF_LOCATION_SERVICE_EXPAND}

    }

    # 代理evercm日志前端，菜单配置方式： http://${ip}:${port}/evercm-front/#/pe_overview/fullFlow/customRetrieval/e1commlog
    location ^~ /evercm-front/ {
        proxy_pass  http://${EVERCM_FRONT_ADDRESS}/;

        # 扩展配置
        ${DEFAULT_CONF_LOCATION_EXPAND}
        # 静态页扩展配置
        ${DEFAULT_CONF_LOCATION_STATIC_EXPAND}

    }
}

