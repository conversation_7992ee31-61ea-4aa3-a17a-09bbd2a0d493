package com.eversec.antivpn.intelligence.controller;

import com.eversec.antivpn.intelligence.api.VpnIntelligenceApi;
import com.eversec.antivpn.intelligence.api.dto.*;
import com.eversec.antivpn.intelligence.service.ISmsCodeService;
import com.eversec.antivpn.intelligence.service.ISmsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * VPN情报短信控制器
 *
 * <AUTHOR>
 * @since 2025-01-12
 */
@RestController
@RequestMapping(VpnIntelligenceApi.PATH)
@AllArgsConstructor
@Slf4j
@Tag(name = "VPN情报短信", description = "VPN情报短信发送相关接口")
public class VpnIntelligenceSmsController {

    private final ISmsService smsService;
    private final ISmsCodeService smsCodeService;

    @Operation(summary = "发送短信", description = "通过联通云网能力开放平台发送短信")
    @PostMapping("/sendSms")
    public SmsResponseDTO sendSms(@Valid @RequestBody SmsRequestDTO smsRequest) {
        log.info("收到短信发送请求，接收号码：{}，内容长度：{}", smsRequest.getSerialNumber(), smsRequest.getMessage().length());
        return smsService.sendSms(smsRequest);
    }

    @Operation(summary = "发送验证码", description = "发送短信验证码，支持失效时间和频率控制")
    @PostMapping("/sendCode")
    public SmsCodeResponseDTO sendCode(@Valid @RequestBody SmsCodeRequestDTO request) {
        log.info("收到验证码发送请求，手机号：{}，业务类型：{}", request.getPhoneNumber(), request.getBusinessType());
        return smsCodeService.sendCode(request);
    }

    @Operation(summary = "验证验证码", description = "验证短信验证码是否正确")
    @PostMapping("/verifyCode")
    public SmsCodeResponseDTO verifyCode(@Valid @RequestBody SmsCodeVerifyRequestDTO request) {
        log.info("收到验证码验证请求，手机号：{}，业务类型：{}", request.getPhoneNumber(), request.getBusinessType());
        return smsCodeService.verifyCode(request);
    }

}
