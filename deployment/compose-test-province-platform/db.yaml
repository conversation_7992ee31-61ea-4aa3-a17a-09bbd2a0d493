# mkdir -p /data/mysql

version: '3'
services:
  minio:
    image: harbor-bj.eversaas.cn/public/minio:RELEASE.2022-05-26T05-48-41Z
    ports:
      - "39000:9000"
      - "39001:9001"
    volumes:
      - /data/minio:/data
      - /etc/localtime:/etc/localtime
    environment:
      - TZ=Asia/Shanghai
      - MINIO_SERVER_URL=http://minio:9000
      - MINIO_ROOT_USER=eversec
      - MINIO_ROOT_PASSWORD=eversec123456
    networks:
      - net
    command: server /data --console-address :9001
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3
    deploy:
      restart_policy:
        condition: any
        delay: 30s
      resources:
        limits:
          cpus: '1'
          memory: 1G

  mysql:
    image: harbor-bj.eversaas.cn/public/mariadb:10.4.30-5.7-eversec-v1
    ports:
      - 13306:3306
    volumes:
      - /data/mysql:/var/lib/mysql
    environment:
      - TZ=Asia/Shanghai
      - MYSQL_ROOT_PASSWORD=r9xh*yH*DcEFQE
      - MARIADB_ROOT_PASSWORD=r9xh*yH*DcEFQE
    networks:
      - net
    healthcheck:
      test: mysql --user=root --password=$$MARIADB_ROOT_PASSWORD -e 'SHOW DATABASES;'
      interval: 30s
      timeout: 5s
      retries: 30
    deploy:
      replicas: 1
      restart_policy:
        condition: any
        delay: 30s
      resources:
        limits:
          cpus: '1'
          memory: 4G
      placement:
        constraints: [ node.role == manager ]

  redis:
    image: harbor-bj.eversaas.cn/public/redis:3.2.12-withconf
    ports:
      - 16379:6379
    networks:
      - net
    environment:
      - TZ=Asia/Shanghai
      - REQUIREPASS=Redis!QAZ2wsx
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 2s
      timeout: 5s
      retries: 10
    deploy:
      restart_policy:
        condition: any
        delay: 30s
      resources:
        limits:
          cpus: '1'
          memory: 4G
      placement:
        constraints: [ node.role == manager ]

networks:
  net:

volumes:
  logs: