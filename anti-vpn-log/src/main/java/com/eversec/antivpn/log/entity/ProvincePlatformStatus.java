package com.eversec.antivpn.log.entity;

import com.eversec.antivpn.log.entity.po.ProvincePlatformStatusPO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 省平台信息（企业测）
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-23
 */
@Getter
@Setter
@Schema(name = "ProvincePlatformStatus", description = "省平台信息（企业测）")
public class ProvincePlatformStatus extends ProvincePlatformStatusPO implements java.io.Serializable {

    private static final long serialVersionUID = 1L;

    /*
     * 扩展字段，数据库中不存在的字段，数据库中不存在的字段需加 @TableField(exist = false)
     * 如字典翻译、子对象、子对象列表等
     */
//    @com.baomidou.mybatisplus.annotation.TableField(exist = false)
//    private String xxx;

}