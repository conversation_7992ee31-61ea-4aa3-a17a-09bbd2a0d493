package com.eversec.antivpn.log.util;

import com.eversec.antivpn.log.mapper.vo.DateHistogramVO;
import com.eversec.antivpn.log.mapper.vo.TypeCountVO;
import com.eversec.antivpn.log.mapper.vo.UserDateHistogramVO;
import com.eversec.antivpn.log.mapper.vo.VpnHistogramAggVO;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/7/18 14:25
 **/

public class AggregateDataUtil {
    public static Map<Integer, Long> aggregateIntegerKeyData(List<Map<Integer, Long>> dataList) {
        Map<Integer, Long> result = new HashMap<>();
        dataList.forEach(map -> {
            for (Map.Entry<Integer, Long> entry : map.entrySet()) {
                if (!result.containsKey(entry.getKey())) {
                    result.put(entry.getKey(), entry.getValue());
                } else {
                    result.put(entry.getKey(), entry.getValue() + result.get(entry.getKey()));
                }
            }
        });
        return result;
    }

    public static List<TypeCountVO> mergeTypeCountVO(List<TypeCountVO> dataList) {
        List<TypeCountVO> result = new ArrayList<>();
        if (dataList == null || dataList.size() == 0) return result;
        Map<String, TypeCountVO> map = new HashMap<>();
        dataList.forEach(typeCountVO -> {
            if (!map.containsKey(typeCountVO.getKey())) {
                map.put(typeCountVO.getKey(), typeCountVO);
                result.add(typeCountVO);
            } else {
                TypeCountVO targetedTypeCountVO = map.get(typeCountVO.getKey());
                targetedTypeCountVO.setNum(targetedTypeCountVO.getNum() + typeCountVO.getNum());
            }
        });
        for (TypeCountVO vo : result) {
            if (vo.getNum() == null) vo.setNum(0l);
        }
        return result;
    }

    public static List<TypeCountVO> mergeSameKeyTypeCountVO(List<TypeCountVO> dataList) {
        return mergeTypeCountVO(dataList);
    }

    public static List<TypeCountVO> mergeAllAndTodayTypeCountVO(List<TypeCountVO> dataList, List<TypeCountVO> todayDataList) {
        Map<String, TypeCountVO> map = new HashMap<>();
        dataList.forEach(typeCountVO -> {
            map.put(typeCountVO.getKey(), typeCountVO);
        });
        todayDataList.forEach(typeCountVO -> {
            if (map.containsKey(typeCountVO.getKey()))
                map.get(typeCountVO.getKey()).setTodayNum(typeCountVO.getTodayNum());
        });
        return dataList;
    }

    //AI大屏特殊使用
    public static List<TypeCountVO> mergeAllAndTodayTypeCountVOV2(List<TypeCountVO> dataList, List<TypeCountVO> todayDataList) {
        Map<String, TypeCountVO> map = new HashMap<>();
        dataList.forEach(typeCountVO -> {
            map.put(typeCountVO.getKey(), typeCountVO);
        });
        todayDataList.forEach(typeCountVO -> {
            if (map.containsKey(typeCountVO.getKey()))
                map.get(typeCountVO.getKey()).setTodayNum(typeCountVO.getNum());
        });
        return dataList;
    }

    public static List<DateHistogramVO> mergeE1AndE2TypeCountVO(List<TypeCountVO> e1, List<TypeCountVO> e2) {
        List<DateHistogramVO> result = new ArrayList<>();
        int e1Index = 0;
        int e2Index = 0;
        while (e1Index < e1.size() && e2Index < e2.size()) {
            DateHistogramVO dateHistogramVO = new DateHistogramVO();
            TypeCountVO e1TypeCountVO = e1.get(e1Index);
            TypeCountVO e2TypeCountVO = e2.get(e2Index);
            if (StringUtils.isBlank(e1TypeCountVO.getKey()) || "".equals(e1TypeCountVO.getKey())) {
                e1Index++;
                continue;
            }
            if (StringUtils.isBlank(e2TypeCountVO.getKey()) || "".equals(e2TypeCountVO.getKey())) {
                e2Index++;
                continue;
            }
            //同一天合并数据
            if (e1TypeCountVO.getKey().equals(e2TypeCountVO.getKey())) {
                e1Index++;
                e2Index++;
                dateHistogramVO.setKey(e1TypeCountVO.getKey());
                dateHistogramVO.setE1Num(e1TypeCountVO.getNum());
                dateHistogramVO.setE2Num(e2TypeCountVO.getNum());
            } else if (Integer.valueOf(e1TypeCountVO.getKey()) > Integer.valueOf(e2TypeCountVO.getKey())) {
                //e1数据天数大，使用e2作为基准数据
                e2Index++;
                dateHistogramVO.setKey(e2TypeCountVO.getKey());
                dateHistogramVO.setE2Num(e2TypeCountVO.getNum());
            } else {
                e1Index++;
                dateHistogramVO.setKey(e1TypeCountVO.getKey());
                dateHistogramVO.setE1Num(e1TypeCountVO.getNum());
            }
            result.add(dateHistogramVO);
        }
        while (e1Index < e1.size()) {
            DateHistogramVO dateHistogramVO = new DateHistogramVO();
            dateHistogramVO.setKey(e1.get(e1Index).getKey());
            dateHistogramVO.setE1Num(e1.get(e1Index).getNum());
            result.add(dateHistogramVO);
            e1Index++;
        }
        while (e2Index < e2.size()) {
            DateHistogramVO dateHistogramVO = new DateHistogramVO();
            dateHistogramVO.setKey(e2.get(e2Index).getKey());
            dateHistogramVO.setE2Num(e2.get(e2Index).getNum());
            result.add(dateHistogramVO);
            e2Index++;
        }
        return result;
    }


    public static List<VpnHistogramAggVO> mergeVpnHistogramAggVO(List<VpnHistogramAggVO> e1, List<VpnHistogramAggVO> e2) {
        List<VpnHistogramAggVO> result = new ArrayList<>();
        int e1Index = 0;
        int e2Index = 0;
        while (e1Index < e1.size() && e2Index < e2.size()) {
            VpnHistogramAggVO vpnHistogramAggVO = new VpnHistogramAggVO();
            VpnHistogramAggVO e1VpnHistogramAggVO = e1.get(e1Index);
            VpnHistogramAggVO e2VpnHistogramAggVO = e2.get(e2Index);
            //同一天合并数据
            if (e1VpnHistogramAggVO.getKey().equals(e2VpnHistogramAggVO.getKey())) {
                e1Index++;
                e2Index++;
                vpnHistogramAggVO.setKey(e1VpnHistogramAggVO.getKey());
                vpnHistogramAggVO.setVpnServiceNum(e1VpnHistogramAggVO.getVpnServiceNum() + e2VpnHistogramAggVO.getVpnServiceNum());
                vpnHistogramAggVO.setResourceNodeNum(e1VpnHistogramAggVO.getResourceNodeNum() + e2VpnHistogramAggVO.getResourceNodeNum());
            } else if (Integer.parseInt(e1VpnHistogramAggVO.getKey()) > Integer.parseInt(e2VpnHistogramAggVO.getKey())) {
                //e1数据天数大，使用e2作为基准数据
                e2Index++;
                vpnHistogramAggVO.setKey(e2VpnHistogramAggVO.getKey());
                vpnHistogramAggVO.setResourceNodeNum(e2VpnHistogramAggVO.getResourceNodeNum());
            } else {
                e1Index++;
                vpnHistogramAggVO.setKey(e1VpnHistogramAggVO.getKey());
                vpnHistogramAggVO.setResourceNodeNum(e1VpnHistogramAggVO.getResourceNodeNum());
            }
            result.add(vpnHistogramAggVO);
        }
        while (e1Index < e1.size()) {
            VpnHistogramAggVO vpnHistogramAggVO = new VpnHistogramAggVO();
            vpnHistogramAggVO.setKey(e1.get(e1Index).getKey());
            vpnHistogramAggVO.setVpnServiceNum(e1.get(e1Index).getVpnServiceNum());
            vpnHistogramAggVO.setResourceNodeNum(e1.get(e1Index).getResourceNodeNum());
            result.add(vpnHistogramAggVO);
            e1Index++;
        }
        while (e2Index < e2.size()) {
            VpnHistogramAggVO vpnHistogramAggVO = new VpnHistogramAggVO();
            vpnHistogramAggVO.setKey(e2.get(e2Index).getKey());
            vpnHistogramAggVO.setVpnServiceNum(e2.get(e2Index).getVpnServiceNum());
            vpnHistogramAggVO.setResourceNodeNum(e2.get(e2Index).getResourceNodeNum());
            result.add(vpnHistogramAggVO);
            e2Index++;
        }
        return result;
    }


    public static List<Long> mergeLongList(List<Long> list1, List<Long> list2) {
        Set<Long> set = new HashSet<>();
        cleanNullData(list1);
        cleanNullData(list2);
        set.addAll(list1);
        set.addAll(list2);
        return new ArrayList<>(set);
    }

    public static List<String> mergeStringList(List<String> list1, List<String> list2) {
        Set<String> set = new HashSet<>();
        cleanNullStringData(list1);
        cleanNullStringData(list2);
        set.addAll(list1);
        set.addAll(list2);
        return new ArrayList<>(set);
    }

    private static void cleanNullStringData(List<String> list) {
        if (list == null || list.isEmpty()) return;
        Iterator<String> iterator = list.iterator();
        while (iterator.hasNext()) {
            String next = iterator.next();
            if (next == null || "".equals(next)) iterator.remove();
        }
    }

    private static void cleanNullData(List<Long> list) {
        if (list == null || list.isEmpty()) return;
        Iterator<Long> iterator = list.iterator();
        while (iterator.hasNext()) {
            Long next = iterator.next();
            if (next == null) iterator.remove();
        }
    }

    public static List<TypeCountVO> mergeTypeCountVOSameKeyString(List<TypeCountVO> list1, List<TypeCountVO> list2) {
        if (list1.size() == 0) return list2;
        if (list2.size() == 0) return list1;
        List<TypeCountVO> result = new ArrayList<>();
        Map<String, TypeCountVO> voMap = new HashMap<>();
        list1.forEach(vo -> {
            result.add(vo);
            voMap.put(vo.getKey(), vo);
        });
        list2.forEach(vo -> {
            if (voMap.containsKey(vo.getKey())) {
                TypeCountVO targetVo = voMap.get(vo.getKey());
                targetVo.setNum(targetVo.getNum() + vo.getNum());
            } else {
                voMap.put(vo.getKey(), vo);
                result.add(vo);
            }
        });
        return result;
    }

    public static List<UserDateHistogramVO> mergeTypeCountVOKeyTime(List<TypeCountVO> list1, List<TypeCountVO> list2) {
        List<UserDateHistogramVO> result = new ArrayList<>();
        int e1Index = 0;
        int e2Index = 0;
        while (e1Index < list1.size() && e2Index < list2.size()) {
            UserDateHistogramVO userDateHistogramVO = new UserDateHistogramVO();
            TypeCountVO data1 = list1.get(e1Index);
            TypeCountVO data2 = list2.get(e2Index);
            if (StringUtils.isBlank(data1.getKey()) || "".equals(data1.getKey())) {
                e1Index++;
                continue;
            }
            if (StringUtils.isBlank(data2.getKey()) || "".equals(data2.getKey())) {
                e2Index++;
                continue;
            }
            //同一天合并数据
            if (data1.getKey().equals(data2.getKey())) {
                e1Index++;
                e2Index++;
                userDateHistogramVO.setKey(data1.getKey());
                userDateHistogramVO.setNum(data1.getNum() + data2.getNum());
            } else if (Integer.parseInt(data1.getKey()) > Integer.parseInt(data2.getKey())) {
                //e1数据天数大，使用e2作为基准数据
                e2Index++;
                userDateHistogramVO.setKey(data2.getKey());
                userDateHistogramVO.setNum(data2.getNum());
            } else {
                e1Index++;
                userDateHistogramVO.setKey(data1.getKey());
                userDateHistogramVO.setNum(data1.getNum());
            }
            result.add(userDateHistogramVO);
        }
        while (e1Index < list1.size()) {
            UserDateHistogramVO userDateHistogramVO = new UserDateHistogramVO();
            userDateHistogramVO.setKey(list1.get(e1Index).getKey());
            userDateHistogramVO.setNum(list1.get(e1Index).getNum());
            result.add(userDateHistogramVO);
            e1Index++;
        }
        while (e2Index < list2.size()) {
            UserDateHistogramVO userDateHistogramVO = new UserDateHistogramVO();
            userDateHistogramVO.setKey(list2.get(e2Index).getKey());
            userDateHistogramVO.setNum(list2.get(e2Index).getNum());
            result.add(userDateHistogramVO);
            e2Index++;
        }
        return result;
    }
}
