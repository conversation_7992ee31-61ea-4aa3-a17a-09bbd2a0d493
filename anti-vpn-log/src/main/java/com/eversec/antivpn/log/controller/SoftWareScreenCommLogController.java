package com.eversec.antivpn.log.controller;

import cn.hutool.core.util.ObjectUtil;
import com.eversec.antivpn.emnus.ScreenTypeEnum;
import com.eversec.antivpn.log.aop.RedisCache;
import com.eversec.antivpn.log.aop.RedisCacheOnlyMethodKey;
import com.eversec.antivpn.log.api.SoftWareScreenCommLogApi;
import com.eversec.antivpn.log.api.dto.TypeCountDTO;
import com.eversec.antivpn.log.api.dto.base.AggCountDTO;
import com.eversec.antivpn.log.api.dto.base.DateHistogramDTO;
import com.eversec.antivpn.log.service.ScreenRedisService;
import com.eversec.antivpn.log.service.SoftWareCommLogService;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 *  软件态势大屏
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-08
 */
@RestController
@RequestMapping(SoftWareScreenCommLogApi.PATH)
@AllArgsConstructor
@Slf4j
public class SoftWareScreenCommLogController implements SoftWareScreenCommLogApi {

    private  final SoftWareCommLogService softWareCommLogService;

    private ScreenRedisService screenRedisService;

    @Override
    @RedisCacheOnlyMethodKey
    public TypeCountDTO dateSoftWareSum() {
        TypeCountDTO result = softWareCommLogService.getDateSoftWareSum();
        return result;

    }

    @Override
    public TypeCountDTO last7Days(ScreenTypeEnum screenTypeEnum) {
        TypeCountDTO result = softWareCommLogService.last7Days();
        return result;
    }


    /**
     * 软件用户量 TOP10（环形图）
     * @param screenTypeEnum
     * @return
     */
    @Override
    @RedisCache
    public List<TypeCountDTO> softwareUserPie(ScreenTypeEnum screenTypeEnum) {
        List<TypeCountDTO> result = softWareCommLogService.getLogNumByUserTop10(screenTypeEnum);
        return result;
    }

    /**
     * 软件访问日志量 TOP10（环形图）
     * @param screenTypeEnum
     * @return
     */
    @Override
    @RedisCache
    public List<TypeCountDTO> softwareAccessLog(ScreenTypeEnum screenTypeEnum) {
        List<TypeCountDTO> result = softWareCommLogService.getLogNumBySofaWareTop10(screenTypeEnum);
        return result;
    }

    /**
     * 用户量TOP10的软件提供服务次数趋势图（折线图）
     * @param screenTypeEnum
     * @return
     */
    @Override
    @RedisCache
    public List<DateHistogramDTO> userNumChart(ScreenTypeEnum screenTypeEnum) {
        List<DateHistogramDTO> result = softWareCommLogService.getLogNumBySofaWareAndDayTop10(screenTypeEnum);
        return result;
    }

    /**
     * 软件对系统的适配度 top10
     * @param screenTypeEnum
     * @return
     */
    @Override
    @RedisCache
    public List<TypeCountDTO> softWareSystemTopo10(ScreenTypeEnum screenTypeEnum) {
        List<TypeCountDTO> result = softWareCommLogService.getsoftWareSystemTopo10(screenTypeEnum);
        return result;
    }
    /**
     * 用户量TOP10的软件开发者信息
     * @param screenTypeEnum
     * @return
     */
    @Override
    @RedisCache
    public List<DateHistogramDTO> userNumDevelop(ScreenTypeEnum screenTypeEnum) {
        List<DateHistogramDTO> result = softWareCommLogService.getUserNumDevelop(screenTypeEnum);
        return result;
    }


    @Override
    @RedisCache
    public List<TypeCountDTO> softWareProtocolTopo10(ScreenTypeEnum screenTypeEnum) {
        List<TypeCountDTO> result = softWareCommLogService.getsoftWareProtocolTopo10(screenTypeEnum);
        return result;
    }

    /**
     * 软件授权类型占比
     * @param screenTypeEnum
     * @return
     */
    @Override
    @RedisCache
    public List<TypeCountDTO> softwareLicence(ScreenTypeEnum screenTypeEnum) {
        List<TypeCountDTO> result = softWareCommLogService.getSoftwareLicence(screenTypeEnum);
        return result;
    }

}
