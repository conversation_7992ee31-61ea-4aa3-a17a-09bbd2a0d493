package com.eversec.antivpn.intelligence.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 短信发送请求DTO
 * 
 * <AUTHOR>
 * @since 2025-01-12
 */
@Data
@Schema(name = "SmsRequestDTO", description = "短信发送请求")
public class SmsRequestDTO {

    @NotNull(message = "接收短信号码不能为空")
    @NotEmpty(message = "接收短信号码不能为空")
    @Schema(description = "接收短信号码(号码要以86开头)", example = "[\"8618612345678\"]")
    private List<String> serialNumber;

    @NotNull(message = "短信内容不能为空")
    @Size(max = 255, message = "短信内容长度不能超过255个字符")
    @Schema(description = "短信内容", example = "您的验证码是1234，在5分钟内有效。如非本人操作请忽略本短信。")
    private String message;

}
