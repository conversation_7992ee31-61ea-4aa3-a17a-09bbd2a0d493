package com.eversec.antivpn.support.province.convertor;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.eversec.antivpn.config.AntiVpnProperties;
import com.eversec.antivpn.support.province.api.dto.ProvincePlatformStatusDTO;
import com.eversec.antivpn.support.province.entity.ProvincePlatformStatus;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;

/**
 * 实体类和DTO转换类
 */
@Slf4j
@Component
@AllArgsConstructor
public class ProvincePlatformStatusFactory {

	private final AntiVpnProperties antiVpnProperties;

	public ProvincePlatformStatusDTO entityToDto(ProvincePlatformStatus entity) {
		ProvincePlatformStatusDTO dto = new ProvincePlatformStatusDTO();
		BeanUtil.copyProperties(entity, dto);
		String networkBusinessIds = entity.getNetworkBusinessIds();
		if (StrUtil.isNotBlank(networkBusinessIds)) {
			dto.setNetworkBusinessIdList(JSONUtil.toList(networkBusinessIds, Integer.class));
		} else {
			dto.setNetworkBusinessIdList(new ArrayList<>());
		}
		return dto;
	}

}
