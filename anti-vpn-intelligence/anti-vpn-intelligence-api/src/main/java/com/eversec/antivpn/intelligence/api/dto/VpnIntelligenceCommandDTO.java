package com.eversec.antivpn.intelligence.api.dto;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * vpn情报指令DTO
 * </p>
 * <p>
 * api接口数据传输对象（用于save、update接收参数，list、page、find返回对象等）
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-10
 */
@Getter
@Setter
@Schema(name = "VpnIntelligenceCommandDTO", description = "vpn情报指令")
@Data
public class VpnIntelligenceCommandDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "id")
    private Long id;

    @Schema(description = "跨境通信情报库下发执行指令ID。当部侧下发时由部侧系统控制惟一；当企业侧系统上报时企业侧内惟一。")
    private Long commandId;

    @Schema(description = "跨境通信情报库版本格式：主版本号.副版本号，本规范主版本号为1，副版本号为0，即1.0。")
    private String version;

    @Schema(description = "运营商编码，见附录F.2")
    private String comCode;

    @Schema(description = "省级区域编号，见附录F.4")
    private Long provinceId;
    @Schema(description = "省平台业务系统标识。省平台部署：当前省平台业务系统标识；集团部署：省平台业务系统标识")
    private String systemCode;

    @Schema(description = "监测网络类型code，见附录F.3。企业侧上报时必填。部侧下发时可选填，当部侧下发为空时表示所有网络类型生效")
    private Integer networkBusinessId;

    @Schema(description = "全量情报库文件名称,当操作类型为3时填写的是全量跨境通信情报库文件名；当操作类型为4、5时填写的是模型或样本文件名，多个用|线分隔； 当填写Vpn名称时，优先使用中文名称，没有中文名称时使用英文名称")
    private String fileName;



    @Schema(description = "是否上报取证文件： 0：上报； 1：不上报。 企业侧系统上报时必填，部侧系统下发时为空。")
    private Integer isUploadFile;

    @Schema(description = "取证文件名称,当isUploadFile为0时必填。上报方法参见7.2.2章节。")
    private String attachMent;

    @Schema(description = "创建时间,生成该文件的时间")
    private String timeStamp;

    @Schema(description = "操作类型,对该记录的操作类型，包括： 0-增量情报下发； 1-上报； 2-删除； 3-全量情报下发； 4-模型下发； 5-样本下发。")
    private Integer operationType;

    @Schema(description = "情报来源，FULL：全量下发。INCREASE：增量下发。REPORT：省端上报。INPUT：页面录入")
    private String source;

    @Schema(description = "情报集合，省端查询情报信息时，增量下发不单独去下发文件，取该值")
    private List<VpnIntelligenceDTO> intelligenceList;

    @Schema(description = "上报状态，REPORTED：未上报，TO_BE_REPORTED：待上报")
    private String reportStatus;

    @Schema(description = "上报时间，上报到部侧时间")
    private LocalDateTime reportDatetime;

    @Schema(description = "下发状态，DISTRIBUTED：已下发，TO_BE_DISTRIBUTED：待下发")
    private String distributeStatus;

    @Schema(description = "下发时间，下发到省端")
    private LocalDateTime distributeDatetime;

    @Schema(description = "创建时间", hidden = true)
    private Date createDatetime;

    @Schema(description = "下发请求参数")
    private String distributeRequestParams;
    @Schema(description = "下发返回参数")
    private String distributeResponseParams;
    @Schema(description = "指令状态，接收：RECEIVE；成功：SUCCESS；失败：FAIL")
    private String commandProcessStatus;

}