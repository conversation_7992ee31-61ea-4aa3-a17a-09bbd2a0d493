package com.eversec.antivpn.log.controller;

import com.eversec.antivpn.log.api.DecryptCommLogApi;
import com.eversec.antivpn.log.service.DecryptService;
import com.eversec.stark.generic.common.types.ContentDispositionTypeEnum;
import com.eversec.stark.generic.common.util.DownloadUtil;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.InputStream;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/12/27 14:30
 **/
@RestController
@RequestMapping(com.eversec.antivpn.log.api.DecryptCommLogApi.PATH)
@Slf4j
public class DecryptController implements DecryptCommLogApi {

    @Resource
    private DecryptService decryptService;

    @Operation(summary = "解密文件")
    @GetMapping("/decryptFile")
    public void vpnType5ContentTypeAggregate(HttpServletResponse response, @RequestParam  String fileName){
        String content = decryptService.decryptFile(fileName);
        byte[] bytes = content.getBytes();
        InputStream inputStream = new ByteArrayInputStream(bytes);
        String downLoadFileName = getFileName(fileName);
        DownloadUtil.download(response,inputStream, ContentDispositionTypeEnum.attachment,downLoadFileName,null);
    }

    private String getFileName(String fileName){
        return fileName.substring(0,fileName.indexOf("."))+".txt";
    }
}
