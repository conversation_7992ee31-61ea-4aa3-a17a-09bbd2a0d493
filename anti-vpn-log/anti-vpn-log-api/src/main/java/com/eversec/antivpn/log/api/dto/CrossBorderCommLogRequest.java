package com.eversec.antivpn.log.api.dto;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.springframework.context.annotation.Configuration;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Date;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/8/2 16:53
 **/
@Getter
@Setter
public class CrossBorderCommLogRequest extends CrossBorderCommLogDTO {

    private static final long serialVersionUID = 1L;

    private Integer current;

    private Integer size;

    private Integer offset;

    private Long startTimestamp;

    private Long endTimestamp;

    private Long startAccessTime;

    private Long endAccessTime;
}
