dependencies {

    implementation "com.eversec.stark.generic:generic-common:${genericVersion}"

    implementation "com.baomidou:mybatis-plus-annotation:${mybatisPlusVersion}"
    implementation "com.baomidou:mybatis-plus-generator:${mybatisPlusVersion}"
    implementation "com.baomidou:mybatis-plus-boot-starter:${mybatisPlusVersion}"

    implementation "org.freemarker:freemarker:2.3.30"
    implementation "org.springdoc:springdoc-openapi-ui:${springdocOpenapiVersion}"
    implementation "com.github.xiaoymin:knife4j-springdoc-ui:${knife4jSpringdocVersion}"

    implementation "mysql:mysql-connector-java:${mysqlConnectorVersion}"
    implementation "com.clickhouse:clickhouse-jdbc:${clickhouseConnectorVersion}"
    implementation "cn.hutool:hutool-all:${hutoolVersion}"
    implementation "org.apache.velocity:velocity-engine-core:2.3"

    test {
        useTestNG()
    }
}
