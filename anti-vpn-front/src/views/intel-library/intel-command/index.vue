<template>
  <div class="intel-command" v-loading="loading">
    <!-- form -->
    <publicForm
      ref="publicForm"
      :formDataLabel="formDataLabel"
      :labelWidth="'0'"
      :form-data="formData"
      @throwEvent="changeItem"
    />
    <div class="is-overflow-hidden is-mb-10">
      <el-button
        class="is-float-right"
        type="danger"
        :disabled="!(activeList && activeList.length > 0)"
        @click="batchDelete"
      >
        批量删除
      </el-button>
    </div>
    <!-- table -->
    <publicTable
      :tableLabel="tableLabel"
      :tableData="dataList"
      ref="table"
      height="100%"
      :options="{
        highlightCurrentRow: false,
        isString: true,
        checkbox: true,
        border: false
      }"
      @handleSelectionChange="handleSelectionChange"
    >

      <template v-slot:download="{ data }">
        <el-link
          v-if="data.row.operationType === 3 || data.row.operationType === 4 || data.row.operationType === 5"
          class="is-ml-10 is-float-left"
          type="primary"
          @click="downloadFun(data.row.id)"
        >
          下载文件
        </el-link>
      </template>
      <template v-slot:handle="{ data }">
        <el-link
          class="is-ml-10 is-float-left"
          :underline="false"
          v-if="data.row.distributeRequestParams"
          type="primary"
          @click="reDistribute(data.row.id)"
        >
          重新下发
        </el-link>
        <el-link
          class="is-ml-10 is-float-left"
          :underline="false"
          type="danger"
          @click="deleteBtn(data.row.id)"
        >
          删除
        </el-link>
      </template>
    </publicTable>
    <!-- 分页 -->
    <el-pagination
      class="is-pt-10 is-float-right"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="pagingInfo.current"
      :page-sizes="pagingInfo.pageSizes"
      :page-size="pagingInfo.size"
      :layout="pagingInfo.layout"
      :total="pagingInfo.total"
      :key="pagingInfo.total"
    />
  </div>
</template>

<script>
import cloneDeep from 'lodash/cloneDeep'
import { pagingInfo } from '@/config/base'
import debounce from 'lodash/debounce'
import antiVpnService from '@/api/antiVpnService'

export default {
  name: 'intel-command',
  components: {},
  data() {
    return {
      loading: false,
      formDataLabel: [
        [
          {
            val: 'comCode',
            type: 'dic-select',
            enumKey: 'ISP_CODE',
            showAll: true,
            funName: 'changeItem',
            className: 'is-one-fifth is-pr-10',
            placeholder: '请选择运营商编码'
          },
          {
            val: 'networkBusinessId',
            type: 'dic-select',
            enumKey: 'NETWORK_TYPE',
            showAll: true,
            funName: 'changeItem',
            className: 'is-one-fifth is-pr-10',
            placeholder: '请选择网络类型'
          },
          {
            val: 'commandId',
            type: 'input',
            funName: 'changeItem',
            className: 'is-one-fifth is-pr-10',
            placeholder: '请输入指令ID'
          },
          {
            val: 'provinceId',
            type: 'dic-select',
            enumKey: 'PROVINCE_CODE',
            showAll: true,
            funName: 'changeItem',
            className: 'is-one-fifth is-pr-10',
            placeholder: '请选择省级区域'
          },
          {
            val: 'operationType',
            type: 'dic-select',
            enumKey: 'INTELLIGENCE_COMMAND_OPERATION_TYPE',
            showAll: true,
            funName: 'changeItem',
            className: 'is-one-fifth',
            placeholder: '请选择操作类型'
          }
        ],
        [
          {
            val: "systemCode",
            type: "dic-select",
            enumKey: "SYSTEM_CODE",
            showAll: true,
            funName: "changeItem",
            className: "is-one-fifth is-pr-10",
            placeholder: "请选择业务系统标识"
          },
          {
            val: 'reportDatetime',
            type: 'date-time-range',
            funName: 'changeItem',
            className: 'is-4',
            startP: '请选择上报开始时间',
            endP: '请选择上报结束时间'
          },
          {
            type: "btn",
            btnTxt: "刷新",
            funName: "refresh",
            btnType: ""
          }
        ]
      ],
      formData: {},
      queryKey: [
        'comCode',
        'networkBusinessId',
        'commandId',
        'provinceId',
        'systemCode',
        'operationType',
        'reportDatetime'
      ],
      pagingInfo: cloneDeep(pagingInfo()), // 分页信息
      tableLabel: [
        {
          label: '指令ID',
          val: 'commandId'
        },
        {
          label: '运营商编码',
          val: 'comCode',
          type: 'dic-val',
          enumKey: 'ISP_CODE'
        },
        {
          label: '省级区域',
          val: 'provinceId',
          type: 'dic-val',
          enumKey: 'PROVINCE_CODE'
        },
        {
          label: "业务系统标识",
          val: "systemCode",
          type: "dic-val",
          enumKey: "SYSTEM_CODE",
          width: 120
        },
        {
          label: '网络类型',
          val: 'networkBusinessId',
          type: 'dic-val',
          enumKey: 'NETWORK_TYPE'
        },
        {
          label: '操作类型',
          val: 'operationType',
          type: 'dic-val',
          enumKey: 'INTELLIGENCE_COMMAND_OPERATION_TYPE'
        },
        {
          label: '发现时间',
          val: 'timeStamp',
          type: 'date',
          width: 180
        },
        {
          label: '上报时间',
          val: 'reportDatetime',
          type: 'date',
          width: 180
        },
        {
          label: '指令状态',
          val: 'commandProcessStatus',
        },
        {
          label: "指令文件",
          val: "downloadFile",
          type: "slot",
          slotName: "download",
          width: 120
        },
        {
          label: '操作',
          type: 'slot',
          slotName: 'handle',
          fixed: 'right',
          width: 80
        }
      ],
      dataList: [],
      activeList: []
    }
  },
  computed: {},
  watch: {},
  mounted() {
    this.formData = {}
    for (let name in this.$route.query) {
      if (name === 'size') {
        this.pagingInfo.size = Number(this.$route.query[name])
      } else if (name === 'current') {
        this.pagingInfo.current = Number(this.$route.query[name])
      } else {
        this.$set(this.formData, name, this.$route.query[name])
        this.formData[name] = this.$route.query[name]
      }
    }
    this.getDataList()
  },
  methods: {
    // 表单监听回调
    changeItem(val) {
      if (val && val.funName === "refresh") {
        this.getDataList();
        return;
      }
      if (val) {
        this.pagingInfo.current = 1
      }
      this.debounce(this)
    },
    // 防抖查询
    debounce: debounce(vm => {
      let tmpData = {
        size: vm.pagingInfo.size,
        current: vm.pagingInfo.current
      }
      vm.queryKey.map(e => {
        if (vm.formData[e]) vm.$set(tmpData, e, vm.formData[e])
      })
      vm.$router.push({
        path: vm.$route.path,
        query: tmpData
      })
      vm.getDataList()
    }, 300),
    // 选中框
    handleSelectionChange(val) {
      this.activeList = val
    },
    // 列表查询
    getDataList() {
      this.loading = true
      let param = {
        current: this.pagingInfo.current, //当前页
        size: this.pagingInfo.size //每页展示数量
      }
      this.queryKey.map(e => {
        this.$set(param, e, this.formData[e])
        if (this.formData[e]) {
          if (e === 'reportDatetime' && this.formData[e].length > 0) {
            this.$set(param, 'reportDatetimeStart', this.formData[e][0])
            this.$set(param, 'reportDatetimeEnd', this.formData[e][1])
          } else {
            this.$set(param, e, this.formData[e])
          }
        }
      })
      antiVpnService
        .getVpnIntelligenceCommand(param)
        .then(res => {
          this.dataList = res.records || []
          this.pagingInfo.total = res.total || 0
          this.loading = false
        })
        .catch(() => {
          this.loading = false
        })
    },
    // 每页数
    handleSizeChange(val) {
      this.pagingInfo.size = val
      this.pagingInfo.current = 1
      this.changeItem()
    },
    // 当前页
    handleCurrentChange(val) {
      this.pagingInfo.current = val
      this.changeItem()
    },
    reDistribute(id) {
      this.$confirm('此操作会产生一条新记录, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.loading = true
          antiVpnService
            .reDistributeCommand(id)
            .then(res => {
              this.loading = false
              this.$message({
                type: 'success',
                message: '重新下发成功!'
              })
              this.getDataList()
            })
            .catch(() => {
              this.loading = false
            })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消重新下发'
          })
        })
    },
    // 删除
    deleteBtn(id) {
      this.$confirm('此操作将删除该数据, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.loading = true
          antiVpnService
            .vpnIntelligenceCommandDeleteByIds(id)
            .then(res => {
              this.loading = false
              this.$message({
                type: 'success',
                message: '删除成功!'
              })
              this.getDataList()
            })
            .catch(() => {
              this.loading = false
            })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    // 批量删除
    batchDelete() {
      this.$confirm('此操作将删除已选数据, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.loading = true
          antiVpnService
            .vpnIntelligenceCommandDeleteByIds(this.activeList.map(e => e.id))
            .then(res => {
              this.loading = false
              this.$message({
                type: 'success',
                message: '删除成功!'
              })
              this.getDataList()
            })
            .catch(() => {
              this.loading = false
            })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },

    // 下载文件
    downloadFun(id) {
      antiVpnService.downloadCommandFile(id);
    },
  }
}
</script>

<style lang="scss" scoped>
.intel-command {
  height: 100%;
  ::v-deep .public-table {
    height: calc(100% - 155px - 30px);
  }
}
</style>
