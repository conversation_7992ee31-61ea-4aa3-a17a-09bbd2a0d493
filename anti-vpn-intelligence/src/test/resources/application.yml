
eureka:
    client:
        serviceUrl:
            defaultZone: ${EUREKA_ADDRESS:http://eureka:8080/eureka}
    instance:
        prefer-ip-address: ${preferIpAddress:true}

eversec:
    web:
        request:
            enable-log: true
        result:
            enable-log: true
    dynamic-flyway:
        anti-vpn:
            table: flyway_schema_history_anti_vpn
            baseline-on-migrate: true
            enabled: true
            ignore-missing-migrations: true
            locations: classpath:db/migration
            # 等待generic先启动
            init-sqls:
              - select `id`, `type_key`, `type_val`, `seq`, `parent_type_key`, `deleted`, `create_user`, `update_user`, `create_datetime`, `update_datetime`
                    from generic_sys_data_dict_type limit 0;
              - select `id`, `enum_key`, `enum_val`, `seq`, `type_key`, `parent_enum_key`, `enum_val2`, `enum_val3`, `enum_val4`, `enum_val5`, `valid`, `deleted`, `create_user`, `update_user`, `create_datetime`, `update_datetime`
                    from generic_sys_data_dict limit 0;
            # 支持乱序执行
            out-of-order: true
            placeholder-prefix: '#('
            placeholder-suffix: )
            validate-on-migrate: true
        # 初始化三件套系统管理数据
        system:
            table: flyway_schema_history_anti_vpn_system
            baseline-on-migrate: true
            enabled: true
            ignore-missing-migrations: true
            locations: classpath:db/migration-system
            # 等待三件套先启动
#            init-sqls:
#                - select id,insert_time,last_update_time,app_desc,app_key,app_name,app_url,app_icon,seq,app_type,show_in_home
#                    from sys_security_application limit 0;
#                - select id,insert_time,last_update_time,description,name,role_alias,role_level,role_style,parent_id
#                    from sys_security_role limit 0;
#                - select id,org_code,org_name,org_desc,org_level,job_grade,org_style,org_alias,parent_id,insert_time,last_update_time,external_id,external_parent_id,extend1,extend2
#                    from sys_security_org limit 0;
#                - select id,user_name,password,reset_password_time,real_name,email,tel,is_valid,comcode,user_desc,sys_org_id,insert_time,last_update_time
#                    from sys_security_user limit 0;
#                - select sys_user_id,sys_role_id from sys_security_user_role limit 0;
#                - select sys_user_id,sys_org_id from sys_security_user_org limit 0;
#                - select `id`, `insert_time`, `last_update_time`, `icon`, `name`, `seq`,
#                    `type`, `url`, `app_id`, `parent_id`, `open_mode`, `permission_keyword`,
#                    `http_method`, `remark`, `is_log`, `other`, `relation`, `subtype`
#                    from sys_security_menu limit 0;
#                - select sys_role_id,sys_menu_id from sys_security_role_menu limit 0;
            # 支持乱序执行
            out-of-order: true
            placeholder-prefix: '#('
            placeholder-suffix: )
            validate-on-migrate: true
spring:
    main:
        allow-circular-references: true
    datasource:
        dynamic:
            primary: anti-vpn
            # 这里用dynamic的p6spy会有启动flyway执行失败进程不退出情况，切换成sleuth的p6spy没有这个问题，暂时没找到原因
            p6spy: false
    flyway:
        enabled: false
    servlet:
        multipart:
            max-file-size: 4096MB
            max-request-size: 4096MB
    profiles:
        active: local
    application:
        name: anti-vpn-service
    sleuth:
        jdbc:
            p6spy:
                enabled: true



logging:
    level:
        root: info
    file:
        name: /app/logs/${spring.application.name:}.${spring.cloud.client.hostname:}.log

        max-size: 100MB
        max-history: 3650
        total-size-cap: 10GB


mybatis:
    mapper-locations=classpath*: mapper/*Mapper.xml

# cxf webservice 前缀
cxf:
    path: /services

springdoc:
    swagger-ui:
        enabled: false