package com.eversec.antivpn;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.netflix.eureka.EnableEurekaClient;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@EnableEurekaClient
@SpringBootApplication
@EnableScheduling
@EnableTransactionManagement
public class AntiVpnServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(AntiVpnServiceApplication.class, args);
    }
}
