package com.eversec.antivpn.intelligence.entity;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 短信验证码记录
 * 
 * <AUTHOR>
 * @since 2025-01-12
 */
@Data
public class SmsCodeRecord {

    /**
     * 手机号码
     */
    private String phoneNumber;

    /**
     * 验证码
     */
    private String code;

    /**
     * 业务类型
     */
    private String businessType;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 过期时间
     */
    private LocalDateTime expireTime;

    /**
     * 是否已使用
     */
    private Boolean used;

    /**
     * 使用时间
     */
    private LocalDateTime usedTime;

    /**
     * 发送次数（当日）
     */
    private Integer sendCount;

    /**
     * 最后发送时间
     */
    private LocalDateTime lastSendTime;

    /**
     * 判断是否过期
     */
    public boolean isExpired() {
        return LocalDateTime.now().isAfter(expireTime);
    }

    /**
     * 判断是否可以使用
     */
    public boolean canUse() {
        return !used && !isExpired();
    }

}
