<style lang="scss" scoped>
.svg-icon {
	width: 1em;
	height: 1em;
	vertical-align: -0.15em;
	fill: currentColor;
	overflow: hidden;
	font-size: 1em;
}
.icon-code {
	vertical-align: 0;
}
</style>
<template>
	<svg :class="svgClass" aria-hidden="true">
		<use :xlink:href="iconName"></use>
	</svg>
</template>
<script>
export default {
	name: 'svgIcon',
	props: {
		type: {
			type: String,
			required: true
		},
		// eslint-disable-next-line vue/require-default-prop
		className: {
			type: String
		}
	},
	computed: {
		iconName() {
			return `#icon-${this.type}`;
		},
		svgClass() {
			if (this.className) {
				return 'svg-icon ' + this.className;
			} else {
				return 'svg-icon';
			}
		}
	}
};
</script>
