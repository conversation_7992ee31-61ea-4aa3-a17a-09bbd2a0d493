<template>
  <article class="public-table">
    <el-table
      ref="publicTable"
      :key="key"
      :data="tableData"
      :border="'border' in options ? options.border : true"
      :stripe="'stripe' in options ? options.stripe : true"
      style="width: 100%;"
      :height="height || tableHeight || null"
      :highlight-current-row="
        'highlightCurrentRow' in options ? options.highlightCurrentRow : true
      "
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        v-if="options.checkbox"
        type="selection"
        width="50"
        :selectable="selectable"
        fixed="left"
      />
      <el-table-column
        v-if="'array' in options ? options.array : true"
        type="index"
        :header-align="options.arrayAlign || 'left'"
        :align="options.arrayAlign || 'left'"
        :width="options.arrayWidth || 50"
        :label="options.arrayName || '序号'"
      />
      <template v-for="(items, index) in tableLabel">
        <el-table-column
          :key="index"
          :show-overflow-tooltip="items.showOverflowTooltip || true"
          :header-align="items.headerAlign || options.headerAlign || 'left'"
          :align="items.align || options.align || 'left'"
          :label="items.label"
          :width="items.width"
          :fixed="items.fixed"
        >
          <template slot-scope="scope">
            <!-- slot -->
            <template v-if="items.type === 'slot'">
              <slot
                :name="items.slotName"
                :data="{
                  ...scope,
                  slotItem: scope.row[items.val],
                  draw_description: items
                }"
              />
            </template>
            <!-- 字典文字反显 -->
            <template v-else-if="items.type === 'dic-val'">
              <dic-value
                v-if="show"
                :dic-key="scope.row[items.val]"
                :enum-key="items.enumKey"
                :multiple="items.multiple"
                :default-value="scope.row[items.val]"
                :is-string="options.isString"
                :dictionaryKey="dictionaryData[items.enumKey] || []"
              />
              <span v-else>{{ scope.row[items.val] }}</span>
            </template>
            <!-- 时间转换 -->
            <template v-else-if="items.type === 'date'">
              {{ formatTime(scope.row[items.val], items.fmt) }}
            </template>
            <template v-else-if="items.type === 'status'">
              <el-popover placement="right" width="200" trigger="click">
                <div>
                  <ul class="statusList">
                    <li>
                      1.采集端-->数据处理<i
                        v-if="scope.row[items.val] == 0"
                        class="el-icon-success"
                        style="color: #67C23A;"
                      ></i>
                      <i
                        v-else
                        class="el-icon-error"
                        style="color: #F56C6C;"
                      ></i>
                    </li>
                    <li>
                      2.数据处理-->企业侧<i
                        v-if="scope.row[items.val] == 0"
                        class="el-icon-success"
                        style="color: #67C23A;"
                      ></i>
                      <i
                        v-else
                        class="el-icon-error"
                        style="color: #F56C6C;"
                      ></i>
                    </li>
                    <li>
                      3.企业侧-->集团侧
                      <i
                        v-if="scope.row[items.val] == 0"
                        class="el-icon-success"
                        style="color: #67C23A;"
                      ></i>
                      <i
                        v-else
                        class="el-icon-error"
                        style="color: #F56C6C;"
                      ></i>
                    </li>
                    <li>
                      4.集团侧-->部侧
                      <i
                        v-if="scope.row[items.val] == 0"
                        class="el-icon-success"
                        style="color: #67C23A;"
                      ></i>
                      <i
                        v-else
                        class="el-icon-error"
                        style="color: #F56C6C;"
                      ></i>
                    </li>
                  </ul>
                </div>
                <el-tag
                  v-if="scope.row[items.val] == 0"
                  slot="reference"
                  type="success"
                  style="cursor: pointer;"
                  >正常</el-tag
                >
                <el-tag
                  v-else
                  type="danger"
                  style="cursor: pointer;"
                  slot="reference"
                  >异常</el-tag
                >
              </el-popover>
            </template>
            <!-- 文字反显 -->
            <template v-else>
              {{ scope.row[items.val] }}
            </template>
          </template>
        </el-table-column>
      </template>
    </el-table>
  </article>
</template>

<script>
import utilsFun from "@/utils/utils";
import { formatTime } from "@/utils/time";
import { mapGetters } from "vuex";
export default {
  name: "PublicTable",
  components: {},
  props: {
    // el表格其他配置
    options: {
      type: Object,
      default: () => ({
        border: true, // 是否带边框
        stripe: true, // 斑马线
        highlightCurrentRow: true, // 是否高亮
        checkbox: false, // 是否多选
        array: true, // 是否显示序号
        headerAlign: "left", // 标题对齐方式
        align: "left" // 内容对齐方式
      })
    },
    // 高度
    height: {
      type: Number | String,
      default: () => ""
    },
    // 表格数据
    tableData: {
      type: Array,
      default: () => []
    },
    // 表格表述
    tableLabel: {
      type: Array,
      default: () => []
    },
    // 多选需要被禁用的列
    checkboxDisable: {
      type: String | Array,
      default: () => ""
    },
    checkboxDisableKey: {
      type: String,
      default: () => "fieldKey"
    }
  },
  data() {
    return {
      formatTime,
      tableHeight: "",
      key: 0
    };
  },
  computed: {
    ...mapGetters("sys-dict", ["dictionaryData"]),
    show: {
      cache: false,
      get() {
        return (
          this.dictionaryData && Object.keys(this.dictionaryData).length > 0
        );
      }
    }
  },
  mounted() {
    this.$store.dispatch("sys-dict/updateDictionary");
  },
  methods: {
    // 选中框
    handleSelectionChange(val) {
      this.$emit("handleSelectionChange", val);
    },
    // 计算哪条数据可以被选中
    selectable(item, index) {
      let row = item;
      if (utilsFun.isString(this.checkboxDisable))
        return eval(this.checkboxDisable || true);
      else return !this.checkboxDisable.includes(item[this.checkboxDisableKey]);
    }
  }
};
</script>

<style lang="scss">
.public-table {
  .el-table.is-pa-10 {
    padding: 0 !important;
  }
}
.statusList {
  li {
    line-height: 24px;
    i {
      float: right;
      position: relative;
      top: 4px;
    }
  }
}
</style>
