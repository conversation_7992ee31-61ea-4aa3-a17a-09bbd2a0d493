package ${package.Controller};

import ${apiPackage}.${apiName};
import ${apiDtoPackage}.${apiDtoName};
import ${package.Service}.${table.serviceName};

import org.springframework.web.bind.annotation.RequestMapping;
<#if restControllerStyle>
import org.springframework.web.bind.annotation.RestController;
<#else>
import org.springframework.stereotype.Controller;
</#if>
<#if superControllerClassPackage??>
import ${superControllerClassPackage};
</#if>
import lombok.AllArgsConstructor;
import java.util.List;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * ${table.comment!} 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since ${date}
 */
<#if restControllerStyle>
@RestController
<#else>
@Controller
</#if>
@RequestMapping(${apiName}.PATH)
@AllArgsConstructor
@Slf4j
<#if kotlin>
class ${table.controllerName}<#if superControllerClass??> : ${superControllerClass}()</#if>
<#else>
<#if superControllerClass??>
public class ${table.controllerName} extends ${superControllerClass} implements ${apiName} {
<#else>
public class ${table.controllerName} implements ${apiName} {
</#if>

    private final ${table.serviceName} service;


    @Override
    public ${apiDtoName} getById(Long id) {
        // TODO
        return null;
    }


    @Override
    public List<${apiDtoName}> listAll() {
        // TODO
        return null;
    }

    @Override
    public Page<${apiDtoName}> page(${apiDtoName} paramDto, Page<${apiDtoName}> pageInfo) {
        // TODO
        return null;
    }

    @Override
    public void save(${apiDtoName} paramDto) {
        // TODO

    }

    @Override
    public void updateById(${apiDtoName} paramDto) {
        // TODO

    }

    @Override
    public void deleteByIds(List<Long> ids) {
        // TODO

    }

}
</#if>
