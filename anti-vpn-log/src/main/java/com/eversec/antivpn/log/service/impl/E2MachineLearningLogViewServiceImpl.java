package com.eversec.antivpn.log.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eversec.antivpn.emnus.ScreenTypeEnum;
import com.eversec.antivpn.log.api.dto.TypeCountDTO;
import com.eversec.antivpn.log.api.dto.base.AggCountDTO;
import com.eversec.antivpn.log.api.dto.base.DateHistogramDTO;
import com.eversec.antivpn.log.constants.CommonField;
import com.eversec.antivpn.log.entity.E2MachineLearningLogView;
import com.eversec.antivpn.log.mapper.E2MachineLearningLogViewMapper;
import com.eversec.antivpn.log.mapper.vo.BaseSearchVO;
import com.eversec.antivpn.log.mapper.vo.TypeCountVO;
import com.eversec.antivpn.log.service.GenericSysDataDictService;
import com.eversec.antivpn.log.service.IE2MachineLearningLogViewService;
import com.eversec.antivpn.log.util.AggregateDataUtil;
import com.eversec.antivpn.log.util.CKPartitionUtil;
import com.eversec.antivpn.log.util.CalculateUtil;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-18
 */
@Service
@Slf4j
public class E2MachineLearningLogViewServiceImpl extends ServiceImpl<E2MachineLearningLogViewMapper, E2MachineLearningLogView> implements IE2MachineLearningLogViewService {

    @Resource
    private E2MachineLearningLogViewMapper e2MachineLearningLogViewMapper;

    @Resource
    private GenericSysDataDictService genericSysDataDictService;

    @Override
    public AggCountDTO countNum(ScreenTypeEnum screenTypeEnum) {
        AggCountDTO result = new AggCountDTO();
        BaseSearchVO baseSearchVO = new BaseSearchVO();
        baseSearchVO.setTodayTimePartition(ScreenTypeEnum.getTimeCKPartition(ScreenTypeEnum.TODAY));
        Long todayCount = e2MachineLearningLogViewMapper.countNum(baseSearchVO);
        result.setTodayCount(todayCount);
        if(ScreenTypeEnum.TODAY == screenTypeEnum){
            result.setDateRangeCount(todayCount);
        }else {
            baseSearchVO.clear();
            baseSearchVO.setStartTimePartition( ScreenTypeEnum.getTimeCKPartition(screenTypeEnum));
            Long dateRangeCount =  e2MachineLearningLogViewMapper.countNum(baseSearchVO);
            result.setDateRangeCount(dateRangeCount);
        }
        return result;
    }


    public AggCountDTO dateVisitorArea(ScreenTypeEnum screenTypeEnum) {
        AggCountDTO aggCountDTO = new AggCountDTO();
        BaseSearchVO baseSearchVO = new BaseSearchVO();
        baseSearchVO.setTodayTimePartition(ScreenTypeEnum.getTimeCKPartition(screenTypeEnum));
        Long todayCount = e2MachineLearningLogViewMapper.getLogNumVisitAreaAndProvinceId(baseSearchVO);
        aggCountDTO.setTodayCount(todayCount);
        if(screenTypeEnum == ScreenTypeEnum.TODAY){
            aggCountDTO.setDateRangeCount(todayCount);
        }else {
            baseSearchVO.clear();
            baseSearchVO.setStartTimePartition(ScreenTypeEnum.getTimeCKPartition(screenTypeEnum));
            Long dateRangeCount = e2MachineLearningLogViewMapper.getLogNumVisitAreaAndProvinceId(baseSearchVO);
            aggCountDTO.setDateRangeCount(dateRangeCount);
        }
        return aggCountDTO;
    }

    public AggCountDTO dateDestinationArea(ScreenTypeEnum screenTypeEnum) {
        AggCountDTO aggCountDTO = new AggCountDTO();
        BaseSearchVO baseSearchVO = new BaseSearchVO();
        baseSearchVO.setTodayTimePartition(ScreenTypeEnum.getTimeCKPartition(screenTypeEnum));
        Long todayCount = e2MachineLearningLogViewMapper.getLogNumPurposeAreaAndProvinceId(baseSearchVO);
        aggCountDTO.setTodayCount(todayCount);
        if(screenTypeEnum == ScreenTypeEnum.TODAY){
            aggCountDTO.setDateRangeCount(todayCount);
        }else {
            baseSearchVO.clear();
            baseSearchVO.setStartTimePartition(ScreenTypeEnum.getTimeCKPartition(screenTypeEnum));
            Long dateRangeCount = e2MachineLearningLogViewMapper.getLogNumPurposeAreaAndProvinceId(baseSearchVO);
            aggCountDTO.setDateRangeCount(dateRangeCount);
        }
        return aggCountDTO;
    }

    @Override
    public Long countSum(ScreenTypeEnum screenTypeEnum) {
        //获取总量
        BaseSearchVO baseSearchVO = CKPartitionUtil.setScreenTimeByBaseSearchVO(screenTypeEnum);
        Long dateRangeCount =  e2MachineLearningLogViewMapper.countNum(baseSearchVO);
        return dateRangeCount;
    }

    @Override
    public List<TypeCountDTO> getDatedDiscernTopo10(ScreenTypeEnum screenTypeEnum) {
        List<TypeCountDTO> result = new ArrayList<>(10);

        BaseSearchVO baseSearchVO = CKPartitionUtil.setScreenTimeByBaseSearchVO(screenTypeEnum);

        List<E2MachineLearningLogView> e2MachineLearningLogs = e2MachineLearningLogViewMapper.getModelDiscernLogNum(baseSearchVO);

        if(CollectionUtil.isEmpty(e2MachineLearningLogs)){
            return  result;
        }
        //获取E2日志总量
        Long countSum = countSum(screenTypeEnum);
        e2MachineLearningLogs.stream().forEach(e2->{
            TypeCountDTO dto = new TypeCountDTO();
            dto.setKey(e2.getModelName()+"("+e2.getModelCode()+")");
            dto.setNum(e2.getNum());
            dto.setPercent(CalculateUtil.myPercent1(e2.getNum(),countSum));
            result.add(dto);
        });
        return  result;
    }

    @Override
    public List<TypeCountDTO> getLogListByProtocolType(ScreenTypeEnum screenTypeEnum) {
        List<TypeCountDTO> result = new ArrayList<>();

        BaseSearchVO baseSearchVO = CKPartitionUtil.setScreenTimeByBaseSearchVO(screenTypeEnum);

        List<E2MachineLearningLogView> e2MachineLearningLogs = e2MachineLearningLogViewMapper.getProtocolTypeLogNum(baseSearchVO);


        if(CollectionUtil.isEmpty(e2MachineLearningLogs)){
            return  result;
        }
        //获取E2日志总量
        Long countSum = countSum(screenTypeEnum);
        e2MachineLearningLogs.stream().forEach(e2->{
            TypeCountDTO dto = new TypeCountDTO();
            dto.setKey(CommonField.PROTOCOL_TYPE_TCP==e2.getProtocolType() ? CommonField.PROTOCOL_TYPE_TCP_STR : CommonField.PROTOCOL_TYPE_UDP_STR);
            dto.setNum(e2.getNum());
            result.add(dto);
        });
        return result;
    }

    @Override
    public List<TypeCountDTO> getAILogByYearORmonthORDayORWeek(ScreenTypeEnum screenTypeEnum) {
        List<TypeCountDTO> result = new ArrayList<>();
        QueryWrapper<E2MachineLearningLogView> lambdaQueryWrapper = new QueryWrapper<>();

        CKPartitionUtil.setScreenTimeGroupByEnumsE2MachineLearningLogView(screenTypeEnum, lambdaQueryWrapper);

        BaseSearchVO baseSearchVO = CKPartitionUtil.setScreenTimeByBaseSearchVOAndGroupTime(screenTypeEnum);

        List<E2MachineLearningLogView> e2MachineLearningLogs = new ArrayList<>();
            switch (screenTypeEnum) {

            case TODAY:
                e2MachineLearningLogs = e2MachineLearningLogViewMapper.getAILogByDay(baseSearchVO);
                break;
            case THISWEEK:
                //上周今天（往前7天）
                e2MachineLearningLogs = e2MachineLearningLogViewMapper.getAILogByWeek(baseSearchVO);
                break;
            case THISMONTH:
                e2MachineLearningLogs = e2MachineLearningLogViewMapper.getAILogByMonth(baseSearchVO);
                break;
            case SOFAR:
                e2MachineLearningLogs = e2MachineLearningLogViewMapper.getAILogByYear(baseSearchVO);
                break;
        }

        if(CollectionUtil.isEmpty(e2MachineLearningLogs)){
            return  result;
        }
        e2MachineLearningLogs.stream().forEach(e2->{
            TypeCountDTO dto = new TypeCountDTO();
            dto.setKey(String.valueOf(e2.getTimes()));
            dto.setNum(e2.getNum());
            result.add(dto);
        });
        return  result;
    }

    @Override
    public List<DateHistogramDTO> getLogNumByRateList(ScreenTypeEnum screenTypeEnum) {
        //1.获取E2查询条件
        List<DateHistogramDTO> result = new ArrayList<>();
        BaseSearchVO baseSearchVO = CKPartitionUtil.setScreenTimeByBaseSearchVO(screenTypeEnum);
        List<E2MachineLearningLogView> e2MachineLearningLogs = e2MachineLearningLogViewMapper.getLogNumByRateList(baseSearchVO);
        if(CollectionUtil.isEmpty(e2MachineLearningLogs)){
            return  result;
        }
        e2MachineLearningLogs.stream().forEach(e2->{
            DateHistogramDTO dto = new DateHistogramDTO();
            dto.setKey(e2.getModelName());
            dto.setE1Num(ObjectUtil.isNotEmpty(e2.getRate())?(long)e2.getRate():0L);
            dto.setE2Num(e2.getNum());
            result.add(dto);
        });
        return  result;
    }

    @Override
    public List<DateHistogramDTO> getLogNumByRateDistribute(ScreenTypeEnum screenTypeEnum) {
        //1.获取E2查询条件
        List<DateHistogramDTO> result = new ArrayList<>();

        BaseSearchVO baseSearchVO = CKPartitionUtil.setScreenTimeByBaseSearchVO(screenTypeEnum);
        List<E2MachineLearningLogView> e2MachineLearningLogs = e2MachineLearningLogViewMapper.getLogNumByRateList(baseSearchVO);

        if(CollectionUtil.isEmpty(e2MachineLearningLogs)){
            return  result;
        }
        e2MachineLearningLogs.stream().forEach(e2->{
            DateHistogramDTO dto = new DateHistogramDTO();
            dto.setKey(String.valueOf(e2.getRate()));
            dto.setE2Num(e2.getNum());
            result.add(dto);
        });
        return  result;
    }

    @Override
    public List<TypeCountDTO> getLogNumByProvinceId(ScreenTypeEnum screenTypeEnum,Boolean startCtiySearch) {
        //1.获取E2查询条件
        List<TypeCountDTO> result = new ArrayList<>();
        //是否开启上省所属市分组查询
        BaseSearchVO baseSearchVO = new BaseSearchVO();
        baseSearchVO.setStartTimePartition(ScreenTypeEnum.getTimeCKPartition(screenTypeEnum));
        List<TypeCountVO> dataVOList;
        if(startCtiySearch){//默认按市分组
            dataVOList = e2MachineLearningLogViewMapper.getLogNumByCity(baseSearchVO);
        }else{//默认按省分组
            dataVOList = e2MachineLearningLogViewMapper.getLogNumByProvince(baseSearchVO);
        }
        dataVOList.forEach(vo -> {
            result.add(BeanUtil.copyProperties(vo,TypeCountDTO.class));
        });
        return  result;
    }

    @Override
    public List<TypeCountDTO> getChinaMapByProvinceId(ScreenTypeEnum screenTypeEnum,Boolean startCtiySearch) {
        List<TypeCountVO> voList = new ArrayList<>();
        //1.获取E2查询条件
        if(startCtiySearch){//默认按市分组
            voList = getLogNumByCity(screenTypeEnum);
        }else{//默认按省分组
            voList = getLogNumByProvince(screenTypeEnum);
        }
        List<TypeCountDTO> result = new ArrayList<>();
        voList.forEach(vo -> {
            result.add(BeanUtil.copyProperties(vo,TypeCountDTO.class));
        });
        return  result;
    }

    private List<TypeCountVO> getLogNumByCity(ScreenTypeEnum screenTypeEnum){
        BaseSearchVO baseSearchVO = new BaseSearchVO();
        baseSearchVO.setStartTimePartition(ScreenTypeEnum.getTimeCKPartition(screenTypeEnum));
        List<TypeCountVO> dateRangeList = e2MachineLearningLogViewMapper.getLogNumByCity(baseSearchVO);
        if(ScreenTypeEnum.TODAY == screenTypeEnum){
            dateRangeList.forEach(vo -> {
                vo.setTodayNum(vo.getNum());
            });
        }else {
            baseSearchVO.clear();
            baseSearchVO.setTodayTimePartition(ScreenTypeEnum.getTimeCKPartition(ScreenTypeEnum.TODAY));
            List<TypeCountVO> todayList = e2MachineLearningLogViewMapper.getLogNumByCity(baseSearchVO);
            AggregateDataUtil.mergeAllAndTodayTypeCountVOV2(dateRangeList,todayList);
        }
        return dateRangeList;
    }
    private List<TypeCountVO> getLogNumByProvince(ScreenTypeEnum screenTypeEnum){
        BaseSearchVO baseSearchVO = new BaseSearchVO();
        baseSearchVO.setStartTimePartition(ScreenTypeEnum.getTimeCKPartition(screenTypeEnum));
        List<TypeCountVO> dateRangeList =  e2MachineLearningLogViewMapper.getLogNumByProvince(baseSearchVO);
        if(ScreenTypeEnum.TODAY == screenTypeEnum){
            dateRangeList.forEach(vo -> {
                vo.setTodayNum(vo.getNum());
            });
        }else {
            baseSearchVO.clear();
            baseSearchVO.setTodayTimePartition(ScreenTypeEnum.getTimeCKPartition(ScreenTypeEnum.TODAY));
            List<TypeCountVO> todayList =  e2MachineLearningLogViewMapper.getLogNumByProvince(baseSearchVO);
            AggregateDataUtil.mergeAllAndTodayTypeCountVOV2(dateRangeList,todayList);
        }
        return dateRangeList;
    }


    @Override
    public List<TypeCountDTO> getSystemLogNum(ScreenTypeEnum screenTypeEnum) {
        //获取数据来源城市码表
        Map<String,String> systemNameMap = genericSysDataDictService.getSystemNameMap();
        List<TypeCountDTO> result = new ArrayList<>();
        BaseSearchVO baseSearchVO = CKPartitionUtil.setScreenTimeByBaseSearchVO(screenTypeEnum);
        List<E2MachineLearningLogView>  e2MachineLearningLogs = e2MachineLearningLogViewMapper.getSystemLogNum(baseSearchVO);

        if(CollectionUtil.isEmpty(e2MachineLearningLogs)){
            return  result;
        }

        e2MachineLearningLogs.stream().forEach(e2->{
            TypeCountDTO dto = new TypeCountDTO();
            dto.setKey(ObjectUtil.isEmpty(e2.getSystemCode())? "":systemNameMap.get(e2.getSystemCode()));
            dto.setNum(ObjectUtil.isEmpty(e2.getNum())? 0L:e2.getNum());
            result.add(dto);
        });
        return  result;
    }

    @Override
    public List<TypeCountDTO> modelTwiter(ScreenTypeEnum screenTypeEnum) {
        List<TypeCountDTO> result = new ArrayList<>();

        BaseSearchVO baseSearchVO = CKPartitionUtil.setScreenTimeByBaseSearchVO(screenTypeEnum);

        List<E2MachineLearningLogView> e2MachineLearningLogs = e2MachineLearningLogViewMapper.getModelTwiterLogNum(baseSearchVO);

        if(CollectionUtil.isEmpty(e2MachineLearningLogs)){
            return  result;
        }
        //获取E2日志总量
        Long countSum = countSum(screenTypeEnum);
        e2MachineLearningLogs.stream().forEach(e2->{
            TypeCountDTO dto = new TypeCountDTO();
            dto.setKey(e2.getModelName()+"("+e2.getModelCode()+")");
            dto.setNum(e2.getNum());
            dto.setPercent(CalculateUtil.myPercent1(e2.getNum(),countSum));
            result.add(dto);
        });
        return  result;
    }


    private long getTodayCount(){
        Integer today =  CKPartitionUtil.getTodayCKPartition();
        LambdaQueryWrapper<E2MachineLearningLogView> lambdaQueryWrapper = new LambdaQueryWrapper();
        lambdaQueryWrapper.eq(E2MachineLearningLogView::getDay,today);
        return e2MachineLearningLogViewMapper.selectCount(lambdaQueryWrapper);
    }

}
