package com.eversec.antivpn.intelligence.entity.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.eversec.stark.generic.common.infra.mybatis.plus.BaseDO;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * vpn情报
 * </p>
 * <p>
 * 持久化对象，此对象与数据库属性一一对应，被Entity对象继承，扩展属性写在Entity对象中
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-31
 */
@Getter
@Setter
@TableName("vpn_intelligence")
@Schema(name = "VpnIntelligencePO", description = "vpn情报")
public class VpnIntelligencePO extends BaseDO {

    private static final long serialVersionUID = 1L;

    @Schema(description = "跨境通信情报库版本格式：主版本号.副版本号，本规范主版本号为1，副版本号为0，即1.0。")
    private String version;

    @Schema(description = "跨境通信情报库下发执行指令ID。当部侧下发时由部侧系统控制惟一；当企业侧系统上报时企业侧内惟一。")
    private Long commandId;

    @Schema(description = "规则id，14位")
    private Long ruleId;

    @Schema(description = "运营商编码，见附录F.2")
    private String comCode;

    @Schema(description = "省级区域编号，见附录F.4")
    private Long provinceId;

    @Schema(description = "省平台业务系统标识。省平台部署：当前省平台业务系统标识；集团部署：省平台业务系统标识")
    private String systemCode;

    @Schema(description = "监测网络类型code，见附录F.3。企业侧上报时必填。部侧下发时可选填，当部侧下发为空时表示所有网络类型生效")
    private Integer networkBusinessId;

    @Schema(description = "情报类型code： 1-VPN服务地址,例:提供VPN下载、注册、登录、购买、登出等各类访问地址和链接； 2-VPN 跨境服务地址,例：海外域名通道或海外IP通道地址； 3-模型研判； 4-样本库； 999-其他。 当操作类型为0、1、2时必填，当操作类型为3、4、5时选填。 ")
    private Integer typeId;

    @Schema(description = "情报库ID，当部侧系统下发时必填；企业侧系统上报、全量库下发时可不填写")
    private Long vpnId;

    @Schema(description = "Vpn名称或文件名称，当操作类型为0、1、2时填写VPN名称，当操作类型为3时填写的是全量跨境通信情报库文件名；当操作类型为4、5时填写的是模型或样本文件名，多个用|线分隔；当填写Vpn名称时，优先使用中文名称，没有中文名称时使用英文名称。")
    private String vpnName;

    @Schema(description = "域名。VPN活动所使用的域名地址。")
    private String vpnDomain;

    @Schema(description = "IP地址,VPN活动所使用的IP地址；IPv4使用点分表示法，IPv6使用冒分小写0位压缩表示法。")
    private String vpnIp;

    @Schema(description = "URL地址,VPN活动所使用的URL地址，采用base64编码后的URL。")
    private String vpnUrl;
    @Schema(description = "URL地址,VPN活动所使用的URL地址，未编码。")
    private String vpnUrlDecode;

    @Schema(description = "报文（最少支持snort规则，版本语法为2.9)，采用base64编码")
    private String vpnMessage;

    @Schema(description = "报文（最少支持snort规则，版本语法为2.9)，未编码")
    private String vpnMessageDecode;

    @Schema(description = "取证链接,包含VMess、VLESS、Shadowsocks、Socks、Trojan等链接地址。企业侧系统上报时根据实际情况填写。")
    private String vpnLink;

    @Schema(description = "匹配内容类型。1-域名；2-IP；3-URL；4-报文（最少支持snort规则，版本语法为2.9)；5-VPN协议（具体参见F.5节）；999-其他。")
    private Integer contentType;

    @Schema(description = "匹配内容。当类型为3和4时采用base64编码后结果。当类型为5时填写的是VPN协议编号。")
    private String vpnContent;

    @Schema(description = "端口")
    private String vpnPort;

    @Schema(description = "vpn机场编码")
    private String vpnAirportCode;

    @Schema(description = "vpn机场编码")
    private String vpnAirportName;

    @Schema(description = "vpn机场名称。在保存时冗余，用于E1话单回填")
    private String vpnSoftwareCodes;

    @Schema(description = "vpn软件编码，数组存储。在保存时冗余，用于E1话单回填")
    private String vpnSoftwareNames;

    @Schema(description = "所属国家")
    private String vpnCountry;

    @Schema(description = "vpn协议编码")
    private String vpnProtocolCode;

    @Schema(description = "vpn协议编码（eversec）")
    private String vpnProtocolEversecCode;

    @Schema(description = "应用协议编码")
    private String applicationProtocolCode;

    @Schema(description = "创建时间,生成该文件的时间")
    private String timeStamp;

    @Schema(description = "情报来源，FULL：全量下发。INCREASE：增量下发。REPORT：省端上报。INPUT：页面录入")
    private String source;

    @Schema(description = "情报来源,存储云信安的source")
    private String vpnSource;


    @Schema(description = "上报状态，REPORTED：未上报，TO_BE_REPORTED：待上报")
    private String reportStatus;

    @Schema(description = "上报时间，上报到部侧时间")
    private LocalDateTime reportDatetime;

    @Schema(description = "下发状态，DISTRIBUTED：已下发，TO_BE_DISTRIBUTED：待下发")
    private String distributeStatus;

    @Schema(description = "下发时间，下发到省端")
    private LocalDateTime distributeDatetime;

    @Schema(description = "取证文件名称,当isUploadFile为0时必填。上报方法参见7.2.2章节。")
    private String attachMent;

}