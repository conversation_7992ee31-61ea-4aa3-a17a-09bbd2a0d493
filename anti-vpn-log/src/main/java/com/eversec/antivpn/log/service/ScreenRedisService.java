package com.eversec.antivpn.log.service;

import cn.hutool.core.util.ObjectUtil;
import com.eversec.antivpn.redis.RedisConstants;
import com.eversec.antivpn.redis.RedisService;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * 大屏数据缓存
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/25 16:41
 */
@Service
@Slf4j
public class ScreenRedisService extends RedisService {

    @Value("${app.cache.seconds:600}")
    private Long cacheLiveSeconds;

    private static final String VERIFY_CODE_CACHE_PREFIX = ":SCREEN:";

    /**
     * 根据指定的方法名称和查询时间分区获取缓存
     * @param methodAndTimePartition
     * @return
     */
    public Object  getScreenResult(String methodAndTimePartition)  {

        log.info("获取缓存数据,methodAndTimePartition = "+methodAndTimePartition);
        String wrapperKey = this.wrapperKey(RedisConstants.REDIS_CACHE_FUNCTION,VERIFY_CODE_CACHE_PREFIX, methodAndTimePartition );
        Object result = this.get(wrapperKey);
        if(ObjectUtil.isNotEmpty(result)){
            return  this.get(wrapperKey);
        }
        return null;
    }
    /**
     * 根据指定的方法名称和查询时间分区设置缓存
     * @param methodAndTimePartition
     * @return
     */
    public void setScreenResult(String methodAndTimePartition, Object result)  {
        log.info("设置缓存数据:{},{}",methodAndTimePartition,result);
        String wrapperKey = this.wrapperKey(RedisConstants.REDIS_CACHE_FUNCTION,VERIFY_CODE_CACHE_PREFIX, methodAndTimePartition );
        this.set(wrapperKey, result, cacheLiveSeconds, TimeUnit.SECONDS);
        log.info("设置缓存数据:"+result);
    }


}
