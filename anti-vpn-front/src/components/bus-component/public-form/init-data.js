import { formatTime } from '@/utils/time'
// 初始化数据集合
export const initData = () => {
  return {
    input: '', // input输入框
    select: '', // 单选select
    'check-select': [], // 复选select
    'date-picker': '', // 日期选择器
    'date-time': '', // 时间日期选择器
    textarea: '', // 文本域
    'radio-group': '', // 单选框
    checkbox: [], // 复选框
    'input-btn': '', // input输入框，带按钮
    'input-num': null, // input输入框number类型
    'input-number': null, // input输入框number类型
    img: '', // 单张图片显示
    'date-picker-range': [], // 日期范围选择器
    'date-time-range': [], // 时间范围选择器
    cascader: [], // 级联选择器
    switch: false, // 开关
    'dic-select': '', // 字典下拉 select
    'shortcuts-time-picker': ['', ''], // 时间范围选择
    'text-editor': null // 富文本编辑器
  }
}
// 初始化数据方法集合
export const initDataFun = (funName, type) => {
  switch (funName) {
    // 当前时间
    case 'hoje':
      return formatTime(new Date(), type)
  }
  // return {
  //
  // }
}
