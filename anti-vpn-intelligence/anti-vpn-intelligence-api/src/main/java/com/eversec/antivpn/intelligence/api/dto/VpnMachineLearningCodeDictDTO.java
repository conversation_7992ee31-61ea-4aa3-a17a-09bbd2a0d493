package com.eversec.antivpn.intelligence.api.dto;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * DTO
 * </p>
 * <p>
 * api接口数据传输对象（用于save、update接收参数，list、page、find返回对象等）
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-21
 */
@Getter
@Setter
@Schema(name = "VpnMachineLearningCodeDictDTO", description = "")
public class VpnMachineLearningCodeDictDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "主键-雪花")
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    @Schema(description = "模型及样本生成时间。模型及样本生成时间，采用yyyy-MM-dd HH:mm:ss格式。")
    private String timeStamp;

    @Schema(description = "模型协议")
    private String protocolType;

    @Schema(description = "模型中文名称")
    private String modelChName;

    @Schema(description = "模型编码")
    private String modelCode;

    @Schema(description = "模型英文名称")
    private String modelName;

    @Schema(description = "模型版本")
    private String modelVersion;

    @Schema(description = "关联vpn名称")
    private String vpnName;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    @Schema(description = "跨境通信情报库版本格式：主版本号.副版本号，本规范主版本号为1，副版本号为0，即1.0。")
    private String version;

    @Schema(description = "运营商编码，见附录F.2")
    private String comCode;

    @Schema(description = "省级区域编号，见附录F.4")
    private Long provinceId;

    @Schema(description = "省平台业务系统标识")
    private String systemCode;

    @Schema(description = "监测网络类型code，见附录F.3。企业侧上报时必填。部侧下发时可选填，当部侧下发为空时表示所有网络类型生效")
    private Integer networkBusinessId;

    @Schema(description = "文件类型: 1:机器学习监测模型； 2:样本文件(格式类型只为PCAP包)。")
    private Integer fileType;

    @Schema(description = "压缩包文件名称。机器学习监测模型及样本压缩包文件名称，具体命名及上报方法见7.2.3章节")
    private String fileName;

    @Schema(description = "压缩包文件包含内容文件名称。机器学习监测模型及样本文件文件包含内容文件名称，具体命名及上报方法见7.2.3章节")
    private String contentFileName;

    @Schema(description = "压缩包文件包含内容文件格式。压缩包文件包含内容文件格式，例：.pcap")
    private String contentFilesuffix;

    @Schema(description = "模型、样本文件ID")
    private Long modelFileId;

}