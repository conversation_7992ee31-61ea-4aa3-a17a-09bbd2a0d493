<template>
  <div class="areaList mt20">
    <ul class="areaUl" v-if="listD.length">
      <li v-for="(item, i) in listD" :key="'area' + item.name + i">
        <div class="idx">{{ i + 1 }}</div>
        <div class="name" :title="item.name">
          {{ item.name }}
          <div class="linebox">
            <div class="line" :style="{ width: item.percent }"></div>
          </div>
        </div>
        <div class="value">{{ item.value | num2Ft }}</div>
        <div class="percent">{{ item.percent }}</div>
      </li>
    </ul>
    <empty color="#fff" v-else />
  </div>
</template>
<script>
export default {
  props: {
    lists: {
      type: Array,
      default: () => []
    }
  },
  watch: {
    lists: {
      handler(val) {
        this.listD = val;
        // this.listD = [
        //   { name: "北京市", value: 2000 },
        //   { name: "河北省", value: 1000 },
        //   { name: "河南省", value: 1000 },
        //   { name: "江苏省", value: 1000 },
        //   { name: "黑龙江", value: 1000 },
        //   { name: "黑龙江", value: 1000 }
        // ]
      },
      immediate: true
    }
  },
  data() {
    return {
      listD: []
    };
  },
};
</script>

<style scoped lang="scss">
.areaList {
  height: 215px;
  // height: 176px;
  color: #fff;
  overflow-y: auto;
  .areaUl {
    > li {
      &:not(:last-child) {
        margin-bottom: 7px;
      }
      > div {
        text-align: center;
        background: rgba(205, 146, 80, 0.07);
        &:not(:last-child) {
          margin-right: 4px;
        }
      }
      display: flex;
      height: 37px;
      line-height: 37px;
      .idx {
        width: 37px;
      }
      .name {
        width: 145px;
        overflow: hidden;
        text-align: left;
        padding: 0 10px;
        position: relative;
        line-height: 30px;
        .linebox {
          width: calc(100% - 20px);
          position: absolute;
          bottom: 5px;
          left: 10px;
          .line {
            height: 2px;
            background: #4cdafe;
            width: calc(100% - 20px);
          }
        }
      }
      .value {
        width: 135px;
      }
      .percent {
        width: 105px;
        color: #4cdafe;
      }
      &:nth-of-type(2n-1) {
        > div {
          background: rgba(76, 218, 254, 0.07);
        }
      }
      &:nth-of-type(1),
      &:nth-of-type(2) {
        > div {
          // background: rgba(76, 218, 254, 0.07);
        }
        .percent {
          color: #ce914e;
        }
        .name {
          .linebox .line {
            background: #cd9250;
          }
        }
      }
    }
  }
}
</style>
