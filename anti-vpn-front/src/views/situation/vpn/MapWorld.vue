<template>
  <div class="mapWorld">
    <div class="mapbg"></div>
    <e-charts
      map="世界中国"
      key="世界中国"
      type="map"
      empty-type="2"
      empty-color="#e4fdff"
      :options="options"
      @click="mapClick"
    ></e-charts>
  </div>
</template>

<script>
import { cloneDeep } from "lodash";
import { nameMap, worldSecurity } from "@/assets/mapStatic.js";
import { mapOpt2, mapOpt3 } from "./mapOption.js";

export default {
  name: "MapWorldNote",
  props: {},
  data() {
    return {
      lists: [],
      options: {},
      dianImg: require("./img/map_dian.png"),
      kuangbg: require("./img/kuangbg.png"),
      kuangbg2: require("./img/kuangbg2.png")
    };
  },
  mounted() {
    // this.drawMap();
  },
  methods: {
    mapClick(e) {
      // console.log("地图点击", e);
      // if (e.data) {
      //   // 有数据
      //   this.$emit("updateDetail", e.data);
      // } else {
      //   // 无数据
      //   this.$emit("closeDetail");
      // }
    },
    drawMap(d=[]) {
      // d = [
          // { name: "中国", value: 100, value2: 100 },
          // { name: "美国", value: 200,  value2: 100 },
          // {
          //   name: "日本", value: 500, value2: 100 },
          // { name: "韩国", value: 500, value2: 100 },
          // { name: "加拿大", value: 500, value2: 100 },
        // ];
      if (d.length == 0) {
        this.lists = [{ name: "中国", value: 0, value2: 0 }]; // 默认给一条数据，地图可渲染
      } else {
        this.lists = d; 
      }
      
      this.options = mapOpt3(this.lists, this.dianImg, this.kuangbg2);
      console.log("map-options", this.options);
    }
  }
};
</script>

<style lang="scss" scoped>
.mapWorld {
  height: 580px;
  position: relative;
  margin-top: 30px;
  .echarts-panel {
    position: absolute;
    z-index: 2;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
  }
  .mapbg {
    background: url(./img/world_map.png) no-repeat -54px -5px;
    background-size: 1067px 577px;
    // background-size: 1129px 566px;
    width: 960px;
    height: 580px;
    position: relative;
    z-index: 2;
    margin: 0 auto;
  }

  ::v-deep .tooltipbox_world {
    background: url(./img/pop_bg.png) no-repeat;
    background-size: 100% 100%;
    height: 107px;
    width: 190px;
    font-size: 14px;
    padding: 8px 12px;
    line-height: 30px;
    .title {
      font-weight: bold;
    }
    .color_blue {
      color: #44c4ff;
    }
    .color_orange {
      color: #ffa74b;
    }
  }
}
</style>
