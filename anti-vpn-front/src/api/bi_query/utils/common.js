/**
 * 可视化组件类型
 */
export const ChartType = {
  Histogram: { value: "Histogram", label: "柱状图" },
  Bar: { value: "Bar", label: "条形图" },
  Line: { value: "Line", label: "折线图" },
  Biaxial: { value: "Biaxial", label: "组合图" },
  Area: { value: "Area", label: "面积图" },
  Pie: { value: "Pie", label: "饼图" },
  Ring: { value: "Ring", label: "环图" },
  Scatter: { value: "Scatter", label: "散点图" },
  Map: { value: "Map", label: "着色地图" },
  Scattermap: { value: "Scattermap", label: "标记地图" },
  Heatmap: { value: "Heatmap", label: "热力地图" },
  Attackmap: { value: "Attackmap", label: "LBS飞线地图" },
  Table: { value: "Table", label: "统计表格" },
  Tree: { value: "Tree", label: "树图" },
  Target: { value: "Target", label: "指标卡" },
  Custom: { value: "Custom", label: "个性化-模板图表" },
  Custom1: { value: "Custom1", label: "个性化-外链图表" },
  Custom2: { value: "Custom2", label: "个性化-扩展图表" }
};

/**
 * 元数据类型
 */
export const MetadataType = {
  Measure: 0,
  Dimension: 1
};

/**
 * 数据类型
 */
export const DataType = {
  Text: 0,
  Numerical: 1,
  Coordinate: 2,
  Date: 3,
  Blob: 4
};

/**
 * 跳转类型
 */
export const RedirectType = {
  Current: { label: "当前图表", value: "0" },
  Chart: { label: "其它图表", value: "2" },
  Dashboard: { label: "仪表盘", value: "1" },
  Iframe: { label: "第三方", value: "3" },
  EndlessDrilling: { label: "无极下钻", value: "4" },
  TimeDrilling: { label: "多粒度下钻", value: "5" }
};

export const CompatStyle = {
  Same: 0, // 相同
  Compatible: 1, // 兼容
  Incompatible: 2 // 不兼容
};
