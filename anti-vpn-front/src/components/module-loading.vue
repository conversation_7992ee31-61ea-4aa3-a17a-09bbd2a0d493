<template>
  <div class="module-loading" :style="mainStyle">
    <div class="site-loading">
      <div class="spinner">
        <div class="rect1" />
        <div class="rect2" />
        <div class="rect3" />
        <div class="rect4" />
        <div class="rect5" />
      </div>
      <p class="tip">
        页面加载中...
      </p>
    </div>
  </div>
</template>

<script>
export default {
  computed: {
    mainStyle() {
      return {
        height: window.globalConfig.pageAdaptToWindow ? '100%' : '600px'
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.module-loading {
  position: relative;
}
</style>
