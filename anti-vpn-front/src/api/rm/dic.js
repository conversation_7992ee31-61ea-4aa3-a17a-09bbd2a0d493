import httpRequest from '@/utils/httpRequest'
import serverProxy from '@/config/serverProxy'
let gateway = serverProxy.generic

export default {
  // 分页获取一级数据字典
  getList(o) {
    return httpRequest({
      url: gateway + '/sys-data-dict/pageType',
      params: o
    })
  },
  // 新增字典分类
  addDicType(o) {
    return httpRequest({
      url: gateway + '/sys-data-dict/saveType',
      method: 'post',
      data: o
    })
  },
  // 更新字典分类
  updateDicType(o) {
    return httpRequest({
      url: gateway + '/sys-data-dict/updateTypeById',
      method: 'post',
      data: o
    })
  },
  // 删除字典分类
  deleteDicType(ids) {
    return httpRequest({
      url: gateway + '/sys-data-dict/deleteTypeByIds/' + ids,
      method: 'delete'
    })
  },
  // 获取二级数据字典
  getListValue(o) {
    return httpRequest({
      url: gateway + '/sys-data-dict/page',
      params: o
    })
  },
  // 新增字典数据
  addDicVal(o) {
    return httpRequest({
      url: gateway + '/sys-data-dict/saveDict',
      method: 'post',
      data: o
    })
  },
  // 更新字典数据
  updateDicVal(o) {
    return httpRequest({
      url: gateway + '/sys-data-dict/updateDictById',
      method: 'post',
      data: o
    })
  },
  // 删除字典数据
  deleteDicVal(ids) {
    return httpRequest({
      url: gateway + '/sys-data-dict/deleteDictByIds/' + ids,
      method: 'delete'
    })
  },
  // 根据类型查询字典数据（支持多类型）
  getDictList(dictTypes) {
    return httpRequest({
      url: gateway + `/sys-data-dict/dictList/${dictTypes}`
    })
  },
  // 获取所有字典数据
  getDictAllListTree() {
    return httpRequest({
      url: gateway + `/sys-data-dict/allListTree`
    })
  }
}
