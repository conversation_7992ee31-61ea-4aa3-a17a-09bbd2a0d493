/**
 * 字典中的图片类型
 */
export const IMG_TYPE = 'imgType'
/**
 * 图片类型集合
 */
export const IMAGETYPES = [
  '.xbm',
  '.tif',
  '.pjp',
  '.svgz',
  '.jpg',
  '.jpeg',
  '.ico',
  '.tiff',
  '.gif',
  '.svg',
  '.jfif',
  '.webp',
  '.png',
  '.bmp',
  '.pjpeg',
  '.avif'
]
export const valueType = [
  {
    label: '数字',
    val: 'numType'
  },
  {
    label: '字符串',
    val: 'stringType'
  },
  {
    label: '图片',
    val: 'imgType'
  },
  {
    label: 'json',
    val: 'jsonType'
  }
]
