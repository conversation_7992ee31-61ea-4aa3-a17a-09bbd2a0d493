package com.eversec.antivpn.support.province.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 省平台信息（企业测）DTO
 * </p>
 * <p>
 * api接口数据传输对象（用于save、update接收参数，list、page、find返回对象等）
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-11
 */
@Getter
@Setter
@Schema(name = "ProvincePlatformStatusDTO", description = "省平台状态")
public class ProvincePlatformStatusDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "id")
    private Long id;

    @Schema(description = "省平台名称")
    private String name;

    @Schema(description = "运营商编码")
    private String comCode;

    @Schema(description = "省级区域编号")
    private Long provinceId;

    @Schema(description = "省平台业务系统标识")
    private String systemCode;

    @Schema(description = "网络类型编码，数组存储 ")
    private String networkBusinessIds;

    @Schema(description = "网络类型编码，集合 ")
    private List<Integer> networkBusinessIdList;

    @Schema(description = "当前状态。监测网络类型的系统状态：0：正常；1：异常；2：未覆盖；9：其他。")
    private Integer currentState;

    @Schema(description = "创建时间,生成该文件的时间")
    private String timeStamp;

    @Schema(description = "创建时间")
    private Date createDatetime;


}