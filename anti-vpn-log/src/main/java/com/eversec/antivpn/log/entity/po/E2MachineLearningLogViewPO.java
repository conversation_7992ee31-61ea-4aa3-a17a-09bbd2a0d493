package com.eversec.antivpn.log.entity.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.eversec.stark.generic.common.infra.mybatis.plus.BaseDO;
import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 * <p>
 * 持久化对象，此对象与数据库属性一一对应，被Entity对象继承，扩展属性写在Entity对象中
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-18
 */
@Getter
@Setter
@TableName("e2_machine_learning_log_view")
@Schema(name = "E2MachineLearningLogViewPO", description = "")
public class E2MachineLearningLogViewPO {

    private static final long serialVersionUID = 1L;

    @Schema(description = "日期分区")
    private Long day;

    @Schema(description = "小时")
    private Integer hour;

    @Schema(description = "月")
    private Long month;

    @Schema(description = "周")
    private Integer week;

    @Schema(description = "所属省份")
    private String provinceId;

    @Schema(description = "省所属市")
    private String cityId;

    private String srcIp;

    private String srcProvinceId;

    private String destIp;

    private String destCountry;

    @Schema(description = "流量类型")
    private Long trafficType;

    private String modelCode;

    private String modelName;

    @Schema(description = "传输层协议类型")
    private Long protocolType;

    @Schema(description = "结果置信度")
    private Integer rate;

    @Schema(description = "日志数量")
    private Long logCount;

    @Schema(description = "网络类型")
    private Integer networkBusinessId;

    private String user;

    @Schema(description = "所属市")
    private Long logCityId;

    @Schema(description = "日志所属系统")
    private String systemCode;


}