<template>
  <div class="box_con" style="height:240px; padding-top:0px">
    <el-table :data="tableData" style="width: 100%" height="270">
      <el-table-column type="index" label="序号" width="50" align="center" />
      <el-table-column
        prop="srcIp"
        label="使用者IP"
        width="120"
        show-overflow-tooltip
      />
      <el-table-column
        prop="vpnAirportName"
        label="使用的VPN服务商"
        width="130"
        show-overflow-tooltip
      />
      <el-table-column
        prop="resourceIp"
        label="使用的资源"
        width="100"
        show-overflow-tooltip
      />
      <el-table-column
        prop="vpnSoftwareName"
        label="使用的软件"
        width="100"
        show-overflow-tooltip
      />
      <el-table-column
        prop="vpnSoftwareProtocol"
        label="使用的协议"
        width="100"
        show-overflow-tooltip
      />
      <el-table-column
        prop="province"
        label="所在地"
        width="80"
        show-overflow-tooltip
      />
      <el-table-column
        prop="destCountry"
        label="访问目的地"
        width="95"
        show-overflow-tooltip
      />
      <el-table-column
        prop="time"
        min-width="140"
        label="访问时间"
        show-overflow-tooltip
      >
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
export default {
  props: {
    tableD: {
      type: Array,
      default: () => []
    }
  },
  watch: {
    tableD: {
      handler(v) {
        this.tableData = v;
      },
      immediate: true
    }
  },
  data() {
    return {
      tableData: [
        // {
        //   destCountry: 0,
        //   provinceId: 0,
        //   resourceIp: "10.0.0.0",
        //   srcIp: "10.0.0.0",
        //   vpnAirportCode: null,
        //   vpnSoftwareName: "软件名称0",
        //   vpnSoftwareProtocol: "",
        //   time: ""
        // }
      ]
    };
  }
};
</script>

<style scoped lang="scss">
.box_con {
  // height: 254px;
  ::v-deep.el-table {
    background: transparent;
    border-top: none;
    &::before {
      background: transparent;
    }
    .el-table__header-wrapper {
      thead {
        > tr {
          background: transparent;
          > th {
            border-color: #10284a;
            background: transparent;
            color: #fff;
            padding: 0 3px;
            font-weight: normal;
            .cell {
              padding: 0;
            }
          }
        }
      }
    }
    .el-table__body-wrapper {
      .el-table__row {
        color: #fff;
        background: rgba(8, 39, 79, 0.6);

        > td {
          border-top: 1px solid #10284a;
          border-bottom: none;
          height: 36px;
          line-height: 36px;
          padding: 0 3px;
          .cell {
            padding: 0;
          }
          &:nth-of-type(4) {
            color: #90cee5;
          }
        }
        &:nth-of-type(2n) {
          background: rgba($color: #02182f, $alpha: 0.6);
          > td {
            // &:nth-of-type(4),
            &:nth-of-type(5),
            &:nth-of-type(6),
            &:nth-of-type(7),
            &:nth-of-type(8),
            &:nth-of-type(9) {
              color: #e4666a;
            }
          }
        }
        &:nth-of-type(2n-1) {
          > td {
            // &:nth-of-type(4),
            &:nth-of-type(5),
            &:nth-of-type(6),
            &:nth-of-type(7),
            &:nth-of-type(8),
            &:nth-of-type(9) {
              color: #8cc3d8;
            }
          }
        }
        &:hover {
          > td {
            background: rgba($color: #02182f, $alpha: 0.6);
            color: #fff;
          }
        }
      }
    }
  }
}
</style>
