package com.eversec.antivpn.intelligence.service;

import com.eversec.antivpn.intelligence.api.dto.SmsCodeRequestDTO;
import com.eversec.antivpn.intelligence.api.dto.SmsCodeResponseDTO;
import com.eversec.antivpn.intelligence.api.dto.SmsCodeVerifyRequestDTO;

/**
 * 短信验证码服务接口
 * 
 * <AUTHOR>
 * @since 2025-01-12
 */
public interface ISmsCodeService {

    /**
     * 发送短信验证码
     * 
     * @param request 发送请求
     * @return 发送结果
     */
    SmsCodeResponseDTO sendCode(SmsCodeRequestDTO request);

    /**
     * 验证短信验证码
     * 
     * @param request 验证请求
     * @return 验证结果
     */
    SmsCodeResponseDTO verifyCode(SmsCodeVerifyRequestDTO request);

    /**
     * 清理过期验证码
     */
    void cleanExpiredCodes();

}
