<template>
  <div class="box_con" style="height:260px; padding-top:10px">
    <!-- <dl class="dlbox">
      <dt>
        <ul class="itemUl">
          <li>序号</li>
          <li>VPN服务商</li>
          <li>用户量</li>
          <li>提供服务次数</li>
          <li>资源节点</li>
          <li>发现时间</li>
        </ul>
      </dt>
      <dd>
        <ul
          class="itemUl"
          v-for="(item, i) in tableData"
          :key="item.vpnServiceName + i"
        >
          <li>{{ i + 1 }}</li>
          <li>{{ item.vpnServiceName }}</li>
          <li class="customerNum">{{ item.customerNum }}</li>
          <li class="customerNum">{{ item.serviceTimes }}</li>
          <li class="customerNum">{{ item.resourceNodeNum }}</li>
          <li class="customerNum">{{ item.createDateTime }}</li>
        </ul>
      </dd>
    </dl> -->

    <el-table :data="tableData" style="width: 100%" height="285">
      <el-table-column type="index" label="序号" width="70" align="center" />
      <el-table-column prop="vpnServiceName" label="VPN服务商" width="180" show-overflow-tooltip />
      <el-table-column prop="customerNum" label="用户量" width="180" />
      <el-table-column prop="serviceTimes" label="提供服务次数"/>
      <el-table-column prop="resourceNodeNum" label="资源节点"/>
      <el-table-column prop="time" min-width="150" label="发现时间">
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import vpnApi from "./vpnApi.js";
import { formatTime } from "@/utils/time";
export default {
  // props: {
  //   title: {
  //     type: String,
  //     default: ''
  //   }
  // },
  data() {
    return {
      tableData: [
        // {
        //   vpnServiceName: "WgetCloud 全球加速机场",
        //   customerNum: 100,
        //   serviceTimes: 200,
        //   resourceNodeNum: 2321,
        //   createDateTime: "2023-08-23"
        // },
      ]
    };
  },
  methods: {
    init(p) {
      vpnApi.theNewestVpnService(p).then(res => {
        console.log("mid2--", res);
        this.tableData = res.map(el => {
          return {
            ...el,
            time: formatTime(el.createDateTime)
          }
        });
      });
    }
  }
};
</script>

<style scoped lang="scss">
.box_con {
  // height: 254px;
  ::v-deep.el-table {
    background: transparent;
    border-top: none;
    &::before {
      background: transparent;
    }
    .el-table__header-wrapper {
      thead {
        > tr {
          background: transparent;
          > th {
            border-color: #10284a;
            background: transparent;
            color: #fff;
          }
        }
      }
    }
    .el-table__body-wrapper {
      .el-table__row {
        color: #fff;
        background: rgba(8, 39, 79, 0.6);

        > td {
          border-top: 1px solid #10284a;
          border-bottom: none;
          height: 40px;
          line-height: 40px;
          padding: 0;
        }
        &:nth-of-type(2n) {
          background: rgba($color: #02182f, $alpha: 0.6);
          > td {
            &:nth-of-type(3),
            &:nth-of-type(4),
            &:nth-of-type(5),
            &:nth-of-type(6) {
              color: #e4666a;
            }
          }
        }
        &:hover {
          > td {
            background: rgba($color: #02182f, $alpha: 0.6);
            color: #fff;
          }
        }
      }
    }
  }
  .dlbox {
    width: 100%;
    line-height: 40px;
    .itemUl {
      display: flex;
      > li {
        &:nth-of-type(1) {
          width: 60px;
          text-align: center;
        }
        &:nth-of-type(2) {
          width: 24%;
          // text-align: center;
        }
        &:nth-of-type(3) {
          width: 18%;
        }
        &:nth-of-type(4) {
          width: 18%;
        }
        &:nth-of-type(5) {
          width: calc(20% - 60px);
        }
        &:nth-of-type(6) {
          width: 20%;
        }
      }
    }

    dd {
      max-height: 253px;
      overflow-y: auto;
      .itemUl {
        border-top: 1px solid #10284a;
        margin-top: 1px;
        .customerNum {
          color: #8ec8de;
        }
        background: rgba(8, 39, 79, 0.6);
        &:nth-of-type(2n) {
          background: rgba($color: #02182f, $alpha: 0.6);
          .customerNum {
            color: #e4666a;
          }
        }
      }
    }
  }
}
</style>
