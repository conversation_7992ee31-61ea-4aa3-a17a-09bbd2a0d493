package com.eversec.antivpn.intelligence.feign.smart;

import com.eversec.antivpn.intelligence.feign.smart.dto.SmartUpgradeRequestDTO;
import com.eversec.antivpn.intelligence.feign.smart.dto.SmartUploadResponseDTO;
import com.eversec.framework.feignboot.conf.EversecFeignConfiguration;
import com.eversec.framework.feignboot.decoder.AutoUnpack;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;

import java.io.File;
import java.net.URI;

/**
 * smart 接口
 * @see feign.auth.BasicAuthRequestInterceptor
 * <AUTHOR>
 * @version 1.0
 * @date 2023-08-08 11:57
 */
@Validated
@Tag(name = "vpn情报")
@FeignClient(value = "smart-service",
		path = SmartApi.PATH,
		url = "EMPTY",
		contextId = "com.eversec.antivpn.intelligence.feign.smart.SmartApi",
		configuration = { EversecFeignConfiguration.class})
public interface SmartApi {

	String PATH = "/api/feature";

	/**
	 * 特征库文件上传
	 * 通过该接口进行上传规则升级文件，smart将对文件的格式进行校验，并返回该文件的对应id，待上传完毕后，上层应用持有该文件id进行升级任务的创建和下发。
	 *
	 * @param featureFile 特征库文件。文件格式暂定支持txt、dat、zip、rar、gz文件
	 * @param md5 文件对应的md5值
	 * @param fileType 黑名单文件特征库：tz.evt
	 * 黑名单URL特征库：zk.evt
	 * 封堵策略库：fd.prc
	 * 黑名单IP特征库：ip.evt
	 * 运行参数配置库：config.list
	 * 全网恶意软件url过滤白名单：white.url
	 * 全网恶意软件md5过滤白名单：white.files
	 * 疑似筛选规则特征库：ys.evt
	 * 两防特征库：st.evt
	 * MTX规则：mtx.evt
	 * 全网统一特征库：tz.rule
	 * PACP特征库：pcap.evt
	 * SNORT特征库：snort.evt
	 * 流量筛选规则库：mdf_filter.rule
	 * 具体文件内容格式参考（附录三：特征格式说明）
	 * 德特赛维全流量探针的特征库参考附录三中的德特赛维全流量探针独有特征库格式
	 * @param version 下发更新的版本。格式为：
	 * 特征库版本号标识_当前时间（yyyyMMddHHmmss），升级包统一以tar.gz方式进行压缩，特征库升级包名称为：特征库版本号标识_yyyyMMddHHmmss.tar.gz，如特征库升级包名称为snort_evt_20210817182033.tar.gz，则其特征库版本为snort_evt_20210817182033。特征库升级定义详见附件六。
	 * @param num 本地对应特征库特征数量
	 * @param incremental 1 ：表示本次请求中的特征信息为增量下发（当前版本暂不支持）；
	 * 0 ：表示本次请求中特征信息为全量下发。
	 * @param content 描述本次升级的主要内容
	 * @param remarks 预留字段，描述升级文件的额外信息
	 * @return 特征库的id、唯一标识，该标识主要用于特征库下发，相同的文件会返回相同的fileId
	 */
	@AutoUnpack
	@PostMapping(value =  PATH + "/upload", consumes = "multipart/form-data")
	SmartUploadResponseDTO upload(URI uri,
			@RequestHeader(value = "Authorization") String Authorization,
			@RequestPart("featureFile") final File featureFile,
			@RequestParam(value = "md5") String md5,
			@RequestParam(value = "fileType") String fileType,
			@RequestParam(value = "version") String version,
			@RequestParam(value = "num") Integer num,
			@RequestParam(value = "incremental") Integer incremental,
			@RequestParam(value = "content") String content,
			@RequestParam(value = "remarks") String remarks
			);

	/**
	 *
	 * 特征规则全量下发
	 * 通过该接口进行规则升级操作。通过规则库上传接口，拿到smart返回的升级文件id，持有该id进行升级任务创建和下发。
	 *
	 *
	 * @return true表示请求成功，false表示请求失败
	 */
	@AutoUnpack
	@PostMapping(PATH + "/upgrade")
	Boolean upgrade(URI uri,
			@RequestHeader(value = "Authorization") String Authorization,
			@RequestBody SmartUpgradeRequestDTO requestDTO);


}
