package com.eversec.antivpn.log.service.impl;


import com.alibaba.fastjson2.JSONObject;
import com.eversec.antivpn.log.service.DecryptService;
import com.eversec.antivpn.log.util.FileDcrypt;
import com.eversec.antivpn.support.config.api.PlatformInterfaceInfoApi;
import com.eversec.antivpn.support.config.api.dto.PlatformInterfaceInfoDTO;
import com.eversec.stark.generic.sys.api.SysConfItemApi;
import com.eversec.stark.generic.sys.dto.SysConfItemDto;
import io.minio.GetObjectArgs;
import io.minio.MinioClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.eversec.stark.generic.sys.api.SysDataDictApi;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/12/26 16:37
 **/

@Service
@Slf4j
public class DecryptServiceImpl implements DecryptService {

    @Resource
    private SysConfItemApi sysConfItemApi;

    @Resource
    private PlatformInterfaceInfoApi platformInterfaceInfoApi;


    private Map<String, String> decryptInfo = new ConcurrentHashMap<>();

    private MinioClient minioClient;

    private String bucket;

    private String aesOffsets;

    private String aesKey;

    private String messageKey;

    private volatile Boolean isInit = false;

    @Override
    public String decryptFile(String fileName) {
        if(!isInit) init();
        String decryptContent;
        InputStream inputStream = null;
        try {
            GetObjectArgs getObjectArgs = GetObjectArgs.builder()
                    .bucket(bucket)
                    .object(fileName)
                    .build();
            inputStream = minioClient.getObject(getObjectArgs);
            decryptContent = FileDcrypt.doFileDcrypt(inputStream, aesOffsets, aesKey, messageKey);
        } catch (Exception e) {
            log.error("解析文件失败", e);
            throw new RuntimeException("解析文件失败");
        } finally {
            if(inputStream !=null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    log.error("inputStream流关闭失败", e);
                    throw new RuntimeException("inputStream流关闭失败");
                }
            }
        }
        return decryptContent;
    }

    public synchronized void init() {
        if(isInit) return;
        initMinioClient();
        initDecryptInfo();
        isInit = true;
    }

    private void initMinioClient() {
        SysConfItemDto sysConfItemDto = sysConfItemApi.getByKey("MINIO_CONFIG");
        if (sysConfItemDto == null) return;
        String itemValue = sysConfItemDto.getItemValue();
        if (itemValue == null || "".equals(itemValue)) return;
        JSONObject jsonObject = JSONObject.parseObject(itemValue);
        if (!jsonObject.containsKey("endpoint")) return;
        String endpoint = jsonObject.getString("endpoint");
        String accessKey = jsonObject.getString("accessKey");
        String secretKey = jsonObject.getString("secretKey");
        this.minioClient = MinioClient.builder()
                .endpoint(endpoint)
                .credentials(accessKey, secretKey)
                .build();
        this.bucket = jsonObject.getString("bucketName");
    }

    private void initDecryptInfo() {
        PlatformInterfaceInfoDTO platformInterfaceInfoDTO = platformInterfaceInfoApi.getCurrentPlatform();
        if (platformInterfaceInfoDTO == null) return;
        String southReceiveSecretKey = platformInterfaceInfoDTO.getSouthReceiveSecretKey();
        if (southReceiveSecretKey == null || "".equals(southReceiveSecretKey)) return;
        JSONObject jsonObject = JSONObject.parseObject(southReceiveSecretKey);
        aesOffsets = jsonObject.getString("aesOffsets");
        aesKey = jsonObject.getString("aesKey");
        messageKey = jsonObject.getString("messageKey");
    }

}
