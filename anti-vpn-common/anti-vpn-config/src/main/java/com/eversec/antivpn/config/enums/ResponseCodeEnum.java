package com.eversec.antivpn.config.enums;

import lombok.Getter;

/**
 * 部侧系统接口调用异常码;文件处理结果异常码
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023-07-31 14:38
 */
@Getter
public enum ResponseCodeEnum {

	处理完成(0, "处理完成"),
	文件解密失败(1, "文件解密失败"),
	文件校验失败(2, "文件校验失败"),
	文件解压缩失败(3, "文件解压缩失败"),
	文件格式异常(4, "文件格式异常"),
	文件内容异常(5, "文件内容异常（版本错误）"),
	文件内容异常1(51, "文件内容异常——上报类型错误"),
	文件内容异常2(52, "文件内容异常——节点/子节点长度错误"),
	文件内容异常3(53, "文件内容异常——节点/子节点类型错误"),
	文件内容异常4(54, "文件内容异常——节点/子节点内容错误"),
	文件内容异常5(55, "文件内容异常——节点/子节点缺漏"),
	其他异常(900, "其他异常（存在其他错误，需重新上报）"),

	;


	ResponseCodeEnum(int code, String message){
		this.code = code;
		this.message = message;
	}

	private int code;
	private String message;


}
