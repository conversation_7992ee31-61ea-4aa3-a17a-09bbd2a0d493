.cssicon {
  display: inline-block;
  height: 1em;
  width: 1em;
  font-size: 20px;
  box-sizing: border-box;
  text-indent: -9999px;
  vertical-align: middle;
  position: relative;
  z-index: 1;
}
.cssicon::before,
.cssicon::after {
  content: "";
  box-sizing: inherit;
  position: absolute;
  left: 50%;
  top: 50%;
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

.cssicon-close::before {
  width: 90%;
  border-top: 2px solid;
  -ms-transform: translate(-50%, -50%) rotate(45deg);
  transform: translate(-50%, -50%) rotate(45deg);
}
.cssicon-close::after {
  height: 90%;
  border-left: 2px solid;
  -ms-transform: translate(-50%, -50%) rotate(45deg);
  transform: translate(-50%, -50%) rotate(45deg);
}

.cssicon-arrow-up::before {
  height: 0.65em;
  width: 0.65em;
  border-style: solid;
  border-width: 2px 0 0 2px;
  -ms-transform: translate(-50%, -25%) rotate(45deg);
  transform: translate(-50%, -25%) rotate(45deg);
}
.cssicon-arrow-down::before {
  height: 0.65em;
  width: 0.65em;
  border-style: solid;
  border-width: 2px 0 0 2px;
  -ms-transform: translate(-50%, -75%) rotate(225deg);
  transform: translate(-50%, -75%) rotate(225deg);
}

.cssicon-search::before {
  width: 0.75em;
  height: 0.75em;
  border: 2px solid;
  border-radius: 50%;
  left: 5%;
  top: 5%;
  -ms-transform: translate(0, 0) rotate(45deg);
  transform: translate(0, 0) rotate(45deg);
}
.cssicon-search::after {
  width: 0.4em;
  border-top: 2px solid;
  left: 80%;
  top: 80%;
  -ms-transform: translate(-50%, -50%) rotate(45deg);
  transform: translate(-50%, -50%) rotate(45deg);
}

.cssicon-edit::before {
  border: 2px solid;
  width: 0.85em;
  height: 0.4em;
  -ms-transform: translate(-0.35em, -0.25em) rotate(-45deg);
  transform: translate(-0.35em, -0.25em) rotate(-45deg);
}
.cssicon-edit::after {
  border: 0.15em solid;
  border-right-color: transparent;
  border-top-color: transparent;
  left: 5%;
  top: 95%;
  -ms-transform: translate(0, -100%);
  transform: translate(0, -100%);
}
