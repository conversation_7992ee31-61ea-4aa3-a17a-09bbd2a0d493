rootProject.name = 'anti-vpn'

include ':anti-vpn-common:anti-vpn-config'
include ':anti-vpn-common:anti-vpn-util'
include ':anti-vpn-common:anti-vpn-code-generator'

// 通用域
include 'anti-vpn-supoort'
include 'anti-vpn-supoort:anti-vpn-supoort-api'

include ':anti-vpn-service'

include ':anti-vpn-intelligence'
include ':anti-vpn-intelligence:anti-vpn-intelligence-api'

include ':anti-vpn-log'
include ':anti-vpn-log:anti-vpn-log-api'

//
//findProject(':generic-file:generic-file-api')?.name = 'generic-file-api'
//findProject(':generic-sys:generic-sys-api')?.name = 'generic-sys-api'

