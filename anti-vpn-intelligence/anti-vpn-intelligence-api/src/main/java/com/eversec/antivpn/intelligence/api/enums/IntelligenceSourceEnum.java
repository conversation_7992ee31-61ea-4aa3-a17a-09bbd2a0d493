package com.eversec.antivpn.intelligence.api.enums;

import lombok.Getter;

/**
 * 情报来源
 *
 *
 */
@Getter
public enum IntelligenceSourceEnum {


    FULL(1, "全量下发"),
    INCREASE(2, "增量下发"),
    REPORT(3, "省端上报"),
    INPUT(4, "页面录入"),

    ;



    IntelligenceSourceEnum(Integer ruleIdPrefix, String desc){
        this.desc = desc;
        this.ruleIdPrefix = ruleIdPrefix;
    }

    /**
     * 规则id前缀
     * 规则 ruleId = ruleClass + ruleSubClass + ruleIdPrefix + xxx
     */
    private Integer ruleIdPrefix;
    private String desc;



}
