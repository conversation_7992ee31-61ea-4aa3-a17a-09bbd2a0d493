<template>
  <div class="areaList mt10">
    <Empty class="empty" v-if="isEmpty" color="#fff" />
    <ul class="areaUl" v-else>
      <li v-for="(item, i) in listD" :key="item.name + i">
        <div class="idx">{{ i + 1 }}</div>
        <div class="name">
          {{ item.name }}
          <div class="linebox">
            <div class="line" :style="{ width: item.percent + '%' }"></div>
          </div>
        </div>
        <div class="num">{{ item.value }}</div>
        <div class="percent">{{ item.percent + "%" }}</div>
      </li>
    </ul>
  </div>
</template>
<script>
import Empty from "@/components/empty.vue";
export default {
  props: {
    areaList: {
      type: Array,
    },
  },
  components: {
    Empty,
  },
  watch: {
    areaList: {
      handler(val) {
        this.listD = val;
        this.init();
      },
      immediate: true,
      deep: true,
    },
  },
  data() {
    return {
      listD: [],
      isEmpty: false,
    };
  },
  mounted() {
    // this.init();
  },
  methods: {
    init() {
      console.log(this.listD)
      let total = this.listD.reduce((n, cur) => n + cur.num, 0);
      this.listD.map(item=>{
        item.percent=(item.num/total*100).toFixed(2)
      })
      this.listD.length == 0 ? (this.isEmpty = true) : (this.isEmpty = false);
    },
  },
};
</script>

<style scoped lang="scss">
.areaList {
  height: 215px;
  color: #fff;
  overflow-y: auto;
  .areaUl {
    > li {
      margin-bottom: 7px;
      > div {
        text-align: center;
        background: rgba(205, 146, 80, 0.07);
        &:not(:last-child) {
          margin-right: 4px;
        }
      }
      display: flex;
      height: 37px;
      line-height: 37px;
      .idx {
        width: 37px;
      }
      .name {
        width: 135px;
        text-align: left;
        padding: 0 10px;
        position: relative;
        line-height: 30px;
        .linebox {
          width: calc(100% - 20px);
          position: absolute;
          bottom: 5px;
          left: 10px;
          .line {
            height: 2px;
            background: #4cdafe;
            width: calc(100% - 20px);
          }
        }
      }
      .num {
        width: 135px;
      }
      .percent {
        width: 115px;
      }
      &:nth-of-type(2n-1) {
        > div {
          background: rgba(76, 218, 254, 0.07);
        }
      }
      &:nth-of-type(1),
      &:nth-of-type(2) {
        > div {
          // background: rgba(76, 218, 254, 0.07);
        }
        .percent {
          color: #ce914e;
        }
        .name {
          .linebox .line {
            background: #cd9250;
          }
        }
      }
    }
  }
}
</style>
