package com.eversec.antivpn.support.knowledge.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.eversec.antivpn.config.enums.AntiVpnBusinessExceptionEnum;
import com.eversec.antivpn.config.enums.AntiVpnDataDictTypeEnum;
import com.eversec.antivpn.support.knowledge.api.KnowledgeAirportApi;
import com.eversec.antivpn.support.knowledge.api.dto.ImportDownloadRequest;
import com.eversec.antivpn.support.knowledge.api.dto.ImportResult;
import com.eversec.antivpn.support.knowledge.api.dto.KnowledgeAirportDTO;
import com.eversec.antivpn.support.knowledge.api.dto.KnowledgeBaseDTO;
import com.eversec.antivpn.support.knowledge.api.dto.KnowledgeBaseQueryDto;
import com.eversec.antivpn.support.knowledge.convertor.KnowledgeDtoConvertor;
import com.eversec.antivpn.support.knowledge.service.IKnowledgeBaseService;
import com.eversec.antivpn.support.knowledge.upload.ImportAirport;
import com.eversec.cloud.core.api.data.PageResult;
import com.eversec.cloud.core.common.utils.DownloadUtil;
import com.eversec.framework.webboot.common.exception.BusinessException;
import com.eversec.stark.generic.common.util.PageBuilder;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;


/**
 * <p>
 * 知识库-机场信息 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-12
 */
@RestController
@RequestMapping(KnowledgeAirportApi.PATH)
@AllArgsConstructor
@Slf4j
public class KnowledgeAirportController implements KnowledgeAirportApi {

    private final IKnowledgeBaseService service;

    private final KnowledgeDtoConvertor knowledgeDtoConvertor;


    @Override
    public void save(@Valid KnowledgeAirportDTO airportDTO) {
        if(ObjectUtil.isEmpty(airportDTO)){
            throw new BusinessException(AntiVpnBusinessExceptionEnum.CODE_10006);
        }
        airportDTO.setTypeKey(AntiVpnDataDictTypeEnum.AIRPORT_INFORMATION.name());
        service.save(knowledgeDtoConvertor.airporToDomain(airportDTO));
    }

    @Override
    public void updateById(@Valid KnowledgeAirportDTO airportDTO) {
        if(ObjectUtil.isEmpty(airportDTO)){
            throw new BusinessException(AntiVpnBusinessExceptionEnum.CODE_10006);
        }
        airportDTO.setTypeKey(AntiVpnDataDictTypeEnum.AIRPORT_INFORMATION.name());
        service.updateById(knowledgeDtoConvertor.airporToDomain(airportDTO));
    }

    @Override
    public void deleteByIds(List<Long> ids) {
        if(CollectionUtil.isEmpty(ids)){
            throw new BusinessException(AntiVpnBusinessExceptionEnum.CODE_10006);
        }
        service.deleteByIds(ids);
    }

    @Override
    public PageResult<KnowledgeAirportDTO> page(KnowledgeBaseQueryDto queryDto) {
        queryDto.setTypeKey(AntiVpnDataDictTypeEnum.AIRPORT_INFORMATION.name());
        PageResult<KnowledgeBaseDTO> pageResult = service.page(queryDto);
        return PageBuilder.convertPageResult(pageResult, knowledgeDtoConvertor::toAirportDto);
    }

    @Override
    public void downloadTemplate(HttpServletResponse response) throws IOException {
        DownloadUtil.download(response,
            "VPN服务商信息导入模板.xlsx",
            this.getClass().getClassLoader().getResourceAsStream("template/import_airport.xlsx"),
            DownloadUtil.APPLICATION_EXCEL_VALUE);
    }

    @Override
    public ImportResult importTemplate(Long userId, String userName, MultipartFile file) {
        if(ObjectUtil.isEmpty(file)){
            throw new BusinessException(AntiVpnBusinessExceptionEnum.CODE_10006);
        }
        return service.importTemplate( userId,  userName, file, ImportAirport.class,AntiVpnDataDictTypeEnum.AIRPORT_INFORMATION, ImportAirport.MUST_FILL);
    }

    @Override
    public Object importDownload(ImportDownloadRequest req) throws IOException {
        return service.importDownload(req,ImportAirport.class);
    }


}
