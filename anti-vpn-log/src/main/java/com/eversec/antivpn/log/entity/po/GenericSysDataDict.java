package com.eversec.antivpn.log.entity.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eversec.stark.generic.sys.dto.SysDataDictDto;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Map;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 *  字典表-实体类
 * </p>
 * <p>
 * 持久化对象，此对象与数据库属性一一对应，被Entity对象继承，扩展属性写在Entity对象中
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-25
 */
@Data
@TableName("generic_sys_data_dict")
@Schema(name = "GenericSysDataDict", description = "")
public class GenericSysDataDict {

    private static final long serialVersionUID = 1L;

    @Schema(description = "主键-雪花")
    private Long id;

    @Schema(description = "字典编码")
    private String enumKey;

    @Schema(description = "字典编码2")
    private String enumKey2;

    @Schema(description = "字典名称")
    private String enumVal;

    @Schema(description = "顺序")
    private Integer seq;

    @Schema(description = "字典类型")
    private String typeKey;

    @Schema(description = "上级字典编码")
    private String parentEnumKey;

    @Schema(description = "字段补充字段")
    private String enumValExtend;

    @Schema(description = "字典名称2")
    private String enumVal2;

    @Schema(description = "字典名称3")
    private String enumVal3;

    @Schema(description = "字典名称4")
    private String enumVal4;

    @Schema(description = "字典名称5")
    private String enumVal5;

    @Schema(description = "有效标识")
    private Boolean valid;

    @Schema(description = "创建人员")
    private String createUser;

    @Schema(description = "修改人员")
    private String updateUser;

    @Schema(description = "创建时间")
    private Date createDatetime;

    @Schema(description = "修改时间")
    private Date updateDatetime;
    @Schema(description = "删除标志")
    private Integer deleted;

    @com.baomidou.mybatisplus.annotation.TableField(exist = false)
    private Long num;

    @com.baomidou.mybatisplus.annotation.TableField(exist = false)
    private String keyType;
}