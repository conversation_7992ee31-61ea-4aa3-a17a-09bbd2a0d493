// 对数据源中的单表进行操作
import httpRequest from "@/utils/httpRequest";
import { encryptAES } from "./utils/des";
import { ChartDataBll } from "./utils/chartbll";
import { buildFilter } from "./utils/chartdataconvert";
import { getDicData } from "./utils/dicUtils";

// window.encryptAES = encryptAES;

const prefix = window.globalConfig.bi_query_useGateway ? "/everbi" : ""
const url = prefix + "/singletable/V2/table";

export default {
  // 直接通过数据源和表名查数据
  getList(o, cancel) {
    if (o.advancedFilter) o.advancedFilter = encryptAES(o.advancedFilter);
    return httpRequest({
      url: url + "/sec/get",
      method: "post",
      data: {
        dsId: window.globalConfig.bi_query_dsId,
        tblType: "TABLE",
        ...o,
        tableName: encryptAES(o.tableName)
      },
      cancel
    });
  },

  /**
   * 根据 chartId 获取配置 chartConf
   * @param {*} chartId 图表管理中图表的id，或仪表盘中图表的uuid
   * @param {*} dashId 仪表盘的id（可选）
   */
  getChartConfByChartId(chartId, dashId) {
    const bll = new ChartDataBll();
    return bll.getChartData(chartId, dashId);
  },

  /**
   * 根据 chartConf 获取数据，并可设置翻页参数
   * @param {*} chartConf BI中图表的配置
   * @param {*} extendParam 额外参数，如 { paging: 1, limit: 10, conditions: [], fields: [] }
   * @param {*} callback 成功回调
   * @param {*} error 失败回调
   * @param {*} transDic 是否翻译字典
   */
   getDataByChartConf(chartConf, extendParam, callback, error, transDic = true) {
    let tmp = {
      dsId: chartConf.dsId,
      tableName: chartConf.tblName,
      tblType: chartConf.tblType,
      paging: 1,
      limit: 10,
      fields: [],
      internalFilter: chartConf.innerFilter ? buildFilter(chartConf.innerFilter) : undefined,
      ...extendParam,
    };
    this.getList(tmp).then(data => {
      if (callback) {
        if (transDic) {
          // 获取字典
          let dicColumns = [];
          let dicCodes = [];
          chartConf.columns.forEach(item => {
            if (item.dicCode) {
              dicColumns.push(item);
              if (!dicCodes.includes(item.dicCode)) dicCodes.push(item.dicCode);
            }
          });
          if (dicColumns.length == 0) {
            callback(data);
            return;
          }

          getDicData(data.list, dicCodes, dicColumns, chartConf.columns, (list) => {
            data.list = list;
            callback(data);
          });
        } else {
          callback(data);
        }
      }
    }).catch(() => {
      if (error) error();
    });
  },

  //
  insert(o, cancel) {
    return httpRequest({
      url: url,
      method: "put",
      data: {
        dsId: o.dsId || window.globalConfig.bi_query_dsId,
        ...o,
        tableName: encryptAES(o.tableName)
      },
      cancel
    });
  },
  // 批量新增
  batchInsert(o, cancel) {
    let data = []
    if (Array.isArray(o)) {
      data = o.map(item => {
        return {
          ...item,
          dsId: item.dsId || window.globalConfig.bi_query_dsId,
          tableName: encryptAES(item.tableName)
        }
      })
    }
    return httpRequest({
      url: prefix + "/singletable/batchAdd",
      method: "put",
      data: data,
      cancel
    });
  },
  update(o, cancel) {
    return httpRequest({
      url: url + "?t=" + new Date().getTime(),
      method: "post",
      data: {
        dsId: o.dsId || window.globalConfig.bi_query_dsId,
        ...o,
        tableName: encryptAES(o.tableName)
      },
      cancel
    });
  },
  // 删除和批量删除，o传数组
  batchDelete(o, cancel) {
    let data = []
    if (Array.isArray(o)) {
      data = o.map(item => {
        return {
          ...item,
          dsId: item.dsId || window.globalConfig.bi_query_dsId,
          tableName: encryptAES(item.tableName)
        }
      })
    }
    return httpRequest({
      url: url + "/batch",
      method: "delete",
      data: data,
      cancel
    });
  },
  export(o, cancel, logName='') {
    console.log('o',o)
    return httpRequest({
      url: url + "/exportExcel",
      method: "post",
      data: o,
    });
  }
};
