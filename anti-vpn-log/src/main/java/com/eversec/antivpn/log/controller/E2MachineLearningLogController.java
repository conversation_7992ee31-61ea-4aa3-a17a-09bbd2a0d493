package com.eversec.antivpn.log.controller;

import com.eversec.antivpn.emnus.ScreenTypeEnum;
import com.eversec.antivpn.log.aop.RedisCache;
import com.eversec.antivpn.log.aop.RedisCacheOnlyMethodKey;
import com.eversec.antivpn.log.api.E2MachineLearningLogApi;
import com.eversec.antivpn.log.api.dto.E2MachineLearningLogDTO;
import com.eversec.antivpn.log.api.dto.base.AggCountDTO;
import com.eversec.antivpn.log.service.IE2MachineLearningLogService;

import com.eversec.antivpn.log.service.IE2MachineLearningLogViewService;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import lombok.AllArgsConstructor;
import java.util.List;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-08
 */
@RestController
@RequestMapping(E2MachineLearningLogApi.PATH)
@AllArgsConstructor
@Slf4j
public class E2MachineLearningLogController implements E2MachineLearningLogApi {

    private final IE2MachineLearningLogService service;

    private final IE2MachineLearningLogViewService iE2MachineLearningLogViewService;

    @Override
    public Page<E2MachineLearningLogDTO> page(E2MachineLearningLogDTO paramDto, Page<E2MachineLearningLogDTO> pageInfo) {
        // TODO
        return null;
    }

    @Override
    @RedisCache
    public AggCountDTO timeRangeAndTodayCount(ScreenTypeEnum screenTypeEnum) {
        return iE2MachineLearningLogViewService.countNum(screenTypeEnum);
    }


}
