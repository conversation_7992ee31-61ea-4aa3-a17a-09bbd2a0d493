package com.eversec.antivpn.support.province.entity.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eversec.stark.generic.common.infra.mybatis.plus.BaseDO;
import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 省平台信息（企业测）
 * </p>
 * <p>
 * 持久化对象，此对象与数据库属性一一对应，被Entity对象继承，扩展属性写在Entity对象中
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-12
 */
@Getter
@Setter
@TableName("province_platform_status")
@Schema(name = "ProvincePlatformStatusPO", description = "省平台信息（企业测）")
public class ProvincePlatformStatusPO extends BaseDO {

    private static final long serialVersionUID = 1L;

    @Schema(description = "运营商编码")
    private String comCode;

    @Schema(description = "省级区域编号")
    private Long provinceId;

    @Schema(description = "省平台业务系统标识")
    private String systemCode;

    @Schema(description = "网络类型编码，数组存储 ")
    private String networkBusinessIds;

    @Schema(description = "当前状态。监测网络类型的系统状态：0：正常；1：异常；2：未覆盖；9：其他。")
    private Integer currentState;

    @Schema(description = "创建时间,生成该文件的时间")
    private String timeStamp;

}