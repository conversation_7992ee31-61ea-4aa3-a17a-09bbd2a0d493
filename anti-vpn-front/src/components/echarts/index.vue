<style lang="scss" scoped>
.echarts-panel {
  width: 100%;
  height: 100%;
  user-select: none;
  position: relative;
  .echarts-main {
    width: 100%;
    height: 100%;
  }
  .empty {
    position: absolute;
    top: 40%;
    left: 50%;
    margin: 0;
    z-index: 10;
    backface-visibility: hidden;
    transform: translate(-50%, -50%);
  }
}
</style>
<template>
  <div :role="type" class="echarts-panel">
    <div ref="echarts" class="echarts-main"></div>
    <transition name="fade">
      <Empty
        v-show="isEmpty && empty"
        :type="emptyType"
        :color="emptyColor"
        :description="emptyText"
        class="empty"
      ></Empty>
    </transition>
  </div>
</template>
<script>
import Empty from "../empty";
import echarts from "echarts";
import MapGeo from "MapGeo";
import mapList from "./mapList.json";
import { debounce, isEmpty } from "lodash";
import { addResizeListener, removeResizeListener } from "@/utils/resize-event";

const log = function (msg) {
  if (typeof console !== "undefined") {
    console && console.error && console.error(msg);
  }
};

// 事件句柄
const eventList = [
  "click",
  "dblclick",
  "mousedown",
  "mousemove",
  "mouseup",
  "mouseover",
  "mouseout",
  "globalout",
  "contextmenu",
  "legendselectchanged"
];

// 全国市区，特殊后缀集合
const specialCity = ["市", "盟", "自治州", "县", "区", "其他", "兵团"];

export const specialInclude = (name) => {
  if (!new RegExp(`(.*?)(?:${specialCity.join("|")})$`).test(name)) {
    return `${name}市`;
  }
  return name;
};

// 加载
// const reslove = (name, bool) => {
// 	const isProvince = bool ? 'province/' : '';
// 	return require(`./map/${isProvince}${name}.json`);
// };

// 过滤
export const filterValue = (name) => {
  try {
    const map = mapList.filter(
      (item) => item.label === name || item.value === name
    )[0];
    return map.value;
  } catch (error) {
    return specialInclude(name);
  }
};

export default {
  name: "ECharts",
  components: { Empty },
  props: {
    options: {
      type: Object,
      default() {
        return {};
      },
    },
    theme: {
      type: Object,
      default() {
        return {};
      },
    },
    // eslint-disable-next-line vue/require-default-prop
    map: {
      type: [String, Array],
      default: "",
      // validator(val) {
      // 	if (Array.isArray(val)) {
      // 		for (let i = 0; i < val.length; i++) {
      // 			return oneOf(val[i], mapList);
      // 		}
      // 	}
      // 	return oneOf(val, mapList);
      // }
    },
    type: {
      type: String,
      required: true,
    },
    empty: {
      type: Boolean,
      default: true,
    },
    emptyType: {
      type: [String, Number],
      default: 2,
    },
    emptyColor: {
      type: String,
      default: "rgba(0,0,0,0.25)",
    },
    emptyText: {
      type: String,
      default: "暂无数据",
    },
    other: {
      type: [Object||Array]
    },
  },
  data() {
    return {
      isEmpty: false,
    };
  },
  watch: {
    options: {
      deep: true,
      handler(val) {
        this.setOption(val);
      },
    },
    map: {
      deep: true,
      handler() {
        // 注册地图
        this.formatMap();
        // 重绘一次
        this.setOption(this.options);
      },
    },
    theme: {
      deep: true,
      handler() {
        this.setTheme();
      },
    },
  },
  mounted() {
    window.echarts = echarts;
    if (this.map) {
      this.formatMap();
    }
    this._initCharts();
  },
  beforeDestroy() {
    this.destroyMap();
    if(this.$el.parentNode)this.$el.parentNode.removeChild(this.$el);
  },
  methods: {
    _initCharts() {
      this.$nextTick(() => {
        const el = this.$refs.echarts;
        this._echarts = echarts.init(el, this.theme);
        this.setOption(this.options);
        eventList.forEach((type) => {
          this._echarts.on(type, (e) => {
            this.$emit(type, e, this._echarts, this.other);
          });
        });
        this.__outSideResize__ = debounce(this.resize, 100);
        addResizeListener(el, this.__outSideResize__);
      });
    },
    setOption(val) {
      if (!this._echarts) return; 
      this.isEmpty = isEmpty(val);
      this._echarts.clear();
      this._echarts.setOption(val, true);
    },
    formatMap() {
      const nameList = Array.isArray(this.map) ? this.map : [this.map];
      nameList.forEach((item) => {
        const name = item === "中国" ? filterValue(item) : item;
        // console.log("----", name, filterValue(item), MapGeo);
        this.registerMap(name, filterValue(item));
      });
    },
    // 地图resize
    resize() {
      if (!this._echarts) return;
      this._echarts.resize();
      this.$emit("resize");
    },
    // 销毁地图
    destroyMap() {
      if (!this._echarts) return;
      removeResizeListener(this.$refs.echarts, this.__outSideResize__);
      this.__outSideResize__ = null;
      // 地图清空
      this._echarts.clear();
      // 地图销毁
      this._echarts.dispose();
      this._echarts = null;
    },
    // 由于echarts 3.0 不支持setTheme 导致
    setTheme() {
      this.destroyMap();
      this._initCharts();
    },
    // 供父组件使用
    outRegisterMap(name, json) {
      echarts.registerMap(name, json);
    },
    registerMap(name, json) {
      if (!echarts) {
        log("ECharts is not Loaded");
        return;
      }
      if (!echarts.registerMap) {
        log("ECharts Map is not loaded");
        return;
      }
      if (!json) return;
      json = MapGeo[json];
      if (!name || !json) return console.error("name 或json不存在", name, json);
      let o =echarts.getMap(name)&&echarts.getMap(name).geoJson;
      // console.log("registerMap:::", name, json);
      if(!o)echarts.registerMap(name, json);
    },
  },
};
</script>
