<template>
  <div class="chartbox">
    <div class="piebg" v-if="!isEmpty">
      <e-charts type="pie" :options="pieOpt"></e-charts>
      <div class="dashLine"></div>
    </div>

    <ul class="chartUl" v-if="!isEmpty">
      <li v-for="(item, i) in pieD" :key="item.name">
        <span class="dian" :style="{ background: color[i] }"> </span>
        {{ item.name }}
        <span
          class="fr percent"
          style="display: inline-block; width: 50px; text-align: right"
          >{{ item.percentNum }}%</span
        >
      </li>
    </ul>
    <Empty class="empty" v-if="isEmpty" color="#fff" />
  </div>
</template>
    
    <script>
import Empty from "@/components/empty.vue";

export default {
  props: {
    systemLogList: {
      type: Array,
    },
  },
  components: {
    Empty,
  },
  watch: {
    systemLogList: {
      handler(val) {
        this.pieD = val
        this.init();
      },
      immediate: true,
    },
  },
  data() {
    return {
      pieOpt: null,
      color: [
        "#5379f4",
        "#b8ef5d",
        "#f6525b",
        "#7ae15e",
        "#fcf94c",
        "#fdd35a",
        "#fe7b69",
        "#b52be2",
        "#6646ee",
        "#9442f1",
      ],
      pieD: [],
      isEmpty: false,
    };
  },

  methods: {
    init() {
      this.pieD.length == 0 ? (this.isEmpty = true) : (this.isEmpty = false);
      let color = this.color;
      let total = this.pieD.reduce((n, cur) => n + cur.num, 0);
      // this.pieD.sort((a, b) => {
      //   return a.num - b.num;
      // });
      this.pieD = this.pieD.map((el) => {
        return {
          ...el,
          percentNum: ((el.num * 100) / total).toFixed(2),
          name: el.key,
          value: el.num,
        };
      });
      this.pieOpt = {
        color,
        tooltip: {
          show: true,
          trigger: "item",
          // backgroundColor: "none",
          // extraCssText: "border:0; box-shadow: none;",
          formatter: function (p) {
            let d = p.data;
            // console.log('aa', p, p.color)
            //  style="background:#082243; border: 1px solid #3b4356; padding: 0 10px; min-width: 90px;"
            return `
                  <div><span style="background:${p.color}; display:inline-block; width:7px;height:7px;border-radius:50%;margin-right:6px"> </span>
                    <span style="color:${p.color}">${d.key}</span>
                    ${d.num}
                    <div>${d.percentNum}%</div>
                  </div>`;
          },
        },
        legend: {
          show: false,
          bottom: "5%",
          left: "center",
          icon: "circle",
          itemWidth: 7,
          itemHeight: 7,
          textStyle: {
            color: "#fff",
          },
        },
        series: [
          {
            name: "",
            type: "pie",
            minAngle: 6,
            radius: ["80%", "95%"],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: "center",
            },
            emphasis: {
              label: {
                show: false,
                fontSize: 30,
                fontWeight: "bold",
              },
            },
            labelLine: {
              show: false,
            },
            itemStyle: {
              borderColor: "#020513",
              borderWidth: 8,
            },
            data: this.pieD,
          },
          // {
          //   name: "外环",
          //   hoverAnimation: false,
          //   clockWise: false,
          //   tooltip: {
          //     show: false,
          //   },
          //   type: "pie",
          //   radius: ["28%", "38%"],
          //   color: ["#fff440", "#2e9bb2", "#f2e93f", "#2767d0"],
          //   label: {
          //     normal: {
          //       position: "center",
          //     },
          //   },
          //   itemStyle: {
          //     borderColor: "#020513",
          //     borderWidth: 8,
          //   },
          //   data: this.pieD,
          // },
        ],
      };
    },
  },
};
</script>
    
    <style scoped lang="scss">
.chartbox {
  // height: 500px;
  height: 240px;
  padding: 20px 0;

  display: flex;
  justify-content: space-between;
  .empty {
    display: flex;
    justify-content: center;
    flex-direction: column;
    margin: 0;
    flex: 1;
  }
  .piebg {
    height: 200px;
    width: 50%;
    position: relative;
    margin-bottom: 20px;
  }
  .chartUl {
    overflow: auto;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    li {
      color: #fff;
      font-size: 12px;
      padding: 0 5px;
      margin-bottom: 10px;
      padding-bottom: 10px;
      border-bottom: 1px dashed #1e2230;
      &:last-child {
        margin-bottom: 0;
      }

      .dian {
        display: inline-block;
        width: 10px;
        height: 10px;
        vertical-align: middle;
        margin-right: 6px;
      }
      .percent {
        color: #21d5d2;
      }
      .num {
        margin-right: 100px;
        b {
          color: #21d5d2;
          font-weight: 400;
        }
      }
    }
  }
  .dashLine {
    width: 150px;
    height: 150px;

    border-radius: 50%;
    position: absolute;
    left: 50%;
    margin-left: -75px;
    top: 50%;
    margin-top: -75px;
    background: url(./img/pieBg.png) no-repeat;
    background-size: contain;
  }
}
</style>
    