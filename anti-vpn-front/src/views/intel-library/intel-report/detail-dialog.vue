<template>
  <el-dialog
    class="dialog-handle"
    :title="title"
    :visible.sync="visible"
    :close-on-click-modal="false"
    :loading="loading"
  >
    <!-- form -->
    <publicForm
      ref="publicForm"
      :formDataLabel="formDataLabel"
      :labelWidth="'140px'"
      :form-data="formData"
    />
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" :loading="loading" @click="dataFormSubmit">
        确定
      </el-button>
    </span>
  </el-dialog>
</template>

<script>
import cloneDeep from 'lodash/cloneDeep'
import antiVpnService from '@/api/antiVpnService'

export default {
  name: 'knowledge-base-dialog',
  components: {},
  props: {
    type: {
      type: String,
      default: () => ''
    }
  },
  data() {
    return {
      loading: false,
      visible: false,
      title: '',
      formDataLabel: [
        [
          {
            label: '运营商编码',
            val: 'comCode',
            type: 'dic-select',
            enumKey: 'ISP_CODE',
            showAll: true,
            specialRules: 'selectCheck'
          }
        ],
        [
          {
            label: '省级区域编号',
            val: 'provinceId',
            type: 'dic-select',
            enumKey: 'PROVINCE_CODE',
            showAll: true,
            specialRules: 'selectCheck'
          }
        ],
        [
          {
            label: '业务系统标识',
            val: 'systemCode',
            type: 'dic-select',
            enumKey: 'SYSTEM_CODE',
            showAll: true,
          }
        ],
        [
          {
            label: '情报类型',
            val: 'typeId',
            type: 'dic-select',
            enumKey: 'INTELLIGENCE_TYPE',
            showAll: true,
          }
        ],
        [
          {
            label: '应用协议编号',
            val: 'applicationProtocolCode',
            type: 'dic-select',
            enumKey: 'APPLICATION_LAYER_PROTOCOL',
            showAll: true,
          }
        ],
        [
          {
            label: '网络类型',
            val: 'networkBusinessId',
            type: 'dic-select',
            enumKey: 'NETWORK_TYPE',
          }
        ],
        [
          {
            label: '情报库ID',
            val: 'vpnId',
            type: 'input',
            specialRules: 'nullCheck',
            maxlength: 20,
            limit: true
          }
        ],
        [
          {
            label: 'VPN名称',
            val: 'vpnName',
            type: 'input',
            specialRules: 'nullCheck',
            maxlength: 1024,
            limit: true
          }
        ],
        [
          {
            label: '发现时间',
            val: 'timeStamp',
            type: 'date-time',
            specialRules: 'selectCheck'
          }
        ],
        [
          {
            label: 'VPN域名',
            val: 'vpnDomain',
            type: 'input',
            maxlength: 1024,
            limit: true
          }
        ],
        [
          {
            label: 'IP地址',
            val: 'vpnIp',
            type: 'input',
            maxlength: 200,
            limit: true
          }
        ],
        [
          {
            label: 'URL地址',
            val: 'vpnUrlDecode',
            type: 'input',
            maxlength: 1024,
            limit: true
          }
        ],
        [
          {
            label: '报文',
            val: 'vpnMessageDecode',
            type: 'input',
            maxlength: 1024,
            limit: true
          }
        ],
        [
          {
            label: '取证链接',
            val: 'vpnLink',
            type: 'input',
            maxlength: 1024,
            limit: true
          }
        ],
        [
          {
            label: '端口',
            val: 'vpnPort',
            type: 'input',
            maxlength: 10,
            limit: true
          }
        ],
        [
          {
            label: '节点所属国家',
            val: 'vpnCountry',
            type: 'input',
            maxlength: 100,
            limit: true
          }
        ],
        [
          {
            label: '所属VPN服务商',
            val: 'vpnAirportCode',
            type: 'dic-select',
            enumKey: 'AIRPORT_INFORMATION'
          }
        ],
        [
          {
            label: '软件',
            val: 'vpnSoftwareCodeList',
            type: 'dic-select',
            enumKey: 'SOFTWARE_INFORMATION',
            multiple: true
          }
        ],
        [
          {
            label: '协议',
            val: 'vpnProtocolCode',
            type: 'dic-select',
            showAll: true,
            enumKey: 'VPN_PROTOCOL'
          }
        ]
      ], // form树
      formData: {}
    }
  },
  computed: {},
  watch: {},
  mounted() {},
  methods: {
    // 打开dialog
    openDialog(e) {
      if (e) {
        this.formData = cloneDeep(e)
        this.title = `修改情报信息`
      } else {
        this.title = `新增情报信息`
        this.formData = {}
      }
      this.visible = true
      this.$nextTick(() => {
        this.$refs.publicForm.$refs.form.clearValidate()
      })
    },
    // 提交前数据验证以及格式转换
    dataFormSubmit() {
      this.$refs.publicForm.$refs.form.validate(valid => {
        if (valid) {
          this.loading = true
          let param = this.formData
          this.submit(param)
        }
      })
    },
    // 提交表单
    submit(param) {
      antiVpnService[
        this.formData.id ? 'vpnIntelligenceUpdateById' : 'vpnIntelligenceSave'
      ](param)
        .then(res => {
          this.$message({
            message: this.formData.id ? '修改成功' : '新增成功',
            type: 'success'
          })
          this.loading = false
          this.visible = false
          this.$emit('dataFormSubmit')
        })
        .catch(() => {
          this.loading = false
        })
    }
  }
}
</script>

<style lang="scss" scoped></style>
