package com.eversec.antivpn.support.config.api;

import com.eversec.antivpn.support.config.api.dto.PlatformInterfaceInfoDTO;
import com.eversec.framework.feignboot.conf.EversecFeignDecoderConfiguration;
import com.eversec.framework.feignboot.conf.EversecFeignLogConfiguration;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

import java.util.List;
import java.util.Map;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

/**
* 平台接口信息 Api接口
*
* 模块对外暴露的api
*
* @FeignClient 注解参数解释
* value: 服务提供者提供的服务名
* url: 开发、测试环境调试无法通过服务名调用，填写服务对外暴露的地址。该参数有值 value则不生效，该参数为空时使用value值
* path: 服务前缀，实现类 @RequestMapping 也使用该值
* contextId: 全局唯一，默认为包名加类名
* configuration:
*          EversecFeignDecoderConfiguration: 自动解包，异常传递。
*          EversecFeignLogConfiguration: 记录请求日志。默认开启日志，可通过如下参数控制是否记录日志
*                  eversec.feign.request-log=true
*                  eversec.feign.result-log=true
*
*/

@Validated
@Tag(name = "平台接口信息")
@FeignClient(value = "anti-vpn-service",
        path = PlatformInterfaceInfoApi.PATH,
        url = "${app.anti-vpn.service-url:}",
        contextId = "com.eversec.antivpn.support.config.api.PlatformInterfaceInfoApi",
        configuration = { EversecFeignDecoderConfiguration.class, EversecFeignLogConfiguration.class})
public interface PlatformInterfaceInfoApi {

    String PATH = "/config/platformInterfaceInfo";

    @Operation(summary = "获取当前平台配置。不存在抛出异常，存在多条返回第一条记录")
    @GetMapping("/getCurrentPlatform")
    PlatformInterfaceInfoDTO getCurrentPlatform();

    @Operation(summary = "获取当前平台配置。不存在抛出异常，存在多条返回第一条记录")
    @GetMapping("/getCurrentPlatformForFront")
    PlatformInterfaceInfoDTO getCurrentPlatformForFront();


    /**
     * 根据运营商和省编码和系统标识查询省平台信息
     * @param comCode
     * @param provinceId
     * @param systemCode 系统标识，部侧调用集团时通过不同的webservice接口区分不同的标识
     * @return
     */
    @Operation(summary = "根据运营商和省编码和系统标识查询省平台信息")
    @GetMapping("/getByIspAndProvinceAndSystemCode/{comCode}/{provinceId}/{systemCode}")
    PlatformInterfaceInfoDTO getByComCodeAndProvinceAndSystemCode(@PathVariable(value = "comCode")String comCode, @PathVariable(value = "provinceId")Long provinceId, @PathVariable(value = "systemCode")String systemCode);

    @Operation(summary = "根据comCode和系统标识查询省平台信息")
    @GetMapping("/getByComCodeAndSystemCode/{comCode}/{systemCode}")
    PlatformInterfaceInfoDTO getByComCodeAndSystemCode(@PathVariable(value = "comCode")String comCode, @PathVariable(value = "systemCode")String systemCode);


    /**
     * 根据运营商和省编码查询多条省平台信息
     * 适用于省平台部署一套系统对接僵木蠕、恶意程序、IDC等多套系统获取多个系统信息情况
     * @param comCode
     * @param provinceId
     * @return
     */
    @Operation(summary = "根据运营商和省编码查询多条省平台信息")
    @GetMapping("/getByIspAndProvince/{comCode}/{provinceId}")
    List<PlatformInterfaceInfoDTO> getByIspAndProvince(@PathVariable(value = "comCode")String comCode, @PathVariable(value = "provinceId")Long provinceId);


    @Operation(summary = "是否开启省端查询")
    @GetMapping("/startProvince")
    Map<String,Object> startProvinceMap();

}





