# 跨境通信监测(anti-vpn)

## 模块说明


### common

1. [anti-vpn-common/anti-vpn-config](anti-vpn-common%2Fanti-vpn-config)
    配置模块，所有模块的配置统一放在此模块，统一管理，配置文件入口是 [AntiVpnProperties.java](anti-vpn-common%2Fanti-vpn-config%2Fsrc%2Fmain%2Fjava%2Fcom%2Feversec%2Fantivpn%2Fconfig%2FAntiVpnProperties.java)

2. [anti-vpn-common/anti-vpn-util](anti-vpn-common%2Fanti-vpn-util)
    工具类模块， anti-vpn通用的逻辑如，StrUtil，FileUtil等，此处StrUtil只是举例

3. [anti-vpn-common/anti-vpn-xxx](anti-vpn-common%2Fanti-vpn-xxx)
   通用业务，如 anti-vpn-harbor\anti-vpn-docker\anti-vpn-minio

4. [anti-vpn-code-generator](anti-vpn-common%2Fanti-vpn-code-generator)
   代码生成器，通过运行[Generator.java](anti-vpn-common%2Fanti-vpn-code-generator%2Fsrc%2Fmain%2Fjava%2Fcom%2Feversec%2Fantivpn%2Fcode%2Fgenerator%2Fgenerator%2FGenerator.java)，生成相应模块代码，代码生成在项目的generated目录下，复制生成的代码到相应的工程下
    

### web入口
[anti-vpn-service](anti-vpn-service)
web入口，整合所有业务模块，对前端暴露此模块

### 业务模块
1. [anti-vpn-xxx](anti-vpn-xxx)
业务模块

2. [anti-vpn-xxx/anti-vpn-xxx-api](anti-vpn-xxx%2Fanti-vpn-xxx-api)
业务模块对应的api接口（api模块中只定义feign接口），各个业务模块不可直接依赖其他业务模块，可依赖其他模块的api。各业务模块可依赖anti-vpn-util和anti-vpn-config

[anti-vpn-supoort](anti-vpn-supoort)
支撑模块（支撑域），非核心业务模块，为其他核心业务模块提供服务，如企业（省平台）信息模块。

[anti-vpn-intelligence](anti-vpn-intelligence)
情报模块，包含vpn信息、情报指令功能

[anti-vpn-log](anti-vpn-log)
日志模块，包含vpn日志、dns日志

### 前端模块

[anti-vpn-front](anti-vpn-front)

### 外部依赖模块

三件套: 用户角色菜单等信息

security-tech-generic: 数据字典、文件管理

## 开发规范、约束


### flyway 规范说明
该项目采用动态flyway

模块flyway存放于 db.migration中。
避免各个模块之间版本文件冲突财经 A.B.C.D四位版本表示，其中A.B.C为代码版本，D代表模块，各个模块取值不能重复。

### 分页
接收参数和返回分页信息采用mybatisplus的 Page对象

### 枚举
代码中需要根据业务属性值判断的都定义为枚举，通过枚举进行判断，不可通过 "0".equals(comCode)这种方式进行判断


## 需要考虑的问题
1. 情报生效状态
2. 生成的ruleId可能重复
3. 样本和模型文件留存
4. 

## 部署时需要配置 platform_interface_info 表数据

集团调用省平台webservice地址(省端gateway需要配置白名单):
http://125.39.152.205:20000/service/anti-vpn/services/IDCWebService/idcCommand/2?wsdl&systemCodes=2


