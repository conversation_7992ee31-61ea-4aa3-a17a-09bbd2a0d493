<template>
  <el-dialog
    :title="dataForm.id == null ? '新增' : '修改'"
    :visible.sync="visible"
    :close-on-click-modal="false"
    @close="reset"
  >
    <el-form
      ref="dataForm"
      :model="dataForm"
      :rules="dataRule"
      label-width="100px"
    >
      <el-form-item label="配置名称" prop="itemName">
        <el-input v-model.trim="dataForm.itemName" placeholder="配置名称" />
      </el-form-item>
      <el-form-item label="配置项KEY" prop="itemKey">
        <el-input
          v-model.trim="dataForm.itemKey"
          :disabled="dataForm.id !== null"
          placeholder="配置项KEY"
        />
      </el-form-item>
      <!-- 值类型 -->
      <el-form-item label="值类型" prop="valueType">
        <dic-select
          v-model="dataForm.valueType"
          enum-key="VALUE_TYPE"
          placeholder="请选择值类型"
          :disabled="dataForm.id !== null"
          style="width:100%;"
        />
      </el-form-item>
      <el-form-item
        label="配置项值"
        prop="itemValue"
        :rules="{ required: false, message: '配置项值不能为空' }"
      >
        <el-switch
          v-if="dataForm.valueType === 'booleType'"
          v-model="dataForm.itemValue"
        />
        <el-input
          v-else
          v-model="dataForm.itemValue"
          :type="
            dataForm.valueType === 'numType'
              ? 'number'
              : dataForm.valueType === 'jsonType'
              ? 'textarea'
              : 'text'
          "
          :rows="15"
          :disabled="
            dataForm.valueType === 'imgType' ||
              dataForm.valueType === 'fileType'
          "
          placeholder="配置项值"
        />
      </el-form-item>
      <el-form-item
        v-if="dataForm.valueType === 'imgType'"
        label="图片上传："
        :rules="{ required: false, message: '图片不能为空' }"
      >
        <UploadFile
          v-model="fileList"
          :is-preview="true"
          :accept="'.png,.jpg,.jpeg'"
          :biz-type="'MOOC_VIDEO'"
          :validation-format="false"
          :limit="1"
          @change="changeFiles"
        />
      </el-form-item>
      <el-form-item
        v-if="dataForm.valueType === 'fileType'"
        label="文件上传："
        :rules="{ required: false, message: '文件不能为空' }"
      >
        <UploadFile
          v-model="fileList"
          :limit="1"
          :validation-format="false"
          @change="changeFiles"
        />
      </el-form-item>
      <el-form-item label="提示信息">
        <el-input
          v-model.trim="dataForm.itemDesc"
          type="textarea"
          placeholder="提示信息"
        />
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button
        type="primary"
        :loading="submitLoading"
        @click="dataFormSubmit"
      >
        确定
      </el-button>
    </span>
  </el-dialog>
</template>

<script>
import api_app from '@/api/rm/sysConf'
import cloneDeep from 'lodash/cloneDeep'
import sysApi from '@/api/rm/sysConf'
export default {
  components: {
    // uploadFile: () =>
    //   import('@/components/bus-component/public-form/upload-file')
  },
  data() {
    return {
      visible: false,
      submitLoading: false,
      dataForm: {
        itemName: '',
        valueType: '',
        itemKey: '',
        itemValue: '',
        id: null
      },
      //要上传的文件列表
      fileList: [],
      dataRule: {
        itemName: [
          { required: true, message: '配置名称不能为空', trigger: 'blur' }
        ],
        itemKey: [
          { required: true, message: '配置项KEY不能为空', trigger: 'blur' }
        ],
        valueType: [
          { required: true, message: '值类型不能为空', trigger: 'blur' }
        ]
      }
    }
  },
  mounted() {},
  methods: {
    init(row) {
      this.visible = true
      // 修复焦点在带校验的项按回车提交后，再次打开对话框，该项会带有校验提示的问题
      // 而 resetFields 仍然放在关闭对话框时执行，以避免下次打开时，带动画的控件（如checkbox）会闪现之前的结果
      this.$nextTick(() => {
        this.$refs.dataForm.clearValidate()
      })

      if (row) {
        const convertValue = value => {
          if (value === null) {
            return false
          } else if (value === 'true') {
            return true
          } else if (value === 'false') {
            return false
          } else {
            return Boolean(value)
          }
        }
        this.dataForm = {
          ...row,
          itemValue:
            row.valueType === 'booleType'
              ? convertValue(row.itemValue)
              : row.itemValue
        }
        // 附件反显
        if (
          (this.dataForm.valueType === 'imgType' ||
            this.dataForm.valueType === 'fileType') &&
          this.dataForm.itemValue
        ) {
          api_app.getFile(this.dataForm.itemValue).then(res => {
            this.fileList = [
              {
                ...res,
                attachmentId: this.dataForm.itemValue,
                name: res.fileName
              }
            ]
          })
        }
      } else {
        this.dataForm.id = null
        this.dataForm.itemName = null
        this.dataForm.itemKey = null
        this.dataForm.itemValue = null
        this.dataForm.valueType = null
      }
    },
    reset() {
      this.fileList = []
      this.$refs.dataForm.resetFields()
    },
    // 移除文件
    handleRemove(file, fileList) {
      this.fileList = fileList
    },
    // 获取上传的文件ids
    changeFiles(e) {
      this.dataForm.itemValue =
        e.length > 0 ? e[0].attachmentId : this.dataForm.itemValue || ''
    },
    submit() {
      const tmp = { ...this.dataForm }

      let type = this.dataForm.id == null ? 'add' : 'update'

      api_app[type](tmp)
        .then(() => {
          this.visible = false
          this.submitLoading = false
          this.$emit('onSubmit')
          this.reset()
          this.$message({
            message: '操作成功',
            type: 'success',
            duration: 1500
          })
        })
        .catch(() => {
          this.submitLoading = false
        })
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs.dataForm.validate(valid => {
        if (valid) {
          this.submitLoading = true
          this.submit()
        }
      })
    }
  }
}
</script>
