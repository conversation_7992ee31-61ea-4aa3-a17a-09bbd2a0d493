-- 记录请求和返回参数，用于定位问题，以及重试
ALTER TABLE `vpn_intelligence_command`
    ADD COLUMN `distribute_request_params` json DEFAULT NULL COMMENT '下发请求参数';

ALTER TABLE `vpn_intelligence_command`
    ADD COLUMN `distribute_response_params` json DEFAULT NULL COMMENT '下发返回参数';

ALTER TABLE `vpn_intelligence_command`
    ADD COLUMN `command_process_status` varchar(50) NOT NULL COMMENT '指令处理状态，接收：RECEIVE；成功：SUCCESS；失败：FAIL';




