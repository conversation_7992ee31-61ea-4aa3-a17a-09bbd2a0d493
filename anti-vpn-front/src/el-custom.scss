//--- Element UI 样式覆盖 ---//
// 布局容器
.el-header {
  // background: #fff;
  color: #555;
  line-height: 60px;
  box-shadow: 3px 0 3px rgba(0, 0, 0, 0.3);
  // margin-bottom: 3px;
}
.el-footer {
  border-top: 1px solid #eceef3;
  color: #333;
  text-align: center;
  line-height: 40px;
  font-size: 12px;
}
/* .el-aside {
  background: #454545;
  color: #333;
  transition: width 0.3s;
} */

.el-table .cell {
  // 去掉表格中文字按钮的内边距
  .el-button--text {
    padding: 0;
  }
  .el-link {
    font-size: 12px;
  }
  .el-dropdown {
    font-size: 12px;
    line-height: 1;
    color: var(--primary-bg);
    cursor: pointer;
  }
}

.el-link+.el-link {
  margin-left: 10px;
}
.el-link+.el-button {
  margin-left: 10px;
}
.el-button+.el-link {
  margin-left: 10px;
}

// 去掉卡片标题中按钮的内边距
.el-card__header {
  line-height: 1; /* 修正：默认中英文行高会不同 */
  .el-button {
    padding: 0;
    font-size: 14px;
    vertical-align: middle;
  }
}

// 行内表单对项进行响应式布局时的样式
.el-form.el-form--inline {
  width: 100%;
  overflow: hidden;
  .el-col {
    .el-form-item {
      width: 100%;
      height: 32px;
      margin-right: 0;
    }
    .el-form-item__content {
      width: 100%;
      > div {
        width: 100%;
      }
      > span {
        display: inline-block;
        width: 100%;
      }
    }
    .el-form-item__label {
      // width: 30%;
      width: 120px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .el-form-item__label + .el-form-item__content {
      // width: 70%;
      width: calc(100% - 120px);
    }
  }
}

// 表格展开
.el-table__expanded-cell {
  label {
    color: #99a9bf;
  }
  .el-form-item__content {
    color: #999;
  }
}

// 避免tooltip内容过长时，超出屏幕
.el-tooltip__popper {
  max-width: 80%;
}

// 横向菜单高度
.el-menu--horizontal > .el-submenu .el-submenu__title,
.el-menu--horizontal > .el-menu-item {
  height: 40px;
  line-height: 40px;
}

// 全局异常提示
.el-message.globalError {
  z-index: 3000 !important;
}

// 对话框标题，右侧留出关闭按钮的位置，长英文数字换行
.el-dialog__header {
  padding-right: 35px;
  word-break: break-all;
}

.el-date-editor .el-range-separator {
  box-sizing: content-box;
  overflow: hidden;
}

// 级联选择控件优化（单选选择任意一级）
.el-cascader-panel .el-radio {
  width: 132px;
  height: 34px;
  line-height: 34px;
  padding: 0 10px;
  z-index: 10;
  position: absolute;
}
.el-cascader-panel .el-radio__input {
  visibility: hidden;
}
.el-cascader-panel .el-cascader-node__postfix {
  top: 10px;
}

.el-tree {
  font-size: 14px;
}
