package com.eversec.antivpn.log.api;

import com.eversec.antivpn.emnus.ScreenTypeEnum;
import com.eversec.antivpn.log.api.dto.E2MachineLearningLogDTO;

import com.eversec.antivpn.log.api.dto.base.AggCountDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eversec.framework.feignboot.conf.EversecFeignDecoderConfiguration;
import com.eversec.framework.feignboot.conf.EversecFeignLogConfiguration;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;

import org.springdoc.api.annotations.ParameterObject;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Api接口
 * <p>
 * 模块对外暴露的api
 *
 * @FeignClient 注解参数解释
 * value: 服务提供者提供的服务名
 * url: 开发、测试环境调试无法通过服务名调用，填写服务对外暴露的地址。该参数有值 value则不生效，该参数为空时使用value值
 * path: 服务前缀，实现类 @RequestMapping 也使用该值
 * contextId: 全局唯一，默认为包名加类名
 * configuration:
 * EversecFeignDecoderConfiguration: 自动解包，异常传递。
 * EversecFeignLogConfiguration: 记录请求日志。默认开启日志，可通过如下参数控制是否记录日志
 * eversec.feign.request-log=true
 * eversec.feign.result-log=true
 */

@Validated
@Tag(name = "E2:机器学习")
@FeignClient(value = "anti-vpn-service",
        path = E2MachineLearningLogApi.PATH,
        url = "${app.anti-vpn.service-url:}",
        contextId = "com.eversec.antivpn.log.api.E2MachineLearningLogApi",
        configuration = {EversecFeignDecoderConfiguration.class, EversecFeignLogConfiguration.class})
public interface E2MachineLearningLogApi {

    String PATH = "/log/e2MachineLearningLog";


    /**
     * @param paramDto 查询参数
     * @param pageInfo 分页参数，用到其中的size和current
     */
    @Operation(summary = "查询-分页")
    @GetMapping("/page")
    Page<E2MachineLearningLogDTO> page(@ModelAttribute @ParameterObject E2MachineLearningLogDTO paramDto,
                                       @ModelAttribute @ParameterObject Page<E2MachineLearningLogDTO> pageInfo);

    @Operation(summary = "总体态势-上报AI模型监测日志")
    @GetMapping("/timeRangeAndTodayCount")
    AggCountDTO timeRangeAndTodayCount(ScreenTypeEnum screenTypeEnum);

}





