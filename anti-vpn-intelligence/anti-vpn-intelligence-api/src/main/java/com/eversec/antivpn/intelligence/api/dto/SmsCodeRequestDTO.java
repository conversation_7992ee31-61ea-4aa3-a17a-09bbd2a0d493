package com.eversec.antivpn.intelligence.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * 短信验证码发送请求DTO
 * 
 * <AUTHOR>
 * @since 2025-01-12
 */
@Data
@Schema(name = "SmsCodeRequestDTO", description = "短信验证码发送请求")
public class SmsCodeRequestDTO {

    @NotBlank(message = "手机号码不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号码格式不正确")
    @Schema(description = "手机号码（11位，不需要加86前缀）", example = "***********")
    private String phoneNumber;

    @Schema(description = "业务类型（可选，用于区分不同业务场景）", example = "LOGIN")
    private String businessType;

}
