package com.eversec.antivpn.log.util;

import com.eversec.antivpn.config.AntiVpnProperties;
import com.eversec.antivpn.emnus.ScreenTypeEnum;
import com.eversec.antivpn.log.mapper.vo.BaseSearchVO;
import org.springframework.boot.env.EnvironmentPostProcessor;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/8/24 15:13
 **/

@Component
public class SearchParamUtil {


    @Resource
    private AntiVpnProperties antiVpnProperties;



    public BaseSearchVO getBaseSearchVOWithTimePartition(ScreenTypeEnum screenTypeEnum){
        BaseSearchVO baseSearchVO = new BaseSearchVO();
        if(screenTypeEnum == ScreenTypeEnum.TODAY){
            baseSearchVO.setTodayTimePartition(ScreenTypeEnum.getTimeCKPartition(screenTypeEnum));
        }else {
            baseSearchVO.setStartTimePartition(ScreenTypeEnum.getTimeCKPartition(screenTypeEnum));
        }
        setMapLocationSearchDimension(baseSearchVO);
        return baseSearchVO;
    }

    public BaseSearchVO getBaseSearchVO(){
        BaseSearchVO baseSearchVO = new BaseSearchVO();
        setMapLocationSearchDimension(baseSearchVO);
        return baseSearchVO;
    }

    private void setMapLocationSearchDimension(BaseSearchVO baseSearchVO){
        if(antiVpnProperties.getStartProvinceSearch().getOpenStatus()){
            baseSearchVO.setMapLocationSearchDimension("city");
        }else{
            baseSearchVO.setMapLocationSearchDimension("province");
        }
    }
}
