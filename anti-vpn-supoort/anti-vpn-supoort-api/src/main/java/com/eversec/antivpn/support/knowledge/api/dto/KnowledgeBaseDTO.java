package com.eversec.antivpn.support.knowledge.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import java.util.Map;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <p>
 * DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-12
 */
@Getter
@Setter
@Schema(name = "KnowledgeAirportDTO", description = "省平台信息（企业测）")
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class KnowledgeBaseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "主键-雪花")
    public Long id;

    @NotNull(message = "编码不能为空")
    @Schema(description = "字典编码")
    public String enumKey;

    @Schema(description = "字典编码2")
    public String enumKey2;

    @NotNull(message = "名称不能为空")
    @Schema(description = "字典名称")
    public String enumVal;

    @Schema(description = "顺序")
    public Integer seq;

//    @NotNull(message = "类型不能为空")
    @Schema(description = "字典类型")
    public String typeKey;

    @Schema(description = "字段补充字段")
    public Map<String,Object> enumValExtend;

    @NotNull(message = "创建人不能为空")
    @Schema(description = "创建人员")
    public String createUser;

    @NotNull(message = "修改人不能为空")
    @Schema(description = "修改人员")
    public String updateUser;

    @Schema(description = "创建时间")
    public Date createDatetime;

    @Schema(description = "修改时间")
    public Date updateDatetime;

    @Schema(description = "创建时间-Str")
    public String createDatetimeStr;


}