<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eversec.antivpn.log.mapper.E2MachineLearningLogViewMapper">
    <resultMap id="TypeCount" type="com.eversec.antivpn.log.mapper.vo.TypeCountVO">
        <result column="num" jdbcType="BIGINT" property="num"/>
        <result column="key_type" jdbcType="INTEGER" property="key"/>
    </resultMap>
    <resultMap id="VpnAggVO" type="com.eversec.antivpn.log.mapper.vo.VpnAggVO">
        <result column="num" jdbcType="BIGINT" property="personNum"/>
        <result column="resource_node_name" jdbcType="VARCHAR" property="resourceNodeName"/>
    </resultMap>
    <resultMap id="VpnHistogramAggVO" type="com.eversec.antivpn.log.mapper.vo.VpnHistogramAggVO">
        <result column="key_type" jdbcType="INTEGER" property="key"/>
        <result column="vpn_service_num" jdbcType="INTEGER" property="vpnServiceNum"/>
        <result column="resource_node_num" jdbcType="BIGINT" property="resourceNodeNum"/>
    </resultMap>
    <resultMap id="UserViewVO" type="com.eversec.antivpn.log.mapper.vo.UserViewVO">
        <result column="user" jdbcType="VARCHAR" property="user"/>
        <result column="src_ip" jdbcType="VARCHAR" property="srcIp"/>
    </resultMap>
    <resultMap id="baseResult" type="com.eversec.antivpn.log.entity.E2MachineLearningLogView">
        <result column="day" jdbcType="BIGINT" property="day"/>
        <result column="hour" jdbcType="INTEGER" property="hour"/>
        <result column="month" jdbcType="BIGINT" property="month"/>
        <result column="week" jdbcType="INTEGER" property="week"/>
        <result column="province_id" jdbcType="VARCHAR" property="provinceId"/>
        <result column="network_business_id" jdbcType="INTEGER" property="networkBusinessId"/>
        <result column="src_ip" jdbcType="VARCHAR" property="srcIp"/>
        <result column="src_province_id" jdbcType="VARCHAR" property="srcProvinceId"/>
        <result column="dest_ip" jdbcType="VARCHAR" property="destIp"/>
        <result column="dest_country" jdbcType="VARCHAR" property="destCountry"/>
        <result column="traffic_type" jdbcType="VARCHAR" property="trafficType"/>
        <result column="model_code" jdbcType="VARCHAR" property="modelCode"/>
        <result column="model_name" jdbcType="VARCHAR" property="modelName"/>
        <result column="rate" jdbcType="INTEGER" property="rate"/>
        <result column="user" jdbcType="VARCHAR" property="user"/>
        <result column="times" jdbcType="BIGINT" property="times"/>
        <result column="log_count" jdbcType="BIGINT" property="logCount"/>
        <result column="system_code" jdbcType="VARCHAR" property="systemCode"/>
        <result column="log_city_id" jdbcType="VARCHAR" property="logCityId"/>
    </resultMap>
    <sql id="groupByMapLocation">
        group by
        <if test="mapLocationSearchDimension=='province'"> province_id </if>
        <if test="mapLocationSearchDimension=='city'"> log_city_id </if>
    </sql>
    <sql id="keyMapLocation">
        <if test="mapLocationSearchDimension=='province'"> province_id as key_type </if>
        <if test="mapLocationSearchDimension=='city'"> log_city_id  as key_type </if>
    </sql>
    <sql  id="whereBase">
        where 1=1
            <if test="todayTimePartition!=null">
                AND day = #{todayTimePartition}
            </if>
            <if test="startTimePartition!=null">
                AND day >= #{startTimePartition}
            </if>
            <if test="endTimePartition!=null">
                AND day &lt;= #{endTimePartition}
            </if>
            <if test="nowTimePartition!=null">
                AND day &lt;= #{nowTimePartition}
            </if>
    </sql>

    <sql id="viewBaseSql">
        (
            select
                day,
                hour,
                anyMerge(month) AS month,
                anyMerge(week) AS week,
                anyMerge(model_name) AS model_name,
                province_id,
                network_business_id,
                traffic_type,
                src_ip,
                src_province_id,
                dest_ip,
                dest_country,
                user,
                model_code,
                protocol_type,
                rate,
                countMerge(log_count) AS log_count,
                system_code,
                log_city_id
            from e2_machine_learning_log_view
            <where>
                <if test="todayTimePartition!=null and todayTimePartition != '' ">
                    AND day = #{todayTimePartition}
                </if>
                <if test="startTimePartition!=null and startTimePartition != ''">
                    AND day >= #{startTimePartition}
                </if>
                <if test="endTimePartition!=null and endTimePartition != ''">
                    AND day &lt;= #{endTimePartition}
                </if>
            </where>
            group by
                day,
                hour,
                province_id,
                network_business_id,
                traffic_type,
                src_ip,
                src_province_id,
                dest_ip,
                dest_country,
                user,
                model_code,
                protocol_type,
                rate,
                system_code,
                log_city_id
                ) AS base_view
    </sql>
    <sql id="keyBaseSql">
        <if test="screenTypeEnumCode=='day'"> hour as key_type </if>
        <if test="screenTypeEnumCode=='week'"> day  as key_type </if>
        <if test="screenTypeEnumCode=='month'"> anyMerge(week) as key_type </if>
        <if test="screenTypeEnumCode=='sofar'"> anyMerge(month)  as key_type </if>
    </sql>
    <sql id="groupByTimeBaseSql">
        <if test="screenTypeEnumCode=='day'">group by hour </if>
        <if test="screenTypeEnumCode=='week'">group by day </if>
    </sql>
    <sql id="sumLogCountSql">
        sum(log_count) as num
    </sql>

    <select id="countNum" resultType="java.lang.Long" parameterType="com.eversec.antivpn.log.mapper.vo.BaseSearchVO">
        select
            countMerge(log_count) AS log_count
        from
            e2_machine_learning_log_view
        <include refid="whereBase"></include>
    </select>

    <select id="getDateHistogram" resultMap="TypeCount">
        SELECT
        <include refid="keyBaseSql"></include>
          ,
        countMerge(log_count) AS num
        FROM e2_machine_learning_log_view
        <include refid="whereBase"></include>
        <include refid="groupByTimeBaseSql"></include>
        order by key_type
    </select>


    <select id="getProvinceAggregate" resultMap="TypeCount" parameterType="com.eversec.antivpn.log.mapper.vo.BaseSearchVO">
        SELECT
        <include refid="keyMapLocation"></include>
        ,countMerge(log_count) as num
        FROM  e2_machine_learning_log_view
        <include refid="whereBase"></include>
        and key_type is not null
        <include refid="groupByMapLocation"></include>
    </select>


    <select id="getProtocolTypeAggregate" resultMap="TypeCount" parameterType="com.eversec.antivpn.log.mapper.vo.BaseSearchVO">
    SELECT
    protocol_type key_type,
    countMerge(log_count)  as num
    FROM e2_machine_learning_log_view
    <include refid="whereBase"></include>
    GROUP BY
    protocol_type
    </select>


    <select id="getResourceNodeCount" resultType="java.lang.Long" parameterType="com.eversec.antivpn.log.mapper.vo.BaseSearchVO">
        SELECT
        count(distinct (dest_ip)) num
        FROM  e2_machine_learning_log_view
        <include refid="whereBase"></include>
        and traffic_type=2
    </select>

    <select id="getResourceNodeUserNum" resultMap="VpnAggVO" parameterType="com.eversec.antivpn.log.mapper.vo.BaseSearchVO">
        SELECT
        count(distinct (user)) num,
        dest_ip as resource_node_name
        FROM  e2_machine_learning_log_view
        <include refid="whereBase"></include>
        and traffic_type=2
        group by dest_ip
        order by num desc
    </select>


    <select id="getResourceNodeDateHistogram" resultMap="VpnHistogramAggVO" parameterType="com.eversec.antivpn.log.mapper.vo.BaseSearchVO">
        SELECT
        <include refid="keyBaseSql"></include>,
        count(distinct (dest_ip)) resource_node_num
        FROM e2_machine_learning_log_view
        <include refid="whereBase"></include>
        and traffic_type=2
        <include refid="groupByTimeBaseSql"></include>
        order by key_type
    </select>

    <select id="getLogNumByProvince" resultMap="TypeCount" parameterType="com.eversec.antivpn.log.mapper.vo.BaseSearchVO">
        select
        province_id as key_type,countMerge(log_count)  as num
        from
            e2_machine_learning_log_view
        <include refid="whereBase"></include>
        group by province_id
        order by num desc
    </select>

    <select id="getLogNumByCity" resultMap="TypeCount" parameterType="com.eversec.antivpn.log.mapper.vo.BaseSearchVO">
        select
        log_city_id as key_type,countMerge(log_count)  as num
        from
             e2_machine_learning_log_view
        <include refid="whereBase"></include>
        group by log_city_id
        order by num desc
    </select>

    <select id="getLogNumVisitAreaAndProvinceId" resultType="java.lang.Long" parameterType="com.eversec.antivpn.log.mapper.vo.BaseSearchVO">
        select
            count(distinct(src_province_id)) as num
        from
            e2_machine_learning_log_view
        <include refid="whereBase"></include>
        and traffic_type = 2
    </select>


    <select id="getLogNumPurposeAreaAndProvinceId" resultType="java.lang.Long" parameterType="com.eversec.antivpn.log.mapper.vo.BaseSearchVO">
        select
        count(distinct(dest_country)) as num
        from
            e2_machine_learning_log_view
        <include refid="whereBase"></include>
        and traffic_type = 2
    </select>

    <select id="getModelDiscernLogNum" resultMap="baseResult" parameterType="com.eversec.antivpn.log.mapper.vo.BaseSearchVO">
        select
        anyMerge(model_name) model_name , model_code , countMerge(log_count)  as num
        from e2_machine_learning_log_view
        <include refid="whereBase"></include>
        group by model_code
        HAVING model_code != ''
        order by num desc
        limit 10
    </select>

    <select id="getProtocolTypeLogNum" resultMap="baseResult" parameterType="com.eversec.antivpn.log.mapper.vo.BaseSearchVO">
        select
        protocol_type , sum(log_count)  as num
        from
        <include refid="viewBaseSql"></include>
        group by protocol_type
        order by num desc
    </select>

    <select id="getAILogByDay" resultMap="baseResult" parameterType="com.eversec.antivpn.log.mapper.vo.BaseSearchVO">
        select
        hour as times ,countMerge(log_count)  as num
        from e2_machine_learning_log_view
        <include refid="whereBase"></include>
        group by hour
    </select>

    <select id="getAILogByWeek" resultMap="baseResult" parameterType="com.eversec.antivpn.log.mapper.vo.BaseSearchVO">
        select
        day as times ,countMerge(log_count)  as num
        from e2_machine_learning_log_view
        <include refid="whereBase"></include>
        group by day
    </select>
    <select id="getAILogByMonth" resultMap="baseResult" parameterType="com.eversec.antivpn.log.mapper.vo.BaseSearchVO">
        select
        anyMerge(week) as times ,countMerge(log_count)  as num
        from e2_machine_learning_log_view
        <include refid="whereBase"></include>
    </select>

    <select id="getAILogByYear" resultMap="baseResult" parameterType="com.eversec.antivpn.log.mapper.vo.BaseSearchVO">
        select
        anyMerge(month) as times ,countMerge(log_count)  as num
        from e2_machine_learning_log_view
        <include refid="whereBase"></include>
    </select>

    <select id="getLogNumByRateList" resultMap="baseResult" parameterType="com.eversec.antivpn.log.mapper.vo.BaseSearchVO">
        select
        rate,countMerge(log_count)  as num
        from e2_machine_learning_log_view
        <include refid="whereBase"></include>
        group by rate
        order by rate
    </select>
    <select id="getSystemLogNum" resultMap="baseResult" parameterType="com.eversec.antivpn.log.mapper.vo.BaseSearchVO">
        select
        system_code,countMerge(log_count)  as num
        from e2_machine_learning_log_view
        <include refid="whereBase"></include>
        group by system_code
        order by  num
    </select>

    <select id="getVpnUserNum" resultType="java.lang.Long" parameterType="com.eversec.antivpn.log.mapper.vo.BaseSearchVO">
        select
            count(distinct (user))  as num
        from
            e2_machine_learning_log_view
        <include refid="whereBase"></include>
        and user is not null
    </select>


    <select id="getUserLocation" resultType="java.lang.Long" parameterType="com.eversec.antivpn.log.mapper.vo.BaseSearchVO">
        select
        <if test="mapLocationSearchDimension=='province'"> distinct(province_id) as key_type </if>
        <if test="mapLocationSearchDimension=='city'"> distinct(log_city_id)  as key_type </if>
        from
        e2_machine_learning_log_view
        <include refid="whereBase"></include>
        and
        <if test="mapLocationSearchDimension=='province'"> province_id is not null </if>
        <if test="mapLocationSearchDimension=='city'"> log_city_id is not null</if>
    </select>

    <select id="getDestCountry" resultType="java.lang.String" parameterType="com.eversec.antivpn.log.mapper.vo.BaseSearchVO">
        select
        distinct (dest_country)  as key_type
        from e2_machine_learning_log_view
        <include refid="whereBase"></include>
        and dest_country != '中国' and dest_country!='NA'
    </select>


    <select id="getUserDest" resultMap="TypeCount" parameterType="com.eversec.antivpn.log.mapper.vo.BaseSearchVO">
        select
        dest_country as key_type,
        countMerge(log_count)  as num
        from e2_machine_learning_log_view
        <include refid="whereBase"></include>
        and dest_country not in ('中国','NA')
        group by dest_country
        order by num desc limit 20
    </select>


    <select id="getLocationAggregate" resultMap="TypeCount" parameterType="com.eversec.antivpn.log.mapper.vo.BaseSearchVO">
        SELECT
        <include refid="keyMapLocation"></include>
        ,
        count(distinct (user)) num
        FROM  e2_machine_learning_log_view
        <include refid="whereBase"></include> and key_type is not null
        <include refid="groupByMapLocation"></include>
        order by num desc
    </select>


    <select id="userCountLogNum" resultMap="TypeCount" parameterType="com.eversec.antivpn.log.mapper.vo.BaseSearchVO">
        SELECT
        user key_type,
        countMerge(log_count) as num
        FROM e2_machine_learning_log_view
        <include refid="whereBase"></include>
        GROUP BY
        user
        order by num desc
        limit 20
    </select>



    <select id="getUserDateHistogram" resultMap="TypeCount" parameterType="com.eversec.antivpn.log.mapper.vo.BaseSearchVO">
        SELECT
        <include refid="keyBaseSql"></include>,
        count(distinct (user)) num
        FROM  e2_machine_learning_log_view
        <include refid="whereBase"></include>
        <include refid="groupByTimeBaseSql"></include>
        order by key_type
    </select>


    <select id="getNetworkBusinessTypeAggregate" resultMap="TypeCount" parameterType="com.eversec.antivpn.log.mapper.vo.BaseSearchVO">
        SELECT
        network_business_id key_type,
        countMerge(log_count)  as num
        FROM  e2_machine_learning_log_view
        <include refid="whereBase"></include>
        GROUP BY network_business_id
    </select>

    <select id="getSystemLogAggCount" resultMap="TypeCount" parameterType="com.eversec.antivpn.log.mapper.vo.BaseSearchVO">
        SELECT
        system_code key_type,
        countMerge(log_count)  as num
        FROM e2_machine_learning_log_view
        <include refid="whereBase"></include>
        and key_type != ''
        GROUP BY system_code
    </select>

    <select id="getCountryResourceNodeNumTop10" resultMap="TypeCount" parameterType="com.eversec.antivpn.log.mapper.vo.BaseSearchVO">
        SELECT
        dest_country key_type,
        count(distinct (dest_ip))  as num
        FROM e2_machine_learning_log_view
        <include refid="whereBase"></include>
        and dest_country not in ('','NA') and traffic_type = 2
        GROUP BY dest_country
        order by num desc
    </select>

    <select id="getUserAndSrcIp" resultMap="UserViewVO" parameterType="com.eversec.antivpn.log.mapper.vo.BaseSearchVO">
        select user as user,
        max(src_ip) as src_ip
        FROM  <include refid="viewBaseSql"></include>
        where user in
        <foreach collection="strIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        group by user
    </select>

    <resultMap id="MonitorDataVO" type="com.eversec.antivpn.log.mapper.vo.MonitorDataVO">
        <result column="province_id" jdbcType="INTEGER" property="provinceId"/>
        <result column="day" jdbcType="INTEGER" property="day"/>
        <result column="hour" jdbcType="INTEGER" property="hour"/>
        <result column="system_code" jdbcType="VARCHAR" property="systemCode"/>
        <result column="num" jdbcType="BIGINT" property="num"/>
    </resultMap>
    <select id="getSystemProvinceCount" resultMap="MonitorDataVO">
        SELECT
            province_id,
            day,hour,
            system_code,
            countMerge(log_count)  as num
        FROM  e1_comm_log_view
        where day=#{day} and hour in (#{hour},#{hour}-1)
        GROUP BY system_code,province_id,day,hour
        ORDER BY province_id,system_code,day,hour
    </select>
    <select id="getModelTwiterLogNum" resultMap="baseResult" parameterType="com.eversec.antivpn.log.mapper.vo.BaseSearchVO">
        select
        anyMerge(model_name) model_name , model_code , countMerge(log_count)  as num
        from e2_machine_learning_log_view
        <include refid="whereBase"></include>
        group by model_code
        HAVING model_code != '' and model_name like '%Twiter%'
        order by num desc
    </select>
</mapper>
