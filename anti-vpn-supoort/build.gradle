
dependencies {

    implementation project(":anti-vpn-supoort:anti-vpn-supoort-api")
    implementation project(":anti-vpn-common:anti-vpn-config")
    implementation project(":anti-vpn-common:anti-vpn-util")

    // mybatis-plus工具扩展等
    implementation "com.eversec.stark:stark-common:${starkCommonVersion}"
    implementation "com.eversec.stark.generic:generic-common:${genericVersion}"
    // 数据字典、系统配置api
    implementation("com.eversec.stark.generic:generic-sys-api:${genericVersion}") {changing = true}

    // https://mvnrepository.com/artifact/commons-beanutils/commons-beanutils
    implementation 'commons-beanutils:commons-beanutils:1.9.4'

    implementation "cn.hutool:hutool-all:${hutoolVersion}"
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation "org.springframework.cloud:spring-cloud-starter-bootstrap"
    implementation "org.springframework.cloud:spring-cloud-starter-netflix-eureka-client"
    // leader判断
    implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
    implementation "org.springframework.integration:spring-integration-jdbc"
    implementation "com.baomidou:mybatis-plus-boot-starter:${mybatisPlusVersion}"
    implementation "org.springdoc:springdoc-openapi-ui:${springdocOpenapiVersion}"
    implementation "com.github.xiaoymin:knife4j-springdoc-ui:${knife4jSpringdocVersion}"
    //知识库分页返回
    implementation "com.eversec.cloud:cloud-core:${cloudCoreVersion}"
    //知识库导入
    implementation 'org.apache.poi:poi:3.15'
    implementation 'org.apache.poi:poi-ooxml:3.15'
    implementation 'cn.afterturn:easypoi-web:3.0.1'
    implementation 'cn.afterturn:easypoi-annotation:3.0.1'

}
