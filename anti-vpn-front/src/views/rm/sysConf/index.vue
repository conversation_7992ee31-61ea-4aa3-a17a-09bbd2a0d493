<template>
  <el-main class="nacMain sys-conf">
    <page-title />
    <!-- form -->
    <el-form
      ref="form"
      v-loading="dataListLoading"
      class="public-form"
      :model="formData"
      :label-width="'0'"
    >
      <section
        v-for="(items, indexs) in formDataLabel"
        :key="indexs"
        class="columns"
      >
        <el-form-item
          v-for="(item, index) in items"
          :key="index"
          :label="item.label"
          :width="item.width"
          :label-width="item.labelWidth"
          class="column"
          :class="item.className"
        >
          <!-- input输入框 -->
          <el-input
            v-if="item.type === 'input'"
            v-model="formData[item.val]"
            :placeholder="item.placeholder || `请输入${item.label || ''}`"
            :disabled="item.disabled"
            :clearable="item.clearable || true"
            :size="item.size"
            :maxlength="item.maxlength"
            :show-word-limit="item.limit"
            :show-password="item.password"
            @input="changeItem(formData[item.val])"
          />
        </el-form-item>
      </section>
    </el-form>

    <section class="is-overflow-hidden">
      <el-button
        class="is-float-left is-mb-10"
        type="primary"
        @click="addOrUpdateHandle(null)"
      >
        <!--      icon="el-icon-plus"-->
        新增
      </el-button>
      <el-table
        v-loading="dataListLoading"
        :data="dataList"
        border
        stripe
        style="width: 100%;"
      >
        <el-table-column
          prop="itemName"
          header-align="left"
          align="left"
          label="配置名称"
        />
        <el-table-column
          prop="itemKey"
          header-align="left"
          align="left"
          label="配置项KEY"
        />
        <el-table-column
          prop="itemValue"
          header-align="left"
          align="left"
          label="配置项值"
        >
          <template slot-scope="scope">
            <el-popover
              v-if="scope.row.valueType !== 'imgType'"
              trigger="hover"
              :content="scope.row.itemValue"
            >
              <div
                class="is-scroll-y"
                style="max-width: 500px;max-height: 500px;"
              >
                {{ scope.row.itemValue }}
              </div>
              <span slot="reference">
                {{ scope.row.itemValue }}
              </span>
            </el-popover>

            <el-tooltip
              v-else-if="scope.row.itemValue"
              placement="top"
              effect="light"
            >
              <div slot="content">
                <img
                  :src="
                    `/service/${gateway}/common-file/download/${scope.row.itemValue}?contentDispositionType=inline`
                  "
                  alt="图片"
                  class="img-icon"
                />
              </div>
              <el-button
                type="text"
                icon="el-icon-zoom-in"
                style="font-size:24px;"
                title="点击查看大图"
                @click="showImage(scope.row.itemValue)"
              />
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column
          prop="valueType"
          header-align="left"
          align="left"
          width="150"
          label="值类型"
        >
          <template slot-scope="scope">
            <dic-value :dic-key="scope.row.valueType" :enumKey="'VALUE_TYPE'" />
          </template>
        </el-table-column>
        <el-table-column
          header-align="left"
          align="left"
          width="150"
          label="操作"
        >
          <template slot-scope="scope">
            <el-button type="text" @click="addOrUpdateHandle(scope.row)">
              修改
            </el-button>
            <el-button type="text" @click="deletes(scope.row.id)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </section>
    <!-- 分页 -->
    <el-pagination
      class="is-pt-10 is-float-right"
      :current-page="pagingInfo.pageNo"
      :page-sizes="pagingInfo.pageSizes"
      :key="pagingInfo.total"
      :page-size="pagingInfo.pageSize"
      v-loading="dataListLoading"
      :layout="pagingInfo.layout"
      :total="pagingInfo.total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
    <!-- 查看图片大图 -->
    <div ref="viewImage" class="viewImage" v-if="viewImage">
      <img :src="viewImage" alt="大图" />
      <span class="close el-icon-close" @click="closeImage" />
    </div>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update ref="addOrUpdate" @onSubmit="getDataList" />
  </el-main>
</template>

<script>
import api_app from '@/api/rm/sysConf'
import AddOrUpdate from './add-or-update'
import debounce from 'lodash/debounce'
import cloneDeep from 'lodash/cloneDeep'
import serverProxy from '@/config/serverProxy'
let gateway = serverProxy.generic

// 自定义分页配置信息
export const customPagingInfo = ({
  pageNo = 1,
  pageSizes = [10, 30, 50, 100],
  pageSize = 10,
  total = 0,
  layout = 'total, sizes, prev, pager, next, jumper'
}) => {
  return {
    pageNo: pageNo,
    pageSizes: pageSizes,
    pageSize: pageSize,
    total: total,
    layout: layout
  }
}

export default {
  components: {
    AddOrUpdate
  },
  data() {
    return {
      formDataLabel: [
        [
          {
            val: 'itemName',
            type: 'input',
            clearable: true,
            funName: 'changeItem',
            className: 'is-mr-10',
            placeholder: '请输入配置名称'
          },
          {
            val: 'itemKey',
            type: 'input',
            clearable: true,
            funName: 'changeItem',
            className: 'is-mr-10',
            placeholder: '请输入配置项KEY'
          },
          {},
          {}
        ]
      ], // form树
      viewImage: '',
      formData: {},
      dataList: [],
      dataListLoading: false,
      pagingInfo: cloneDeep(customPagingInfo({ pageSize: 100 })), // 分页信息
      gateway
    }
  },
  mounted() {
    this.formData = {}
    for (let name in this.$route.query) {
      if (name === 'pageSize') {
        this.pagingInfo.pageSize = Number(this.$route.query[name])
      } else if (name === 'pageNo') {
        this.pagingInfo.pageNo = Number(this.$route.query[name])
      } else {
        this.$set(this.formData, name, this.$route.query[name])
        this.formData[name] = this.$route.query[name]
      }
    }
    this.getDataList()
  },
  methods: {
    // form组件回调事件
    throwEvent({ funName, val }) {
      if (funName) this[funName](funName)
    },
    // 表单监听回调
    changeItem(val) {
      if (val) {
        this.pagingInfo.pageNo = 1
      }
      this.debounce(this)
    },
    // 防抖查询
    debounce: debounce(vm => {
      let tmpData = {
        itemName: vm.formData.itemName || '',
        itemKey: vm.formData.itemKey || '',
        pageSize: vm.pagingInfo.pageSize,
        pageNo: vm.pagingInfo.pageNo
      }
      let data = {}
      for (let name in tmpData) {
        if (tmpData[name] !== '') {
          data[name] = tmpData[name]
        }
      }
      vm.$router.push({
        path: '/rm/sysConf',
        query: data
      })
      vm.getDataList()
    }, 300),
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      api_app
        .getList({
          pageNo: this.pagingInfo.pageNo,
          pageSize: this.pagingInfo.pageSize,
          itemName: this.formData.itemName,
          itemKey: this.formData.itemKey
        })
        .then(res => {
          this.dataList = res.items
          this.pagingInfo.total = parseInt(res.total)
          this.dataListLoading = false
        })
        .catch(() => {
          this.dataListLoading = false
        })
    },
    // 每页数
    handleSizeChange(val) {
      this.pagingInfo.pageSize = val
      this.pagingInfo.pageNo = 1
      this.changeItem()
    },
    // 当前页
    handleCurrentChange(val) {
      this.pagingInfo.pageNo = val
      this.changeItem()
    },
    showImage(src) {
      this.viewImage = `/service/${gateway}/common-file/download/${src}?contentDispositionType=inline`
    },
    closeImage(src) {
      this.viewImage = ''
    },
    // 新增 / 修改
    addOrUpdateHandle(row) {
      this.$refs.addOrUpdate.init(row)
    },
    // 批量删除
    deletes(id) {
      this.deleteHandle(id)
    },
    // 删除
    deleteHandle(id) {
      this.$confirm(`确定进行[删除]操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          api_app.deleteByIds(id).then(() => {
            this.getDataList()
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500
            })
          })
        })
        .catch(() => {})
    }
  }
}
</script>
<style lang="scss" scoped>
.sys-conf {
  .img-icon {
    max-width: 180px;
    max-height: 180px;
    min-width: 100px;
    min-height: 100px;
  }
  .viewImage {
    z-index: 1000;
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    text-align: center;
    img {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      max-width: 50%;
      max-height: 50%;
    }
    .close {
      position: absolute;
      top: 10%;
      right: 20%;
      color: #fff;
      font-size: 40px;
      cursor: pointer;
    }
  }
  .el-table {
    ::v-deep .cell {
      max-height: 80px;
      span {
        height: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 3;
        word-break: break-all;
      }
    }
  }
}
</style>
