import httpRequest from '@/utils/httpRequest.js';
const BaseServer = '/anti-vpn-service';

// AI
export default {
  // 服务监控-AI模型监测日志
  dateMonitoring(o) {
    return httpRequest({
      url: BaseServer + `/log/aiScreen/dateMonitoring`,
      method: "get",
      params: o,
    });
  },
  // 服务监控-模型识别准确率
  dateRecognition(o) {
    return httpRequest({
      url: BaseServer + `/log/aiScreen/dateRecognition`,
      method: "get",
      params: o,
    });
  },
  // 服务监控-访问者地区数量
  dateVisitorArea(o) {
    return httpRequest({
      url: BaseServer + `/log/aiScreen/dateVisitorArea`,
      method: "get",
      params: o
    });
  },
  // 服务监控-目的地地区数量
  dateDestinationArea(o) {
    return httpRequest({
      url: BaseServer + `/log/aiScreen/dateDestinationArea`,
      method: "get",
      params: o
    });
  },
  // 服务监控-AI模型总量
  dateAISum(o) {
    return httpRequest({
      url: BaseServer + `/log/aiScreen/dateAISum`,
      method: "get",
      params: o
    });
  },
  // AI模拟识别日志量TOP10
  datedDiscernTop(o) {
    return httpRequest({
      url: BaseServer + `/log/aiScreen/datedDiscernTopo10`,
      method: "get",
      params: o
    });
  },
  // AI模拟识别日志量TOP10 twitter
  datedDiscernTopTwitter(o) {
    return httpRequest({
      url: BaseServer + `/log/aiScreen/modelTwiter`,
      method: "get",
      params: o
    });
  },
  // 传输层协议占比
  protocolTypeAggregate(o) {
    return httpRequest({
      url: BaseServer + `/log/aiScreen/protocolTypeAggregate`,
      method: "get",
      params: o
    });
  },
  // AI 模型监测跨境通信日志趋势图
  tendencyChart(o) {
    return httpRequest({
      url: BaseServer + `/log/aiScreen/tendencyChart`,
      method: "get",
      params: o
    });
  },
  // AI 模型识别通信日志置信度分布
  rateChar(o) {
    return httpRequest({
      url: BaseServer + `/log/aiScreen/rateChart`,
      method: "get",
      params: o
    });
  },
  // AI 模型识别通信日志置信度分布（饼图）
  ratePie(o) {
    return httpRequest({
      url: BaseServer + `/log/aiScreen/ratePie`,
      method: "get",
      params: o
    });
  },
  // 省区域上报监测日志TOP10
  datedProvinceTop(o) {
    return httpRequest({
      url: BaseServer + `/log/aiScreen/datedProvinceTopo10`,
      method: "get",
      params: o
    });
  },
  // 省区域上报监测日志TOP10
  mapList(o) {
    return httpRequest({
      url: BaseServer + `/log/aiScreen/chinaMap`,
      method: "get",
      params: o
    });
  },
  // 是否开启省端查询
  startProvince() {
    return httpRequest({
      url: BaseServer + `/config/platformInterfaceInfo/startProvince`,
      method: "get",
    });
  },
  // 各系统上报监测日志占比
  systemRatio(o) {
    return httpRequest({
      url: BaseServer + `/log/aiScreen/systemRatio`,
      method: "get",
      params: o
    });
  },
};
