package com.eversec.antivpn.support.knowledge.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <p>
 * 协议信息DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-12
 */
@Schema(name = "KnowledgeProtocolDTO", description = "协议信息")
@EqualsAndHashCode(callSuper=false)
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class KnowledgeProtocolDTO extends KnowledgeBaseDTO {


    @NotNull(message = "支持软件不能为空")
    @Schema(description = "支持软件")
    private String supportSoftware;

    @NotNull(message = "加密体系不能为空")
    @Schema(description = "加密体系")
    private String encryptSystem;

    @NotNull(message = "传输协议不能为空")
    @Schema(description = "传输协议")
    private String transportProtocol;

    @NotNull(message = "可伪装网络协议不能为空")
    @Schema(description = "可伪装网络协议")
    private String camouflageProtocol;

    @NotNull(message = "OSI模型不能为空")
    @Schema(description = "OSI模型")
    private String osiModel;

    @NotNull(message = "发现时间不能为空")
    @Schema(description = "发现时间")
    private String discoveryTime;

//    @NotNull(message = "入库时间不能为空")
//    @Schema(description = "入库时间")
//    private String insertTime;



}