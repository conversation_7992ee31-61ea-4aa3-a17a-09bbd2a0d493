package com.eversec.antivpn.log.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eversec.antivpn.emnus.ScreenTypeEnum;
import com.eversec.antivpn.log.api.dto.CrossBorderCommLogDTO;
import com.eversec.antivpn.log.api.dto.CrossBorderCommLogRequest;
import com.eversec.antivpn.log.api.dto.base.AggCountDTO;
import com.eversec.antivpn.log.api.dto.base.DateHistogramDTO;
import com.eversec.antivpn.log.constants.CKColumn;
import com.eversec.antivpn.log.constants.CommonField;
import com.eversec.antivpn.log.entity.E1CommLog;
import com.eversec.antivpn.log.entity.E1CommLogPOExt;
import com.eversec.antivpn.log.entity.E2MachineLearningLog;
import com.eversec.antivpn.log.mapper.E1CommLogMapper;;
import com.eversec.antivpn.log.mapper.E1CommLogViewMapper;
import com.eversec.antivpn.log.mapper.vo.BaseSearchVO;
import com.eversec.antivpn.log.mapper.vo.TypeCountVO;
import com.eversec.antivpn.log.mapper.vo.UserLogPageVO;
import com.eversec.antivpn.log.service.IE1CommLogService;
import com.eversec.antivpn.log.util.CKPartitionUtil;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;


/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-12
 */
@Service
@Slf4j
public class E1CommLogServiceImpl extends ServiceImpl<E1CommLogMapper, E1CommLog> implements IE1CommLogService {

    @Resource
    private E1CommLogMapper e1CommLogMapper;

    @Resource
    private E1CommLogViewMapper e1CommLogViewMapper;



    @Override
    public List<TypeCountVO> getApplicationProtocolTypeAggregate(ScreenTypeEnum screenTypeEnum) {
        BaseSearchVO baseSearchVO = new BaseSearchVO();
        baseSearchVO.setStartTimePartition(ScreenTypeEnum.getTimeCKPartition(screenTypeEnum));
        return e1CommLogViewMapper.getApplicationProtocolTypeAggregate(baseSearchVO);
    }

    @Override
    public List<TypeCountVO> getProvinceAggregate(Long startTime, Long endTime) {
        return null;
        //return crossBorderCommLogMapper.getProvinceAggregate(CKPartitionUtil.getStartTimeCKPartition(startTime), CKPartitionUtil.getEndTimeCKPartition(endTime));
    }

    @Override
    public void deleteByLogId(Long logId) {

    }

    @Override
    public AggCountDTO countNum(ScreenTypeEnum screenTypeEnum) {
        AggCountDTO aggCountDTO = new AggCountDTO();
        Long todayTimeCKPartition = ScreenTypeEnum.getTimeCKPartition(ScreenTypeEnum.TODAY);
        LambdaQueryWrapper<E1CommLog> lambdaQueryWrapper = new LambdaQueryWrapper();
        lambdaQueryWrapper.ge(E1CommLog::getDay, todayTimeCKPartition);
        Long todayCount = e1CommLogMapper.selectCount(lambdaQueryWrapper);
        aggCountDTO.setTodayCount(todayCount);
        if (screenTypeEnum == ScreenTypeEnum.TODAY) {
            aggCountDTO.setDateRangeCount(todayCount);
        } else {
            Long startTimeCKPartition = ScreenTypeEnum.getTimeCKPartition(screenTypeEnum);
            lambdaQueryWrapper.clear();
            lambdaQueryWrapper.ge(startTimeCKPartition != null,E1CommLog::getDay, startTimeCKPartition)
                    .le(E1CommLog::getDay, todayTimeCKPartition);
            Long dateRangeCount = e1CommLogMapper.selectCount(lambdaQueryWrapper);
            aggCountDTO.setDateRangeCount(dateRangeCount);
        }
        return aggCountDTO;
    }

    @Override
    public List<E1CommLog> getE1LogsByPartition(ScreenTypeEnum screenTypeEnum) {
        LambdaQueryWrapper<E1CommLog> lambdaQueryWrapper = new LambdaQueryWrapper();
        setScreenTimeByEnums(screenTypeEnum,lambdaQueryWrapper);
        List<E1CommLog> e1CommLogs = e1CommLogMapper.selectList(lambdaQueryWrapper);
        return e1CommLogs;
    }

    @Override
    public List<E1CommLog> getLogNumBySofaWareTop10(ScreenTypeEnum screenTypeEnum) {
        QueryWrapper<E1CommLog> queryWrapper = new QueryWrapper();
        queryWrapper.select("count( log_count) num,arrayJoin(JSONExtractArrayRaw(vpn_software_code)) as softWareIds");
        setScreenTimeByEnumsAndQueryWrapper(screenTypeEnum,queryWrapper);
        queryWrapper.groupBy("softWareIds");
        queryWrapper.last("order by num  desc");
        queryWrapper.last("limit 10");
        List<E1CommLog> e1CommLogs = e1CommLogMapper.selectList(queryWrapper);
        return e1CommLogs;
    }

    @Override
    public List<E1CommLog> getLogNumByUserTop10(ScreenTypeEnum screenTypeEnum) {
        QueryWrapper<E1CommLog> queryWrapper = new QueryWrapper();
        queryWrapper.select("count(user) num,arrayJoin(JSONExtractArrayRaw(vpn_software_code)) as softWareIds");
        setScreenTimeByEnumsAndQueryWrapper(screenTypeEnum,queryWrapper);
        queryWrapper.groupBy("softWareIds");
        queryWrapper.last("order by num  desc");
        queryWrapper.last("limit 10");
        List<E1CommLog> e1CommLogs = e1CommLogMapper.selectList(queryWrapper);
        return e1CommLogs;
    }

    @Override
    public List<E1CommLog> getLogNumBySofaWareAndDayTop10(ScreenTypeEnum screenTypeEnum) {
        QueryWrapper<E1CommLog> queryWrapper = new QueryWrapper();
        queryWrapper.select("sum(log_count) num,arrayJoin(JSONExtractArrayRaw(vpn_software_code)) as softWareIds,day ");
        setScreenTimeByEnumsAndQueryWrapper(screenTypeEnum,queryWrapper);
        queryWrapper.groupBy("softWareIds,day");
        queryWrapper.last("order by day");
        queryWrapper.last("limit 10");
        List<E1CommLog> e1CommLogs = e1CommLogMapper.selectList(queryWrapper);
        return e1CommLogs;
    }

    /**
     * 根据查询枚举设置查询条件时间
     * @param screenTypeEnum
     * @param lambdaQueryWrapper
     */
    private void setScreenTimeByEnums(ScreenTypeEnum screenTypeEnum, LambdaQueryWrapper<E1CommLog> lambdaQueryWrapper) {
        switch (screenTypeEnum) {

            case TODAY:
                lambdaQueryWrapper.eq(E1CommLog :: getDay,ScreenTypeEnum.getTimeCKPartition(ScreenTypeEnum.TODAY));
                break;
            case THISWEEK:
                //上周今天（往前7天）
                lambdaQueryWrapper.ge(E1CommLog :: getDay,ScreenTypeEnum.getTimeCKPartition(ScreenTypeEnum.THISWEEK));
                lambdaQueryWrapper.le(E1CommLog :: getDay,ScreenTypeEnum.getTimeCKPartition(ScreenTypeEnum.TODAY));
                break;
            case THISWEEK_BEFORE:
                //上周今天（往前14天）
                lambdaQueryWrapper.ge(E1CommLog :: getDay,ScreenTypeEnum.getTimeCKPartition(ScreenTypeEnum.THISWEEK_BEFORE));
                lambdaQueryWrapper.le(E1CommLog :: getDay,ScreenTypeEnum.getTimeCKPartition(ScreenTypeEnum.THISWEEK));
                break;
            case THISMONTH:
                lambdaQueryWrapper.ge(E1CommLog :: getDay,ScreenTypeEnum.getTimeCKPartition(ScreenTypeEnum.THISMONTH));
                lambdaQueryWrapper.le(E1CommLog :: getDay,ScreenTypeEnum.getTimeCKPartition(ScreenTypeEnum.TODAY));
                break;
            case SOFAR:
                lambdaQueryWrapper.ge(E1CommLog :: getDay,ScreenTypeEnum.getTimeCKPartition(ScreenTypeEnum.SOFAR));
                lambdaQueryWrapper.le(E1CommLog :: getDay,ScreenTypeEnum.getTimeCKPartition(ScreenTypeEnum.TODAY));
                break;
        }
    }

    /**
     * 根据查询枚举设置查询条件时间
     * @param screenTypeEnum
     * @param lambdaQueryWrapper
     */
    private void setScreenTimeByEnumsAndQueryWrapper(ScreenTypeEnum screenTypeEnum, QueryWrapper<E1CommLog> lambdaQueryWrapper) {
        switch (screenTypeEnum) {

            case TODAY:
                lambdaQueryWrapper.eq(CKColumn.DAY,ScreenTypeEnum.getTimeCKPartition(ScreenTypeEnum.TODAY));
                break;
            case THISWEEK:
                //上周今天（往前7天）
                lambdaQueryWrapper.ge(CKColumn.DAY,ScreenTypeEnum.getTimeCKPartition(ScreenTypeEnum.THISWEEK));
                break;
            case THISWEEK_BEFORE:
                //上周今天（往前14天）
                lambdaQueryWrapper.ge(CKColumn.DAY,ScreenTypeEnum.getTimeCKPartition(ScreenTypeEnum.THISWEEK_BEFORE));
                lambdaQueryWrapper.le(CKColumn.DAY,ScreenTypeEnum.getTimeCKPartition(ScreenTypeEnum.THISWEEK));
                break;
            case THISMONTH:
                lambdaQueryWrapper.ge(CKColumn.DAY,ScreenTypeEnum.getTimeCKPartition(ScreenTypeEnum.THISMONTH));
                break;
            case SOFAR:
                lambdaQueryWrapper.le(CKColumn.DAY,ScreenTypeEnum.getTimeCKPartition(ScreenTypeEnum.TODAY));
                break;
        }
    }

}
