package com.eversec.antivpn.log.util;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.eversec.antivpn.emnus.ScreenTypeEnum;
import com.eversec.antivpn.log.constants.CKColumn;
import com.eversec.antivpn.log.constants.CommonField;
import com.eversec.antivpn.log.entity.E1CommLogUserView;
import com.eversec.antivpn.log.entity.E2MachineLearningLogView;
import com.eversec.antivpn.log.mapper.vo.BaseSearchVO;
import io.swagger.models.auth.In;
import org.apache.commons.lang3.time.DateUtils;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/8/4 14:37
 **/

public class CKPartitionUtil {
    public static Integer getStartTimeCKPartition(Long startTime){
        Integer dayCKPartition;
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        if(startTime != null) {
            dayCKPartition = Integer.valueOf(sdf.format(new Date(startTime)));
        }else {
            Date now = new Date();
            Date startDate = DateUtils.addDays(now, -6);
            dayCKPartition = Integer.valueOf(sdf.format(startDate));
        }
        return dayCKPartition;
    }

    public static Integer getEndTimeCKPartition(Long endTime){
        Integer dayCKPartition;
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        if(endTime != null){
            dayCKPartition = Integer.valueOf(sdf.format(new Date(endTime)));
        }else{
            Date now = new Date();
            dayCKPartition = Integer.valueOf(sdf.format(now));
        }
        return dayCKPartition;
    }

    public static Long getNumsDayBeforeCKPartition(int daysBefore){
        Date now = new Date();
        daysBefore = daysBefore*-1;
        Date date = DateUtils.addDays(now, daysBefore);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        return Long.valueOf(sdf.format(date));
    }

    public static Long getStartTimeCKPartition(){
        return ScreenTypeEnum.getTimeCKPartition(ScreenTypeEnum.TODAY);
    }
    public static Integer getTodayCKPartition(){
        return getEndTimeCKPartition(null);
    }

    /**
     * 根据查询枚举设置查询条件时间
     * @param screenTypeEnum
     * @param lambdaQueryWrapper
     */
    public static void setScreenTimeByEnumsE2MachineLearningLogView(ScreenTypeEnum screenTypeEnum, QueryWrapper<E2MachineLearningLogView> lambdaQueryWrapper) {
        switch (screenTypeEnum) {

            case TODAY:
                lambdaQueryWrapper.eq(CKColumn.DAY,ScreenTypeEnum.getTimeCKPartition(ScreenTypeEnum.TODAY));
                break;
            case THISWEEK:
                //上周今天（往前7天）
                lambdaQueryWrapper.ge(CKColumn.DAY,ScreenTypeEnum.getTimeCKPartition(ScreenTypeEnum.THISWEEK));
                lambdaQueryWrapper.le(CKColumn.DAY,ScreenTypeEnum.getTimeCKPartition(ScreenTypeEnum.TODAY));
                break;
            case THISMONTH:
                lambdaQueryWrapper.ge(CKColumn.DAY,ScreenTypeEnum.getTimeCKPartition(ScreenTypeEnum.THISMONTH));
                lambdaQueryWrapper.le(CKColumn.DAY,ScreenTypeEnum.getTimeCKPartition(ScreenTypeEnum.TODAY));
                break;
            case SOFAR:
                lambdaQueryWrapper.ge(CKColumn.DAY,ScreenTypeEnum.getTimeCKPartition(ScreenTypeEnum.SOFAR));
                lambdaQueryWrapper.le(CKColumn.DAY,ScreenTypeEnum.getTimeCKPartition(ScreenTypeEnum.TODAY));
                break;
        }
    }
    /**
     * 根据查询枚举设置查询条件时间
     * @param screenTypeEnum
     * @param lambdaQueryWrapper
     */
    public static void setScreenTimeByEnumsE1CommLogUserView(ScreenTypeEnum screenTypeEnum, LambdaQueryWrapper<E1CommLogUserView> lambdaQueryWrapper) {
        switch (screenTypeEnum) {

            case TODAY:
                lambdaQueryWrapper.eq(E1CommLogUserView :: getDay,ScreenTypeEnum.getTimeCKPartition(ScreenTypeEnum.TODAY));
                break;
            case THISWEEK:
                //上周今天（往前7天）
                lambdaQueryWrapper.ge(E1CommLogUserView :: getDay,ScreenTypeEnum.getTimeCKPartition(ScreenTypeEnum.THISWEEK));
                lambdaQueryWrapper.le(E1CommLogUserView :: getDay,ScreenTypeEnum.getTimeCKPartition(ScreenTypeEnum.TODAY));
                break;
            case THISWEEK_BEFORE:
                //上周今天（往前14天）
                lambdaQueryWrapper.ge(E1CommLogUserView :: getDay,ScreenTypeEnum.getTimeCKPartition(ScreenTypeEnum.THISWEEK_BEFORE));
                lambdaQueryWrapper.le(E1CommLogUserView :: getDay,ScreenTypeEnum.getTimeCKPartition(ScreenTypeEnum.THISWEEK));
                break;
            case THISMONTH:
                lambdaQueryWrapper.ge(E1CommLogUserView :: getDay,ScreenTypeEnum.getTimeCKPartition(ScreenTypeEnum.THISMONTH));
                lambdaQueryWrapper.le(E1CommLogUserView :: getDay,ScreenTypeEnum.getTimeCKPartition(ScreenTypeEnum.TODAY));
                break;
            case SOFAR:
                lambdaQueryWrapper.ge(E1CommLogUserView :: getDay,ScreenTypeEnum.getTimeCKPartition(ScreenTypeEnum.SOFAR));
                lambdaQueryWrapper.le(E1CommLogUserView :: getDay,ScreenTypeEnum.getTimeCKPartition(ScreenTypeEnum.TODAY));
                break;
        }
    }

    /**
     * 根据查询枚举设置查询条件时间
     * @param screenTypeEnum
     * @param lambdaQueryWrapper
     */
    public static void setScreenTimeByEnumsAndQueryWrapperE1CommLogUserView(ScreenTypeEnum screenTypeEnum, QueryWrapper<E1CommLogUserView> lambdaQueryWrapper) {
        switch (screenTypeEnum) {

            case TODAY:
                lambdaQueryWrapper.eq(CKColumn.DAY,ScreenTypeEnum.getTimeCKPartition(ScreenTypeEnum.TODAY));
                break;
            case THISWEEK:
                //上周今天（往前7天）
                lambdaQueryWrapper.ge(CKColumn.DAY,ScreenTypeEnum.getTimeCKPartition(ScreenTypeEnum.THISWEEK));
                break;
            case THISWEEK_BEFORE:
                //上周今天（往前14天）
                lambdaQueryWrapper.ge(CKColumn.DAY,ScreenTypeEnum.getTimeCKPartition(ScreenTypeEnum.THISWEEK_BEFORE));
                lambdaQueryWrapper.le(CKColumn.DAY,ScreenTypeEnum.getTimeCKPartition(ScreenTypeEnum.THISWEEK));
                break;
            case THISMONTH:
                lambdaQueryWrapper.ge(CKColumn.DAY,ScreenTypeEnum.getTimeCKPartition(ScreenTypeEnum.THISMONTH));
                break;
            case SOFAR:
                lambdaQueryWrapper.le(CKColumn.DAY,ScreenTypeEnum.getTimeCKPartition(ScreenTypeEnum.TODAY));
                break;
        }
    }



    /**
     * 根据查询枚举设置查询条件时间
     *  查询条件：今天-> 按小时、近一周-> 按天、月-> 按周、至今-> 按月
     * @param screenTypeEnum
     * @param lambdaQueryWrapper
     */
    public static void setScreenTimeGroupByEnumsE2MachineLearningLogView(ScreenTypeEnum screenTypeEnum, QueryWrapper<E2MachineLearningLogView> lambdaQueryWrapper) {
        switch (screenTypeEnum) {

            case TODAY:
                lambdaQueryWrapper.select(" hour as times ,sum(log_id)  as num");
                lambdaQueryWrapper.eq(CKColumn.DAY,ScreenTypeEnum.getTimeCKPartition(ScreenTypeEnum.TODAY));
                lambdaQueryWrapper.groupBy(CKColumn.HOUR);
                break;
            case THISWEEK:
                //上周今天（往前7天）
                lambdaQueryWrapper.select(" day as times,sum(log_id)  as num");
                lambdaQueryWrapper.ge(CKColumn.DAY,ScreenTypeEnum.getTimeCKPartition(ScreenTypeEnum.THISWEEK));
                lambdaQueryWrapper.le(CKColumn.DAY,ScreenTypeEnum.getTimeCKPartition(ScreenTypeEnum.TODAY));
                lambdaQueryWrapper.groupBy(CKColumn.DAY);
                break;
            case THISMONTH:
                lambdaQueryWrapper.select(" week as times,sum(log_id)  as num");
                lambdaQueryWrapper.ge(CKColumn.DAY,ScreenTypeEnum.getTimeCKPartition(ScreenTypeEnum.THISMONTH));
                lambdaQueryWrapper.le(CKColumn.DAY, ScreenTypeEnum.getTimeCKPartition(ScreenTypeEnum.TODAY));
                lambdaQueryWrapper.groupBy(CKColumn.WEEK);
                break;
            case SOFAR:
                lambdaQueryWrapper.select(" month as times,sum(log_id)  as num");
                lambdaQueryWrapper.le(CKColumn.DAY,ScreenTypeEnum.getTimeCKPartition(ScreenTypeEnum.TODAY));
                lambdaQueryWrapper.groupBy(CKColumn.MONTH);
                break;
        }
    }

    /**
     * 根据查询枚举设置查询视图条件时间
     * @param screenTypeEnum
     * @return BaseSearchVO
     */
    public static BaseSearchVO setScreenTimeByBaseSearchVO(ScreenTypeEnum screenTypeEnum) {
        BaseSearchVO baseSearchVO = new BaseSearchVO();
        switch (screenTypeEnum) {

            case TODAY:
                baseSearchVO.setTodayTimePartition(ScreenTypeEnum.getTimeCKPartition(screenTypeEnum));
                break;
            case THISWEEK:
                //上周今天（往前7天）
                baseSearchVO.setStartTimePartition(ScreenTypeEnum.getTimeCKPartition(screenTypeEnum));
                break;
            case THISWEEK_BEFORE:
                //上周今天（往前14天）
                baseSearchVO.setStartTimePartition(ScreenTypeEnum.getTimeCKPartition(ScreenTypeEnum.THISWEEK_BEFORE));
                baseSearchVO.setEndTimePartition(ScreenTypeEnum.getTimeCKPartition(ScreenTypeEnum.THISWEEK));
                break;
            case THISMONTH:
                baseSearchVO.setStartTimePartition(ScreenTypeEnum.getTimeCKPartition(screenTypeEnum));
                break;
            case SOFAR:
                baseSearchVO.setEndTimePartition(ScreenTypeEnum.getTimeCKPartition(screenTypeEnum));
                break;
        }
        return baseSearchVO;
    }


    /**
     * 根据查询枚举设置查询视图条件时间
     * @param screenTypeEnum
     * @return BaseSearchVO
     */
    public static BaseSearchVO setScreenTimeByBaseSearchVOAndGroupTime(ScreenTypeEnum screenTypeEnum) {
        BaseSearchVO baseSearchVO = new BaseSearchVO();
        switch (screenTypeEnum) {

            case TODAY:
                baseSearchVO.setTodayTimePartition(ScreenTypeEnum.getTimeCKPartition(screenTypeEnum));
                baseSearchVO.setScreenTypeEnumCode(screenTypeEnum.getCode());
                break;
            case THISWEEK:
                //上周今天（往前7天）
                baseSearchVO.setStartTimePartition(ScreenTypeEnum.getTimeCKPartition(screenTypeEnum));
                baseSearchVO.setScreenTypeEnumCode(screenTypeEnum.getCode());
                break;
            case THISWEEK_BEFORE:
                //上周今天（往前14天）
                baseSearchVO.setStartTimePartition(ScreenTypeEnum.getTimeCKPartition(ScreenTypeEnum.THISWEEK_BEFORE));
                baseSearchVO.setEndTimePartition(ScreenTypeEnum.getTimeCKPartition(ScreenTypeEnum.THISWEEK));
                baseSearchVO.setScreenTypeEnumCode(screenTypeEnum.getCode());
                break;
            case THISMONTH:
                baseSearchVO.setStartTimePartition(ScreenTypeEnum.getTimeCKPartition(screenTypeEnum));
                baseSearchVO.setScreenTypeEnumCode(screenTypeEnum.getCode());
                break;
            case SOFAR:
                baseSearchVO.setEndTimePartition(ScreenTypeEnum.getTimeCKPartition(screenTypeEnum));
                baseSearchVO.setScreenTypeEnumCode(screenTypeEnum.getCode());
                break;
        }
        return baseSearchVO;
    }
}
