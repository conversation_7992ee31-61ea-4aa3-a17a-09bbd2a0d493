<template>
  <el-dialog
    :title="dataForm.id == null ? '新增' : '修改'"
    :visible.sync="visible"
    :close-on-click-modal="false"
    @closed="reset"
  >
    <el-form
      ref="dataForm"
      :model="dataForm"
      :rules="dataRule"
      label-width="80px"
      @keyup.enter.native="dataFormSubmit"
    >
      <el-form-item label="枚举名称" prop="typeKey">
        <el-input
          v-model="dataForm.typeKey"
          placeholder="枚举名称"
          :disabled="dataForm.id"
        />
      </el-form-item>
      <el-form-item label="描述" prop="typeVal">
        <el-input v-model="dataForm.typeVal" placeholder="描述" />
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button
        type="primary"
        :loading="submitLoading"
        @click="dataFormSubmit"
      >
        确定
      </el-button>
    </span>
  </el-dialog>
</template>

<script>
import api_setManager from '@/api/rm/dic'
import cloneDeep from 'lodash/cloneDeep'

export default {
  data() {
    return {
      visible: false,
      submitLoading: false,
      dataForm: {
        typeVal: '',
        typeKey: '',
        id: null
      },
      dataRule: {
        typeVal: [{ required: true, message: '描述不能为空', trigger: 'blur' }],
        typeKey: [
          { required: true, message: '枚举名称不能为空', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    init(row) {
      this.visible = true
      this.$nextTick(() => {
        this.$refs.dataForm.clearValidate()
      })
      if (row) {
        this.dataForm = cloneDeep(row)
      }
    },
    reset() {
      this.$refs.dataForm.resetFields()
      this.dataForm.id = null
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs.dataForm.validate(valid => {
        if (valid) {
          this.submitLoading = true
          const type = this.dataForm.id == null ? 'addDicType' : 'updateDicType'
          const params = {
            typeVal: this.dataForm.typeVal,
            typeKey: this.dataForm.typeKey,
            id: this.dataForm.id
          }
          api_setManager[type](params)
            .then(() => {
              this.visible = false
              this.submitLoading = false
              //子组件触发父组件事件
              this.$emit('onSubmit')
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500
              })
            })
            .catch(() => {
              this.submitLoading = false
            })
        }
      })
    }
  }
}
</script>
