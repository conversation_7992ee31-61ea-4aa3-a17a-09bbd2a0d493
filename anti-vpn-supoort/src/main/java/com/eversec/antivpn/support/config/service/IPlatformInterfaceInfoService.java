package com.eversec.antivpn.support.config.service;

import com.eversec.antivpn.support.config.api.dto.PlatformInterfaceInfoDTO;
import com.eversec.antivpn.support.config.entity.PlatformInterfaceInfo;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 平台接口信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-17
 */
public interface IPlatformInterfaceInfoService extends IService<PlatformInterfaceInfo> {

    PlatformInterfaceInfoDTO getCurrentPlatform();

    PlatformInterfaceInfoDTO getByComCodeAndProvinceAndSystemCode(String comCode, Long provinceId, String systemCode);

    List<PlatformInterfaceInfoDTO> getByIspAndProvince(String comCode, Long provinceId);

    void save(PlatformInterfaceInfoDTO paramDto);

    void updateById(PlatformInterfaceInfoDTO paramDto);

    PlatformInterfaceInfoDTO getByComCodeAndSystemCode(String comCode, String systemCode);

}
