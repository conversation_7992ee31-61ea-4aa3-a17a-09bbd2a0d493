<template>
  <div>
    <!-- form -->
    <el-form
      ref="form"
      v-loading="dataListLoading"
      class="public-form"
      :model="formData"
      :label-width="'0'"
    >
      <section
        v-for="(items, indexs) in formDataLabel"
        :key="indexs"
        class="columns"
      >
        <el-form-item
          v-for="(item, index) in items"
          :key="index"
          :label="item.label"
          :width="item.width"
          :label-width="item.labelWidth"
          class="column"
          :class="item.className"
        >
          <!-- input输入框 -->
          <el-input
            v-if="item.type === 'input'"
            v-model="formData[item.val]"
            :placeholder="item.placeholder || `请输入${item.label || ''}`"
            :disabled="item.disabled"
            :clearable="item.clearable || true"
            :size="item.size"
            :maxlength="item.maxlength"
            :show-word-limit="item.limit"
            :show-password="item.password"
            @input="changeItem(formData[item.val])"
          />
        </el-form-item>
      </section>
    </el-form>
    <!-- 按钮 -->
    <section class="is-mb-10 is-overflow-hidden">
      <el-button
        class="is-float-left"
        type="primary"
        :disabled="!(multipleSelection.length > 0)"
        @click="deleteDics(null)"
      >
        删除已选
      </el-button>
      <el-button
        class="is-float-left"
        type="primary"
        @click="addOrUpdateHandle()"
      >
        新增
      </el-button>
    </section>
    <!-- table -->
    <el-table
      v-loading="dataListLoading"
      :data="dataList"
      border
      stripe
      style="width: 100%;"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="50" />
      <el-table-column
        type="index"
        :index="getIndex"
        header-align="left"
        align="left"
        width="80"
        label="序号"
      />
      <el-table-column
        prop="typeKey"
        header-align="left"
        align="left"
        label="枚举名称"
      >
        <template slot-scope="scope">
          <el-link type="primary" :underline="false" @click="goto(scope.row)">
            {{ scope.row.typeKey }}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column
        prop="typeVal"
        header-align="left"
        align="left"
        label="描述"
      />
      <el-table-column
        header-align="left"
        align="left"
        width="110"
        label="操作"
      >
        <template slot-scope="scope">
          <el-link
            type="success"
            :underline="false"
            @click="addOrUpdateHandle(scope.row)"
          >
            修改
          </el-link>
          <el-link
            type="danger"
            :underline="false"
            @click="deleteHandle(scope.row.id)"
          >
            删除
          </el-link>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-pagination
      class="is-pt-10 is-float-right"
      :current-page="pagingInfo.pageNo"
      :page-sizes="pagingInfo.pageSizes"
      :key="pagingInfo.total"
      :page-size="pagingInfo.pageSize"
      v-loading="dataListLoading"
      :layout="pagingInfo.layout"
      :total="pagingInfo.total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update ref="addOrUpdate" @onSubmit="getDataList" />
  </div>
</template>

<script>
import api_setManager from '@/api/rm/dic'
import AddOrUpdate from './add-or-update'
import debounce from 'lodash/debounce'
import cloneDeep from 'lodash/cloneDeep'

// 自定义分页配置信息
export const customPagingInfo = ({
  pageNo = 1,
  pageSizes = [10, 30, 50, 100],
  pageSize = 10,
  total = 0,
  layout = 'total, sizes, prev, pager, next, jumper'
}) => {
  return {
    pageNo: pageNo,
    pageSizes: pageSizes,
    pageSize: pageSize,
    total: total,
    layout: layout
  }
}

export default {
  components: {
    AddOrUpdate
  },
  data() {
    return {
      formDataLabel: [
        [
          {
            // label: '名称',
            val: 'typeKey',
            type: 'input',
            clearable: true,
            funName: 'changeItem',
            className: 'is-mr-10',
            placeholder: '请输入枚举名称'
          },
          {
            // label: '描述',
            val: 'typeVal',
            type: 'input',
            clearable: true,
            funName: 'changeItem',
            className: 'is-mr-10',
            placeholder: '请输入描述'
          },
          {},
          {}
        ]
      ], // form树
      formData: {},
      dataList: [],
      pagingInfo: cloneDeep(customPagingInfo({ pageSize: 100 })), // 分页信息
      dataListLoading: false,
      multipleSelection: []
    }
  },
  mounted() {
    this.formData = {}
    for (let name in this.$route.query) {
      if (name === 'pageSize') {
        this.pagingInfo.pageSize = Number(this.$route.query[name])
      } else if (name === 'pageNo') {
        this.pagingInfo.pageNo = Number(this.$route.query[name])
      } else {
        this.$set(this.formData, name, this.$route.query[name])
        this.formData[name] = this.$route.query[name]
      }
    }
    this.getDataList()
  },
  methods: {
    // 表单监听回调
    changeItem(val) {
      if (val) {
        this.pagingInfo.pageNo = 1
      }
      this.debounce(this)
    },
    // 防抖查询
    debounce: debounce(vm => {
      let tmpData = {
        typeKey: vm.formData.typeKey || '',
        typeVal: vm.formData.typeVal || '',
        pageSize: vm.pagingInfo.pageSize,
        pageNo: vm.pagingInfo.pageNo
      }
      let data = {}
      for (let name in tmpData) {
        if (tmpData[name] !== '') {
          data[name] = tmpData[name]
        }
      }
      vm.$router.push({
        path: vm.$route.path,
        query: data
      })
      vm.getDataList()
    }, 300),
    getIndex(index) {
      return index + 1 + this.pagingInfo.pageSize * (this.pagingInfo.pageNo - 1)
    },
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      api_setManager
        .getList({
          pageNo: this.pagingInfo.pageNo, //当前页
          pageSize: this.pagingInfo.pageSize, //每页展示数量
          typeKey: this.formData.typeKey,
          typeVal: this.formData.typeVal
        })
        .then(res => {
          this.dataList = res.items
          this.pagingInfo.total = parseInt(res.total)
          this.dataListLoading = false
        })
        .catch(() => {
          this.dataListLoading = false
        })
    },
    // 选中框
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    // 每页数
    handleSizeChange(val) {
      this.pagingInfo.pageSize = val
      this.pagingInfo.pageNo = 1
      this.changeItem()
    },
    // 当前页
    handleCurrentChange(val) {
      this.pagingInfo.pageNo = val
      this.changeItem()
    },
    // 新增 / 修改
    addOrUpdateHandle(row) {
      this.$refs.addOrUpdate.init({
        ...row
      })
    },
    // 点击前往字典详情
    goto(item) {
      this.$router.push({
        path: this.$route.path,
        query: {
          enumItem: true,
          typeKey: item.typeKey
        }
      })
    },
    // 批量删除字典
    deleteDics(id) {
      let ids = ''
      if (id) ids = id
      else {
        let idsArr = []
        this.multipleSelection.forEach(dic => {
          idsArr.push(dic.id)
        })
        ids = idsArr.join(',')
      }
      this.deleteHandle(ids)
    },
    // 删除
    deleteHandle(id) {
      this.$confirm(`确定进行[删除]操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          api_setManager.deleteDicType(id).then(() => {
            this.getDataList()
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500
            })
          })
        })
        .catch(() => {})
    }
  }
}
</script>

<style lang="scss" scoped></style>
