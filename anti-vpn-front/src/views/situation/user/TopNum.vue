<template>
  <ul class="topnum">
    <li v-for="(item, i) in listD" :key="item.name">
      <div class="name"><img :src="item.icon" /> {{ item.name }}</div>
      <p class="line"></p>
      <div class="num_bot">
        <p class="bot_l">
          <!-- <span class="ft16">{{ getNum(item.num) }}</span>-->
          <span class="ft16">总量: {{ getNum(item.num).num }}</span>
          <span v-if="(item.num >= 10000 && item.num<100000000)">万</span>
          <!-- <span v-else-if="(item.num >= 1000000) && item.num<100000000">百万</span> -->
          <span v-else-if="(item.num >= 100000000)">亿</span>

          <span v-if="item.unit">{{ item.unit }}</span>
        </p>
        <!-- <p>今日：{{ item.todayCount }}</p> -->
      </div>
    </li>
  </ul>
</template>

<script>
export default {
  props: {
    numList: {
      type: Array
    }
  },
  watch: {
    numList: {
      handler(val) {
        this.listD = val;
      },
      immediate: true
    }
  },
  data() {
    return {
      listD: [
        // { name: "上报情报监测日志", num: 100, todayCount: 90, icon: require('./img/icon1.png') },
        // { name: "上报AI模型监测日志", num: 100, todayCount: 90, icon: require('./img/icon2.png') },
        // { name: "上报DNS日志", num: 100, todayCount: 90, icon: require('./img/icon3.png') },
        // { name: "上报企业情报", num: 100, todayCount: 90, icon: require('./img/icon4.png')},
        // { name: "情报总量", num: 100, todayCount: 0, icon: require('./img/icon5.png') }
      ]
    };
  },
  methods: {
    getNum(n) {
      let numFt = n;
      if (n >= 10000 && n < 100000000) {
        n = (n / 10000).toFixed(2);
        numFt = n + "万";
      } else if (n >= 100000000) {
        n = (n / 100000000).toFixed(2);
        numFt = n + "亿";
      }
      return { num: n, numFt };
    }
  }
};
</script>

<style scoped lang="scss">
.topnum {
  display: flex;
  justify-content: space-around;
  > li {
    width: 258px;
    height: 94px;
    background: url(./img/kuang.png) no-repeat;
    color: #fff;
    padding: 20px 20px;
    font-size: 14px;
    .name {
      display: flex;
      > img {
        margin-right: 6px;
      }
    }
    .line {
      height: 1px;
      background: url(./img/line.png) no-repeat;
      margin: 9px 0;
    }
    .num_bot {
      display: flex;
      justify-content: space-between;
      .bot_l {
        color: #a7e9ff;
        font-size: 12px;
        .ft16 {
          font-size: 16px;
          padding-right: 5px;
        }
      }
    }
  }
}
</style>
