import { ChartType, MetadataType } from "./common.js";
import { GENERAL_TYPE, JAVA_TYPE_MAPPING } from "@/consts";
import { formatTime, getTimeRangeByUnit } from "@/utils/time.js";
// import { number, integer } from "@/utils/validate";

/**
 * 获取列信息
 * @param {*} value
 * @param {*} all
 */
function getColumn(value, all) {
  for (let index = 0; index < all.length; index++) {
    const row = all[index];
    let name = row.dimId ? row.dimId : row.meId;
    if (value == name) {
      return row;
    }
  }
}
function getColumnByAlias(value, all) {
  for (let index = 0; index < all.length; index++) {
    const row = all[index];
    let name = row.aliasName ? row.aliasName : row.colName;
    if (value == name) {
      return row;
    }
  }
}
/**
 * 创建过滤条件结构
 * @param {*} row
 */
function buildFilter(row) {
  let data = {
    colExpression: row.expression,
    colName: row.colName,
    aliasName: row.aliasName,
    colType: JSON.parse(row.expression).outParamType,
    orgCol: row.orgCol,
    tblName: row.tblName,
    filterId: row.dimId ? row.dimId : row.meId,
    filterExpressions: []
  };

  if (row.measureAggre) data.measureAggre = row.measureAggre;
  return data;
}
// function checkData(column, values, operator) {
//   for (let index = 0; index < values.length; index++) {
//     let value = values[index];
//     let colType = JSON.parse(column.expression).outParamType;
//     colType = JAVA_TYPE_MAPPING[colType];

//     if (colType != GENERAL_TYPE.string && operator == "like") return false;

//     if (colType == GENERAL_TYPE.number) {
//       let data = value + "";
//       let result = number(data);
//       if (result == false) return false;
//     } else if (colType == GENERAL_TYPE.integer) {
//       let data = value + "";
//       let result = integer(data);
//       if (result == false) return false;
//     } else if (colType == GENERAL_TYPE.date) {
//       let data = value + "";
//       let result = number(data);
//       if (result) continue;
//       if (isNaN(data) && !isNaN(Date.parse(data))) {
//         // console.log(true);
//       } else return false;
//     } else if (colType == GENERAL_TYPE.string) {
//       if (operator != "eq" && operator != "notEq" && operator != "like")
//         return false;
//     }
//   }

//   return true;
// }
function convertData(column, values) {
  for (let index = 0; index < values.length; index++) {
    let value = values[index];
    let colType = JSON.parse(column.expression).outParamType;
    colType = JAVA_TYPE_MAPPING[colType];

    if (typeof value != "string") continue;

    try {
      if (colType == GENERAL_TYPE.number) {
        // values[index] = parseFloat(value);
        values[index] = value;
      } else if (colType == GENERAL_TYPE.integer) {
        // values[index] = parseInt(value);
        values[index] = value;
      } else if (colType == GENERAL_TYPE.date) {
        if (isNaN(value) && !isNaN(Date.parse(value))) {
          values[index] = new Date(value).getTime();
        }
      }
    } catch (ex) {
      console.log(ex);
    }
  }
}
/**
 * 获取数据类型
 * @param {*} column
 */
function getDataType(column) {
  let dataType = "text";

  if (column.measureAggre != null) {
    dataType = "numerical";
  } else {
    let colType = JSON.parse(column.expression).outParamType;
    colType = JAVA_TYPE_MAPPING[colType];

    if (
      colType == GENERAL_TYPE.number ||
      colType == GENERAL_TYPE.integer ||
      colType == GENERAL_TYPE.blob ||
      colType == GENERAL_TYPE.boolean
    ) {
      dataType = "numerical";
    } else if (colType == GENERAL_TYPE.date) {
      dataType = "date";
    }
  }

  return dataType;
}
function columnFilter(filterData, value) {
  if (value.min != null && value.max != null) {
    let data = {
      filterExpression: "between",
      values: [value.min, value.max]
    };
    filterData.push(data);
  } else if (value.min != null) {
    let data = {
      filterExpression: "gt",
      values: [value.min]
    };
    filterData.push(data);
  } else if (value.max != null) {
    let data = {
      filterExpression: "lt",
      values: [value.max]
    };
    filterData.push(data);
  } else if (value.value != null) {
    let data = {
      filterExpression: "eq",
      values: [value.value]
    };
    filterData.push(data);
  } else {
    // else if (value.equal != null) {
    let data = {
      filterExpression: "eq",
      values: [value.equal]
    };
    if (value.equal == null) {
      data = {
        filterExpression: "isNull",
        values: [value.equal]
      };
    }
    if (value.equal instanceof Array) data.values = value.equal;
    filterData.push(data);
  }
}
function columnFilterByDate(filterData, value) {
  if (value.min != null && value.max != null) {
    let data = {
      filterExpression: "between",
      values: [value.min, value.max]
    };
    filterData.push(data);
  } else if (value.min != null) {
    let data = {
      filterExpression: "gte",
      values: [value.min]
    };
    filterData.push(data);
  } else if (value.max != null) {
    let data = {
      filterExpression: "lt",
      values: [value.max]
    };
    filterData.push(data);
  } else if (value.value != null) {
    let data = {
      filterExpression: "eq",
      values: [value.value]
    };
    filterData.push(data);
  } else if (value.equal != null) {
    let data = {
      filterExpression: "eq",
      values: [value.equal]
    };
    if (value.equal instanceof Array) data.values = value.equal;
    filterData.push(data);
  } else {
    let min = "";
    let max = "";

    if (value.type == "1") {
      let minDate = new Date();
      let maxDate = new Date();

      let year = minDate.getFullYear();
      let month = minDate.getMonth();
      let day = minDate.getDate();

      if (value.anchor == 0) {
        let d_value = 0;
        max = formatTime(maxDate, "yyyy-MM-dd hh:mm:ss");

        switch (value.unit) {
          case "year":
            minDate.setFullYear(year - value.number);
            break;
          case "month":
            {
              let d_year = parseInt(value.number / 12);
              let d_month = value.number % 12;
              if (d_month > month) {
                d_year++;
                minDate.setFullYear(year - d_year);
                minDate.setMonth(month + 12 - d_month);
              } else {
                minDate.setFullYear(year - d_year);
                minDate.setMonth(month - d_month);
              }
            }
            break;
          case "week":
            d_value = value.number * 7 * 24 * 60 * 60 * 1000;
            minDate.setTime(maxDate.getTime() - d_value);
            break;
          default:
            // day
            d_value = value.number * 24 * 60 * 60 * 1000;
            minDate.setTime(maxDate.getTime() - d_value);
            break;
        }
        min = formatTime(minDate, "yyyy-MM-dd hh:mm:ss");
      } else {
        let d_value = 0;
        minDate = new Date(year, month, day, 0, 0, 0);
        maxDate = new Date(year, month, day, 0, 0, 0);

        switch (value.unit) {
          case "year":
            {
              maxDate = new Date(year, 0, 1, 0, 0, 0);
              minDate = new Date(year - value.number, 0, 1, 0, 0, 0);
            }
            break;
          case "month":
            {
              maxDate.setDate(1);

              minDate.setDate(1);
              let d_year = parseInt(value.number / 12);
              let d_month = value.number % 12;
              if (d_month > month) {
                d_year++;
                minDate.setFullYear(year - d_year);
                minDate.setMonth(month + 12 - d_month);
              } else {
                minDate.setFullYear(year - d_year);
                minDate.setMonth(month - d_month);
              }
            }
            break;
          case "week":
            {
              let weekDay = minDate.getDay() - 1;
              d_value = weekDay * 24 * 60 * 60 * 1000;
              maxDate.setTime(maxDate.getTime() - d_value);

              d_value = value.number * 7 * 24 * 60 * 60 * 1000;
              minDate.setTime(maxDate.getTime() - d_value);
            }
            break;
          default:
            // day
            d_value = value.number * 24 * 60 * 60 * 1000;
            minDate.setTime(maxDate.getTime() - d_value);
            break;
        }

        maxDate.setTime(maxDate.getTime() - 1);

        min = formatTime(minDate, "yyyy-MM-dd hh:mm:ss");
        max = formatTime(maxDate, "yyyy-MM-dd hh:mm:ss");
      }
    } else {
      let date = new Date();
      if (value.anchor == 0) {
        max = formatTime(date, "yyyy-MM-dd hh:mm:ss");
      } else {
        date.setTime(date.getTime() - 24 * 60 * 60 * 1000);
        max = formatTime(date, "yyyy-MM-dd") + " 23:59:59";
      }
      min = value.date;
    }

    let data = {
      filterExpression: "between",
      values: [new Date(min).getTime(), new Date(max).getTime()]
    };
    filterData.push(data);
  }
}
function columnFilterByText(filterData, dicKey, value) {
  if (value.value != null) {
    if (dicKey == null) {
      let data = {
        filterExpression: "like",
        values: ["%" + value.value + "%"]
      };
      filterData.push(data);
    } else {
      let data = {
        filterExpression: "eq",
        values: value.value
      };
      filterData.push(data);
    }
  } else if (value.equal != null) {
    let data = {
      filterExpression: "eq",
      values: [value.equal]
    };
    if (value.equal instanceof Array) data.values = value.equal;
    filterData.push(data);
  } else if (value.equal1 != null) {
    let data = {
      filterExpression: "eq",
      values: value.equal1
    };
    filterData.push(data);
  } else if (value.value1 != null) {
    let data = {
      filterExpression: "like",
      values: value.value1
    };
    filterData.push(data);
  } else {
    let data = {
      filterExpression: "isNull",
      values: [value.equal]
    };
    if (value.equal instanceof Array) data.values = value.equal;
    filterData.push(data);
  }
}
/**
 * 获取过滤条件
 * @param {*} config
 */
function getFilter(config) {
  let filterConfig = config.filter;
  let filter = [];

  for (let index = 0; index < filterConfig.length; index++) {
    let data = filterConfig[index];

    if (data.filter == null) continue;

    // 获取字段信息
    let column = getColumn(data.value, config.dataset.cfg.dimensions);
    let filterType = "dim";
    if (column == null) {
      column = getColumn(data.value, config.dataset.cfg.measures);
      filterType = "measure";
    }
    if (column == null) continue;

    // 创建过滤格式
    let filterData = buildFilter(column);
    filterData.filterType = filterType;

    // 数据类型
    let dataType = getDataType(column);

    let values = data.filter.values;

    if (data.dicKey != null && dataType == "text") {
      if (values.length == 1) {
        columnFilterByText(filterData.filterExpressions, data.dicKey, values[0]);
      } else {
        let datas = {
          value: []
        };
        for (let j = 0; j < values.length; j++) {
          let value = values[j].value ? values[j].value : values[j].equal;
          datas.value.push(value);
        }
        columnFilterByText(filterData.filterExpressions, data.dicKey, datas);
      }
    } else {
      for (let j = 0; j < values.length; j++) {
        let value = values[j];

        if (dataType == "numerical") {
          columnFilter(filterData.filterExpressions, value);
        } else if (dataType == "date") {
          columnFilterByDate(filterData.filterExpressions, value);
        } else {
          columnFilterByText(filterData.filterExpressions, data.dicKey, value);
        }
      }
    }

    filter.push(filterData);
  }

  return filter;
}

function getMultipleFilter(config, filterConfig) {
  try {
    let filter = [];
    for (let index = 0; index < filterConfig.length; index++) {
      let data = filterConfig[index];
      if (data.alias && (data.alias.toString().replace('，', ',').includes(","))) {
        const aliasArray = data.alias.toString().split(",");
        aliasArray.forEach(alias => {
          let column = getColumnByAlias(alias, config.dataset.cfg.dimensions);
          if (!column) {
            // 维度中没有包含该字段
            return [];
          }
          let values = [data.value];
          if (data.value instanceof Array) values = data.value;
          convertData(column, values);
          let filterDatas = buildFilter(column);
          let filterData = {
            filterExpression: data.operator,
            values: values
          };
          filterDatas.filterExpressions.push(filterData);
          filter.push(filterDatas);
        });
      }
    }
    return filter;
  }
  catch (e) {
    console.log("getMultipleFilter error:", e);
  }
}

function getGlobalFilter(config, filterConfig, advancedFilters) {
  let filter = [];
  // let filterConfig = config.globalFilter;

  for (let index = 0; index < filterConfig.length; index++) {
    let data = filterConfig[index];

    // 自定义SQL
    if (data.operator == "sql") {
      advancedFilters.push(data.value);
      continue;
    }

    // 获取字段信息
    let column = getColumnByAlias(data.alias, config.dataset.cfg.dimensions);
    let filterType = "dim";
    if (column == null) {
      column = getColumnByAlias(data.alias, config.dataset.cfg.measures);
      filterType = "measure";
    }

    if (column == null) continue;

    let values = [data.value];
    if (data.value instanceof Array) values = data.value;

    // if (checkData(column, values, data.operator) == false) continue;
    convertData(column, values);

    // 创建过滤格式
    let filterDatas = buildFilter(column);
    filterDatas.filterType = filterType;
    let filterData = {
      filterExpression: data.operator,
      values: values
    };
    //多粒度组件
    if (data.defaultTimeUnit) {
      Object.assign(filterData, { defaultTimeUnit: data.defaultTimeUnit, rule: data.rule })
    }
    if (data.ruleId) {
      Object.assign(filterData, { ruleId: data.ruleId })
    }

    filterDatas.filterExpressions.push(filterData);

    filter.push(filterDatas);
  }

  return filter;
}

function getBiQueryFilters(config, filterConfig, advancedFilters) {
  let filter = [];
  // let filterConfig = config.globalFilter;

  for (let index = 0; index < filterConfig.length; index++) {
    let data = filterConfig[index];

    // 自定义SQL
    if (data.operator == "sql") {
      advancedFilters.push(data.value);
      continue;
    }

    // 获取字段信息
    let column = getColumnByAlias(data.alias, config.dataset.cfg.dimensions);
    let filterType = "dim";
    if (column == null) {
      column = getColumnByAlias(data.alias, config.dataset.cfg.measures);
      filterType = "measure";
    }

    if (column == null) continue;

    let values = [data.value];
    if (data.value instanceof Array) values = data.value;

    // if (checkData(column, values, data.operator) == false) continue;
    convertData(column, values);

    // 创建过滤格式
    let filterDatas = buildFilter(column);
    filterDatas.filterType = filterType;
    filterDatas.filterExpressions = data.filterExpressions;

    filter.push(filterDatas);
  }

  return filter;
}

function getTimeDrilling(config, data) {
  // 获取字段信息
  let column = getColumnByAlias(data.alias, config.dataset.cfg.dimensions);
  let filterType = "dim";
  if (column == null) {
    column = getColumnByAlias(data.alias, config.dataset.cfg.measures);
    filterType = "measure";
  }

  if (column == null) return;

  // 创建过滤格式
  let filterDatas = buildFilter(column);
  filterDatas.filterType = filterType;

  delete filterDatas.filterExpressions;

  if (data.defaultTimeUnit == "60") {
    filterDatas.second = parseInt(data.defaultTimeUnit) * 60;
    filterDatas.unit = "H";
  } else if (data.defaultTimeUnit == "date") {
    filterDatas.second = 24 * 60 * 60;
    filterDatas.unit = "D";
  } else if (data.defaultTimeUnit == "week") {
    filterDatas.second = 7 * 24 * 60 * 60;
    filterDatas.unit = "W";
  } else if (data.defaultTimeUnit == "month") {
    filterDatas.unit = "M";
  } else if (data.defaultTimeUnit == "year") {
    filterDatas.unit = "Y";
  } else if (data.defaultTimeUnit == "halfDate") {
    filterDatas.unit = "HOD";
  } else if (data.defaultTimeUnit == "halfMonth") {
    filterDatas.unit = "HOM";
  } else if (data.defaultTimeUnit == "halfYear") {
    filterDatas.unit = "HOY";
  } else {
    filterDatas.second = data.defaultTimeUnit * 60;
    filterDatas.unit = "MI";
  }

  filterDatas.rule = true;
  filterDatas.ruleid = 1;

  return filterDatas;
}

function pushData(datas, all, container) {
  for (let index1 = 0; index1 < datas.length; index1++) {
    const data = datas[index1];

    for (let index = 0; index < all.length; index++) {
      const row = all[index];
      let name = row.dimId ? row.dimId : row.meId;
      if (data.value == name) {
        if (data.sort != null) row.order = data.sort;
        container.push(row);
        break;
      }
    }
  }
}

/**
 * 获取排序
 */
function getOrders(datas) {
  let orders = [];
  // orders.length = datas.length;
  let length = 0;
  for (let index = 0; index < datas.length; index++) {
    const element = datas[index];
    if (element.sortIndex != null) {
      orders.push(element.value);
      length++;
    }
  }

  // orders.length = length;

  if (length == 0) return null;
  let news = orders.filter(function (val) {
    return val && val.trim();
  });
  return news;
}

/**
 * 一般图形数据获取检索条件接口
 * @param {*} config
 */
function getCondition(config) {
  // console.log('执行的这里么？')
  let data = {
    dataset: config.dataset,
    dimensions: [],
    measures: [],
    filters: [],
    advancedFilters: [],
  };

  if (config.advancedConfig) {
    data.resultNum = getResultNum(config.advancedConfig);
  }

  let dimension = config.dimensions;
  let dimensionAll = config.dataset.cfg.dimensions;
  pushData(dimension, dimensionAll, data.dimensions);

  let measure = config.measures;
  let measureAll = config.dataset.cfg.measures;
  pushData(measure, measureAll, data.measures);

  if (config.color) {
    if (config.color.metadataType === MetadataType.Dimension) {
      pushData([config.color], dimensionAll, data.dimensions);
    } else {
      pushData([config.color], measureAll, data.measures);
    }
  }

  if (config.filter != null) {
    let filter = getFilter(config);
    if (filter.length > 0) {
      data.filters = filter;
    }
  }

  if (config.globalFilter != null) {
    if (config.globalFilter.isAdvance) {
      data.advancedFilter = config.globalFilter.datas;
    } else {
      let filter = getGlobalFilter(config, config.globalFilter.datas, data.advancedFilters);
      if (filter.length > 0) {
        if (data.filters) data.filters = data.filters.concat(filter);
        else data.filters = filter;
      }
      data.multipleFilters = [];
      const multipleFilter = getMultipleFilter(config, config.globalFilter.datas);
      if (multipleFilter && multipleFilter.length > 0) {
        data.multipleFilters.push({
          filters: multipleFilter
        });
      }
    }
  }

  if (config.filters && config.filters.length > 0) {
    let filter = getBiQueryFilters(config, config.filters, data.advancedFilters);
    if (filter.length > 0) {
      if (data.filters) data.filters = data.filters.concat(filter);
      else data.filters = filter;
    }
  }

  // 排序
  var datas = dimension.concat(measure);
  let orders = getOrders(datas);
  if (orders != null) data.orderIds = orders;

  return data;
}

/**
 * 标记地图获取数据检索条件接口
 * @param {*} config
 */
function getConditionByScattermap(config) {
  let data = {
    dataset: config.dataset,
    dimensions: [],
    measures: [],
    filters: [],
    advancedFilters: []
  };

  if (config.advancedConfig) {
    data.resultNum = getResultNum(config.advancedConfig);
  }

  let dimension = config.dimensions;
  let dimensionAll = config.dataset.cfg.dimensions;
  pushData(dimension, dimensionAll, data.dimensions);

  // let longitude = config.longitude[0];
  // pushData([longitude], dimensionAll, data.dimensions);

  // let latitude = config.latitude[0];
  // pushData([latitude], dimensionAll, data.dimensions);

  let measure = config.measures;
  let measureAll = config.dataset.cfg.measures;
  pushData(measure, measureAll, data.measures);

  if (config.filter != null) {
    let filter = getFilter(config);
    if (filter.length > 0) {
      data.filters = filter;
    }
  }

  if (config.globalFilter != null) {
    if (config.globalFilter.isAdvance) {
      data.advancedFilter = config.globalFilter.datas;
    } else {
      let filter = getGlobalFilter(config, config.globalFilter.datas, data.advancedFilters);
      if (filter.length > 0) {
        if (data.filters) data.filters = data.filters.concat(filter);
        else data.filters = filter;
      }
    }
  }

  if (config.filters && config.filters.length > 0) {
    let filter = getBiQueryFilters(config, config.filters, data.advancedFilters);
    if (filter.length > 0) {
      if (data.filters) data.filters = data.filters.concat(filter);
      else data.filters = filter;
    }
  }

  return data;
}

/**
 * 飞线地图获取数据检索条件接口
 * @param {*} config
 */
function getConditionByAttackmap(config) {
  let data = {
    dataset: config.dataset,
    dimensions: [],
    measures: [],
    filters: [],
    advancedFilters: []
  };

  if (config.advancedConfig) {
    data.resultNum = getResultNum(config.advancedConfig);
  }

  let dimension1 = config.dimensions;
  let dimension2 = config.dimension2;
  let dimensionAll = config.dataset.cfg.dimensions;
  pushData(dimension1, dimensionAll, data.dimensions);
  pushData(dimension2, dimensionAll, data.dimensions);

  // let longitude1 = config.longitude[0];
  // pushData([longitude1], dimensionAll, data.dimensions);

  // let latitude1 = config.latitude[0];
  // pushData([latitude1], dimensionAll, data.dimensions);

  // let longitude2 = config.longitude2[0];
  // pushData([longitude2], dimensionAll, data.dimensions);

  // let latitude2 = config.latitude2[0];
  // pushData([latitude2], dimensionAll, data.dimensions);

  let measure = config.measures;
  let measureAll = config.dataset.cfg.measures;
  pushData(measure, measureAll, data.measures);

  if (config.filter != null) {
    let filter = getFilter(config);
    if (filter.length > 0) {
      data.filters = filter;
    }
  }

  if (config.globalFilter != null) {
    if (config.globalFilter.isAdvance) {
      data.advancedFilter = config.globalFilter.datas;
    } else {
      let filter = getGlobalFilter(config, config.globalFilter.datas, data.advancedFilters);
      if (filter.length > 0) {
        if (data.filters) data.filters = data.filters.concat(filter);
        else data.filters = filter;
      }
    }
  }

  if (config.filters && config.filters.length > 0) {
    let filter = getBiQueryFilters(config, config.filters, data.advancedFilters);
    if (filter.length > 0) {
      if (data.filters) data.filters = data.filters.concat(filter);
      else data.filters = filter;
    }
  }

  return data;
}

/**
 * 统计图表数据获取检索条件接口
 * @param {*} config
 */
function getConditionByTable(config, extendParam) {
  // console.log('走到这里了', config)
  let data = {
    dataset: config.dataset,
    dimensions: [],
    measures: [],
    filters: [],
    advancedFilters: []
    // ,limit: 10,
    // paging: 1
  };

  if (extendParam && extendParam.page) {
    data.limit = extendParam.page.pageSize;
    data.paging = extendParam.page.pageIndex;
  }

  if (config.advancedConfig) {
    data.resultNum = getResultNum(config.advancedConfig);

    if (data.limit == null) {
      data.limit = data.resultNum;
      data.paging = 1;
    }
  }

  // 获取维度
  let dimension = config.dimensions;
  let dimensionAll = config.dataset.cfg.dimensions;
  pushData(dimension, dimensionAll, data.dimensions);

  // 获取度量
  let measure = config.dimensions;
  let measureAll = config.dataset.cfg.measures;
  pushData(measure, measureAll, data.measures);

  // 过滤
  if (config.filter != null) {
    let filter = getFilter(config);
    if (filter.length > 0) {
      data.filters = filter;
    }
  }

  if (config.globalFilter != null) {
    if (config.globalFilter.isAdvance) {
      data.advancedFilter = config.globalFilter.datas;
    } else {
      let filter = getGlobalFilter(config, config.globalFilter.datas, data.advancedFilters);
      if (filter.length > 0) {
        if (data.filters) data.filters = data.filters.concat(filter);
        else data.filters = filter;
      }
      data.multipleFilters = [];
      const multipleFilter = getMultipleFilter(config, config.globalFilter.datas);
      if (multipleFilter && multipleFilter.length > 0) {
        data.multipleFilters.push({
          filters: multipleFilter
        });
      }
    }
  }

  if (config.filters && config.filters.length > 0) {
    let filter = getBiQueryFilters(config, config.filters, data.advancedFilters);
    if (filter.length > 0) {
      if (data.filters) data.filters = data.filters.concat(filter);
      else data.filters = filter;
    }
  }

  if (config.timeDrilling) {
    let timeDrilling = getTimeDrilling(config, config.timeDrilling);
    data.datagrain = timeDrilling;
  }

  // 排序
  var datas = dimension;
  let orders = getOrders(datas);

  console.log(orders,'返回的排序结果')
  if (orders != null) data.orderIds = orders;

  // 更新排序
  if (extendParam && extendParam.order) {
    for (let index = 0; index < data.dimensions.length; index++) {
      let element = data.dimensions[index];
      // delete element.order;
      if (element.dimId == extendParam.order.column) {
        element.order = extendParam.order.order;
      }
    }
    for (let index = 0; index < data.measures.length; index++) {
      let element = data.measures[index];
      // delete element.order;
      if (element.meId == extendParam.order.column) {
        element.order = extendParam.order.order;
      }
    }
    if (data.orderIds) {
      console.log('进入这个判断了么')
      data.orderIds = data.orderIds.filter(item => item != extendParam.order.column);
      data.orderIds.splice(0, 0, extendParam.order.column);
    } else {
      data.orderIds = [extendParam.order.column];
    }
  }

  return data;
}

function getConditionByText(config) {
  let data = {
    dataset: config.dataset,
    dimensions: [],
    measures: [],
    filters: [],
    resultNum: 1
  };

  // 获取维度
  // let dimension = config.dimensions;
  // let dimensionAll = config.dataset.cfg.dimensions;
  // pushData(dimension, dimensionAll, data.dimensions);

  // 获取度量
  let measure = config.dimensions;
  let measureAll = config.dataset.cfg.measures;
  pushData(measure, measureAll, data.measures);

  return data;
}

/**
 * 组合图形数据获取检索条件接口
 * @param {*} config
 */
function getConditionByBiaxial(config) {
  let data = {
    dataset: config.dataset,
    dimensions: [],
    measures: [],
    filters: [],
    advancedFilters: []
  };

  if (config.advancedConfig) {
    data.resultNum = getResultNum(config.advancedConfig);
  }

  let dimension = config.dimensions;
  let dimensionAll = config.dataset.cfg.dimensions;
  pushData(dimension, dimensionAll, data.dimensions);

  let measure = config.measures;
  let measureAll = config.dataset.cfg.measures;
  pushData(measure, measureAll, data.measures);

  let measure1 = config.measures1;
  pushData(measure1, measureAll, data.measures);

  if (config.filter != null) {
    let filter = getFilter(config);
    if (filter.length > 0) {
      data.filters = filter;
    }
  }

  if (config.globalFilter != null) {
    if (config.globalFilter.isAdvance) {
      data.advancedFilter = config.globalFilter.datas;
    } else {
      let filter = getGlobalFilter(config, config.globalFilter.datas, data.advancedFilters);
      if (filter.length > 0) {
        if (data.filters) data.filters = data.filters.concat(filter);
        else data.filters = filter;
      }
    }
  }

  if (config.filters && config.filters.length > 0) {
    let filter = getBiQueryFilters(config, config.filters, data.advancedFilters);
    if (filter.length > 0) {
      if (data.filters) data.filters = data.filters.concat(filter);
      else data.filters = filter;
    }
  }

  // 排序
  var datas = dimension.concat(measure).concat(measure1);
  let orders = getOrders(datas);
  if (orders != null) data.orderIds = orders;

  return data;
}

function getResultNum(advancedConfig) {
  const { limit, isReport } = advancedConfig;
  return limit !== undefined ? limit : isReport ? 100 : 1000;
}

/**
 * 图表获取数据检索条件接口
 * @param {*} config
 */
export function getChartCondition(chartType, config, extendParam) {
  // console.log(chartType, config, extendParam,'这几个值是什么')
  if (
    chartType === ChartType.Scattermap.value ||
    chartType === ChartType.Heatmap.value
  ) {
    return getConditionByScattermap(config);
  } else if (chartType === ChartType.Attackmap.value) {
    return getConditionByAttackmap(config);
  } else if (chartType === ChartType.Table.value || chartType === ChartType.Custom1.value || chartType === ChartType.Custom2.value) {
    return getConditionByTable(config, extendParam);
  } else if (chartType === "Text") {
    return getConditionByText(config);
  } else if (chartType === ChartType.Biaxial.value) {
    return getConditionByBiaxial(config);
  } else {
    return getCondition(config);
  }
}

// 增加高级筛选查询配置
export function addChartAdvancedQuery(condition, config, extendParam) {
  if (config.advancedQuery != null) {
    const advancedQuery = getAdvancedQueryFilter(config, config.advancedQuery);
    if (advancedQuery) {
      condition.advancedQuery = advancedQuery;
    }
    return condition;
  }
  return condition;
}

function formatFilterToAdvancedQuery(config, advancedFilter) {
  const col = advancedFilter.column;
  const condition = advancedFilter.condition;

  function checkedText(data) {
    if (data.expression == "like") data.data = "%" + data.data + "%";
    else if (data.expression == "llike") {
      data.data = "%" + data.data;
      data.expression = "like";
    } else if (data.expression == "rlike") {
      data.data = data.data + "%";
      data.expression = "like";
    }
  }
  // 获取字段信息
  let column = getColumn(col.value, config.dataset.cfg.dimensions);
  let filterType = "dim";
  if (column == null) {
    column = getColumn(col.value, config.dataset.cfg.measures);
    filterType = "measure";
  }

  if (column) {
    // 创建过滤格式
    let filterData = buildFilter(column);
    filterData.filterType = filterType;
    if (col.dataType == 0) checkedText(condition);
    let expression = {
      filterExpression: condition.expression,
      values:
        condition.data instanceof Array ? condition.data : [condition.data]
    };
    if (condition.expression == "dynamic") {
      expression = {
        filterExpression: 'between',
        values: getTimeRangeByUnit(
          condition.data.defaultTime,
          condition.data.defaultTimeUnit,
          condition.data.natureTime)
      }
    }

    filterData.filterExpressions.push(expression);
    return {
      filter: filterData
    };
  }
  return null;
}

function getAdvancedQueryFilter(config, advancedQuery) {
  // 多个筛选条件
  if (advancedQuery && advancedQuery.children) {
    let result = {
      relationType: advancedQuery.relationType,
      children: []
    };
    for (let i = 0; i < advancedQuery.children.length; i += 1) {
      let filterData = getAdvancedQueryFilter(
        config,
        advancedQuery.children[i]
      );
      if (filterData) {
        result.children.push(filterData);
      }
    }
    return result;
  }
  return formatFilterToAdvancedQuery(config, advancedQuery);
}
