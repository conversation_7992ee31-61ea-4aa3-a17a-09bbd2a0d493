package com.eversec.antivpn.log.service;

import com.eversec.antivpn.emnus.ScreenTypeEnum;
import com.eversec.antivpn.log.api.dto.TypeCountDTO;
import com.eversec.antivpn.log.api.dto.base.AggCountDTO;
import com.eversec.antivpn.log.api.dto.base.DateHistogramDTO;
import com.eversec.antivpn.log.entity.E2MachineLearningLog;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-08
 */
public interface IE2MachineLearningLogService extends IService<E2MachineLearningLog> {
    AggCountDTO countNum(ScreenTypeEnum screenTypeEnum);

    /**
     * 根据源Ip或者目的IP+省聚合获取 总量和今日数
     * @param screenTypeEnum
     * @param srcOrDestIp
     * @return
     */
    AggCountDTO countNumByIPAndProvinceId(ScreenTypeEnum screenTypeEnum, String srcOrDestIp);

    /**
     * 获取AI识别日志总量
     */
    Long countSum(ScreenTypeEnum screenTypeEnum);

    /**
     * 获取AI模型识别日志量 TOP10
     * @param screenTypeEnum
     * @return
     */
    List<TypeCountDTO> getDatedDiscernTopo10(ScreenTypeEnum screenTypeEnum);

    /**
     * 传输层协议类型占比
     * @param screenTypeEnum
     * @return
     */
    List<TypeCountDTO> getLogListByProtocolType(ScreenTypeEnum screenTypeEnum);

    /**
     * AI模型监测跨境通信日志趋势图  查询条件：今天-> 按小时、近一周-> 按天、月-> 按周、至今-> 按月
     * @param screenTypeEnum
     * @return
     */
    List<TypeCountDTO> getAILogByYearORmonthORDayORWeek(ScreenTypeEnum screenTypeEnum);

    /**
     * AI模型识别通信日志置信度列表
     * @param screenTypeEnum
     * @return
     */
    List<DateHistogramDTO> getLogNumByRateList(ScreenTypeEnum screenTypeEnum);
    /**
     * AI模型识别通信日志置信度分布
     * @param screenTypeEnum
     * @return
     */
    List<DateHistogramDTO> getLogNumByRateDistribute(ScreenTypeEnum screenTypeEnum);

    /**
     * 省区域上报监测日志 TOP10
     * @param screenTypeEnum
     * @return
     */
    List<TypeCountDTO> getLogNumByProvinceId(ScreenTypeEnum screenTypeEnum);

    /**
     * 全国地图按照各省市展示 今日数、总数
     * @param screenTypeEnum
     * @return
     */
    List<TypeCountDTO> getChinaMapByProvinceId(ScreenTypeEnum screenTypeEnum);
}
