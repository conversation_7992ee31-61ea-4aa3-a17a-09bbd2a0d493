<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eversec.antivpn.log.mapper.ProvincePlatformStatusMapperExt">

    <select id="monitorList" resultType="com.eversec.antivpn.log.entity.ProvincePlatformStatus">
        select id,com_code,province_id,system_code,network_business_ids,current_state,time_stamp,create_user,update_user,update_datetime, max(create_datetime) as create_datetime from province_platform_status order by create_datetime
    </select>
</mapper>
