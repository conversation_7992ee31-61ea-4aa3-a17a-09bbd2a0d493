package com.eversec.antivpn.support.province.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eversec.antivpn.support.province.api.dto.ProvincePlatformStatusDTO;
import com.eversec.antivpn.support.province.api.dto.ProvinceStatusDTO;
import com.eversec.antivpn.support.province.dto.ProvincePlatformStatusQueryConditionDTO;
import com.eversec.antivpn.support.province.entity.ProvincePlatformStatus;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 省平台信息（企业测） 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-12
 */
public interface IProvincePlatformStatusService extends IService<ProvincePlatformStatus> {

    List<ProvincePlatformStatusDTO> listAllLatestReportProvincePlatform(ProvincePlatformStatusQueryConditionDTO paramDto);

    Page<ProvincePlatformStatusDTO> page(ProvincePlatformStatusQueryConditionDTO paramDto, Page<ProvincePlatformStatus> pageInfo);
}
