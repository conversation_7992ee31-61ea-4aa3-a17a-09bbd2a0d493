package com.eversec.antivpn.support.config.convertor;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.eversec.antivpn.config.AntiVpnProperties;
import com.eversec.antivpn.support.config.api.dto.PlatformInterfaceInfoDTO;
import com.eversec.antivpn.support.config.entity.PlatformInterfaceInfo;
import com.eversec.antivpn.support.province.api.dto.ProvincePlatformStatusDTO;
import com.eversec.antivpn.support.province.entity.ProvincePlatformStatus;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;

/**
 * 实体类和DTO转换类
 */
@Slf4j
@Component
@AllArgsConstructor
public class PlatformInterfaceInfoFactory {


	public PlatformInterfaceInfoDTO entityToDto(PlatformInterfaceInfo entity) {
		PlatformInterfaceInfoDTO dto = new PlatformInterfaceInfoDTO();
		BeanUtil.copyProperties(entity, dto);

		return dto;
	}


	public PlatformInterfaceInfo dtoToEntity(PlatformInterfaceInfoDTO dto) {
		PlatformInterfaceInfo entity = new PlatformInterfaceInfo();
		BeanUtil.copyProperties(dto, entity);
		return entity;
	}

}
