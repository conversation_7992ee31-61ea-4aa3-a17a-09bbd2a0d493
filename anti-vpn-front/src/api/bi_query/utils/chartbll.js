import api_olapquery from "../api/olapquery";
import api_chart from "../api/chart";
import api_dash from "../api/dash";
import { MetadataType, ChartType } from "./common";
import { getChartCondition, addChartAdvancedQuery } from "./datacondition.js";
import { GENERAL_TYPE, JAVA_TYPE_MAPPING } from "@/consts";
import {
  getDic, getNameByValues, parseDicByLevel,
  getLevelPropsByLevelValues
} from "@/utils/dic";
import cloneDeep from "lodash/cloneDeep";
import { getDsetDetail } from "./dsetUtils";

export class ChartDataBll {
  currentDataset = null;
  parsedDicCache = {};

  /**
   * 获取数据集列信息
   */
  getColumns(columns) {
    let datas = [];
    if (columns) {
      for (let index = 0; index < columns.length; index++) {
        let row = columns[index];

        // 数据类型
        // "text" "numerical" "coordinate" "date"
        let dataType = "text";
        if (row.measureAggre != null) {
          if (row.measureAggre == "min" || row.measureAggre == "max") {
            let colType = JSON.parse(row.expression).outParamType;
            colType = JAVA_TYPE_MAPPING[colType];

            if (
              colType == GENERAL_TYPE.number ||
              colType == GENERAL_TYPE.integer ||
              colType == GENERAL_TYPE.blob ||
              colType == GENERAL_TYPE.boolean
            ) {
              dataType = "numerical";
            } else if (colType == GENERAL_TYPE.date) {
              dataType = "date";
            }
          } else {
            dataType = "numerical";
          }
        } else {
          let colType = JSON.parse(row.expression).outParamType;
          colType = JAVA_TYPE_MAPPING[colType];

          if (
            colType == GENERAL_TYPE.number ||
            colType == GENERAL_TYPE.integer ||
            colType == GENERAL_TYPE.blob ||
            colType == GENERAL_TYPE.boolean
          ) {
            dataType = "numerical";
          } else if (colType == GENERAL_TYPE.date) {
            dataType = "date";
          }
        }

        let data = {
          label: row.aliasName ? row.aliasName : row.colName,
          value: row.dimId ? row.dimId : row.meId,
          datatype: dataType,
          metadataType: row.dimId
            ? MetadataType.Dimension
            : MetadataType.Measure
        };

        if (row.dicKey) data.dicKey = row.dicKey;
        if (row.pidColumn) data.pidColumn = row.pidColumn;
        if (row.labelDicLevel) data.labelDicLevel = row.labelDicLevel;
        if (row.valueDicLevel) data.valueDicLevel = row.valueDicLevel;
        if (row.multivalue) data.multivalue = row.multivalue;

        datas.push(data);
      }
    }

    return datas;
  }

  /**
   * 为多粒度查询或导出接口配置参数
   */
  configFilterForDatagrain(condition) {
    //为多粒度时间范围过滤器拼json
    const newFilters = [];
    condition.filters.forEach(filter => {
      filter.filterExpressions.forEach(expression => {
        if (expression.defaultTimeUnit !== undefined) {
          condition.datagrain = {
            "rule": expression.rule,
            "ruleid": expression.ruleId,
            "colExpression": filter.colExpression,
            "colName": filter.colName,
            "aliasName": filter.aliasName,
            "colType": filter.colType,
            "orgCol": filter.orgCol,
            "tblName": filter.tblName,
            "filterId": filter.filterId,
            "filterType": filter.filterType
          };
          if (expression.defaultTimeUnit > 0 && expression.defaultTimeUnit < 60) {
            Object.assign(condition.datagrain, { second: expression.defaultTimeUnit * 60, unit: "MI" });
          } else if (expression.defaultTimeUnit == 60) {
            Object.assign(condition.datagrain, { second: expression.defaultTimeUnit * 60, unit: "H" });
          } else if (expression.defaultTimeUnit == "date") {
            Object.assign(condition.datagrain, { second: 24 * 60 * 60, unit: "D" });
          } else if (expression.defaultTimeUnit == "week") {
            Object.assign(condition.datagrain, { second: 7 * 24 * 60 * 60, unit: "W" });
          } else if (expression.defaultTimeUnit == "month") {
            Object.assign(condition.datagrain, { unit: "M" });
          } else if (expression.defaultTimeUnit == "year") {
            Object.assign(condition.datagrain, { unit: "Y" });
          } else if (expression.defaultTimeUnit == "halfDate") {
            Object.assign(condition.datagrain, { unit: "HOD" });
          } else if (expression.defaultTimeUnit == "halfMonth") {
            Object.assign(condition.datagrain, { unit: "HOM" });
          } else if (expression.defaultTimeUnit == "halfYear") {
            Object.assign(condition.datagrain, { unit: "HOY" });
          }
        }
      })
      newFilters.push(filter);
    });
    return newFilters;
  }

  getParameters(charttype, parameter, extendParam) {
    let condition = getChartCondition(charttype, parameter, extendParam);

    // 增加advancedQuery高级查询
    condition = addChartAdvancedQuery(condition, parameter, extendParam);

    return condition;
  }

  /**
   * 获取图表数据, 包括获取数据集
   * @param {*} charttype
   * @param {*} table
   * @param {*} config
   * @param {*} param
   * @param {*} callBack
   * @param {*} error
   */
  getDatasByChart(charttype, table, config, extendParam, callBack, error) {
    let parameter = JSON.parse(JSON.stringify(config));
    let that = this;

    function getDatas(parameter) {
      let condition = that.getParameters(charttype, parameter, extendParam);
      condition.chartId = table.chartId;
      condition.traceId = config.traceId;

      // 为多粒度组件配置
      condition.filters = that.configFilterForDatagrain(condition);

      // BI接口
      if (charttype === ChartType.Table.value) {
        // console.log("getDatasByChart condition:", condition)
        api_olapquery
          .getListByPage(condition)
          .then(data => {
            if (callBack) {
              // console.log(data);
              callBack(data, parameter);
            }
          })
          .catch(() => {
            if (error) error();
          });
      } else {
        api_olapquery
          .getList(condition)
          .then(data => {
            if (callBack) {
              // console.log(data);
              callBack(data, parameter);
            }
          })
          .catch(() => {
            if (error) error();
          });
      }
    }

    if (this.currentDataset && this.currentDataset.id == table.tableId) {
      parameter.dataset = this.currentDataset.data;
      getDatas(parameter);
    } else {
      getDsetDetail(table.tableId, data => {
        if (data == null) {
          if (error) error("获取数据集失败，是否数据集已删除！");
          return;
        }

        parameter.dataset = data;
        this.currentDataset = {
          id: table.tableId,
          data
        };
        getDatas(parameter);
      }, error);
    }
  }

  /**
   * 支持多级字典转换
   * @param {*} dimId
   * @param {*} values
   * @param {*} dimensions
   */
  getDicList(dimId, values, dimensions) {
    let datas = [];
    for (let index = 0; index < dimensions.length; index++) {
      const element = dimensions[index];

      if (element.value == dimId) {
        datas.push(dimId);
        if (element.pidColumn != null && element.pidColumn != "") {
          this.getParent(element.pidColumn, datas, dimensions);
        }
        break;
      }
    }

    let datas1 = [];
    for (let index = 0; index < datas.length; index++) {
      const element = datas[index];
      datas1.push(values[element]);
    }

    return datas1.reverse();
  }
  getParent(dimId, datas, dimensions) {
    for (let index = 0; index < dimensions.length; index++) {
      const element = dimensions[index];

      if (element.value == dimId) {
        datas.push(dimId);

        if (element.pidColumn != null && element.pidColumn != "") {
          this.getParent(element.pidColumn, datas, dimensions);
        }
        break;
      }
    }
  }

  getParsedDic(dicCode, dic) {
    if (!dic) return;
    if (!this.parsedDicCache[dicCode]) {
      this.parsedDicCache[dicCode] = parseDicByLevel(dic);
      // console.log(this.parsedDicCache[dicCode]);
    }
    return this.parsedDicCache[dicCode];
  }

  /**
   * 获取字典转换后的数据
   * @param {*} config
   * @param {*} sdatas
   * @param {*} callback
   */
  getDicDataBase(config, sdatas, callback, isTable = false) {
    let datas = JSON.parse(JSON.stringify(sdatas));
    let dataList = isTable ? datas.list : datas;

    // this.convertDate(config, dataList);
    // this.convertPrecision(config, dataList);

    // 获取字典
    let all = [];
    let dicCodes = [];
    for (let index = 0; index < config.dimensions.length; index++) {
      const element = config.dimensions[index];
      // 20211222 从数据集中取 dicKey
      if (!element.dicKey) {
        const elementInDataset = config.dataset.cfg.dimensions.find(dim => dim.dimId === element.value);
        if (elementInDataset) {
          element.dicKey = elementInDataset.dicKey;
        }
      }
      if (element.dicKey) {
        all.push(element);
        if (!dicCodes.includes(element.dicKey)) dicCodes.push(element.dicKey);
      }
    }
    if (all.length == 0) {
      callback(config, datas);
      return;
    }

    // 转换字典
    getDic(dicCodes, dicData => {
      for (let index = 0; index < dataList.length; index++) {
        let data = dataList[index];
        let oldData = cloneDeep(data);

        for (let index1 = 0; index1 < all.length; index1++) {
          const col = all[index1];
          let id = col.value;
          let value = data[id];
          const dic = dicData[col.dicKey];
          // 字典已删除
          if (dic == null) continue;
          // let value1 = this.getDicList(id, oldData, config.dimensions);
          // data[id] = getNameByValues(value1, dic) || value;

          if (col.labelDicLevel > 0 && col.valueDicLevel > 0) {
            const parsedDic = this.getParsedDic(col.dicKey, dic);
            if (parsedDic) {
              const parentName = getLevelPropsByLevelValues(
                parsedDic,
                value,
                col.valueDicLevel,
                col.labelDicLevel,
                col.multivalue,
                "itemName"
              );
              if (parentName) data[id] = parentName;
            }
          } else {
            let value1 = this.getDicList(id, oldData, config.dimensions);
            data[id] = getNameByValues(value1, dic, col.multivalue) || value;
          }

          data[id + '_origin_'] = value;
        }
      }

      callback(config, datas);
    });
  }

  getChartData(id, dashId) {
    if (!dashId) return api_chart.getInfo(id).then(data => JSON.parse(data.chartConf));

    return api_dash.getInfo(dashId).then(data => {
      const meterConf = JSON.parse(data.meterConf)
      const item = meterConf.layout.find(item => item.i === id);
      if (!item) return console.log("在仪表盘中找不到该图表！");
      const chartConf = meterConf.items[item.chartId].chartConf;
      return chartConf;
    });
  }
}
