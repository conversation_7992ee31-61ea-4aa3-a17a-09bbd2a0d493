{
  "root": true,
  "parserOptions": {
    "parser": "babel-eslint"
  },
  "settings": {
    "html/html-extensions": [".html"]
  },
  "globals": {
    "axios": true,
    "elastic": true
  },
  "extends": ["plugin:vue/essential", "eslint:recommended", "@vue/prettier"],
  "plugins": [
    "prettier"
  ],
  "rules": {
    // "no-console": process.env.NODE_ENV === "production" ? "warn" : "off",
    // "no-debugger": process.env.NODE_ENV === "production" ? "warn" : "off"
    "prettier/prettier": [
      "error",
      {
        "semi": false, // 格式化不加分号
        "printWidth": 80, // 一行的字符数，如果超过会进行换行，默认为80
        "singleQuote": true, // 字符串是否使用单引号，默认为false，使用双引号
        "trailingComma": "none", // 是否使用尾逗号，有三个可选值"<none|es5|all>"
        "bracketSpacing": true, // 对象大括号直接是否有空格，默认为true，效果：{ foo: bar }
        "jsxBracketSameLine": true, // JSX 标签闭合位置，默认false，换行闭合
        "htmlWhitespaceSensitivity": "ignore"
        // htmlWhitespaceSensitivity: 'ignore',
        // 'prettier/prettier': 'off' // 关闭换行符校验
      }
    ],
    "for-direction": 2, // 强制 “for” 循环中更新子句的计数器朝着正确的方向移动
    "getter-return": 2, // 强制 getter 函数中出现 return 语句
    "no-async-promise-executor": 2, // 禁止使用异步函数作为 Promise executor
    "no-compare-neg-zero": 2, // 禁止与 -0 进行比较
    "no-cond-assign": 2, // 禁止条件表达式中出现赋值操作符
    "no-constant-condition": 2, // 禁止在条件中使用常量表达式
    "no-control-regex": 2, // 禁止在正则表达式中使用控制字符
    "no-debugger": 1, // 禁用 debugger
    "no-dupe-args": 2, // 禁止 function 定义中出现重名参数
    "no-dupe-keys": 2, // 禁止对象字面量中出现重复的 key
    "no-duplicate-case": 2, // 禁止出现重复的 case 标签
    "no-empty": 2, // 禁止出现空语句块
    "no-empty-character-class": 2, // 禁止在正则表达式中使用空字符集
    "no-ex-assign": 2, // 禁止对 catch 子句的参数重新赋值
    "no-extra-boolean-cast": 2, // 禁止不必要的布尔转换
    "no-extra-semi": 2, // 禁止不必要的分号
    "no-func-assign": 2, // 禁止对 function 声明重新赋值
    "no-inner-declarations": 2, // 禁止在嵌套的块中出现变量声明或 function 声明
    "no-invalid-regexp": 2, // 禁止 RegExp 构造函数中存在无效的正则表达式字符串
    "no-irregular-whitespace": 2, // 禁止不规则的空白
    "no-misleading-character-class": 2, // 不允许在字符类语法中出现由多个代码点组成的字符
    "no-obj-calls": 2, // 禁止把全局对象作为函数调用
    "no-prototype-builtins": 2, // 禁止直接调用 Object.prototypes 的内置属性
    "no-regex-spaces": 2, // 禁止正则表达式字面量中出现多个空格
    "no-sparse-arrays": 2, // 禁用稀疏数组
    "no-unexpected-multiline": 2, // 禁止出现令人困惑的多行表达式
    "no-unreachable": 2, // 禁止在 return、throw、continue 和 break 语句之后出现不可达代码
    "no-unsafe-finally": 2, // 禁止在 finally 语句块中出现控制流语句
    "no-unsafe-negation": 2, // 禁止对关系运算符的左操作数使用否定操作符
    "require-atomic-updates": 2, // 禁止由于 await 或 yield的使用而可能导致出现竞态条件的赋值
    "use-isnan": 2, // 要求使用 isNaN() 检查 NaN
    "valid-typeof": 2, // 强制 typeof 表达式与有效的字符串进行比较
    "no-case-declarations": 2, // 不允许在 case 子句中使用词法声明
    "no-empty-pattern": 2, // 禁止使用空解构模式
    "no-fallthrough": 2, // 禁止 case 语句落空
    "no-global-assign": 2, // 禁止对原生对象或只读的全局对象进行赋值
    "no-octal": 2, // 禁用八进制字面量
    "no-redeclare": 2, // 禁止多次声明同一变量
    "no-self-assign": 2, // 禁止自我赋值
    "no-unused-labels": 2, // 禁用出现未使用过的标
    "no-useless-catch": 2, // 禁止不必要的 catch 子句
    "no-useless-escape": 2, // 禁用不必要的转义字符
    "no-with": 2, // 禁用 with 语句
    "no-delete-var": 2, // 禁止删除变量
    "no-shadow-restricted-names": 2, // 禁止将标识符定义为受限的名字
    "no-undef": 1, // 禁用未声明的变量，除非它们在 /*global */ 注释中被提到
    "no-unused-vars": 1, // 禁止出现未使用过的变量
    "no-mixed-spaces-and-tabs": 2, // 禁止空格和 tab 的混合缩进
    "constructor-super": 2, // 要求在构造函数中有 super() 的调用
    "no-class-assign": 2, // 禁止修改类声明的变量
    "no-const-assign": 2, // 禁止修改 const 声明的变量
    "no-dupe-class-members": 2, // 禁止类成员中出现重复的名称
    "no-new-symbol": 2, // 禁止 Symbolnew 操作符和 new 一起使用
    "no-this-before-super": 2, // 禁止在构造函数中，在调用 super() 之前使用 this 或 super
    "require-yield": 2 // 要求 generator 函数内有 yield
  }
}
