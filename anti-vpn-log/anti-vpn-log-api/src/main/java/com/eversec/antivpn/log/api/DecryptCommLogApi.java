package com.eversec.antivpn.log.api;

import com.eversec.framework.feignboot.conf.EversecFeignDecoderConfiguration;
import com.eversec.framework.feignboot.conf.EversecFeignLogConfiguration;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;

@Validated
@Tag(name = "话单失败信息解密")
@FeignClient(value = "appnamexxxxxxxxx",
        path = com.eversec.antivpn.log.api.DecryptCommLogApi.PATH,
        url = "${appnamexxxxxxxx.log.url:}",
        contextId = "com.eversec.antivpn.log.api.DecryptCommLogApi",
        configuration = { EversecFeignDecoderConfiguration.class, EversecFeignLogConfiguration.class})
public interface DecryptCommLogApi {

    String PATH = "/log/Decrypt";
}
