package com.eversec.antivpn.log.mapper;


import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.eversec.antivpn.log.api.dto.CrossBorderCommLogRequest;
import com.eversec.antivpn.log.entity.E1CommLog;
import com.eversec.antivpn.log.entity.E1CommLogPOExt;
import com.eversec.antivpn.log.entity.po.GenericSysDataDict;
import com.eversec.antivpn.log.mapper.vo.TypeCountVO;
import java.time.LocalDateTime;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-12
 */
@Mapper
@DS("anti-vpn")
public interface SysDataDictMapper extends BaseMapper<GenericSysDataDict> {

}
