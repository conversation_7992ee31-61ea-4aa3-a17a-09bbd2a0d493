<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eversec.antivpn.log.mapper.LogVpnIntelligenceMapper">
    <resultMap id="DictVO" type="com.eversec.antivpn.log.mapper.vo.DictVO">
        <result column="vpn_airport_code" jdbcType="INTEGER" property="vpnAirportCode"/>
        <result column="vpn_airport_name" jdbcType="INTEGER" property="vpnAirportName"/>
    </resultMap>

    <resultMap id="VpnBaseVO" type="com.eversec.antivpn.log.mapper.vo.VpnBaseVO">
        <result column="vpn_id" jdbcType="INTEGER" property="vpnId"/>
        <result column="vpn_ip" jdbcType="VARCHAR" property="vpnIp"/>
    </resultMap>

    <resultMap id="TypeCount" type="com.eversec.antivpn.log.mapper.vo.TypeCountVO">
        <result column="num" jdbcType="BIGINT" property="num"/>
        <result column="key_type" jdbcType="VARCHAR" property="key"/>
    </resultMap>

    <resultMap id="VpnServicePageVO" type="com.eversec.antivpn.log.mapper.vo.VpnServicePageVO">
        <result column="vpn_service_name" jdbcType="VARCHAR" property="vpnServiceName"/>
        <result column="vpn_airport_code" jdbcType="VARCHAR" property="vpnAirportCode"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDateTime"/>
    </resultMap>
    <resultMap id="PlatformInterfaceVO" type="com.eversec.antivpn.log.mapper.vo.PlatformInterfaceVO">
        <result column="com_code" jdbcType="VARCHAR" property="comCode"/>
        <result column="province_id" jdbcType="BIGINT" property="provinceId"/>
        <result column="system_code" jdbcType="VARCHAR" property="systemCode"/>
        <result column="config_type" jdbcType="INTEGER" property="configType"/>
        <result column="north_current_network_business_ids" jdbcType="VARCHAR" property="networkBusinessIds"/>
    </resultMap>

    <resultMap id="VpnMapAggVO" type="com.eversec.antivpn.log.mapper.vo.VpnMapAggVO">
        <result column="key_type" jdbcType="VARCHAR" property="key"/>
        <result column="num" jdbcType="BIGINT" property="resourceNodeNum"/>
    </resultMap>
    <select id="selectVpnIntelligenceCount" resultType="Long">
        select count(1) from vpn_intelligence where deleted=0
    </select>

    <select id="selectVpnIdAndVpnAirportCode" resultType="java.util.HashMap">
        select vpn_id,vpn_airport_code from vpn_intelligence where deleted=0
    </select>

    <select id="getVpnAirportCodeAndName" resultMap="DictVO">
        SELECT
        enum_key AS vpn_airport_code,
        enum_val AS vpn_airport_name
        FROM
        generic_sys_data_dict
        WHERE
        type_key = 'AIRPORT_INFORMATION' AND deleted=0 AND enum_key IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getVpnIPByVpnId" resultMap="VpnBaseVO">
        SELECT
        vpn_id,vpn_ip
        FROM
        vpn_intelligence
        WHERE
        vpn_id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>


    <select id="getCountryResourceNodeNumTop10" resultMap="TypeCount">
        SELECT
            vpn_country key_type,
            count(*) num
        FROM
            vpn_intelligence
        WHERE
            deleted = 0 and vpn_country is not null
        GROUP BY
            vpn_country
        order by num desc
        limit 20
    </select>
    <select id="getVpnServicePage" resultMap="VpnServicePageVO">
        SELECT
            enum_key as vpn_airport_code,
            enum_val as vpn_service_name,
            create_datetime
        FROM
            generic_sys_data_dict
        WHERE
            type_key = 'AIRPORT_INFORMATION' and deleted=0
        ORDER BY
            create_datetime DESC
            LIMIT 10
    </select>

    <select id="getVpnServiceCount" resultType="java.lang.Long">
        SELECT
            count( 1 )
        FROM
            generic_sys_data_dict
        WHERE
            type_key = 'AIRPORT_INFORMATION'
          AND deleted = 0
    </select>

    <select id="getCountryResourceNodeAgg" resultMap="VpnMapAggVO">
        SELECT
            vpn_country as key_type,
            count(*) as num
        FROM
            vpn_intelligence
        WHERE
            deleted = 0
        GROUP BY
            vpn_country
    </select>
    <select id="getVpnServiceResourceNodeNum" resultMap="TypeCount">
        SELECT
            vpn_airport_code key_type,
            count( 1 ) num
        FROM
            vpn_intelligence
        WHERE
            vpn_airport_code IN
            <foreach collection="list" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
          AND deleted = 0
        group by vpn_airport_code
    </select>

    <select id="getPlatformInterfaceInfo" resultMap="PlatformInterfaceVO">
        select com_code,province_id,system_code,config_type,north_current_network_business_ids from platform_interface_info where enabled=1
    </select>

    <select id="getCurrentPlatformInterfaceInfo" resultMap="PlatformInterfaceVO">
        select com_code,province_id,system_code,config_type,north_current_network_business_ids from platform_interface_info where enabled=1 and config_type='CURRENT'
    </select>

    <select id="getCurrentPlatformInterfaceInfo" resultMap="PlatformInterfaceVO">
        select com_code,province_id,system_code,config_type,north_current_network_business_ids from platform_interface_info where enabled=1 and config_type='CURRENT'
    </select>
</mapper>
