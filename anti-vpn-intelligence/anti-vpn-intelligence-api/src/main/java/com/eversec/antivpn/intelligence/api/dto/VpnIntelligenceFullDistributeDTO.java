package com.eversec.antivpn.intelligence.api.dto;

import cn.hutool.core.text.StrSplitter;
import com.eversec.framework.webboot.common.exception.BusinessException;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 情报全量下发对象
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023-08-01 14:46
 */
@Getter
@Setter
@Schema(name = "VpnIntelligencePO", description = "vpn情报")
public class VpnIntelligenceFullDistributeDTO {

	/**
	 * 根据字符串创建对象
	 * 2|88111|vmess|2|***************:8080|20231016143250
	 * @param line
	 */
	public VpnIntelligenceFullDistributeDTO(String line) {
		try {
			List<String> split = StrSplitter.split(line, "|", false, false);
			this.typeId = Integer.parseInt(split.get(0));
			this.vpnId = Long.parseLong(split.get(1));
			this.vpnName = split.get(2);
			this.contentType = Integer.parseInt(split.get(3));
			this.vpnContent = split.get(4);
			this.timeStamp = split.get(5);
		} catch (Exception e) {
			throw new RuntimeException("解析全量情报库行异常, " + line, e);
		}
	}

	@Schema(description = "情报类型code： 1-VPN服务地址,例:提供VPN下载、注册、登录、购买、登出等各类访问地址和链接； 2-VPN 跨境服务地址,例：海外域名通道或海外IP通道地址； 3-模型研判； 4-样本库； 999-其他。 当操作类型为0、1、2时必填，当操作类型为3、4、5时选填。 ")
	private Integer typeId;

	@Schema(description = "情报库ID，当部侧系统下发时必填；企业侧系统上报、全量库下发时可不填写")
	private Long vpnId;

	@Schema(description = "Vpn名称")
	private String vpnName;

	@Schema(description = "匹配内容类型。1-域名；2-IP；3-URL；4-报文（最少支持snort规则，版本语法为2.9)；5-VPN协议（具体参见F.5节）；999-其他。")
	private Integer contentType;

	@Schema(description = "匹配内容。当类型为3和4时采用base64编码后结果。当类型为5时填写的是VPN协议编号。")
	private String vpnContent;

	@Schema(description = "数据生成时间,生成该文件的时间。")
	private String timeStamp;



}
