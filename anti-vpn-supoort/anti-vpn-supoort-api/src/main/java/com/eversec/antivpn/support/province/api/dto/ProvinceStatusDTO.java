package com.eversec.antivpn.support.province.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 企业状态对象
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023-08-11 18:12
 */
@Data
public class ProvinceStatusDTO {

	@Schema(description = "跨境通信情报库版本格式：主版本号.副版本号，本规范主版本号为1，副版本号为0，即1.0。")
	private String version;

	@Schema(description = "运营商编码")
	private String comCode;

	@Schema(description = "省级区域编号")
	private Long provinceId;

	@Schema(description = "监测网络类型，见附录F.3节，同时覆盖多个网络类型时用|线分隔，例：3|4|5。\n")
	private String networkBusinessId;

	@Schema(description = "监测网络类型的系统状态：\n" + "0：正常；\n" + "1：异常；\n" + "2：未覆盖；\n" + "9：其他。\n")
	private Integer currentState = 0;

	@Schema(description = "创建时间,生成该文件的时间")
	private String timeStamp;

	public String[] toCvsFields() {
		return new String[] { "1.0", provinceId.toString(), comCode, networkBusinessId, currentState.toString(), timeStamp};
	}

}
