<style lang="scss" scoped>
.hy-empty {
  margin: 0 8px;
  font-size: 14px;
  line-height: 22px;
  text-align: center;
  user-select: none;
  color: currentColor;

  &.hy-empty-normal {
    margin: 32px 0;
    .hy-empty-image {
      height: 40px;
      font-size: 60px;
    }
  }

  .hy-empty-image {
    height: 100px;
    margin-bottom: 8px;

    svg {
      height: 100%;
      margin: auto;
    }
  }

  .hy-empty-description {
    margin: 0;
    color: gray;
  }
}

::v-deep.el-empty {
  .el-empty__description {
    margin-top: 5px;
  }
  .el-empty__image {
    width: 100px;
  }
}
</style>
<template>
  <!-- <el-empty :description="description"></el-empty> -->
  <div class="hy-empty hy-empty-normal" :style="{ color: color }" :class="[`hy-empty-${type}`]">
		<div class="hy-empty-image">
			<svg-icon :type="`empty-type${type}`"></svg-icon>
		</div>
		<p class="hy-empty-description">{{ description }}</p>
		<!-- <el-empty :description="description"></el-empty> -->
	</div>
</template>
<script>
const oneOf = (target, list) => {
  return list.some(item => item === target);
};

export default {
  name: "Empty",
  props: {
    type: {
      type: [String, Number],
      default: 2,
      validator(val) {
        return oneOf(+val, [1, 2, 3, 4]);
      }
    },
    color: {
      type: String,
      default: "rgba(0,0,0,0.25)"
    },
    description: {
      type: String,
      default: "暂无数据"
    }
  }
};
</script>
