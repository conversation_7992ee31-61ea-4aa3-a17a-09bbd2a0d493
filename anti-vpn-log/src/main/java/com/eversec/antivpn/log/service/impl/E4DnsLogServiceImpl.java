package com.eversec.antivpn.log.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.eversec.antivpn.emnus.ScreenTypeEnum;
import com.eversec.antivpn.log.api.dto.base.AggCountDTO;
import com.eversec.antivpn.log.entity.E2MachineLearningLog;
import com.eversec.antivpn.log.entity.E4DnsLog;
import com.eversec.antivpn.log.mapper.E4DnsLogMapper;
import com.eversec.antivpn.log.service.IE4DnsLogService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eversec.antivpn.log.util.CKPartitionUtil;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-08
 */
@Service
@Slf4j
public class E4DnsLogServiceImpl extends ServiceImpl<E4DnsLogMapper, E4DnsLog> implements IE4DnsLogService {

    @Resource
    private E4DnsLogMapper e4DnsLogMapper;

    @Override
    public AggCountDTO countNum(ScreenTypeEnum screenTypeEnum) {
        LambdaQueryWrapper<E4DnsLog> lambdaQueryWrapper = new LambdaQueryWrapper();
        Long todayTimeCKPartition = ScreenTypeEnum.getTimeCKPartition(ScreenTypeEnum.TODAY);
        lambdaQueryWrapper.eq(E4DnsLog::getDay, todayTimeCKPartition);
        Long todayCount = e4DnsLogMapper.selectCount(lambdaQueryWrapper);
        AggCountDTO aggCountDTO = new AggCountDTO();
        aggCountDTO.setTodayCount(todayCount);
        if (ScreenTypeEnum.TODAY == screenTypeEnum) {
            aggCountDTO.setTodayCount(todayCount);
            Long dateRangeCount = e4DnsLogMapper.selectCount(lambdaQueryWrapper);
            aggCountDTO.setDateRangeCount(dateRangeCount);
        } else {
            lambdaQueryWrapper.clear();
            Long startTimePartition = ScreenTypeEnum.getTimeCKPartition(screenTypeEnum);
            lambdaQueryWrapper.ge(startTimePartition != null, E4DnsLog::getDay, startTimePartition)
                    .le(E4DnsLog::getDay, todayTimeCKPartition);
            Long dateRangeCount = e4DnsLogMapper.selectCount(lambdaQueryWrapper);
            aggCountDTO.setDateRangeCount(dateRangeCount);
        }
        return aggCountDTO;
    }

}
