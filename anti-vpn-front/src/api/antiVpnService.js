import httpRequest from '@/utils/httpRequest'
import axios from 'axios'
import serverProxy from '@/config/serverProxy'
let antiVpnService = serverProxy.antiVpnService

export default {
  // 知识库-vpn服务商信息-分页查询
  getAirportInfo(params) {
    return httpRequest({
      url: `${antiVpnService}/knowledge/airport/page`,
      params: params
    })
  },
  // 知识库-vpn服务商信息-新增
  saveAirport(data) {
    return httpRequest({
      url: `${antiVpnService}/knowledge/airport/saveAirport`,
      method: 'post',
      data: data
    })
  },
  // 知识库-vpn服务商信息-修改
  updateAirport(data) {
    return httpRequest({
      url: `${antiVpnService}/knowledge/airport/updateAirportById`,
      method: 'post',
      data: data
    })
  },
  // 知识库-vpn服务商信息-删除
  deleteAirport(ids) {
    return httpRequest({
      url: `${antiVpnService}/knowledge/airport/deleteDictByIds/${ids}`,
      method: 'delete'
    })
  },
  // 知识库-vpn服务商信息-下载模版
  downloadAirportTemplate() {
    window.location.href = `/service${antiVpnService}/knowledge/airport/template`
  },
  // 知识库-vpn服务商信息-模版导入
  importTemplateAirport(data) {
    return httpRequest({
      url: `${antiVpnService}/knowledge/airport/import/template`,
      method: 'post',
      data: data
    })
  },
  // 知识库-vpn服务商信息-导出错误数据
  importerrorAirport(data) {
    return axios({
      responseType: 'blob',
      url: `/service${antiVpnService}/knowledge/airport/download/importerror`,
      method: 'post',
      data: data
    })
  },
  // 知识库-协议信息-分页查询
  getProtocolInfo(params) {
    return httpRequest({
      url: `${antiVpnService}/knowledge/protocol/protocolPage`,
      params: params
    })
  },
  // 知识库-协议信息-新增
  saveProtocol(data) {
    return httpRequest({
      url: `${antiVpnService}/knowledge/protocol/saveProtocol`,
      method: 'post',
      data: data
    })
  },
  // 知识库-协议信息-修改
  updateProtocol(data) {
    return httpRequest({
      url: `${antiVpnService}/knowledge/protocol/updateProtocolById`,
      method: 'post',
      data: data
    })
  },
  // 知识库-协议信息-删除
  deleteProtocol(ids) {
    return httpRequest({
      url: `${antiVpnService}/knowledge/protocol/deleteProtocolByIds/${ids}`,
      method: 'delete'
    })
  },
  // 知识库-协议信息-下载模版
  downloadProtocolTemplate() {
    window.location.href = `/service${antiVpnService}/knowledge/protocol/protocolTemplate`
  },
  // 知识库-协议信息-模版导入
  importTemplateProtocol(data) {
    return httpRequest({
      url: `${antiVpnService}/knowledge/protocol/import/protocolTemplate`,
      method: 'post',
      data: data
    })
  },
  // 知识库-协议信息-导出错误数据
  importerrorProtocol(data) {
    return axios({
      responseType: 'blob',
      url: `/service${antiVpnService}/knowledge/protocol/download/importerror`,
      method: 'post',
      data: data
    })
  },
  // 知识库-软件信息-分页查询
  getSoftwareInfo(params) {
    return httpRequest({
      url: `${antiVpnService}/knowledge/software/softwarePage`,
      params: params
    })
  },
  // 知识库-软件信息-新增
  saveSoftware(data) {
    return httpRequest({
      url: `${antiVpnService}/knowledge/software/saveSoftware`,
      method: 'post',
      data: data
    })
  },
  // 知识库-软件信息-修改
  updateSoftware(data) {
    return httpRequest({
      url: `${antiVpnService}/knowledge/software/updateSoftwareById`,
      method: 'post',
      data: data
    })
  },
  // 知识库-软件信息-删除
  deleteSoftware(ids) {
    return httpRequest({
      url: `${antiVpnService}/knowledge/software/deleteSoftwareByIds/${ids}`,
      method: 'delete'
    })
  },
  // 知识库-软件信息-下载模版
  downloadSoftwareTemplate() {
    window.location.href = `/service${antiVpnService}/knowledge/software/softwareTemplate`
  },
  // 知识库-软件信息-模版导入
  importTemplateSoftware(data) {
    return httpRequest({
      url: `${antiVpnService}/knowledge/software/import/softwareTemplate`,
      method: 'post',
      data: data
    })
  },
  // 知识库-软件信息-导出错误数据
  importerrorSoftware(data) {
    return axios({
      responseType: 'blob',
      url: `/service${antiVpnService}/knowledge/software/download/importerror`,
      method: 'post',
      data: data
    })
  },
  // 企业网络活动检测-最近一次查询
  getListAllLatestReportProvincePlatform(params) {
    return httpRequest({
      url: `${antiVpnService}/province/provincePlatformStatus/listAllLatestReportProvincePlatform`,
      params: params
    })
  },
  // 企业网络活动检测-历史查询
  getProvincePlatformStatus(params) {
    return httpRequest({
      url: `${antiVpnService}/province/provincePlatformStatus/page`,
      params: params
    })
  },
  // 情报管理-情报指令-分页查询
  getVpnIntelligenceCommand(params) {
    return httpRequest({
      url: `${antiVpnService}/intelligence/vpnIntelligenceCommand/page`,
      params: params
    })
  },
  // 情报管理-情报指令-重新下发指令
  reDistributeCommand(id) {
    return httpRequest({
      url: `${antiVpnService}/intelligence/vpnIntelligenceCommand/reDistributeCommand/${id}`
    })
  },
  // 情报管理-情报指令-删除
  vpnIntelligenceCommandDeleteByIds(ids) {
    return httpRequest({
      url: `${antiVpnService}/intelligence/vpnIntelligenceCommand/deleteByIds/${ids}`,
      method: 'delete'
    })
  },
  // 情报管理-情报库上报-分页查询
  getVpnIntelligence(params) {
    return httpRequest({
      url: `${antiVpnService}/intelligence/vpnIntelligence/page`,
      params: params
    })
  },
  // 情报管理-情报库上报-新增
  vpnIntelligenceSave(data) {
    return httpRequest({
      url: `${antiVpnService}/intelligence/vpnIntelligence/save`,
      method: 'post',
      data: data
    })
  },
  // 情报管理-情报库上报-修改
  vpnIntelligenceUpdateById(data) {
    return httpRequest({
      url: `${antiVpnService}/intelligence/vpnIntelligence/updateById`,
      method: 'put',
      data: data
    })
  },
  // 情报管理-情报库上报-删除
  vpnIntelligenceDeleteByIds(ids) {
    return httpRequest({
      url: `${antiVpnService}/intelligence/vpnIntelligence/deleteByIds/${ids}`,
      method: 'delete'
    })
  },

  // 情报管理-情报库上报-根据条件删除
  vpnIntelligenceDeleteByCondition(params) {
    return httpRequest({
      url: `${antiVpnService}/intelligence/vpnIntelligence/deleteByCondition`,
      params: params,
      method: 'delete'
    })
  },
  // 情报管理-情报库上报-下载模版
  vpnIntelligenceDownloadTemplate() {
    window.location.href = `/service${antiVpnService}/intelligence/vpnIntelligence/downloadTemplate`
  },
  // 情报管理-情报库上报-导入
  vpnIntelligenceImport(data, comCode, provinceId, systemCode) {
    return httpRequest({
      url: `${antiVpnService}/intelligence/vpnIntelligence/import/${comCode}/${provinceId}/${systemCode}`,
      method: 'post',
      data: data
    })
  },
  // 情报管理-情报库上报-上报
  vpnIntelligenceReport(data, key) {
    return httpRequest({
      url: `${antiVpnService}/intelligence/vpnIntelligence/report/${key}`,
      method: 'post',
      data: data
    })
  },
  // 情报管理-情报库上报-获取平台配置
  getCurrentPlatformForFront(params) {
    return httpRequest({
      url: `${antiVpnService}/config/platformInterfaceInfo/getCurrentPlatformForFront`,
      params: params
    })
  },
  // 情报管理-情报库上报-重新下发到smart
  redistributeToSmart(params) {
    return httpRequest({
      url: `${antiVpnService}/intelligence/vpnIntelligence/redistributeToSmart`,
      params: params
    })
  },
  // 情报管理-情报库上报-重新下发到态势感知
  redistributeToNssa(params) {
    return httpRequest({
      url: `${antiVpnService}/intelligence/vpnIntelligence/redistributeToNssa`,
      params: params
    })
  },
  // 配置管理-平台配置-列表查询
  getListAll(params) {
    return httpRequest({
      url: `${antiVpnService}/config/platformInterfaceInfo/listAll`,
      params: params
    })
  },
  // 配置管理-平台配置-新增
  platformInterfaceInfoSave(data) {
    return httpRequest({
      url: `${antiVpnService}/config/platformInterfaceInfo/save`,
      method: 'post',
      data: data
    })
  },
  // 配置管理-平台配置-修改
  platformInterfaceInfoUpdateById(data) {
    return httpRequest({
      url: `${antiVpnService}/config/platformInterfaceInfo/updateById`,
      method: 'put',
      data: data
    })
  },
  // 配置管理-平台配置-删除
  platformInterfaceInfoDeleteByIds(ids) {
    return httpRequest({
      url: `${antiVpnService}/config/platformInterfaceInfo/deleteByIds/${ids}`,
      method: 'delete'
    })
  },
  // 情报库管理-模版及样本-列表查询
  getVpnMachineLearningCodeDict(params) {
    return httpRequest({
      url: `${antiVpnService}/intelligence/vpnMachineLearningCodeDict/page`,
      params: params
    })
  },
  // 情报库管理-模版及样本-批量上报
  vpnMachineLearningCodeDictReport(ids, fileType) {
    if (fileType) {
      return httpRequest({
        url: `${antiVpnService}/intelligence/vpnMachineLearningCodeDict/report/${fileType}/${ids}`
      })
    } else {
      return httpRequest({
        url: `${antiVpnService}/intelligence/vpnMachineLearningCodeDict/report/${ids}`
      })
    }
  },
  // 情报库管理-模版及样本-批量删除
  vpnMachineLearningCodeDictDeleteByIds(ids) {
    return httpRequest({
      url: `${antiVpnService}/intelligence/vpnMachineLearningCodeDict/deleteByIds/${ids}`,
      method: 'delete'
    })
  },
  // 情报库管理-模版及样本-下载模版
  vpnMachineLearningCodeDictDownloadTemplate() {
    window.location.href = `/service${antiVpnService}/intelligence/vpnMachineLearningCodeDict/downloadTemplate`
  },
  // 情报库管理-模版及样本-导入
  vpnMachineLearningCodeDictImport(
    data,
    comCode,
    provinceId,
    systemCode,
    networkBusinessId
  ) {
    return httpRequest({
      url: `${antiVpnService}/intelligence/vpnMachineLearningCodeDict/import/${comCode}/${provinceId}/${systemCode}/${networkBusinessId}`,
      method: 'post',
      data: data
    })
  },
  // 下载附件
  downloadModuleFileByName(fileName) {
    window.open(
      `/service${antiVpnService}/intelligence/vpnMachineLearningCodeDict/downloadModuleFile/${fileName}`,
      '_blank'
    )
    // window.location.href = `/service${antiVpnService}/intelligence/vpnMachineLearningCodeDict/downloadModuleFile/${fileName}`
  },
  // 上传取证文件
  uploadEvidenceFile(data, id) {
    return httpRequest({
      url: `${antiVpnService}/intelligence/vpnIntelligence/uploadAttachMent/${id}`,
      method: 'post',
      data: data
    })
  },
  // 下载取证文件
  downloadEvidenceFile(attachMent) {
    window.open(
      `/service${antiVpnService}/intelligence/vpnIntelligence/downloadAttachMent/${attachMent}`,
      '_blank'
    )
  },

  // 下载情报文件
  downloadCommandFile(id) {
    window.open(
      `/service${antiVpnService}/intelligence/vpnIntelligenceCommand/downloadCommandDistributeFile/${id}`,
      '_blank'
    )
  }
}
