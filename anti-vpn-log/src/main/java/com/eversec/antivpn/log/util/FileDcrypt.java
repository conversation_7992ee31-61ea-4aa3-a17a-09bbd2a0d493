package com.eversec.antivpn.log.util;

import cn.hutool.core.codec.Base64;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang.StringUtils;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.*;
import java.lang.reflect.Array;
import java.security.Security;
import java.util.regex.Pattern;
import java.util.zip.GZIPInputStream;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;


public class FileDcrypt {
    private static Logger log = LoggerFactory.getLogger(FileDcrypt.class);
    public static final String AES_PKCS7PADDING = "AES/CBC/PKCS7Padding";
    public static final String AES_PKCS7PADDING_BC = "BC";
    // 字符集
    public static final String CHARSET_UTF = "UTF-8";
    // 对称加密算法
    public static final String ENCRYPT_ALGORITHM_AES = "AES";
    // 哈希算法
    public static final String HASH_ALGORITHM_MD5 = "MD5";

    public static String  doFileDcrypt(InputStream inputStream, String aesOffsets, String aesKey, String messageKey) throws DocumentException {
        SAXReader reader = new SAXReader();
        Document doc = reader.read(inputStream);
        Element root = doc.getRootElement();
        String dataUpload = root.elementText("dataUpload");
        int encryptAlgorithmTmp = Integer.parseInt(root.elementText("encryptAlgorithm"));
        int compressionFormatTmp = Integer.parseInt(root.elementText("compressionFormat"));
        int hashAlgorithmTmp = Integer.parseInt(root.elementText("hashAlgorithm"));
        String dataHashTmp = root.elementText("dataHash");
        String data = getDateContent(dataUpload, encryptAlgorithmTmp, aesKey, aesOffsets, dataHashTmp, messageKey, hashAlgorithmTmp, compressionFormatTmp);
        return data;
    }
    //文件解密
    public static String getDateContent(String fileContent, int encryptAlgorithm, String aesKey, String aesOffsets, String dataHash, String messageKey, int hashAlgorithm, int compressionFormat) {
        try {
            // base64解码
            byte[] dataArray = decode(fileContent);
            // 解密文件
            byte[] commandEncryptAlgorithm = getEncryptAlgorithmData(dataArray, encryptAlgorithm, aesKey, aesOffsets);
            // 校验文件完整性
            checkDataHash(dataHash, messageKey, hashAlgorithm, commandEncryptAlgorithm);
            // 解压文件，获得文件内容。
            String data = unCompression(commandEncryptAlgorithm, compressionFormat);
            return data;
        } catch (Exception e) {
            log.error("getDateContent-文件解密失败!", e.getMessage());
        }
        return null;
    }

    /**
     * 解密
     * decode
     * 描述:
     *
     * @param str ?注释
     * @return byte[] ?注释
     */
    public static byte[] decode(String str) {
        byte[] byteArr = null;
        try {
            if (StringUtils.isNotBlank(str)) {
                byteArr = str.getBytes("UTF-8");
                if (org.apache.commons.codec.binary.Base64.isArrayByteBase64(byteArr)) {
                    byteArr = org.apache.commons.codec.binary.Base64.decodeBase64(byteArr);
                }
            }
        } catch (Exception e) {
            log.error("解密异常！", e);
        }
        return byteArr;
    }

    /**
     * 采用参数encryptAlgorithm指定的加密算法对解码后的数据进行解密
     * getEncryptAlgorithmData
     * 描述:
     *
     * @param commandBase64    ?注释
     * @param encryptAlgorithm ?注释
     * @param aesKey           ?注释
     * @param aesOffsets       ?注释
     * @return byte[]
     */
    public static byte[] getEncryptAlgorithmData(byte[] commandBase64, int encryptAlgorithm,
                                                 String aesKey, String aesOffsets) throws Exception {
        byte[] commandEncryptAlgorithm = null;
        if (encryptAlgorithm == 1) {// AES加密算法
            commandEncryptAlgorithm = decrypt(commandBase64, aesKey, aesOffsets);
        } else {// 不进行加密，明文传输
            commandEncryptAlgorithm = commandBase64;
        }
        return commandEncryptAlgorithm;
    }

    /**
     * 校验文件完整性
     * checkDataHash
     * 描述:
     *
     * @param dataHash                ?注释
     * @param messageKey              ?注释
     * @param hashAlgorithm           ?注释
     * @param commandEncryptAlgorithm ?注释
     */
    public static boolean checkDataHash(String dataHash, String messageKey, int hashAlgorithm,
                                        byte[] commandEncryptAlgorithm) throws Exception {
        // 将messageKey转换成byte[]和zipxml相加后转换成字符串--->[x,y,z]
        byte[] md5xml = bytesAddStr(commandEncryptAlgorithm, messageKey, CHARSET_UTF);
        if (hashAlgorithm == 0) {// 无hash

        } else {
            String newDataHash = "";
            if (hashAlgorithm == 1) {// MD5加密
                newDataHash = Base64.encode(getMD5(md5xml));
            } else if (hashAlgorithm == 2) {// SHA-1加密
                newDataHash = Base64.encode(DigestUtils.sha1Hex(md5xml));
            }
            if (StringUtils.isBlank(dataHash) || !dataHash.equals(newDataHash)) {
                log.info("checkDataHash-文件校验失败!");
                throw new Exception("压缩字符串数据出错");
            }
        }
        return true;
    }

    /**
     * 按照compressionFormat指定的压缩格式对data进行解压后即得到指令信息
     * unzip
     * 描述:
     *
     * @param commandEncryptAlgorithm ?注释
     * @param compressionFormat       ?注释
     */
    public static String unCompression(byte[] commandEncryptAlgorithm, int compressionFormat) throws Exception {
        String xmlContent = null;
        if (compressionFormat == 1) {
            xmlContent = unzip(commandEncryptAlgorithm);
        } else if (compressionFormat == 2) {
            xmlContent = decompressContent(commandEncryptAlgorithm);
        } else {
            // 无压缩
            xmlContent = new String(commandEncryptAlgorithm, CHARSET_UTF);

        }
        return xmlContent;
    }

    /**
     * AES解密
     * decrypt
     * 描述:
     */
    public static byte[] decrypt(byte[] cipherText, String aesKey, String aesOffsets) {
        byte[] plainText = null;
        try {
            if (aesKey.length() > 16) {
                byte[] aesBytes = parseHexStr2Byte(aesKey);
                aesKey = new String(aesBytes, CHARSET_UTF);
            }

            Security.addProvider(new BouncyCastleProvider());
            SecretKeySpec skeySpec = new SecretKeySpec(aesKey.getBytes(CHARSET_UTF),
                    ENCRYPT_ALGORITHM_AES);
            Cipher cipher = null;
            if (StringUtils.isNotBlank(aesOffsets)) {
                cipher = Cipher.getInstance(AES_PKCS7PADDING, AES_PKCS7PADDING_BC);
                if (aesOffsets.length() > 16) {
                    byte[] bytes = parseHexStr2Byte(aesOffsets);
                    aesOffsets = new String(bytes, CHARSET_UTF);
                }
                IvParameterSpec iv = new IvParameterSpec(
                        aesOffsets.getBytes(CHARSET_UTF));
                cipher.init(Cipher.DECRYPT_MODE, skeySpec, iv);
            } else {
                cipher = Cipher.getInstance(AES_PKCS7PADDING, AES_PKCS7PADDING_BC);
                cipher.init(Cipher.DECRYPT_MODE, skeySpec);
            }

            if (null != cipher) {
                plainText = cipher.doFinal(cipherText);
            }
        } catch (Exception e) {
            log.error("AES解密异常！", e);
        }
        return plainText;
    }

    public static byte[] bytesAddStr(byte[] bytes, String str, String charset) {
        byte[] targetBytes = null;
        if (StringUtils.isNotBlank(str)) {
            byte[] strBytes = null;
            try {
                strBytes = str.getBytes(charset);
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }
            Object targetObj = bytesAdd(bytes, strBytes);
            if (targetObj != null) {
                targetBytes = (byte[]) targetObj;
            }
        } else {
            targetBytes = bytes;
        }
        return targetBytes;
    }

    /**
     * MD5的算法在RFC1321 中定义 在RFC 1321中，给出了Test suite用来检验你的实现是否正确： MD5 ("") =
     * d41d8cd98f00b204e9800998ecf8427e MD5 ("a") = 0cc175b9c0f1b6a831c399e269772661
     * MD5 ("abc") = 900150983cd24fb0d6963f7d28e17f72 MD5 ("message digest") =
     * f96b697d7cb7938d525a2f31aaf161d0 MD5 ("abcdefghijklmnopqrstuvwxyz") =
     * c3fcd3d76192e4007dfb496cca67e13b
     *
     * <AUTHOR>
     * 传入参数：一个字节数组 传出参数：字节数组的 MD5 结果字符串
     */
    public static String getMD5(byte[] source) {
        String result = null;
        // 用来将字节转换成 16 进制表示的字符
        char[] hexDigits = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd',
                'e', 'f'};
        try {
            java.security.MessageDigest md = java.security.MessageDigest
                    .getInstance(HASH_ALGORITHM_MD5);
            md.update(source);
            // MD5 的计算结果是一个 128 位的长整数
            byte[] tmp = md.digest();
            // 每个字节用 16 进制表示的话，使用两个字符
            char[] str = new char[16 * 2];
            // 所以表示成 16 进制需要 32 个字符
            int k = 0; // 表示转换结果中对应的字符位置
            // 从第一个字节开始，对 MD5 的每一个字节
            for (int i = 0; i < 16; i++) {
                // 转换成 16 进制字符的转换
                byte byte0 = tmp[i]; // 取第i个字节
                str[k++] = hexDigits[byte0 >>> 4 & 0xf];// 取字节中高 4 位的数字转换,
                // >>> 为逻辑右移，将符号位一起右移
                str[k++] = hexDigits[byte0 & 0xf];// 取字节中低 4 位的数字转换
            }
            result = new String(str);// 换后的结果转换为字符串
        } catch (Exception e) {
            log.error("MD5加密异常！", e);
        }
        return result;
    }

    /**
     * 解压文件
     * unzip
     * 描述:
     *
     * @param info ?注释
     * @return String
     */
    public static String unzip(byte[] info) {
        if (null == info) {
            return null;
        }

        String result = null;
        ZipInputStream zipInputStream = null;
        ByteArrayOutputStream baos = null;
        try {
            zipInputStream = new ZipInputStream(new ByteArrayInputStream(info));
            ZipEntry zipEntry = null;
            // zip中只有一个文件
            if ((zipEntry = zipInputStream.getNextEntry()) != null) {
                if (zipEntry.isDirectory()) {
                    return null;
                }
                baos = new ByteArrayOutputStream();
                int len = -1;
                byte[] data = new byte[1024];
                while ((len = zipInputStream.read(data)) != -1) {
                    baos.write(data, 0, len);
                }
                baos.flush();
                byte[] bytes = baos.toByteArray();
                result = new String(bytes, CHARSET_UTF);
            }
        } catch (IOException e) {
            log.error(e.getMessage());
        } finally {
            try {
                if (null != baos) {
                    baos.close();
                }
                if (null != zipInputStream) {
                    zipInputStream.close();
                }
            } catch (IOException e) {
                log.error(e.getMessage());
            }
        }
        return result;
    }

    public static String decompressContent(byte[] compressedContent) throws IOException {
        StringBuilder decompressedContent = new StringBuilder();
        try (ByteArrayInputStream bais = new ByteArrayInputStream(compressedContent);
             GZIPInputStream gzipInputStream = new GZIPInputStream(bais);
             InputStreamReader isr = new InputStreamReader(gzipInputStream);
             BufferedReader reader = new BufferedReader(isr)) {
            String line;
            while ((line = reader.readLine()) != null) {
                decompressedContent.append(line).append("\n");
            }
        }
        return decompressedContent.toString().trim();
    }

    /**
     * 将16进制转换为二进制
     * parseHexStr2Byte
     * 描述:
     */
    private static byte[] parseHexStr2Byte(String hexStr) {
        if (hexStr.length() < 1) {
            return null;
        }

        byte[] result = new byte[hexStr.length() / 2];
        for (int i = 0; i < hexStr.length() / 2; i++) {
            int high = Integer.parseInt(hexStr.substring(i * 2, i * 2 + 1), 16);
            int low = Integer.parseInt(hexStr.substring(i * 2 + 1, i * 2 + 2), 16);
            result[i] = (byte) (high * 16 + low);
        }
        return result;
    }

    /**
     * 数组相加
     * 例如：
     * byte1: [49, 50, 51]
     * byte2: [52, 53, 54]
     * newByte: [49, 50, 51, 52, 53, 54]
     *
     * @param arrayObj1 arrayObj1
     * @param arrayObj2 arrayObj2
     * @return
     */
    @SuppressWarnings("rawtypes")
    public static Object bytesAdd(Object arrayObj1, Object arrayObj2) {
        if (null != arrayObj1 && null != arrayObj2) {
            Class curClazz1 = arrayObj1.getClass();
            Class curClazz2 = arrayObj2.getClass();
            if (!curClazz1.isArray() || !curClazz2.isArray()) {
                return null;
            } else {
                Class arrayClazz = curClazz1.getComponentType();// 返回表示数组组件类型的Class
                int length1 = Array.getLength(arrayObj1);// 返回当前数组的长度
                int length2 = Array.getLength(arrayObj2);// 返回当前数组的长度
                int newLength = length1 + length2;
                Object newArrayObj = Array.newInstance(arrayClazz, newLength);// 多态,创建新数组
                System.arraycopy(arrayObj1, 0, newArrayObj, 0, length1);// 赋值
                System.arraycopy(arrayObj2, 0, newArrayObj, length1, length2);// 赋值
                return newArrayObj;
            }
        }
        return null;
    }

    public static boolean validateTimestamp(String input) {
        String regex = "\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}";
        return Pattern.matches(regex, input);
    }

    public void addMsg(StringBuffer stringBuffer, String msg) {
        if (stringBuffer == null || stringBuffer.length() == 0) {
            stringBuffer.append(msg);
        } else {
            stringBuffer.append("\r\n").append(msg);
        }

    }

}