<template>
  <article class="upload-file">
    <el-upload
      ref="upload"
      action
      multiple
      :accept="accept"
      :limit="limit"
      width="480px"
      :on-exceed="handleExceed"
      :auto-upload="false"
      :before-upload="beforeAvatarUpload"
      :on-change="changeFile"
      :on-remove="removeFile"
      :file-list="fileList"
      :on-preview="cliFile"
      :disabled="disabled || progressFlag"
      :list-type="isPreview ? 'picture-card' : 'text'"
      :drag="drag"
    >
      <template v-if="isPreview">
        <i slot="default" class="el-icon-plus" />
      </template>
      <template v-else>
        <template v-if="drag">
          <i class="el-icon-upload" />
          <div class="el-upload__text">
            将文件拖到此处，或
            <em>点击上传</em>
          </div>
        </template>
        <template v-else>
          <el-button
            v-if="!disabled"
            slot="trigger"
            size="mini"
            type="primary"
            :loading="progressFlag"
          >
            {{ progressFlag ? uploadBtnName : btnName }}
          </el-button>
          <div
            v-if="progressFlag"
            style="width: 100%;height: 30px;position: absolute;top: 0"
          />
        </template>
      </template>
      <div slot="tip" class="el-upload__tip">
        {{ accept ? `只能上传 ${accept} 文件` : '' }}
      </div>
      <el-progress
        v-if="progressFlag"
        class="is-color-red is-overflow-hidden is-mb-10"
        style="width: 100%"
        :percentage="loadProgress"
        :format="format"
      />
    </el-upload>
    <el-dialog append-to-body :visible.sync="dialogVisible">
      <img width="100%" :src="dialogImageUrl" />
    </el-dialog>
  </article>
</template>

<script>
import httpRequest from '@/utils/httpRequest'
import sysConf from '@/api/rm/sysConf'
import SparkMD5 from 'spark-md5'
import serverProxy from '@/config/serverProxy'
let gateway = serverProxy.generic

export default {
  name: 'UploadFile',
  components: {},
  model: {
    prop: 'attachments',
    event: 'change'
  },
  props: {
    // 是否拖动上传
    drag: {
      type: Boolean,
      default: () => false
    },
    // 文件列表
    attachments: {
      type: Array,
      default: () => []
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: () => false
    },
    // 是否预览
    isPreview: {
      type: Boolean,
      default: () => false
    },
    // 上传个数限制
    limit: {
      type: Number,
      default: () => 1
    },
    // 支持文件类型
    accept: {
      type: String,
      default: () => '.doc,.docx,.pdf'
    },
    // 是否开启文件格式验证
    validationFormat: {
      type: Boolean,
      default: () => true
    },
    btnName: {
      type: String,
      default: () => '上传文件'
    },
    // 业务类型
    bizType: {
      type: String,
      default: () => 'GENERIC_SYS_CONF'
    }
  },
  data() {
    return {
      fileList: [],
      loadProgress: 0, // 动态显示进度条
      progressFlag: false, // 进度条显示状态
      startNum: 0, // 上传的文件个数
      endNum: 0, // 上传完成的文件个数
      dialogVisible: false, // 图片放大
      dialogImageUrl: '', // 图片地址
      uploadBtnName: '正在上传中，请稍后...',
      uuId: '' // 后台上传uuId
    }
  },
  watch: {
    attachments: {
      handler(val) {
        if (val)
          this.fileList = val.map(e => ({
            ...e,
            url: `/service/${gateway}/common-file/download/${e.attachmentId ||
              e.id}?contentDispositionType=inline`,
            name: e.originalFileName || e.name
          }))
      },
      immediate: true
    }
  },
  mounted() {
    // 获取uuid,判断是否执行后台上传
    this.$eventBus.$on('addTask', ({ uuId }) => {
      this.uuId = uuId
    })
  },
  methods: {
    // 上传动作完成
    changeFile(file, fileList) {
      if (file.status === 'ready') {
        let has = this.fileList.filter(
          item =>
            item.raw &&
            item.raw.lastModified === file.raw.lastModified &&
            item.raw.name === file.raw.name &&
            item.raw.size === file.raw.size
        )
        if (has.length) {
          this.$message({
            message: '请勿重复上传相同文件',
            type: 'error',
            duration: 1500
          })
          fileList.pop()
        } else {
          if (this.beforeAvatarUpload(file.raw)) {
            this.progressFlag = true
            this.startNum = fileList.length - this.fileList.length
            this.$set(file, 'description', '')
            this.uploadJudge(file)
          } else {
            fileList.splice(-1, 1)
          }
        }
      }
    },
    // 文件上传限制
    handleExceed(files, fileList) {
      this.$message.warning(
        `当前限制选择 ${this.limit} 个文件，本次选择了 ${
          files.length
        } 个文件，共选择了 ${files.length + fileList.length} 个文件`
      )
    },
    // 上传校验
    beforeAvatarUpload(file) {
      if (this.validationFormat) {
        const isJPG =
          this.accept === 'image/*' && file.type.indexOf('image') !== -1
        if (isJPG) return true
        else {
          let legalName = this.accept.split(',')
          let name = file.name.substring(
            file.name.lastIndexOf('.') + 1,
            file.name.length
          )
          if (legalName.includes('.' + name)) {
            return true
          } else {
            this.$message.error(`文件类型只能是${this.accept}`)
            return false
          }
        }
      } else {
        return true
      }
    },
    // 移除附件
    removeFile(file, fileList) {
      let index = this.fileList.indexOf(file)
      this.fileList.splice(index, 1)
      this.$emit('change', this.fileList)
      this.$emit('removeFun', this.fileList)
    },
    // 上传前判断动作
    uploadJudge(file) {
      // 上传开始
      this.$emit('uploadStart')
      if (file.size / 1024 / 1024 > 10) {
        this.getMD5(file)
      } else {
        // 常规上传
        this.uploadAction(file)
      }
    },
    // 常规上传至服务器
    uploadAction(file) {
      let formData = new FormData()
      formData.append('description', file.name)
      formData.append('bizType', this.bizType)
      formData.append(
        'file',
        file ? file.raw : null,
        encodeURIComponent(file.name)
      )
      let self = this
      this.uploadBtnName = `(${this.endNum}/${this.startNum}) 正在上传中，请稍后...`
      self.$forceUpdate()
      httpRequest({
        url: gateway + `/common-file/upload/${this.bizType}`,
        method: 'post',
        headers: {
          'Content-Type': 'multipart/form-data'
        },
        data: formData,
        // 获取上传进度
        onUploadProgress: progressEvent => {
          self.loadProgress =
            ((self.endNum + progressEvent.loaded / progressEvent.total) /
              self.startNum) *
            100
          self.$forceUpdate()
        }
      })
        .then(res => {
          if (this.endNum < this.startNum) {
            this.endNum++
            this.uploadBtnName = `(${this.endNum}/${this.startNum}) 正在上传中，请稍后...`
            self.$forceUpdate()
            this.loadProgress = (this.endNum / this.startNum) * 100
            this.$set(file, 'attachmentId', res.id)
            this.fileList.push(file)
            this.$forceUpdate()
            this.$emit('change', this.fileList)
            // 如果有uuid则视为执行后台上传，则刷新进度监控
            if (this.uuId && this.loadProgress !== 0)
              this.$eventBus.$emit('changeProgress', {
                uuId: this.uuId,
                progress: this.loadProgress,
                state: 0
              })
            if (this.startNum === this.endNum) {
              // 如果有uuid则视为执行后台上传，则刷新进度监控
              this.$emit('uploadEnd') // 结束上传
              if (this.uuId) {
                this.$eventBus.$emit('changeProgress', {
                  uuId: this.uuId,
                  progress: this.loadProgress,
                  state: 1
                })
                this.$eventBus.$emit('uploadSuccess', {
                  file: this.fileList,
                  uuId: this.uuId,
                  state: 1
                })
              }
              this.endNum = 0
              this.startNum = 0
              this.loadProgress = 0
              this.progressFlag = false
            }
          }
        })
        .catch(() => {
          this.endNum = 0
          this.startNum = 0
          this.loadProgress = 0
          this.progressFlag = false
          this.fileList = []
          this.$emit('change', this.fileList)
          this.$eventBus.$emit('uploadSuccess', {
            file: [],
            uuId: this.uuId,
            state: 2
          })
        })
    },
    // 计算MD5
    getMD5(file) {
      // 使用sparkMD5的ArrayBuffer类，读取二进制文件
      const spark = new SparkMD5.ArrayBuffer()
      const fileReader = new FileReader()
      const self = this
      // 异步操作，读完后的结果
      fileReader.onload = e => {
        // 把文件开始传入spark
        spark.append(e.target.result)
        // spark计算出MD5后的结果
        const _md5 = spark.end()
        self.getSliceData(_md5, file)
      }
      // fileReader读取二进制文件
      fileReader.readAsArrayBuffer(file.raw)
    },
    // 获取分片上传数据
    getSliceData(md5, file) {
      let param = {
        bizType: this.bizType,
        fileSize: file.size,
        md5: md5
      }
      sysConf
        .initiateMultipartUpload(param)
        .then(res => {
          this.uploadPart(
            file,
            res.bizType,
            res.maxParts,
            res.partSize,
            res.fileObjectName,
            res.uploadId,
            res.fileSize,
            md5,
            res.parts,
            0
          )
        })
        .catch(() => {
          this.fileList = []
          this.$emit('change', this.fileList)
          this.$eventBus.$emit('uploadSuccess', {
            file: [],
            uuId: this.uuId,
            state: 2
          })
        })
    },
    // 分片上传 文件、业务类型、分片总数、分片大小、文件名称、上传id、文件长度、md5、分片信息、分片片数
    async uploadPart(
      file,
      bizType,
      maxParts,
      partSize,
      fileObjectName,
      uploadId,
      fileSize,
      md5,
      parts,
      partNumber
    ) {
      // 上传进度
      this.progressFlag = true
      this.loadProgress =
        this.startNum === 1
          ? (partNumber / maxParts) * 100
          : (this.endNum / this.startNum) * 100
      // 如果有uuid则视为执行后台上传，则刷新进度监控
      if (this.uuId)
        this.$eventBus.$emit('changeProgress', {
          uuId: this.uuId,
          progress: this.loadProgress,
          state: 0
        })
      this.$forceUpdate()
      // 文件开始结束的位置
      let start = partNumber * partSize
      let end = Math.min(file.size, start + partSize)
      // 切割文件
      let packet = file.raw.slice(start, end)
      let formData = new FormData()
      formData.append('description', file.name)
      formData.append('file', packet)
      let param = {
        bizType, // 业务类型
        fileObjectName, // 文件名
        uploadId, // 上传id
        partNumber: partNumber + 1 // 分片片数
      }
      if (partNumber < maxParts) {
        // 进度条保留两位小数展示
        // progress.value = ((partNumber / maxParts) * 100).toFixed(2) * 1
        try {
          let self = this
          let ids = await httpRequest({
            url: gateway + `/common-file/uploadPart`,
            method: 'post',
            headers: {
              'Content-Type': 'multipart/form-data'
            },
            data: formData,
            params: param,
            // 获取上传进度
            onUploadProgress: progressEvent => {
              if (self.startNum === 1)
                self.loadProgress =
                  ((partNumber + progressEvent.loaded / progressEvent.total) /
                    maxParts) *
                  100
              self.$forceUpdate()
            }
          })
          this.$set(parts[partNumber], 'etag', ids)
          partNumber++
          if (partNumber === maxParts) {
            // this.progressFlag = false
            if (self.startNum === 1) {
              this.loadProgress = 0
              this.progressFlag = false
            }
            this.completeMultipartUpload(
              file,
              bizType,
              fileSize,
              md5,
              fileObjectName,
              uploadId,
              parts
            )
            return
          }
          await this.uploadPart(
            file,
            bizType,
            maxParts,
            partSize,
            fileObjectName,
            uploadId,
            fileSize,
            md5,
            parts,
            partNumber
          )
        } catch (e) {
          this.fileList = []
          this.progressFlag = false
          this.$emit('change', this.fileList)
          this.$eventBus.$emit('uploadSuccess', {
            file: [],
            uuId: this.uuId,
            state: 2
          })
        }
      }
    },
    // 合并分片文件
    completeMultipartUpload(
      file,
      bizType,
      fileSize,
      md5,
      fileObjectName,
      uploadId,
      parts
    ) {
      let formDataFinal = {
        bizType: bizType, // 业务类型
        fileName: file.name, // 文件名称
        fileSize: fileSize, // 文件长度
        md5: md5, // md5
        fileObjectName: fileObjectName, // 文件系统中的名称
        uploadId: uploadId, // 文件系统分片上传id
        description: file.name, // 描述
        partETags: parts.map(e => ({ partNumber: e.partNumber, etag: e.etag })) // 分片etag信息
      }
      sysConf
        .completeMultipartUpload(formDataFinal)
        .then(res => {
          if (this.endNum < this.startNum) {
            this.endNum++
            this.uploadBtnName = `(${this.endNum}/${this.startNum}) 正在上传中，请稍后...`
            this.$forceUpdate()
            this.loadProgress = (this.endNum / this.startNum) * 100
            this.$set(file, 'attachmentId', res.id)
            this.fileList.push(file)
            this.$forceUpdate()
            this.$emit('change', this.fileList)
            if (this.startNum === this.endNum) {
              // 如果有uuid则视为执行后台上传，则刷新进度监控
              this.$emit('uploadEnd') // 结束上传
              if (this.uuId) {
                this.$eventBus.$emit('changeProgress', {
                  uuId: this.uuId,
                  progress: this.loadProgress,
                  state: 1
                })
                this.$eventBus.$emit('uploadSuccess', {
                  file: this.fileList,
                  uuId: this.uuId,
                  state: 1
                })
              }
              this.endNum = 0
              this.startNum = 0
              this.loadProgress = 0
              this.progressFlag = false
            }
          }
        })
        .catch(() => {
          this.endNum = 0
          this.startNum = 0
          this.loadProgress = 0
          this.progressFlag = false
          this.fileList = []
          this.$emit('change', this.fileList)
          this.$eventBus.$emit('uploadSuccess', {
            file: [],
            uuId: this.uuId,
            state: 2
          })
        })
    },
    // 点击文件下载、预览
    cliFile(file) {
      // 预览
      if (this.isPreview) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      } else {
        // 下载
        if (file.attachmentId || file.id)
          sysConf.downloadById(file.attachmentId || file.id)
      }
    },
    // 上传进度提示
    format(percentage) {
      return percentage === 100 ? '' : `${percentage.toFixed(2)}%`
      // if (this.startNum > 1) return percentage === 100 ? `等待上传第${this.endNum + 2}个文件` : `${percentage.toFixed(2)}%`
      // else return percentage === 100 ? '上传未结束' : `${percentage.toFixed(2)}%`
    }
  }
}
</script>

<style lang="scss" scoped></style>

<style lang="scss">
.upload-file {
  .el-upload-list.is-disabled {
    //margin-top: -36px;
  }
  .el-progress {
    .el-progress-bar {
      padding-right: 70px;
      margin-right: -60px;
    }
    .el-progress__text {
      color: #409eff;
    }
  }
}
</style>
