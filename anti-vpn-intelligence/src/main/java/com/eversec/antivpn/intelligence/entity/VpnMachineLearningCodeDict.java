package com.eversec.antivpn.intelligence.entity;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.StrUtil;
import com.eversec.antivpn.intelligence.entity.po.VpnMachineLearningCodeDictPO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-21
 */
@Getter
@Setter
@Schema(name = "VpnMachineLearningCodeDict", description = "")
public class VpnMachineLearningCodeDict extends VpnMachineLearningCodeDictPO implements java.io.Serializable {

    private static final long serialVersionUID = 1L;

    /**
     *
     * @param fileName
     * @param fileType 文件类型，当为空时取记录中的值，当不为空时，取传进来的值
     * @return
     */
    public String toCsvLine(String fileName, Integer fileType) {
        if (StrUtil.isBlank(fileName)) {
            return "";
        }
        List<String> fields = new ArrayList<>();
        fields.add(nullToEmpty(getVersion()));
        fields.add(nullToEmpty(getProvinceId()));
        fields.add(nullToEmpty(getComCode()));
        fields.add(nullToEmpty(getNetworkBusinessId()));
        if (fileType == null) {
            fileType = getFileType();
        }
        fields.add(nullToEmpty(fileType));
        fields.add(nullToEmpty(getModelCode()));
        fields.add(nullToEmpty(getModelName()));
        fields.add(nullToEmpty(getModelVersion()));
        fields.add(nullToEmpty(fileName));
        fields.add(nullToEmpty(getContentFileName()));
        fields.add(nullToEmpty(getContentFilesuffix()));
        // 新增
        fields.add(nullToEmpty(0));
        fields.add(nullToEmpty(getTimeStamp()));


        return StrUtil.join(",", fields);
    }

    private String nullToEmpty(Object value) {
        if (value == null) {
            return "";
        } else {
            return String.valueOf(value);
        }
    }

    /*
     * 扩展字段，数据库中不存在的字段，数据库中不存在的字段需加 @TableField(exist = false)
     * 如字典翻译、子对象、子对象列表等
     */
//    @com.baomidou.mybatisplus.annotation.TableField(exist = false)
//    private String xxx;

}