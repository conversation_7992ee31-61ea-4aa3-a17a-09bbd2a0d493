package com.eversec.antivpn.util;

import cn.hutool.core.util.URLUtil;
import cn.hutool.extra.ftp.Ftp;
import cn.hutool.extra.ftp.FtpConfig;
import cn.hutool.extra.ssh.JschUtil;
import cn.hutool.extra.ssh.Sftp;
import com.jcraft.jsch.Session;
import lombok.SneakyThrows;

import java.net.URL;
import java.nio.charset.StandardCharsets;

/**
 * ftp 工具类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023-07-31 16:49
 */
public class FtpUtil {

	/**
	 * 根据ftp地址, 获取hutoolFtp对象
	 * @param ftpAddress ftp://localhost:21/report-log/01-11
	 * @param username
	 * @param password
	 * @return hutool Ftp对象
	 */
	public static Ftp buildFtpClient(String ftpAddress, String username, String password){
		URL urlForHttp = URLUtil.toUrlForHttp(ftpAddress);
		int port = urlForHttp.getPort();
		if (port == -1) {
			port = 21;
		}
		return new Ftp(urlForHttp.getHost(), port, username, password);
	}

	@SneakyThrows
	public static Sftp buildSftpClient(String ftpAddress, String username, String password){
		URL urlForHttp = URLUtil.toUrlForHttp(ftpAddress);
		int port = urlForHttp.getPort();
		if (port == -1) {
			port = 22;
		}

//		FtpConfig config = new FtpConfig();
//		config.setHost(urlForHttp.getHost());
//		config.setPort(port);
//		config.setUser(username);
//		config.setPassword(password);
//		// 超时时间设置成24小时
//		config.setConnectionTimeout(24* 60 * 60 * 1000L);
//		config.setSoTimeout(24* 60 * 60 * 1000L);
//		config.setCharset(StandardCharsets.UTF_8);

		Session session = JschUtil.createSession(urlForHttp.getHost(), port, username, password);
		session.setTimeout(24* 60 * 60 * 1000);
		return new Sftp(session);
	}



	/**
	 * 根据地址获取ftp路径
	 * @param ftpAddress ftp://localhost:21/report-log/01-11
	 * @return /report-log/01-11
	 */
	public static String getFtpPath(String ftpAddress) {
		URL urlForHttp = URLUtil.toUrlForHttp(ftpAddress);
		return urlForHttp.getPath();
	}

	public static void main(String[] args) {

	}

}
