plugins {
    id 'java-library'
}

dependencies {

    implementation project(":anti-vpn-supoort:anti-vpn-supoort-api")
    implementation project(":anti-vpn-intelligence:anti-vpn-intelligence-api")
    implementation project(':anti-vpn-common:anti-vpn-config')
    implementation project(':anti-vpn-common:anti-vpn-util')

    // mybatis-plus工具扩展等
    implementation "com.eversec.stark:stark-common:${starkCommonVersion}"
    implementation "com.eversec.stark.generic:generic-common:${genericVersion}"
    // 数据字典、系统配置api
    implementation "com.eversec.stark.generic:generic-sys-api:${genericVersion}"
    implementation "com.eversec.stark.generic:generic-file-api:${genericVersion}"
    implementation "com.eversec.framework:eversec-feignboot:${eversecFeignbootVersion}"

    // webservice
    api "org.apache.cxf:cxf-spring-boot-starter-jaxws:${webserviceCxfVersion}"
    api "org.apache.cxf:cxf-rt-transports-http:${webserviceCxfVersion}"
    api "org.apache.cxf:cxf-rt-features-logging:${webserviceCxfVersion}"
    api 'org.springframework.boot:spring-boot-starter-web-services'

    implementation "cn.hutool:hutool-all:${hutoolVersion}"
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-data-jpa:'
    implementation "org.springframework.cloud:spring-cloud-starter-bootstrap"
    implementation "org.springframework.cloud:spring-cloud-starter-netflix-eureka-client"
    implementation "com.baomidou:mybatis-plus-boot-starter:${mybatisPlusVersion}"
    implementation "org.springdoc:springdoc-openapi-ui:${springdocOpenapiVersion}"
    implementation "com.github.xiaoymin:knife4j-springdoc-ui:${knife4jSpringdocVersion}"
    implementation 'commons-fileupload:commons-fileupload:1.5'
    implementation 'org.apache.commons:commons-compress:1.23.0'
    //  导入
    implementation 'org.apache.poi:poi:3.15'
    implementation 'org.apache.poi:poi-ooxml:3.15'
    implementation 'cn.afterturn:easypoi-web:3.0.1'
    implementation 'cn.afterturn:easypoi-annotation:3.0.1'

    implementation files('libs/cb-ability-sdk-1.4.3.jar')

    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'org.testng:testng'
    testImplementation "com.clickhouse:clickhouse-jdbc:${clickhouseConnectorVersion}"
    testImplementation "com.eversec.stark:stark-common:${starkCommonVersion}"
    testImplementation "com.eversec.stark.generic:generic-common:${genericVersion}"
    testImplementation project(":anti-vpn-supoort")
    // leader判断
    testImplementation "org.springframework.integration:spring-integration-jdbc"
    testImplementation 'org.springframework.boot:spring-boot-starter-data-jpa'
    test {
        useTestNG()
    }
}
