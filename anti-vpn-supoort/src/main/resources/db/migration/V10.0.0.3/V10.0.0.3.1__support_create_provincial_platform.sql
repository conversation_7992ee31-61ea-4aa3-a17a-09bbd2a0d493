-- 平台接口信息
CREATE TABLE IF NOT EXISTS `platform_interface_info` (
    `id`  bigint(20) NOT NULL COMMENT '主键，雪花',
    `config_type` varchar(100) NOT NULL COMMENT '配置类型。CURRENT:当前系统; PROVINCE: 省平台。平台部署时，存在一条当前系统配置记录。',
    `name` varchar(100) NOT NULL COMMENT '名称',
    `com_code` varchar(100) NOT NULL COMMENT '运营商编码。省平台部署：当前平台运营商编码；集团部署：运营商编码',
    `province_id` bigint(20) NOT NULL COMMENT '省级区域编号。省平台部署：当前平台省级区域编号；集团部署：省级区域编号',
    `system_code` varchar(100) default NULL COMMENT '1:集团；2:省平台；3:IDC，4:僵木蠕,5:恶意程序；6:DNS；999:其他。 省平台业务系统标识。省平台部署：当前省平台业务系统标识；集团部署：省平台业务系统标识',
    `south_receive_dir` varchar(1000) default NULL COMMENT 'DC消费的目录。省平台部署：DC消费的目录；集团部署：DC消费的目录，每个省平台配置的目录不同，可通过该目录查询省平台信息',
-- 省、集团都有的
    `north_report_ftp_address` varchar(200) default NULL COMMENT '上报ftp地址。例：ftp://localhost:21/report-log/01-11',
    `north_report_ftp_username` varchar(200) default NULL COMMENT '上报ftp username',
    `north_report_ftp_password` varchar(1000) default NULL COMMENT '上报ftp password',
    `north_report_secret_key` varchar(200) default NULL COMMENT '上报加密密钥',
    `north_intelligence_ftp_address` varchar(200) default NULL COMMENT '情报库ftp地址。例：ftp://localhost:21/download-intelligence/01-11',
    `north_intelligence_ftp_username` varchar(200) default NULL COMMENT '情报库ftp username',
    `north_intelligence_ftp_password` varchar(200) default NULL COMMENT '情报库ftp password',
    `north_intelligence_secret_key` varchar(100) default NULL COMMENT '情报库解密密钥',
    `north_current_network_business_ids` varchar(100) default NULL COMMENT '上报企业状态-网络类型id，多类型|分割',
-- 集团部署或者对接CU
    `south_intelligence_dir` varchar(500) default NULL COMMENT '情报库目录。省平台部署：CU情报库目录；集团部署：情报库目录，透传部侧情报库文件',
    `south_ws_address` varchar(200) default NULL COMMENT '省平台部署：CU webservice四肢，集团部署：集团调用省平台webservice地址，省平台调用CU地址，例如：http://localhost:8080/services',
    `south_receive_secret_key` varchar(200) default NULL COMMENT '接收解密密钥',
-- 省平台独有
    `south_smart_address` varchar(200) default NULL COMMENT 'smart 服务地址。',
    `south_smart_username` varchar(200) default NULL COMMENT 'smart 用户名。',
    `south_smart_password` varchar(200) default NULL COMMENT 'smart 密码。',

    `create_user` varchar(100) DEFAULT NULL COMMENT '创建人员',
    `update_user` varchar(100) DEFAULT NULL COMMENT '修改人员',
    `create_datetime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间,入库时间',
    `update_datetime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间,记录最近修改时间',
    `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除状态,0未删除，1已删除',
    PRIMARY KEY (`id`) USING BTREE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='平台接口信息';

CREATE TABLE IF NOT EXISTS `province_platform_status` (
    `id`  bigint(20) NOT NULL COMMENT '主键，雪花',

    `com_code` varchar(100) NOT NULL COMMENT '运营商编码',
    `province_id` bigint(20) NOT NULL COMMENT '省级区域编号',
    `system_code` varchar(100) default NULL COMMENT '省平台业务系统标识',
    `network_business_ids` json DEFAULT NULL COMMENT '网络类型编码，数组存储 ',
    `current_state` int(8) default NULL COMMENT '当前状态。监测网络类型的系统状态：0：正常；1：异常；2：未覆盖；9：其他。',
    `time_stamp` varchar(19) NOT NULL COMMENT '创建时间,生成该文件的时间',

    `create_user` varchar(100) DEFAULT NULL COMMENT '创建人员',
    `update_user` varchar(100) DEFAULT NULL COMMENT '修改人员',
    `create_datetime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间,入库时间',
    `update_datetime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间,记录最近修改时间',
    `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除状态,0未删除，1已删除',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='省平台信息（企业测）';
