package com.eversec.antivpn.log.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.eversec.antivpn.emnus.ScreenTypeEnum;
import com.eversec.antivpn.log.aop.RedisCache;
import com.eversec.antivpn.log.api.dto.DateHistogramCombinationDTO;
import com.eversec.antivpn.log.api.dto.TypeCountDTO;
import com.eversec.antivpn.log.api.dto.base.DateHistogramDTO;
import com.eversec.antivpn.log.mapper.vo.DateHistogramCombinationVO;
import com.eversec.antivpn.log.mapper.vo.DateHistogramVO;
import com.eversec.antivpn.log.mapper.vo.PlatformInterfaceVO;
import com.eversec.antivpn.log.mapper.vo.TypeCountVO;
import com.eversec.antivpn.log.schedule.MonitorDataReport;
import com.eversec.antivpn.log.schedule.TestDataReport;
import com.eversec.antivpn.log.service.LogService;
import com.eversec.antivpn.log.service.ScreenRedisService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.type.JdbcType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/7/24 14:28
 **/
@Slf4j
@Tag(name = "总体态势")
@RestController
@RequestMapping("/log/overall")
public class OverallLogController {

    @Resource
    private LogService logService;

    @Resource
    private MonitorDataReport monitorDataReport;

    @Resource
    private TestDataReport testDataReport;

    @RedisCache
    @Operation(summary = "总体态势-地图统计")
    @GetMapping("/provinceAggregate")
    public List<TypeCountDTO> provinceAggregate(ScreenTypeEnum screenTypeEnum) {
        List<TypeCountVO> list = logService.getProvinceAggregate(screenTypeEnum);
        List<TypeCountDTO> result = dealWithTypeCountVO(list);
        return result;
    }


    @RedisCache
    @Operation(summary = "总体态势-用户使用的网络类型占比")
    @GetMapping("/networkBusinessTypeAggregate")
    public List<TypeCountDTO> networkBusinessTypeAggregate(@RequestParam(required = true) ScreenTypeEnum screenTypeEnum) {
        List<TypeCountVO> list = logService.getNetworkBusinessTypeAggregate(screenTypeEnum);
        List<TypeCountDTO> result = dealWithTypeCountVO(list);
        return result;
    }

    @RedisCache
    @Operation(summary = "总体态势-接收各省端监测日志与上报")
    @GetMapping("/dateHistogram")
    public DateHistogramCombinationDTO dateHistogram(@RequestParam(required = true) ScreenTypeEnum screenTypeEnum) {
        DateHistogramCombinationVO dateHistogramCombinationVO = logService.getDateHistogram(screenTypeEnum);
        DateHistogramCombinationDTO result = dealWithDateHistogramCombinationVO(dateHistogramCombinationVO);
        return result;
    }

    private DateHistogramCombinationDTO dealWithDateHistogramCombinationVO(DateHistogramCombinationVO dateHistogramCombinationVO) {
        DateHistogramCombinationDTO result = new DateHistogramCombinationDTO();
        result.setTotalReport(dateHistogramCombinationVO.getTotalReport());
        result.setTodayReport(dateHistogramCombinationVO.getTodayReport());
        result.setDtoList(dealWithDateHistogramVO(dateHistogramCombinationVO.getDateHistogramDTOList()));
        return result;
    }

    @RedisCache
    @Operation(summary = "总体态势-传输层协议占比")
    @GetMapping("/protocolTypeAggregate")
    public List<TypeCountDTO> protocolTypeAggregate(@RequestParam(required = true) ScreenTypeEnum screenTypeEnum) {
        List<TypeCountDTO> result = dealWithTypeCountVO(logService.getProtocolTypeAggregate(screenTypeEnum));
        return result;
    }

    @RedisCache
    @Operation(summary = "总体态势-匹配检测方式")
    @GetMapping("/vpnTypeAggregate")
    public List<TypeCountDTO> vpnTypeAggregate(@RequestParam(required = true) ScreenTypeEnum screenTypeEnum) {
        List<TypeCountDTO> result = dealWithTypeCountVO(logService.getVpnTypeAggregate(screenTypeEnum));
        return result;
    }


    @RedisCache
    @Operation(summary = "总体态势- 各系统上报检测日志统计")
    @GetMapping("/systemAggCount")
    public List<TypeCountDTO> systemAggCount(@RequestParam(required = true) ScreenTypeEnum screenTypeEnum) {
        List<TypeCountDTO> result = dealWithTypeCountVO(logService.getSystemLogAggCount(screenTypeEnum));;
        return result;
    }

    @RedisCache
    @Operation(summary = "总体态势-testSave")
    @GetMapping("/testSave")
    public List<TypeCountDTO> testSave() {
        List<PlatformInterfaceVO> platformInterfaceVOList = new ArrayList<>();
        PlatformInterfaceVO platformInterfaceVO = new PlatformInterfaceVO();
        platformInterfaceVO.setCurrentState(1);
        platformInterfaceVO.setConfigType("he");
        platformInterfaceVO.setSystemCode("11l");
        platformInterfaceVO.setProvinceId(111l);
        platformInterfaceVO.setComCode("com");
        platformInterfaceVO.setNetworkBusinessIds("hello world");
        platformInterfaceVO.setNetworkBusinessIds("1|2");
        platformInterfaceVOList.add(platformInterfaceVO);
        monitorDataReport.saveProvincePlatformStatus(platformInterfaceVOList);
        return null;
    }

    @Operation(summary = "数据监控接口")
    @GetMapping("/monitor")
    public Map<String,List<String>> monitor() {
        return logService.getMonitorDataMap();
    }


    @Operation(summary = "测试异常上报")
    @GetMapping("/reportError")
    public List<PlatformInterfaceVO> reportError() {
        return testDataReport.monitorSystemReport(1);
    }

    @Operation(summary = "测试未覆盖")
    @GetMapping("/reportUnCover")
    public List<PlatformInterfaceVO> reportUnCover() {
        return testDataReport.monitorSystemReport(2);
    }


    private List<TypeCountDTO> dealWithTypeCountVO(List<TypeCountVO> list) {
        List<TypeCountDTO> result = new ArrayList<>();
        list.forEach(typeCountVO -> {
            result.add(BeanUtil.toBean(typeCountVO, TypeCountDTO.class));
        });
        return result;
    }

    private List<DateHistogramDTO> dealWithDateHistogramVO(List<DateHistogramVO> list) {
        List<DateHistogramDTO> result = new ArrayList<>();
        list.forEach(dateHistogramVO -> {
            result.add(BeanUtil.toBean(dateHistogramVO, DateHistogramDTO.class));
        });
        return result;
    }

}
