# 整体说明

### 在全局配置中设置 useBiDic: true 可支持 bi 外部字典（全部需要）

### 在全局配置中加入 bi_query_useGateway 用于控制查询时是否通过网关（全部需要）

### 在全局配置里加入 bi_query_dsId 以便后期修改（sigleTable 和 execQuery 需要）

### 确保存在依赖 "crypto-js": "^4.0.0"，以免 des 报错（sigleTable 需要）

```js
// 全局配置（public/global.config.js 中）
window.globalConfig = {
  ...
  
  // bi_query
  useBiDic: true,
  bi_query_useGateway: true,
  bi_query_dsId: "9326125e3d5e41ff9d7620454f8341cf", // 示例数据源id
}
```

# sigleTable 使用示例

## 通过 bi 单表查询接口，获取单表的数据

```js
import api_singleTable from "@/api/bi_query/singleTable";

api_singleTable
  .getList({
    tableName: "base_dic_item",
    conditions: [],
    paging: 1,
    limit: 10,
    fields: []
  })
  .then(data => {
    console.log(data);
  })
  .catch(e => {
    console.log(e);
  });
```

# olap 使用示例

## 通过 bi 中配置的图表/统计表格，获取相应的统计数据

### 通过 chartId 获取图表配置后，自己可再加工，之后查询数据

```js
import api_olap from "@/api/bi_query/olap";

api_olap
  .getChartConfByChartId("5e9d1b68330e49828627ae7e9147afa1")
  .then(chartConf => {
    console.log("图表配置", chartConf);
    chartConf.filters = [
      {
        alias: "规则状态",
        filterExpressions: [{
          filterExpression: "eq",
          values: ["2"],
        }],
      },
      {
        alias: "开机天数",
        filterExpressions: [{
          filterExpression: "gt",
          values: [50],
        }],
      },
    ];
    api_olap.getDataByChartConf(
      chartConf,
      { page: { pageSize: 10, pageIndex: 1 } },
      data => {
        console.log("图表数据", data);
      },
      () => {
        console.log("图表数据获取失败！");
      }
    );
  }).catch(e => {
    console.log(e);
  });
```

# execQuery 使用示例

## 针对某一数据源直接执行 sql 语句

### 在全局配置里加入 bi_query_dsId 以便后期修改

```js
import api_execQuery from "@/api/bi_query/execQuery";

api_execQuery
  .execQuerySql({
    strSql: `select item_name as name, item_value as value from base_dic_item`,
    paging: 1,
    limit: 10
  }).then(data => {
    console.log(data);
  }).catch(e => {
    console.log(e);
  });
```
