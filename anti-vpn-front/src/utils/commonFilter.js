import Vue from 'vue'

//时间戳转换为"yyyy-MM-dd HH:mm:ss"格式
Vue.filter('timeFilter', function(val) {
  var unixtimestamp = new Date(val)
  var year = 1900 + unixtimestamp.getYear()
  var month = '0' + (unixtimestamp.getMonth() + 1)
  var date = '0' + unixtimestamp.getDate()
  var hour = '0' + unixtimestamp.getHours()
  var minute = '0' + unixtimestamp.getMinutes()
  var second = '0' + unixtimestamp.getSeconds()
  return (
    year +
    '-' +
    month.substring(month.length - 2, month.length) +
    '-' +
    date.substring(date.length - 2, date.length) +
    ' ' +
    hour.substring(hour.length - 2, hour.length) +
    ':' +
    minute.substring(minute.length - 2, minute.length) +
    ':' +
    second.substring(second.length - 2, second.length)
  )
})
//字典表中获取的下拉选项过滤
Vue.filter('codeFilter', function(val, list) {
  list &&
    list.forEach(item => {
      if (item.itemValue == val) {
        val = item.itemName
      }
    })
  return val
})

//AccessKey-hidden
Vue.filter('accessKeyHiddenFilter', function(val) {
  if (val && val.length == 32) {
    val = val.substring(0, 8) + '****************' + val.substring(25)
  }
  return val
})

// 过滤器，每三位加逗号
Vue.filter('capitalize', function(val) {
  return (+val || 0).toFixed(0).replace(/\d{1,3}(?=(\d{3})+(\.\d*)?$)/g, '$&,')
})

// 过滤器，数值加万，亿
Vue.filter('num2Ft', function(n) {
  let numFt = n;
  if (n >= 10000 && n < 100000000) {
    n = (n / 10000).toFixed(2);
    numFt = n + '万';
  } else if(n >= 100000000) {
    n = (n / 100000000).toFixed(2); 
    numFt = n + '亿';
  }
  return numFt;
})
// // 过滤器，数值加万，百万，亿
// Vue.filter('num2Ft', function(n) {
//   let numFt = n;
//   if (n >= 10000 && n < 100000) {
//     n = (n / 10000).toFixed(2);
//     numFt = n + '万';
//   } else if(n >= 100000 && n < 1000000) {
//     n = (n / 100000).toFixed(2); 
//     numFt = n + '十万';
//   } else if(n >= 1000000 && n < 10000000) {
//     n = (n / 1000000).toFixed(2); 
//     numFt = n + '百万';
//   } else if(n >= 10000000 && n < 100000000) {
//     n = (n / 10000000).toFixed(2); 
//     numFt = n + '千万';
//   } else if(n >= 100000000) {
//     n = (n / 100000000).toFixed(2); 
//     numFt = n + '亿';
//   }
//   return numFt;
// })

