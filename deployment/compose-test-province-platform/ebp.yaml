version: '3'
services:
  eureka:
    image: harbor-bj.eversaas.cn/appcenter/ebp.eureka:2.4.0
    ports:
      - 31000:8080
    volumes:
      - tmp:/tmp
      - logs:/app/logs
    environment:
      - TZ=Asia/Shanghai
      - SPRING_PROFILES_ACTIVE=ebp
      - SESSION_TIME=PT8H
      - SERVER_SESSION_TIMEOUT=28800
      - SERVER_PORT=8080
      - DASHBOARD_ENABLED=true
      - spring.security.basic.enabled=false
      - spring.security.user.name=eversec
      - spring.security.user.password=eversec-eureka-2021
    networks:
      - db_net
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "10"
    healthcheck:
      test: [ "CMD-SHELL", "curl -sS localhost:8080 || exit 1" ]
      interval: 30s
      timeout: 5s
      retries: 40
    deploy:
      restart_policy:
        condition: any
        delay: 30s

      resources:
        limits:
          cpus: '1'
          memory: 500M

  ebp-gateway:
    image: harbor-bj.eversaas.cn/appcenter/ebp.ebp-gateway:2.4.0
    ports:
      - 30000:8080
    volumes:
      - tmp:/tmp
      - logs:/app/logs
    networks:
      - db_net
    environment:
      - TZ=Asia/Shanghai
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=Redis!QAZ2wsx
      - REDIS_DB=0
      - SPRING_PROFILES_ACTIVE=docker
      - SESSION_TIME=PT8H
      - SERVER_PORT=8080
      - COOKIE_PATH=/
#      - COOKIE_DOMAIN=''
      - SERVER_SESSION_TIMEOUT=28800
#      - BASE_DOMAIN=''
      - EUREKA_HOST=eureka
      - EUREKA_PORT=8080
      - IGNORED_INTERFACE=eth[1-2]
      - SWAGGER_ENABLE=false
      - MANAGEMENT_ENABLE=false
      - VALIDATE_OPEN=false
      - SINGLE_USER=false
      - WHITE_LIST=''
      - eureka.client.registry-fetch-interval-seconds=5
      - zuul.set-content-length=true
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "10"
    healthcheck:
      test: [ "CMD-SHELL", "curl -sS localhost:8080 || exit 1" ]
      interval: 30s
      timeout: 5s
      retries: 40
    deploy:
      replicas: 1
      restart_policy:
        condition: any
        delay: 30s

      resources:
        limits:
          cpus: '1'
          memory: 1G

  ebp-system-service:
    image: harbor-bj.eversaas.cn/appcenter/ebp.system-service:2.4.0
    ports:
      - 30010:8080
    volumes:
      - tmp:/tmp
      - logs:/app/logs
    networks:
      - db_net
    environment:
      - TZ=Asia/Shanghai
      - SPRING_PROFILES_ACTIVE=docker
      - SESSION_TIME=PT8H
      - SERVER_SESSION_TIMEOUT=28800
#      - BASE_DOMAIN=''
      - SERVER_PORT=8080
      - DATABASE_HOST=**************
      - DATABASE_PORT=3306
      - DATABASE_NAME=system
      - DATABASE_USER=root
      - DATABASE_USERNAME=root
      - DATABASE_PASSWORD=r9xh*yH*DcEFQE
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=Redis!QAZ2wsx
      - REDIS_DB=0
      - EUREKA_HOST=eureka
      - EUREKA_PORT=8080
      - IGNORED_INTERFACE=eth[1-2]
      - SWAGGER_ENABLE=false
      - MANAGEMENT_ENABLE=false
      - DEFAULT_PWD='Eversec123!@#abc'
      - DEFAULT_PWD_RANDOM=true
      - LOGIN_ERROR=true
      - LOGIN_ERROR_TIME=1
      - LOGIN_ERROR_NUM=10
      - eureka.instance.lease-renewal-interval-in-seconds=30
      - eureka.instance.lease-expiration-duration-in-seconds=90
      - eureka.client.healthcheck.enabled=false
      - eureka.client.register-with-eureka=true
      - eureka.client.fetch-registry=true
      # 密码过期时间：注意，这里的单位是分钟，并不是小时，默认100年
      - login.pass-expire-hours=52560000

    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "10"
    healthcheck:
      test: [ "CMD-SHELL", "curl -sS localhost:8080/swagger-ui.html || exit 1" ]
      interval: 30s
      timeout: 5s
      retries: 40
    deploy:
      replicas: 1
      restart_policy:
        condition: any
        delay: 30s

      resources:
        limits:
          cpus: '1'
          memory: 1G

volumes:
  tmp:
  logs:

networks:
  db_net:
    external: true