// 配置分页信息
export const pagingInfo = () => {
  return {
    current: 1,
    size: 10,
    pageNo: 1,
    pageNumber: 1,
    pageSizes: [10, 30, 50, 100,500],
    pageSize: 10,
    total: 0,
    layout: 'total, sizes, prev, pager, next, jumper'
  }
}
// 自定义分页配置信息
export const customPagingInfo = ({
  pageNo = 1,
  pageNumber = 1,
  pageSizes = [10, 30, 50, 100],
  pageSize = 10,
  total = 0,
  layout = 'total, sizes, prev, pager, next, jumper'
}) => {
  return {
    pageNo: pageNo,
    pageNumber: pageNumber,
    pageSizes: pageSizes,
    pageSize: pageSize,
    total: total,
    layout: layout
  }
}
