<template>
  <div>
    <el-dialog
      class="dialog-handle"
      :title="title"
      :visible.sync="visible"
      :close-on-click-modal="false"
      :loading="loading"
    >
      <el-form ref="ruleForm" label-width="100px" class="demo-ruleForm">
        <el-form-item label="下载模板">
          <el-button type="primary" @click="exportTemplate">
            下载情报信息模板
          </el-button>
          <br />
          <span style="color: #ddd;">*导入信息时需严格按照此模板填写</span>
        </el-form-item>
        <el-form-item label="运营商">
          <dic-select
            v-model="comCode"
            :enum-key="'ISP_CODE'"
            :clearable="true"
            :showAll="true"
            :placeholder="`请选择运营商`"
          />
        </el-form-item>
        <el-form-item label="省级区域">
          <dic-select
            v-model="provinceId"
            :enum-key="'PROVINCE_CODE'"
            :clearable="true"
            :placeholder="`请选择省级区域`"
          />
        </el-form-item>
        <el-form-item label="业务系统标识">
          <dic-select
            v-model="systemCode"
            :enum-key="'SYSTEM_CODE'"
            :clearable="true"
            :placeholder="`请选择业务系统标识`"
          />
        </el-form-item>
        <el-form-item :label="`导入情报信息`" prop="">
          <el-upload
            class="upload-demo"
            action=""
            :beforeUpload="beforeAvatarUpload"
            :http-request="handleUpload"
            :on-success="handleproSuccess"
            :on-remove="removeFile"
            :file-list="fileList"
            :limit="1"
            :on-change="changFile"
          >
            <el-button type="primary">选择文件</el-button>
          </el-upload>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="visible = false">取消</el-button>
        <el-button
          type="primary"
          :loading="loading"
          :disabled="!(fileList && fileList.length > 0)"
          @click="submitFun"
        >
          确定
        </el-button>
      </span>
    </el-dialog>
    <!--    <el-dialog-->
    <!--      class="dialog-handle"-->
    <!--      title="导入失败数据"-->
    <!--      :visible.sync="visibleError"-->
    <!--      :close-on-click-modal="false"-->
    <!--      :loading="loading"-->
    <!--    >-->
    <!--      <div class="is-mb-20">-->
    <!--        共计导入数据 {{ total }} 条，成功-->
    <!--        <span class="is-color-green">{{ successList }}</span>-->
    <!--        条， 失败-->
    <!--        <span class="is-color-red">{{ failureList }}</span>-->
    <!--        条-->
    <!--      </div>-->
    <!--      <div class="is-mb-10" v-for="(val, key) of importErrors" :key="key">-->
    <!--        <span class="is-text-weight-700">{{ key }}</span>-->
    <!--        : {{ val }}-->
    <!--      </div>-->
    <!--      <span slot="footer" class="dialog-footer">-->
    <!--        <el-button @click="visibleError = false">关 闭</el-button>-->
    <!--        <el-button type="primary" :loading="loading" @click="importerror">-->
    <!--          下载错误数据-->
    <!--        </el-button>-->
    <!--      </span>-->
    <!--    </el-dialog>-->
  </div>
</template>

<script>
import antiVpnService from '@/api/antiVpnService'
import { downloadExcel } from '@/utils/download'

export default {
  name: 'knowledge-base-import',
  components: {},
  props: {
    type: {
      type: String,
      default: () => ''
    }
  },
  data() {
    return {
      loading: false,
      visible: false,
      visibleError: false,
      title: `导入情报信息`,
      fileList: [],
      total: null,
      successList: null,
      failureList: null,
      importErrors: {},
      importErrorBases: {},
      comCode: null,
      provinceId: null,
      systemCode: null
    }
  },
  computed: {},
  watch: {},
  mounted() {},
  methods: {
    // 获取平台配置
    getCurrentPlatformForFront() {
      antiVpnService.getCurrentPlatformForFront().then(res => {
        this.comCode = (res && res.comCode + '') || null
        this.provinceId = (res && res.provinceId + '') || null
        this.systemCode = (res && res.systemCode + '') || null
      })
    },
    handleUpload(param) {},
    handleproSuccess(res, file) {},
    openDialog() {
      this.fileList = []
      this.comCode = null
      this.provinceId = null
      this.systemCode = null
      this.visible = true
      this.total = null
      this.successList = null
      this.failureList = null
      this.importErrors = {}
      this.importErrorBases = {}
      this.getCurrentPlatformForFront()
    },
    // 下载模版
    exportTemplate() {
      antiVpnService.vpnIntelligenceDownloadTemplate()
    },
    // 上传回调
    changFile(file, fileList) {
      this.fileList = fileList
    },
    // 上传验证
    beforeAvatarUpload(file) {
      let fileExt = file.name.substring(file.name.lastIndexOf('.') + 1)
      const extension =
        fileExt === 'xlsx' ||
        fileExt === 'xls' ||
        fileExt === 'XLSX' ||
        fileExt === 'XLS' ||
        fileExt === 'csv' ||
        fileExt === 'CSV' ||
        fileExt === 'zip' ||
        fileExt === 'ZIP' ||
        fileExt === 'pcap' ||
        fileExt === 'PCAP'
      if (!extension) {
        this.$message({
          message: '上传文件只能是.XLSX、.XLS格式、.CSV、.ZIP、PCAP!',
          type: 'warning'
        })
      }
      return extension
    },
    // 移除附件
    removeFile(file, fileList) {
      let index = this.fileList.indexOf(file)
      this.fileList.splice(index, 1)
    },
    // 上传
    submitFun() {
      if (this.fileList.length === 0) {
        this.$message({
          message: '请选择需要导入的文件',
          type: 'error'
        })
        return
      }
      if (!this.comCode) {
        this.$message({
          message: '请选择运营商',
          type: 'error'
        })
        return
      }
      if (!this.provinceId) {
        this.$message({
          message: '请选择省级区域',
          type: 'error'
        })
        return
      }
      if (!this.systemCode) {
        this.systemCode = ''
        // this.$message({
        //   message: '请选择业务系统标识',
        //   type: 'error'
        // })
        // return
      }
      this.loading = true
      let formData = new FormData()
      formData.append('file', this.fileList[0].raw)
      antiVpnService
        .vpnIntelligenceImport(
          formData,
          this.comCode,
          this.provinceId,
          this.systemCode
        )
        .then(res => {
          this.loading = false
          // if (res.exists) {
          //   this.total =
          //     res.importErrorBases.length || 0 + res.importBases.length || 0
          //   this.successList = res.importBases.length || 0
          //   this.failureList = res.importErrorBases.length || 0
          //   this.$message({
          //     message: `${res.importErrorBases.length || 0}条数据导入失败, ${res
          //       .importBases.length || 0}条数据成功导入`,
          //     type: 'warning'
          //   })
          //   this.importErrors = res.importErrors
          //   this.importErrorBases = res.importErrorBases
          //   this.visibleError = true
          // } else {
          //   this.$message({
          //     message: `${res.importBases.length || 0}条数据成功导入`,
          //     type: 'success'
          //   })
          // }
          this.$message({
            message: `数据导入成功`,
            type: 'success'
          })
          this.visible = false
          this.$emit('submitFun')
        })
        .catch(() => {
          this.loading = false
        })
    },
    // 下载错误数据
    importerror() {
      antiVpnService
        .vpnIntelligenceDownloadTemplate({
          importErrorBases: this.importErrorBases
        })
        .then(res => {
          let name =
            res.headers['content-disposition'].split('filename=') &&
            res.headers['content-disposition'].split('filename=').length > 1 &&
            res.headers['content-disposition'].split('filename=')[1]
          if (res) downloadExcel(res.data, name)
        })
    }
  }
}
</script>

<style lang="scss" scoped></style>
