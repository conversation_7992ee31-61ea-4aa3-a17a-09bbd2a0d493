package com.eversec.antivpn.intelligence.util;

import org.apache.commons.codec.binary.Base64;
import org.bouncycastle.jce.provider.BouncyCastleProvider;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.Array;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.Security;

public class ByteUtil {

	
	/**
	 * MD5的算法在RFC1321 中定义 在RFC 1321中，给出了Test suite用来检验你的实现是否正确： MD5 ("") =
	 * d41d8cd98f00b204e9800998ecf8427e MD5 ("a") = 0cc175b9c0f1b6a831c399e269772661
	 * MD5 ("abc") = 900150983cd24fb0d6963f7d28e17f72 MD5 ("message digest") =
	 * f96b697d7cb7938d525a2f31aaf161d0 MD5 ("abcdefghijklmnopqrstuvwxyz") =
	 * c3fcd3d76192e4007dfb496cca67e13b
	 * <AUTHOR>
	 * 传入参数：一个字节数组 传出参数：字节数组的 MD5 结果字符串
	 */
	public static String getMD5(byte[] source) {
		String s = null;
		// 用来将字节转换成 16 进制表示的字符
		char[] hexDigits = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd','e', 'f' };
		try {
			MessageDigest md = MessageDigest
					.getInstance("MD5");
			md.update(source);
			byte[] tmp = md.digest(); // MD5 的计算结果是一个 128 位的长整数，
			// 用字节表示就是 16 个字节
			char[] str = new char[16 * 2]; // 每个字节用 16 进制表示的话，使用两个字符，
			// 所以表示成 16 进制需要 32 个字符
			int k = 0; // 表示转换结果中对应的字符位置
			for (int i = 0; i < 16; i++) { // 从第一个字节开始，对 MD5 的每一个字节
				// 转换成 16 进制字符的转换
				byte byte0 = tmp[i]; // 取第 i 个字节
				str[k++] = hexDigits[byte0 >>> 4 & 0xf]; // 取字节中高 4 位的数字转换,
				// >>> 为逻辑右移，将符号位一起右移
				str[k++] = hexDigits[byte0 & 0xf]; // 取字节中低 4 位的数字转换
			}
			s = new String(str); // 换后的结果转换为字符串

		} catch (Exception e) {
			e.printStackTrace();
		}
		return s;
	}
	
	/**
	 * SHA-1加密 
	 */
	public static String shaEncrypt(byte[] bt) {
		MessageDigest md = null;
		String strDes = null;
		
//		byte[] bt = strSrc.getBytes();
		try {
			md = MessageDigest.getInstance("SHA-1");
			md.update(bt);
            strDes = bytes2Hex(md.digest());  //to HexString
		} catch (NoSuchAlgorithmException e) {
			return null;
		}
        return strDes;
	}
	
	public static String bytes2Hex(byte[] bts) {
		String des="";
		String tmp=null;
		for (int i=0; i<bts.length; i++) {
			tmp=(Integer.toHexString(bts[i] & 0xFF));
			if (tmp.length() == 1) {
				des += "0";
			}
			des += tmp;
        }
        return des;
	}
	
	/**
	 * 将字符串base64后转字符串
	 * @param str
	 * @return
	 */
	public static String getBaseByString(String str) {
		String result = "";
		byte[] bArray = getByte(str, "UTF-8");
		bArray = Base64.encodeBase64(bArray);
		try {
			result = new String(bArray, "UTF-8");
		} catch (UnsupportedEncodingException e) {
			e.printStackTrace();
		}
		return result;
	}
	
	public static byte[] getByte(String str, String charSet) {
		byte[] bArray = null;
		try {
			bArray = str.getBytes(charSet);
		} catch (UnsupportedEncodingException e) {
			e.printStackTrace();
		}
		return bArray;
	}
	
	/**
	 * 将字符串转为字符数组并解base64
	 * @param str
	 * @return
	 */
	public static byte[] getByteByBaseString(String str) {
		byte[] bArray = null;
		try {
			bArray = str.getBytes("UTF-8");
			if (Base64.isBase64(bArray)) {
				bArray = Base64.decodeBase64(bArray);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		
		return bArray;
	}
	
	/**
     * 对AES算法加密的数据解密
     * @param cipherText  要解密的字节数组
     * @param aeskey  密钥
     * @param aesOffsets
     * @return 解密后的字节数组
     */
	public static byte[] decrypt(byte[] cipherText, String aeskey, String aesOffsets) throws Exception{
		if(aeskey.length() > 16){
			byte[] aesBytes = parseHexStr2Byte(aeskey);
			aeskey = new String(aesBytes, "UTF-8");
			System.out.println("aeskey转换后的偏移量：" + aeskey);
		}
		Security.addProvider(new BouncyCastleProvider());
		SecretKeySpec skeySpec = new SecretKeySpec(aeskey.getBytes("UTF-8"), "AES");
		Cipher cipher = null;
		if (aesOffsets != null && !(aesOffsets.trim()).equals("")) {
			cipher = Cipher.getInstance("AES/CBC/PKCS7Padding");
			if(aesOffsets.length() > 16){
				byte[] bytes = parseHexStr2Byte(aesOffsets);
				aesOffsets = new String(bytes, "UTF-8");
				System.out.println("转换后的偏移量：" + aesOffsets);
			}
			IvParameterSpec iv = new IvParameterSpec(aesOffsets.getBytes("UTF-8"));
			cipher.init(Cipher.DECRYPT_MODE, skeySpec, iv);
		} else {
			cipher = Cipher.getInstance("AES/CBC/PKCS7Padding");
			cipher.init(Cipher.DECRYPT_MODE, skeySpec);
		}
		byte[] plainText = null;
		if(cipher != null){
			plainText = cipher.doFinal(cipherText);
		}
		return plainText;
	}
	
	/**
	 * 加密
	 * @param plainText
	 * @param aeskey
	 * @param aesOffsets
	 * @return
	 */
	public static byte[] aes(byte[] plainText, String aeskey, String aesOffsets) {
		try{
			if(aeskey.length() > 16){
				byte[] aesBytes = parseHexStr2Byte(aeskey);
				aeskey = new String(aesBytes, "UTF-8");
				System.out.println("aeskey转换后的偏移量：" + aeskey);
			}
			Security.addProvider(new BouncyCastleProvider());
	        SecretKeySpec skeySpec = new SecretKeySpec(aeskey.getBytes("UTF-8"), "AES");
	        Cipher cipher = null;
	        if(aesOffsets != null && !(aesOffsets.trim()).equals("")){
	        	cipher = Cipher.getInstance("AES/CBC/PKCS7Padding","BC");
	        	if(aesOffsets.length() > 16){
	        		byte[] bytes = parseHexStr2Byte(aesOffsets);
	        		aesOffsets = new String(bytes, "UTF-8");
	        		System.out.println("转换后的偏移量：" + aesOffsets);
	        	}
	        	IvParameterSpec iv = new IvParameterSpec(aesOffsets.getBytes("UTF-8"));
		        cipher.init(Cipher.ENCRYPT_MODE, skeySpec, iv);
	        }else{
	        	cipher = Cipher.getInstance("AES/CBC/PKCS7Padding","BC");
	        	cipher.init(Cipher.ENCRYPT_MODE, skeySpec);
	        }
	        
	        byte[] decryptText = null;
	        if(cipher != null){
	        	decryptText = cipher.doFinal(plainText);
	        }
	        return decryptText;
		} catch(Exception e) {
			e.printStackTrace();
			return null;
		}
	}
	
	/**
	 * 将16进制转换为二进制
	 * @param hexStr
	 * @return
	 */
	public static byte[] parseHexStr2Byte(String hexStr) {
		if (hexStr.length() < 1) {
			return null;
		}
    	byte[] result = new byte[hexStr.length()/2];
    	for (int i = 0;i< hexStr.length()/2; i++) {
    		int high = Integer.parseInt(hexStr.substring(i*2, i*2+1), 16);
    		int low = Integer.parseInt(hexStr.substring(i*2+1, i*2+2), 16);
    		result[i] = (byte) (high * 16 + low);
    	}
    	return result;
	}

	/**
	 * 将字符数组base64后转字符串
	 * 
	 * @param bArray
	 *            字符数组
	 * @return 字符转
	 */
	public static String getBaseByByte(byte[] bArray) {
		String str = null;
		bArray = Base64.encodeBase64(bArray);
		try {
			if(bArray==null){
				System.out.println("getBaseByByte-bArray为空！");
			}
			str = new String(bArray,"UTF-8");
		} catch (UnsupportedEncodingException e) {
			e.printStackTrace();
		}
		return str;
	}
	
	public static byte[] bytesAddStr(byte[] bytes, String str, String charset){
//		String targetStr = "";
		byte[] targetBytes = null;
		
		if(str != null && !str.equals("")){
			
			byte[] strBytes = null;
			try {
				strBytes = str.getBytes(charset);
			} catch (UnsupportedEncodingException e) {
				e.printStackTrace();
			}
			
			Object targetObj = bytesAdd(bytes, strBytes);
			if(targetObj != null){
				targetBytes = (byte[])targetObj;
			}
		}else{
			targetBytes = bytes;
		}
		
		return targetBytes;
	}
	
	/**
	 * 数组相加
	 * 例如：
	 * byte1: [49, 50, 51]
	 * byte2: [52, 53, 54]
	 * newByte: [49, 50, 51, 52, 53, 54]
	 * @param arrayObj1
	 * @param arrayObj2
	 * @return
	 */
	@SuppressWarnings("rawtypes")
	public static Object bytesAdd(Object arrayObj1, Object arrayObj2){
		
		if(arrayObj1 != null && arrayObj2 != null){
		
			Class curClazz1 = arrayObj1.getClass();
			Class curClazz2 = arrayObj2.getClass();
			
			if(!curClazz1.isArray() || !curClazz2.isArray()){
				return null;
			}else{
				Class arrayClazz = curClazz1.getComponentType();//返回表示数组组件类型的 Class
				int length1 = Array.getLength(arrayObj1);//返回当前数组的长度
				int length2 = Array.getLength(arrayObj2);//返回当前数组的长度
				int newLength = length1 + length2;
				Object newArrayObj = Array.newInstance(arrayClazz, newLength);//多态,创建新数组
				
				System.arraycopy(arrayObj1, 0, newArrayObj, 0, length1);//赋值
				System.arraycopy(arrayObj2, 0, newArrayObj, length1, length2);//赋值
				
				return newArrayObj;
			}
		}
		
		return null;
	}
	
	/**
	 * 获得指定文件的byte数组
	 */
	public static byte[] getBytes(String filePath){
		byte[] buffer = null;
		try {
			File file = new File(filePath);
			if(file.exists()){
				FileInputStream fis = new FileInputStream(file);
				ByteArrayOutputStream bos = new ByteArrayOutputStream(1000);
				byte[] b = new byte[1000];
				int n;
				while ((n = fis.read(b)) != -1) {
					bos.write(b, 0, n);
				}
				fis.close();
				bos.close();
				buffer = bos.toByteArray();
			}
		} catch (FileNotFoundException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		}
		return buffer;
	}

	/**
	 * SHA-1加密
	 */
	public static String sheEncrypt(byte[] bt) {
		MessageDigest md = null;
		String strDes = null;

//		byte[] bt = strSrc.getBytes();
		try {
			md = MessageDigest.getInstance("SHA-1");
			md.update(bt);
			strDes = bytes2Hex(md.digest());  //to HexString
		} catch (NoSuchAlgorithmException e) {
			return null;
		}
		return strDes;
	}
}
