package com.eversec.antivpn.log.entity;


import com.eversec.antivpn.log.entity.po.E1CommLogPO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-12
 */
@Getter
@Setter
@Schema(name = "E1CommLog", description = "")
public class E1CommLog extends E1CommLogPO implements java.io.Serializable {

    private static final long serialVersionUID = 1L;

    /*
     * 扩展字段，数据库中不存在的字段，数据库中不存在的字段需加 @TableField(exist = false)
     * 如字典翻译、子对象、子对象列表等
     */
//    @com.baomidou.mybatisplus.annotation.TableField(exist = false)
//    private String xxx;

    @com.baomidou.mybatisplus.annotation.TableField(exist = false)
    private String vpnSoftwareCode;

    @com.baomidou.mybatisplus.annotation.TableField(exist = false)
    private Long num;




}