package com.eversec.antivpn.util;


import cn.hutool.core.io.FileUtil;
import cn.hutool.extra.ssh.Sftp;
import org.testng.annotations.Test;

import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;

public class FtpUtilTest {

    //String ftpAddress = "ftp://cloud.beijing.everdevcloud.com:32500/";
    static String ftpAddress = "ftp://127.0.0.1:22/";
    static String ftpUsername = "zs";

    static String ftpPassword = "123456";


    public static void main(String[] args) {
//        String ftpPath = "cross_info_home";
//        Path downloadToDir = Paths.get("D:\\\\ftp-test");
        try (Sftp ftp = FtpUtil.buildSftpClient(ftpAddress, ftpUsername, ftpPassword)) {
//            ftp.put("D://harbor-anti-vpn-anti-vpn-front-10.0.0-beta.x86.tar", "upload/harbor-anti-vpn-anti-vpn-front-10.0.0-beta.x86.tar");
            ftp.put("C://Users//101202105004//temp//555.txt", "/upload/554.txt");

            List<String> crossInfoHome = ftp.ls("upload");
            System.out.println(crossInfoHome);
            //            ftp.recursiveDownloadFolder(ftpPath, downloadToDir.toFile());
        }

    }


    public void getTest() {
        Path downloadToDir = Paths.get("D:\\\\ftp-test");
        FileUtil.mkdir(downloadToDir);

        try (Sftp ftp = FtpUtil.buildSftpClient(ftpAddress, ftpUsername, ftpPassword)) {
            List<String> crossInfoHome = ftp.ls("upload");
            System.out.println(crossInfoHome);
            ftp.recursiveDownloadFolder("upload", downloadToDir.toFile());
        }

    }
}