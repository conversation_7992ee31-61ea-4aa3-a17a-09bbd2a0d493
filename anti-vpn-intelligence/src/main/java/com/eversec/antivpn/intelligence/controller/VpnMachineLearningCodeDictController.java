package com.eversec.antivpn.intelligence.controller;

import cn.hutool.core.io.FileUtil;
import com.eversec.antivpn.intelligence.api.VpnMachineLearningCodeDictApi;
import com.eversec.antivpn.intelligence.api.dto.VpnIntelligenceDTO;
import com.eversec.antivpn.intelligence.api.dto.VpnMachineLearningCodeDictDTO;
import com.eversec.antivpn.intelligence.dto.VpnMachineLearningCodeDictQueryConditionDTO;
import com.eversec.antivpn.intelligence.entity.VpnIntelligence;
import com.eversec.antivpn.intelligence.entity.VpnMachineLearningCodeDict;
import com.eversec.antivpn.intelligence.factory.VpnMachineLearningCodeDictFactory;
import com.eversec.antivpn.intelligence.service.IVpnMachineLearningCodeDictService;

import com.eversec.stark.generic.common.types.ContentDispositionTypeEnum;
import com.eversec.stark.generic.common.types.auth.Login;
import com.eversec.stark.generic.common.util.DownloadUtil;
import com.eversec.stark.generic.common.util.PageBuilder;
import io.swagger.v3.oas.annotations.Operation;
import lombok.SneakyThrows;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import lombok.AllArgsConstructor;

import java.io.File;
import java.util.List;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-21
 */
@RestController
@RequestMapping(VpnMachineLearningCodeDictApi.PATH)
@AllArgsConstructor
@Slf4j
public class VpnMachineLearningCodeDictController implements VpnMachineLearningCodeDictApi {

    private final IVpnMachineLearningCodeDictService service;

    private final VpnMachineLearningCodeDictFactory vpnMachineLearningCodeDictFactory;

    private final HttpServletResponse response;

    private final ResourceLoader resourceLoader;

    @Operation(summary = "导入情报机器学习样本")
    @PostMapping(value = "/import/{comCode}/{provinceId}/{systemCode}/{networkBusinessId}", consumes = "multipart/form-data")
    public void importVpnMachineLearningCodeDict(@PathVariable("comCode") String comCode, @PathVariable("provinceId")  Long provinceId, @PathVariable("systemCode")  String systemCode,
                                                 @PathVariable("networkBusinessId") Integer networkBusinessId,  MultipartFile file) {
        service.importVpnMachineLearningCodeDict(comCode, provinceId, systemCode, networkBusinessId, file);

    }

    @SneakyThrows
    @Operation(summary = "下载模型文件")
    @GetMapping("/downloadModuleFile/{fileName}")
    @Login(false)
    public void downloadModuleFile(@PathVariable(value = "fileName") String fileName) {
        File moduleFile = service.getModuleFile(fileName);
        DownloadUtil.download(response, FileUtil.getInputStream(moduleFile), ContentDispositionTypeEnum.attachment,
                fileName + ".zip", moduleFile.length());
    }


    @SneakyThrows
    @Operation(summary = "下载导入机器学习样本模板")
    @GetMapping("/downloadTemplate")
    @Login(false)
    public void downloadTemplate() {
        Resource resource = resourceLoader.getResource("classpath:template/import_machine_learning.zip");
        DownloadUtil.download(response, resource.getInputStream(), ContentDispositionTypeEnum.attachment,
                "机器学习样本导入模板.zip", resource.contentLength());
    }

    @Operation(summary = "批量上报，逗号分割。可指定文件类型")
    @GetMapping({"/report/{ids}", "/report/{fileType}/{ids}"})
    public void report(@PathVariable(value = "ids") List<Long> ids, @PathVariable(required = false, value = "fileType")Integer fileType) {
        service.report(ids, fileType);
    }

    /**
     * @param paramDto 查询参数
     * @param pageInfo 分页参数，用到其中的size和current
     */
    @Operation(summary = "查询-分页")
    @GetMapping("/page")
    public Page<VpnMachineLearningCodeDictDTO> page(VpnMachineLearningCodeDictQueryConditionDTO paramDto, Page<VpnMachineLearningCodeDict> pageInfo) {
        Page<VpnMachineLearningCodeDict> page = service.page(paramDto, pageInfo);
        Page<VpnMachineLearningCodeDictDTO> vpnIntelligenceDTOPage = PageBuilder.convertMybatisPlusPage(page, vpnMachineLearningCodeDictFactory::entityToDto);
        return vpnIntelligenceDTOPage;
    }


    @Override
    public void deleteByIds(List<Long> ids) {
        service.removeByIds(ids);
    }

}
