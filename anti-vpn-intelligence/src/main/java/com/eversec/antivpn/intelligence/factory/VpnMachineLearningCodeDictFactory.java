package com.eversec.antivpn.intelligence.factory;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.eversec.antivpn.intelligence.api.dto.VpnIntelligenceDTO;
import com.eversec.antivpn.intelligence.api.dto.VpnMachineLearningCodeDictDTO;
import com.eversec.antivpn.intelligence.dto.VpnIntelligenceImportDTO;
import com.eversec.antivpn.intelligence.dto.VpnMachineLearningCodeDictImportDTO;
import com.eversec.antivpn.intelligence.entity.VpnIntelligence;
import com.eversec.antivpn.intelligence.entity.VpnMachineLearningCodeDict;
import org.springframework.stereotype.Component;

import java.util.ArrayList;

/**
 * VpnMachineLearningCodeDict 工厂，创建、修改VpnMachineLearningCodeDict对象都在这里
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023-09-21 14:42
 */
@Component
public class VpnMachineLearningCodeDictFactory {

	/**
	 * entity转换为dto，用于页面查询
	 * @param entity
	 * @return
	 */
	public VpnMachineLearningCodeDictDTO entityToDto(VpnMachineLearningCodeDict entity) {
		VpnMachineLearningCodeDictDTO dto = new VpnMachineLearningCodeDictDTO();
		BeanUtil.copyProperties(entity, dto);


		return dto;
	}

	public VpnMachineLearningCodeDict importDtoToEntity(VpnMachineLearningCodeDictImportDTO dto, String comCode, Long provinceId, String systemCode,
														Integer networkBusinessId, String contentFileNameMd5, String contentFilesuffix, Long modelFileId) {
		VpnMachineLearningCodeDict entity = new VpnMachineLearningCodeDict();
		entity.setTimeStamp(dto.getTimeStamp());
		entity.setProtocolType(dto.getProtocolType());
		entity.setModelChName(dto.getModelChName());
		entity.setModelCode(dto.getModelCode());
		entity.setModelName(dto.getModelName());
		entity.setModelVersion(dto.getModelVersion());
		entity.setVpnName(dto.getVpnName());
		entity.setVersion("1.0");
		entity.setComCode(comCode);
		entity.setProvinceId(provinceId);
		entity.setSystemCode(systemCode);
		entity.setNetworkBusinessId(networkBusinessId);
		entity.setFileType(dto.getFileType());
		entity.setFileName(null);
		entity.setContentFileName(contentFileNameMd5);
		entity.setContentFilesuffix(contentFilesuffix);
		entity.setModelFileId(modelFileId);
		return entity;

	}

}
