package com.eversec.antivpn.log.api.dto;

import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * DTO
 * </p>
 * <p>
 * api接口数据传输对象（用于save、update接收参数，list、page、find返回对象等）
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-09
 */
@Getter
@Setter
@Schema(name = "E4DnsLogDTO", description = "")
public class E4DnsLogDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String version;

    private String comCode;

    private Integer networkBusinessId;

    private Long logId;

    private Long houseId;

    private Long logProvinceId;

    private Long logCityId;

    private Long logCountyId;

    private Integer ipType;

    private String srcIp;

    private Integer srcPort;

    private String dnsServerIp;

    private Integer dnsPort;

    private Integer transactionId;

    private Integer aa;

    private Integer truncation;

    private String domain;

    private String type;

    private Integer replyCode;

    private Integer answersCount;

    private String responseValue;

    private Long trafficType;

    private LocalDateTime accessTime;

    private LocalDateTime timestamp;

    private Long day;

    private Integer hour;
}