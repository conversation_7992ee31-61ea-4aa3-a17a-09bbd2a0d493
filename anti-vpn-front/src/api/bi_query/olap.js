import { ChartDataBll } from './utils/chartbll';

export default {
  /**
   * 根据 chartId 获取配置 chartConf
   * @param {*} chartId 图表管理中图表的id，或仪表盘中图表的uuid
   * @param {*} dashId 仪表盘的id（可选）
   */
  getChartConfByChartId(chartId, dashId) {
    const bll = new ChartDataBll();
    return bll.getChartData(chartId, dashId);
  },

  /**
   * 根据 chartConf 获取数据，并可设置翻页参数
   * @param {*} chartConf BI中图表的配置
   * @param {*} extendParam 额外参数，如分页 { page: { pageSize: 10, pageIndex: 1 } }
   * @param {*} callback 成功回调
   * @param {*} error 失败回调
   * @param {*} transDic 是否翻译字典
   * @param {*} transKey 是否翻译数据的 key，从 uuid 翻译为 label
   */
  getDataByChartConf(chartConf, extendParam, callback, error, transDic = true, transKey = true) {
    const bll = new ChartDataBll();
    const isTable = chartConf.chartType == 'Table';

    const doCallback = (data) => {
      if (transKey) {
        if (isTable) {
          data.list = getTransKeyList(data.list, chartConf);
        } else {
          data = getTransKeyList(data, chartConf);
        }
      }
      callback(data);
    }

    bll.getDatasByChart(
      chartConf.chartType,
      chartConf.table,
      {
        ...chartConf.dataConfig,
        filters: chartConf.filters,
      },
      extendParam,
      (data, config) => {
        if (transDic) {
          bll.getDicDataBase(config, data, (_config, _data) => { doCallback(_data) }, isTable)
        } else {
          doCallback(data);
        }
      },
      error
    );
  },
};

function getTransKeyList(list, chartConf) {
  const dimensions = chartConf.dataConfig.dimensions;
  // 20211223 修复组合图 measures1 的 key 未转换问题
  let measures = chartConf.dataConfig.measures;
  const measures1 = chartConf.dataConfig.measures1;
  if (measures1) {
    measures = measures.concat(measures1);
  }

  let transObj = {};

  return list.map((item, index) => {
    let o = {};

    if (index == 0) {
      // 根据第一个 item 获得转换对象
      for (let key in item) {
        let isOrigin = false;
        let _key = key;
        if (key.includes('_origin_')) {
          isOrigin = true;
          _key = key.split('_origin_')[0];
        }
        let tmp;
        if (dimensions) {
          tmp = dimensions.find(d => d.value == _key);
        }
        if (!tmp && measures) {
          tmp = measures.find(m => m.value == _key);
        }
        let label = tmp && tmp.label || _key;
        if (isOrigin) label = label + '_origin_';
        o[label] = item[key];
        transObj[key] = label;
      }
    } else {
      // 根据已有的转换对象直接转换
      for (let key in item) {
        o[transObj[key]] = item[key];
      }
    }

    return o;
  });
}
