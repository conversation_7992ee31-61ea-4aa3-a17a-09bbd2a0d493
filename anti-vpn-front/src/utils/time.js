import moment from 'moment'
import cloneDeep from 'lodash/cloneDeep'

/**
 * 时间格式化
 * @param {*} date Date对象 或 时间戳
 * @param {*} fmt "yyyy-MM-dd hh:mm:ss"
 */
export function formatTime(date, fmt = 'yyyy-MM-dd hh:mm:ss') {
  if (!date) return date
  if (typeof date === 'string') {
    const timestamp = Date.parse(date)
    date = isNaN(timestamp) ? new Date() : new Date(timestamp)
  }
  if (!(date instanceof Date)) return date

  const o = {
    'M+': date.getMonth() + 1,
    'd+': date.getDate(),
    'h+': date.getHours(),
    'm+': date.getMinutes(),
    's+': date.getSeconds(),
    'q+': Math.floor((date.getMonth() + 3) / 3)
  }
  // 格式化年
  if (/(y+)/.test(fmt)) {
    fmt = fmt.replace(
      RegExp.$1,
      (date.getFullYear() + '').substr(4 - RegExp.$1.length)
    )
  }
  // 格式化毫秒
  if (/(S+)/.test(fmt)) {
    const milliseconds = date.getMilliseconds()
    fmt = fmt.replace(
      RegExp.$1,
      ('000' + milliseconds).substr(('' + milliseconds).length)
    )
  }
  // 格式化其它
  for (let k in o) {
    if (new RegExp('(' + k + ')').test(fmt)) {
      fmt = fmt.replace(
        RegExp.$1,
        RegExp.$1.length === 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length)
      )
    }
  }
  return fmt
}

/**
 * 通过最近时间和单位（最近一周，最近一月等），获取时间段（时间戳数组）
 * @param {*} timeValue 最近时间
 * @param {*} timeUnit  时间单位
 * @param {*} natureTime  自然时间
 */
export function getTimeRangeByUnit(timeValue, timeUnit, natureTime) {
  if (timeValue == undefined || !timeUnit || timeValue < 0) return []
  if (timeUnit == 'hours') timeUnit = 'hour' // 旧数据兼容

  let start
  let end

  if (natureTime) {
    if (timeValue == 0) {
      start = moment().startOf(timeUnit)
      end = moment().endOf(timeUnit)
    } else {
      start = moment()
        .startOf(timeUnit)
        .subtract(timeValue, timeUnit + 's')
      end = moment()
        .endOf(timeUnit)
        .subtract(1, timeUnit + 's')
    }
  } else {
    start = moment().subtract(timeValue, timeUnit + 's')
    end = moment()
  }

  return [start.valueOf(), end.valueOf()]
}

/**
 * 换算得到毫秒值
 * @param {*} timeValue
 * @param {*} timeUnit
 */
export function getMilliSecond(timeValue, timeUnit) {
  switch (timeUnit) {
    case 'second':
      return timeValue * 1000
    case 'minute':
      return timeValue * 1000 * 60
    case 'hour':
      return timeValue * 1000 * 3600
    case 'day':
      return timeValue * 1000 * 3600 * 24
    case 'week':
      return timeValue * 1000 * 3600 * 24 * 7
    case 'month':
      return timeValue * 1000 * 3600 * 24 * 30
    case 'year':
      return timeValue * 1000 * 3600 * 24 * 365
  }
}

/** 秒转成时分秒 */
export function formatSeconds(value) {
  if (value == null) return ''
  var theTime = parseInt(value) // 秒
  var theTime1 = 0 // 分
  var theTime2 = 0 // 小时
  if (theTime > 60) {
    theTime1 = parseInt(theTime / 60)
    theTime = parseInt(theTime % 60)
    if (theTime1 > 60) {
      theTime2 = parseInt(theTime1 / 60)
      theTime1 = parseInt(theTime1 % 60)
    }
  }

  var result = '' + parseInt(theTime) //秒

  result = (theTime < 10 ? '0' : '') + parseInt(theTime) + ' 秒' //秒

  if (theTime1 > 0) {
    result = (theTime1 < 10 ? '0' : '') + parseInt(theTime1) + ' 分 ' + result //分，不足两位数，首位补充0，
  }

  if (theTime2 > 0) {
    result = parseInt(theTime2) + ' 小时 ' + result //时
  }
  return result
}

// 获取当天0点的时间戳
export function getStartTime(unit = 'day') {
  return moment()
    .startOf(unit)
    .valueOf()
}
/**
 * 获取指定日期
 * @param {date} date 当前日期
 * @param {number} n 时间段 例：-5 ->当前日期5日前的日期， 2 -> 当前日期两天后的日期
 * @return {date} 返回指定月份的时间对象
 */
export const designatedDate = (days, day) => {
  //getTime()返回 1970 年 1 月 1 日至今的毫秒数。
  let gettimes = days.getTime() + 1000 * 60 * 60 * 24 * day
  //setTime()以毫秒设置 Date 对象。
  days.setTime(gettimes)
  let year = days.getFullYear()
  let month = days.getMonth() + 1
  if (month < 10) {
    month = '0' + month
  }
  let today = days.getDate()
  if (today < 10) {
    today = '0' + today
  }
  return year + '-' + month + '-' + today + ' 00:00:00'
}

/**
 * 获取上下月
 * @param {date} date 当前日期
 * @param {number} state 0上月,1下月,默认获取上个月
 * @return {date} 返回指定月份的时间戳对象
 */
export const lastMonth = (date, state = 0) => {
  let d = cloneDeep(date)
  const d1 = new Date(d.setMonth(state === 0 ? d.getMonth() - 1 : d.getMonth()))
  let t1 = new Date(d1.setDate(1)).setHours(0, 0, 0, 0)
  const d2 = new Date(t1)
  return {
    orgDate: d2,
    date: new Date(
      new Date(d2.setMonth(d2.getMonth() + 1)).setMilliseconds(
        state === 0 ? -1 : 1
      )
    )
  }
}

// 获取上个月
export const getLastMonth = () => {
  let now = new Date()
  let year = now.getFullYear() //getYear()+1900=getFullYear()
  let month = now.getMonth() + 1 //0-11表示1-12月
  let day = now.getDate()
  let dateObj = {}
  if (parseInt(month) < 10) {
    month = '0' + month
  }
  if (parseInt(day) < 10) {
    day = '0' + day
  }

  dateObj.now = year + '-' + month + '-' + day

  if (parseInt(month) === 1) {
    //如果是1月份，则取上一年的12月份
    dateObj.last = parseInt(year) - 1 + '-12-' + day
    return dateObj
  }

  let preSize = new Date(year, parseInt(month) - 1, 0).getDate() //上月总天数
  if (preSize < parseInt(day)) {
    //上月总天数<本月日期，比如3月的30日，在2月中没有30
    dateObj.last = year + '-' + month + '-01'
    return dateObj
  }

  if (parseInt(month) <= 10) {
    dateObj.last = year + '-0' + (parseInt(month) - 1) + '-' + day
    return dateObj
  } else {
    dateObj.last = year + '-' + (parseInt(month) - 1) + '-' + day
    return dateObj
  }
}

// 获取近三个月
export const getLast3Month = () => {
  let now = new Date()
  let year = now.getFullYear()
  let month = now.getMonth() + 1 //0-11表示1-12月
  let day = now.getDate()
  let dateObj = {}
  let last3MonthDay = null
  dateObj.now = year + '-' + month + '-' + day
  let nowMonthDay = new Date(year, month, 0).getDate() //当前月的总天数
  if (month - 3 <= 0) {
    //如果是1、2、3月，年数往前推一年
    last3MonthDay = new Date(year - 1, 12 - (3 - parseInt(month)), 0).getDate() //3个月前所在月的总天数
    if (last3MonthDay < day) {
      //3个月前所在月的总天数小于现在的天日期
      dateObj.last = year - 1 + '-' + (12 - (3 - month)) + '-' + last3MonthDay
    } else {
      dateObj.last = year - 1 + '-' + (12 - (3 - month)) + '-' + day
    }
  } else {
    last3MonthDay = new Date(year, parseInt(month) - 3, 0).getDate() //3个月前所在月的总天数
    if (last3MonthDay < day) {
      //3个月前所在月的总天数小于现在的天日期
      if (day < nowMonthDay) {
        //当前天日期小于当前月总天数,2月份比较特殊的月份
        dateObj.last =
          year + '-' + (month - 3) + '-' + (last3MonthDay - (nowMonthDay - day))
      } else {
        dateObj.last = year + '-' + (month - 3) + '-' + last3MonthDay
      }
    } else {
      dateObj.last = year + '-' + (month - 3) + '-' + day
    }
  }
  return dateObj
}
