package com.eversec.antivpn.log.util;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.eversec.antivpn.emnus.ScreenTypeEnum;
import com.eversec.antivpn.log.constants.CommonField;
import com.eversec.antivpn.log.entity.E1CommLogUserView;
import com.eversec.antivpn.log.entity.E2MachineLearningLogView;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;

import org.apache.commons.lang3.time.DateUtils;

/**
 * 计算工具类
 *
 * @Description
 * <AUTHOR>
 * @Date 2023/8/4 14:37
 **/

public class CalculateUtil {
    /**
     * 计算环比
     * 环比的计算公式=（本期数-上期数）/上期数×100%
     *
     * @param currentNum 本期
     * @param lastNum    上期
     * @return
     */
    public static Double myEcyclePercent(long currentNum, long lastNum) {
        if (lastNum == 0L) {
            return Double.valueOf(0);
        }
        if (ObjectUtil.isEmpty(currentNum)) {
            return Double.valueOf(0);
        }

        BigDecimal ecount = new BigDecimal(currentNum);
        BigDecimal totalCount = new BigDecimal(lastNum);
        BigDecimal divisor = ecount.subtract(totalCount);
        BigDecimal divide = divisor.divide(totalCount, 2, BigDecimal.ROUND_HALF_UP);
        double searchRate = divide.multiply(new BigDecimal(100)).doubleValue();

        return searchRate;
    }

    /**
     * 计算百分比
     *
     * @param count 数量
     * @param total 总数
     * @return
     */
    public static Double myPercent1(long count, long total) {
        if (total == 0L) {
            return Double.valueOf(0);
        }
        if (ObjectUtil.isEmpty(count)) {
            return Double.valueOf(0);
        }
        BigDecimal ecount = new BigDecimal(count);
        BigDecimal totalCount = new BigDecimal(total);
        BigDecimal divide = ecount.divide(totalCount, 2, BigDecimal.ROUND_HALF_UP);
        double searchRate = divide.multiply(new BigDecimal(100)).doubleValue();

        return searchRate;
    }

    public static Double cpuCyclePercent(long currentNum, long lastNum) {
        return myEcyclePercent(currentNum, lastNum);
    }


    public static Double cpuPercent(long num, long total) {
        return myPercent1(num, total);
    }
}
