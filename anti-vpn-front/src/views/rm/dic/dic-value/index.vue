<template>
  <div class="dic">
    <section class="section is-float-right">
      <el-button type="success" @click="$router.go(-1)">
        <span
          class="el-icon-back"
          style="font-weight:600;position:relative;left:-3px;"
        />
        返回数据字典
      </el-button>
      <el-button
        type="primary"
        icon="el-icon-refresh-left"
        @click="getDataList()"
      >
        刷新
      </el-button>
      <el-button
        type="primary"
        icon="el-icon-plus"
        @click="addOrUpdateHandle(null, 0)"
      >
        新增
      </el-button>
      <el-button
        type="danger"
        :disabled="!(multipleSelection.length > 0)"
        icon="el-icon-delete"
        @click="deleteDics(null)"
      >
        删除
      </el-button>
    </section>
    <el-table
      v-loading="dataListLoading"
      :data="dataList"
      border
      stripe
      style="width: 100%;"
      row-key="id"
      :tree-props="{ children: 'children' }"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="50" />
      <el-table-column
        type="index"
        :index="getIndex"
        header-align="left"
        align="left"
        width="80"
        label="序号"
      />
      <el-table-column
        prop="enumKey"
        header-align="left"
        align="left"
        label="枚举键"
      />
      <el-table-column
        prop="enumVal"
        header-align="left"
        align="left"
        label="枚举值"
      />
      <el-table-column
        prop="seq"
        header-align="left"
        align="left"
        label="枚举序列"
      />
      <el-table-column
        prop="valid"
        header-align="left"
        align="left"
        label="是否有效"
      >
        <template slot-scope="scope">
          {{ scope.row.valid ? '是' : '否' }}
        </template>
      </el-table-column>
      <el-table-column
        header-align="left"
        align="left"
        width="160"
        label="操作"
      >
        <template slot-scope="scope">
          <el-link
            type="success"
            :underline="false"
            @click="addOrUpdateHandle(scope.row, 1)"
          >
            修改
          </el-link>
          <el-link
            type="primary"
            :underline="false"
            @click="addOrUpdateHandle(scope.row, 2)"
          >
            增加子集
          </el-link>
          <el-link
            type="danger"
            :underline="false"
            @click="deleteDics(scope.row.id)"
          >
            删除
          </el-link>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      :disabled="dataListLoading"
      class="is-pt-10 is-float-right"
      :key="total"
      :current-page="paging"
      :page-sizes="[10, 20, 50, 100, 500, 1000]"
      :page-size="limit"
      :total="total"
      style="text-align:right;"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
    />

    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update ref="addOrUpdate" @onSubmit="getDataList" />
  </div>
</template>
<script>
import api_setManager from '@/api/rm/dic'
import AddOrUpdate from './add-or-update-value'
export default {
  components: {
    AddOrUpdate
  },
  data() {
    return {
      dataForm: {},
      dataList: [],
      paging: 1,
      limit: 100,
      total: 0,
      dataListLoading: false,
      multipleSelection: []
    }
  },
  watch: {},
  mounted() {
    this.getDataList()
  },
  methods: {
    getIndex(index) {
      return index + 1 + this.limit * (this.paging - 1)
    },
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      api_setManager
        .getListValue({
          typeKey: this.$route.query.typeKey,
          pageNo: this.paging, //当前页
          pageSize: this.limit //每页展示数量
        })
        .then(res => {
          this.dataList = res.items
          this.total = parseInt(res.total)
          this.dataListLoading = false
        })
        .catch(() => {
          this.dataListLoading = false
        })
    },
    // 选中框
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    // 每页数
    sizeChangeHandle(val) {
      this.limit = val
      this.paging = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.paging = val
      this.getDataList()
    },
    // 新增 / 修改
    addOrUpdateHandle(row, state) {
      this.$refs.addOrUpdate.init(
        {
          enumName: this.$route.query.enumName,
          enumId: this.$route.query.enumId,
          valid: true,
          ...row
        },
        state // 0新增第一级， 1修改第一级， 2新增子集
      )
    },
    // 批量删除字典
    deleteDics(id) {
      let ids = ''
      if (id) ids = id
      else {
        let idsArr = []
        this.multipleSelection.forEach(dic => {
          idsArr.push(dic.id)
        })
        ids = idsArr.join(',')
      }
      this.deleteHandle(ids)
    },
    // 删除
    deleteHandle(id) {
      this.$confirm(`确定进行[删除]操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          api_setManager.deleteDicVal(id).then(() => {
            this.getDataList()
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500
            })
          })
        })
        .catch(() => {})
    }
  }
}
</script>
<style lang="scss" scoped>
.dic {
  .dicItem {
    .el-input {
      position: relative;
      left: 25px;
      width: 200px;
    }

    .treeList {
      float: right;
    }
  }
}
</style>
