<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eversec.antivpn.log.mapper.E1CommLogUserViewMapper">

    <resultMap id="baseResult" type="com.eversec.antivpn.log.entity.E1CommLogUserView">
        <result column="day" jdbcType="BIGINT" property="day"/>
        <result column="hour" jdbcType="INTEGER" property="hour"/>
        <result column="month" jdbcType="BIGINT" property="month"/>
        <result column="province_id" jdbcType="VARCHAR" property="provinceId"/>
        <result column="network_business_id" jdbcType="INTEGER" property="networkBusinessId"/>
        <result column="src_ip" jdbcType="VARCHAR" property="srcIp"/>
        <result column="src_province_id" jdbcType="VARCHAR" property="srcProvinceId"/>
        <result column="dest_ip" jdbcType="VARCHAR" property="destIp"/>
        <result column="dest_country" jdbcType="VARCHAR" property="destCountry"/>
        <result column="traffic_type" jdbcType="VARCHAR" property="trafficType"/>
        <result column="vpn_id" jdbcType="BIGINT" property="vpnId"/>
        <result column="msisdn_md5" jdbcType="VARCHAR" property="msisdnMd5"/>
        <result column="mask_msisdn" jdbcType="VARCHAR" property="maskMsisdn"/>
        <result column="user" jdbcType="VARCHAR" property="user"/>
        <result column="vpn_airport_code" jdbcType="VARCHAR" property="vpnAirportCode"/>
        <result column="vpn_airport_code" jdbcType="VARCHAR" property="vpnRegistered"/>
        <result column="vpn_software_code" jdbcType="VARCHAR" property="vpnSoftwareCode"/>
        <result column="log_count" jdbcType="BIGINT" property="logCount"/>
    </resultMap>

    <resultMap id="VpnAggVO" type="com.eversec.antivpn.log.mapper.vo.VpnResourceNodeAggVO">
        <result column="vpn_airport_code" jdbcType="INTEGER" property="vpnAirportCode"/>
        <result column="vpn_id" jdbcType="INTEGER" property="vpnId"/>
        <result column="num" jdbcType="BIGINT" property="num"/>
    </resultMap>
    <resultMap id="TypeCount" type="com.eversec.antivpn.log.mapper.vo.TypeCountVO">
        <result column="num" jdbcType="BIGINT" property="num"/>
        <result column="num1" jdbcType="BIGINT" property="num1"/>
        <result column="key_type" jdbcType="VARCHAR" property="key"/>
    </resultMap>
    <resultMap id="UserViewVO" type="com.eversec.antivpn.log.mapper.vo.UserViewVO">
        <result column="user" jdbcType="VARCHAR" property="user"/>
        <result column="src_ip" jdbcType="VARCHAR" property="srcIp"/>
    </resultMap>
    <resultMap id="VpnHistogramAggVO" type="com.eversec.antivpn.log.mapper.vo.VpnHistogramAggVO">
        <result column="key_type" jdbcType="INTEGER" property="key"/>
        <result column="vpn_service_num" jdbcType="INTEGER" property="vpnServiceNum"/>
        <result column="resource_node_num" jdbcType="BIGINT" property="resourceNodeNum"/>
    </resultMap>
    <sql id="keyBaseSql">
        <if test="screenTypeEnumCode=='day'"> hour as key_type </if>
        <if test="screenTypeEnumCode=='week'"> day  as key_type </if>
        <if test="screenTypeEnumCode=='month'"> anyMerge(week) as key_type </if>
        <if test="screenTypeEnumCode=='sofar'"> anyMerge(month)  as key_type </if>
    </sql>
    <sql id="groupByMapLocation">
        group by
        <if test="mapLocationSearchDimension=='province'"> province_id </if>
        <if test="mapLocationSearchDimension=='city'"> log_city_id </if>
    </sql>
    <sql id="keyMapLocation">
        <if test="mapLocationSearchDimension=='province'"> province_id as key_type </if>
        <if test="mapLocationSearchDimension=='city'"> log_city_id  as key_type </if>
    </sql>
    <sql id="keyMapLocationNum">
        <if test="mapLocationSearchDimension=='province'"> count(distinc(province_id)) as num </if>
        <if test="mapLocationSearchDimension=='city'"> count(distinc(log_city_id))  as num </if>
    </sql>
    <sql  id="whereBase">
        where 1=1
            <if test="todayTimePartition!=null">
                AND day = #{todayTimePartition}
            </if>
            <if test="startTimePartition!=null">
                AND day >= #{startTimePartition}
            </if>
            <if test="endTimePartition!=null">
                AND day &lt;= #{endTimePartition}
            </if>
            <if test="nowTimePartition!=null">
                AND day &lt;= #{nowTimePartition}
            </if>
    </sql>
    <sql id="viewBaseSql">
        (
            select
                day,
                hour,
                anyMerge(month) AS month,
                anyMerge(week) AS week,
                province_id,
                network_business_id,
                src_ip,
                src_province_id,
                dest_ip,
                dest_country,
                traffic_type,
                vpn_id,
                user,
                msisdn_md5,
                anyMerge(mask_msisdn) AS mask_msisdn,
                vpn_airport_code,
                vpn_software_code,
                countMerge(log_count) AS log_count,
                system_code,
                log_city_id
            from e1_comm_log_user_view
            <where>
                <if test="todayTimePartition!=null">
                    AND day = #{todayTimePartition}
                </if>
                <if test="startTimePartition!=null">
                    AND day >= #{startTimePartition}
                </if>
                <if test="endTimePartition!=null">
                    AND day &lt;= #{endTimePartition}
                </if>
            </where>
            group by
                day,
                hour,
                province_id,
                network_business_id,
                src_ip,
                src_province_id,
                dest_ip,
                dest_country,
                traffic_type,
                vpn_id,
                user,
                msisdn_md5,
                vpn_airport_code,
                vpn_software_code,
                system_code,
                log_city_id
        ) AS base_view
    </sql>
    <sql id="viewBaseSoftWareSql">
        (
        select
        user,
        vpn_software_code,
        countMerge(log_count) AS log_count
        from e1_comm_log_user_view
        <where>
            <if test="todayTimePartition!=null">
                AND day = #{todayTimePartition}
            </if>
            <if test="startTimePartition!=null">
                AND day >= #{startTimePartition}
            </if>
            <if test="endTimePartition!=null">
                AND day &lt;= #{endTimePartition}
            </if>
        </where>
        group by
        user,
        vpn_software_code

        ) AS base_view
    </sql>

    <sql id="viewBaseSoftWareAndDaySql">
        (
        select
        user,
        vpn_software_code,
        countMerge(log_count) AS log_count
        from e1_comm_log_user_view
        <where>
            <if test="todayTimePartition!=null">
                AND day = #{todayTimePartition}
            </if>
            <if test="startTimePartition!=null">
                AND day >= #{startTimePartition}
            </if>
            <if test="endTimePartition!=null">
                AND day &lt;= #{endTimePartition}
            </if>
        </where>
        group by
        day,
        user,
        vpn_software_code

        ) AS base_view
    </sql>
    <sql id="groupByTimeBaseSql">
        <if test="screenTypeEnumCode=='day'"> group by hour </if>
        <if test="screenTypeEnumCode=='week'"> group by day </if>
    </sql>
    <sql id="sumLogCountSql">
        sum(log_count) as num
    </sql>

    <select id="countNum" resultType="java.lang.Long" parameterType="com.eversec.antivpn.log.mapper.vo.BaseSearchVO">
        select
        countMerge(log_count) as num
        from e1_comm_log_user_view
        <include refid="whereBase"></include>
    </select>

    <select id="selectListByPartition" resultMap="baseResult" parameterType="com.eversec.antivpn.log.mapper.vo.BaseSearchVO">
        select
        *
        from
        <include refid="viewBaseSql"></include>
    </select>
    <select id="getVpnAirportCodeCount" resultType="java.lang.Long" parameterType="com.eversec.antivpn.log.mapper.vo.BaseSearchVO">
        SELECT
        count(distinct(vpn_airport_code)) num
        FROM  e1_comm_log_user_view
        <include refid="whereBase"></include>
    </select>

    <select id="getDistinctVpnIdNum" resultType="java.lang.Long" parameterType="com.eversec.antivpn.log.mapper.vo.BaseSearchVO">
        SELECT
        count(distinct (vpn_id))
        FROM  e1_comm_log_user_view
        <include refid="whereBase"></include>
    </select>

    <select id="getResourceNodeUserNum" resultMap="VpnAggVO" parameterType="com.eversec.antivpn.log.mapper.vo.BaseSearchVO">
        SELECT
        vpn_airport_code,
        vpn_id,
        count(distinct (user)) num
        FROM  e1_comm_log_user_view
        <include refid="whereBase"></include>
        group by vpn_airport_code,vpn_id
        order by num desc limit 20
    </select>

    <select id="getVpnServiceAndResourceNodeDateHistogram" resultMap="VpnHistogramAggVO" parameterType="com.eversec.antivpn.log.mapper.vo.BaseSearchVO">
        SELECT
        <include refid="keyBaseSql"></include>,
        count(distinct (vpn_airport_code)) as vpn_service_num,
        count(distinct (vpn_id)) as resource_node_num
        FROM  e1_comm_log_user_view
        <include refid="groupByTimeBaseSql"></include>
        order by key_type
    </select>


    <select id="selectSoftwareCodeByPartition" resultType="java.lang.String" parameterType="com.eversec.antivpn.log.mapper.vo.BaseSearchVO">
        select
        DISTINCT vpn_software_code
        from e1_comm_log_user_view
        <include refid="whereBase"></include>
    </select>

    <select id="getLogNumByUserTop10" resultMap="baseResult" parameterType="com.eversec.antivpn.log.mapper.vo.BaseSearchVO">
        select
        count(user) num,REPLACE(arrayJoin(JSONExtractArrayRaw(vpn_software_code)) , '"', '') as vpn_software_code
        from
        <include refid="viewBaseSoftWareAndDaySql"></include>
        group by vpn_software_code
        order by num  desc
        limit 10
    </select>
    <select id="getLogNumBySofaWareAndDayTop10" resultMap="baseResult" parameterType="com.eversec.antivpn.log.mapper.vo.BaseSearchVO">
        select
        sum( log_count) log_count,REPLACE(arrayJoin(JSONExtractArrayRaw(vpn_software_code)) , '"', '') as vpn_software_code,
        count(user) num
        from
        <include refid="viewBaseSoftWareAndDaySql"></include>
        group by  vpn_software_code
        order by num
        limit 10
    </select>

    <select id="getLogNumBySofaWareTop10" resultMap="baseResult" parameterType="com.eversec.antivpn.log.mapper.vo.BaseSearchVO">
        select
        sum( log_count) num,REPLACE(arrayJoin(JSONExtractArrayRaw(vpn_software_code)) , '"', '') as vpn_software_code
        from
        <include refid="viewBaseSoftWareAndDaySql"></include>
         group by  vpn_software_code
         order by num  desc
        limit 10
    </select>


    <select id="getVpnServicePersonNumTop10" resultMap="TypeCount" parameterType="com.eversec.antivpn.log.mapper.vo.BaseSearchVO">
        SELECT
        vpn_airport_code as key_type,
        count(distinct (user)) as num
        FROM  e1_comm_log_user_view
        <include refid="whereBase"></include>
        and vpn_airport_code is not null and vpn_software_code != ''
        group by vpn_airport_code
        order by num desc
        limit 10
    </select>

    <select id="getUserCount" resultType="java.lang.Long" parameterType="com.eversec.antivpn.log.mapper.vo.BaseSearchVO">
        select sum(num) as total_num from
        (SELECT
        vpn_airport_code as key_type,
        count(distinct (user)) as num
        FROM  <include refid="viewBaseSql"></include>
        group by vpn_airport_code
        )
    </select>

    <select id="getVpnServicePageInfo" resultMap="TypeCount" >
        SELECT
        vpn_airport_code as key_type,
        count(distinct (user)) as num,
        sum( log_count) as num1
        FROM
        (
        select
        day,
        hour,
        anyMerge(month) AS month,
        anyMerge(week) AS week,
        province_id,
        network_business_id,
        src_ip,
        src_province_id,
        dest_ip,
        dest_country,
        traffic_type,
        vpn_id,
        user,
        msisdn_md5,
        anyMerge(mask_msisdn) AS mask_msisdn,
        vpn_airport_code,
        vpn_software_code,
        countMerge(log_count) AS log_count
        from e1_comm_log_user_view
        group by
        day,
        hour,
        province_id,
        network_business_id,
        src_ip,
        src_province_id,
        dest_ip,
        dest_country,
        traffic_type,
        vpn_id,
        user,
        msisdn_md5,
        vpn_airport_code,
        vpn_software_code
        ) AS base_view
        where vpn_airport_code in
        <foreach collection="list" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
        group by vpn_airport_code

    </select>

    <select id="getUserAndSrcIp" resultMap="UserViewVO" parameterType="com.eversec.antivpn.log.mapper.vo.BaseSearchVO">
        select user as user,
        max(src_ip) as src_ip
        FROM  <include refid="viewBaseSql"></include>
        where user in
        <foreach collection="strIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        group by user
    </select>
    <select id="getVpnUserNum" resultType="java.lang.Long" parameterType="com.eversec.antivpn.log.mapper.vo.BaseSearchVO">
        select
        count(distinct (user))  as num
        from
            e1_comm_log_user_view
        <include refid="whereBase"></include>
        and (msisdn_md5 is not null and msisdn_md5 != '') and user is not null
    </select>

    <select id="getUserHaveTelPhoneNum" resultType="java.lang.Long" parameterType="com.eversec.antivpn.log.mapper.vo.BaseSearchVO">
        select
        count(distinct (user))  as num
        from
            e1_comm_log_user_view
        <include refid="whereBase"></include>
        and (msisdn_md5 is not null and msisdn_md5 != '') and user is not null
    </select>

    <select id="getUserLocation" resultType="java.lang.Long" parameterType="com.eversec.antivpn.log.mapper.vo.BaseSearchVO">
        select
        <if test="mapLocationSearchDimension=='province'"> distinct(province_id) as province_id </if>
        <if test="mapLocationSearchDimension=='city'"> distinct(log_city_id)  as log_city_id </if>
        from e1_comm_log_user_view
        <include refid="whereBase"></include>
    </select>

    <select id="getDestCountry" resultType="java.lang.String" parameterType="com.eversec.antivpn.log.mapper.vo.BaseSearchVO">
        select
        distinct (dest_country)  as key_type
        from
        e1_comm_log_user_view
        <include refid="whereBase"></include>
        and dest_country != '中国'
    </select>


    <select id="getUserDest" resultMap="TypeCount" parameterType="com.eversec.antivpn.log.mapper.vo.BaseSearchVO">
        select
        dest_country as key_type,
        countMerge(log_count)  as num
        from e1_comm_log_user_view
        <include refid="whereBase"></include>
        and dest_country not in ('中国','NA')
        group by dest_country
        order by num desc limit 20
    </select>


    <select id="getUserOverAllMap" resultMap="TypeCount" parameterType="com.eversec.antivpn.log.mapper.vo.BaseSearchVO">
        select
        province_id as key_type,
        count(distinct (user))
        from
        <include refid="viewBaseSql"></include>
        group by province_id
    </select>

    <select id="getUserDateHistogram" resultMap="TypeCount" parameterType="com.eversec.antivpn.log.mapper.vo.BaseSearchVO">
        SELECT
        <include refid="keyBaseSql"></include>,
        count(distinct (user)) num
        FROM  e1_comm_log_user_view
        <include refid="whereBase"></include>
        <include refid="groupByTimeBaseSql"></include>
        order by key_type
    </select>

    <select id="getLocationAggregate" resultMap="TypeCount" parameterType="com.eversec.antivpn.log.mapper.vo.BaseSearchVO">
        SELECT
        <include refid="keyMapLocation"></include>,
        count(distinct (user)) num
        FROM  e1_comm_log_user_view
        <include refid="whereBase"></include>
        and key_type is not null
        <include refid="groupByMapLocation"></include>
        order by num desc
    </select>

    <select id="userCountLogNum" resultMap="TypeCount" parameterType="com.eversec.antivpn.log.mapper.vo.BaseSearchVO">
        SELECT
        user key_type,
        countMerge(log_count) as num
        FROM  e1_comm_log_user_view
        <include refid="whereBase"></include>
        GROUP BY
        user
        order by num desc limit 20
    </select>

    <select id="getUserCrossBorderCommResourceNodeTop" resultMap="TypeCount" parameterType="com.eversec.antivpn.log.mapper.vo.BaseSearchVO">
        SELECT
        user key_type,
        count(distinct(vpn_id)) num
        FROM  e1_comm_log_user_view
        <include refid="whereBase"></include>
        GROUP BY user
        order by num desc
        limit 10
    </select>

    <select id="getNetworkBusinessTypeAggregate" resultMap="TypeCount" parameterType="com.eversec.antivpn.log.mapper.vo.BaseSearchVO">
        SELECT
        network_business_id key_type,
        countMerge(log_count)  as num
        FROM e1_comm_log_user_view
        <include refid="whereBase"></include>
        GROUP BY network_business_id
    </select>
</mapper>
