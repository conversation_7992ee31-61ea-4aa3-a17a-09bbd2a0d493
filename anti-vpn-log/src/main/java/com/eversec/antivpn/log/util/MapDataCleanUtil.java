package com.eversec.antivpn.log.util;

import com.eversec.antivpn.config.AntiVpnProperties;
import com.eversec.antivpn.log.api.dto.TypeCountDTO;
import com.eversec.antivpn.log.mapper.vo.TypeCountVO;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Iterator;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/9/21 14:45
 **/
@Component
public class MapDataCleanUtil {
    @Resource
    private AntiVpnProperties antiVpnProperties;

    public void cleanCityOutOfTheProvince(List<TypeCountVO> mapDataList){
        if(!antiVpnProperties.getStartProvinceSearch().getOpenStatus()) return;
        if(antiVpnProperties.getStartProvinceSearch().getProvinceId()==null) return;
        Long provinceId = antiVpnProperties.getStartProvinceSearch().getProvinceId();
        String provinceIdStr = String.valueOf(provinceId);
        Iterator<TypeCountVO> iterator = mapDataList.iterator();
        while(iterator.hasNext()){
            TypeCountVO vo= iterator.next();
            if(vo.getKey() == null) {
                iterator.remove();
                continue;
            }
            if(!vo.getKey().startsWith(provinceIdStr)){
                iterator.remove();
                continue;
            }
        }
    }

    public void cleanCityOutOfTheProvinceTypeCountDTO(List<TypeCountDTO> mapDataList){
        if(!antiVpnProperties.getStartProvinceSearch().getOpenStatus()) return;
        if(antiVpnProperties.getStartProvinceSearch().getProvinceId()==null) return;
        Long provinceId = antiVpnProperties.getStartProvinceSearch().getProvinceId();
        String provinceIdStr = String.valueOf(provinceId);
        Iterator<TypeCountDTO> iterator = mapDataList.iterator();
        while(iterator.hasNext()){
            TypeCountDTO vo= iterator.next();
            if(vo.getKey() == null) {
                iterator.remove();
                continue;
            }
            if(!vo.getKey().startsWith(provinceIdStr)){
                iterator.remove();
                continue;
            }
        }
    }
}
