<template>
  <div class="mapWorld">
    <div class="mapbg"></div>
    <e-charts
      map="世界中国"
      key="世界中国"
      type="map"
      empty-type="2"
      empty-color="#e4fdff"
      :options="options"
      @click="mapClick"
    />
  </div>
</template>

<script>
import { mapOpt2 } from './mapOption.js'

export default {
  name: 'MapWorldNote',
  props: {},
  data() {
    return {
      lists: [],
      options: {},
      dianImg: require('./img/map_dian.png'),
      kuangbg: require('./img/kuangbg.png'),
      kuangbg2: require('./img/kuangbg2.png')
    }
  },
  mounted() {
    // this.drawMap();
  },
  methods: {
    mapClick(e) {
      if (e.data) {
        // 有数据
        this.$emit('updateDetail', e.data)
      } else {
        // 无数据
        this.$emit('closeDetail')
      }
    },
    drawMap(d) {
      this.lists = d || []
      this.options = mapOpt2(this.lists, this.dianImg, this.kuangbg2)
    }
  }
}
</script>

<style lang="scss" scoped>
.mapWorld {
  height: 870px;
  position: relative;
  .echarts-panel {
    position: absolute;
    z-index: 2;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
  }
  .mapbg {
    background: url(./img/world_map.png) no-repeat -77px -12px;
    background-size: 1592px 870px;
    // background-size: 1129px 566px;
    width: 1440px;
    height: 870px;
    position: relative;
    z-index: 2;
    margin: 0 auto;
  }

  ::v-deep .tooltipbox_world {
    background: url(./img/pop_bg.png) no-repeat;
    background-size: 100% 100%;
    height: 107px;
    width: 190px;
    font-size: 14px;
    padding: 8px 12px;
    line-height: 30px;
    .title {
      font-weight: bold;
    }
    .color_blue {
      color: #44c4ff;
    }
    .color_orange {
      color: #ffa74b;
    }
  }
}
</style>
