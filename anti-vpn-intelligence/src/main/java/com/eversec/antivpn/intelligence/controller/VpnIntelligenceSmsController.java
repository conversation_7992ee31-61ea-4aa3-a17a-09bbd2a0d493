package com.eversec.antivpn.intelligence.controller;

import com.eversec.antivpn.intelligence.api.VpnIntelligenceApi;
import com.eversec.antivpn.intelligence.api.dto.SmsRequestDTO;
import com.eversec.antivpn.intelligence.api.dto.SmsResponseDTO;
import com.eversec.antivpn.intelligence.service.ISmsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * VPN情报短信控制器
 *
 * <AUTHOR>
 * @since 2025-01-12
 */
@RestController
@RequestMapping(VpnIntelligenceApi.PATH)
@AllArgsConstructor
@Slf4j
@Tag(name = "VPN情报短信", description = "VPN情报短信发送相关接口")
public class VpnIntelligenceSmsController {

    private final ISmsService smsService;

    @Operation(summary = "发送短信", description = "通过联通云网能力开放平台发送短信")
    @PostMapping("/sendSms")
    public SmsResponseDTO sendSms(@Valid @RequestBody SmsRequestDTO smsRequest) {
        log.info("收到短信发送请求，接收号码：{}，内容长度：{}", smsRequest.getSerialNumber(), smsRequest.getMessage().length());
        return smsService.sendSms(smsRequest);
    }

}
