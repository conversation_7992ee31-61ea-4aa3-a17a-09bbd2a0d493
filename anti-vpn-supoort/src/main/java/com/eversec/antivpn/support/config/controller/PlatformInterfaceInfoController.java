package com.eversec.antivpn.support.config.controller;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.eversec.antivpn.config.AntiVpnProperties;
import com.eversec.antivpn.support.config.api.PlatformInterfaceInfoApi;
import com.eversec.antivpn.support.config.api.dto.PlatformInterfaceInfoDTO;
import com.eversec.antivpn.support.config.convertor.PlatformInterfaceInfoFactory;
import com.eversec.antivpn.support.config.entity.PlatformInterfaceInfo;
import com.eversec.antivpn.support.config.service.IPlatformInterfaceInfoService;
import com.eversec.stark.generic.common.util.ListBuilder;
import io.swagger.v3.oas.annotations.Operation;
import java.util.HashMap;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 平台接口信息 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-17
 */
@RestController
@RequestMapping(PlatformInterfaceInfoApi.PATH)
@AllArgsConstructor
@Slf4j
public class PlatformInterfaceInfoController implements PlatformInterfaceInfoApi {

    private final IPlatformInterfaceInfoService service;
    private final PlatformInterfaceInfoFactory platformInterfaceInfoFactory;
    private final AntiVpnProperties antiVpnProperties;

    @Override
    public PlatformInterfaceInfoDTO getCurrentPlatform() {
        return service.getCurrentPlatform();
    }

    @Override
    public PlatformInterfaceInfoDTO getCurrentPlatformForFront() {
        PlatformInterfaceInfoDTO currentPlatform = service.getCurrentPlatform();
        // 过滤敏感数据
        currentPlatform.setNorthReportFtpPassword(null);
        currentPlatform.setNorthIntelligenceFtpPassword(null);
        currentPlatform.setSouthSmartPassword(null);
        currentPlatform.setNorthIntelligenceSecretKey(null);
        currentPlatform.setNorthReportSecretKey(null);
        currentPlatform.setSouthReceiveSecretKey(null);
        return currentPlatform;
    }

    @Override
    public PlatformInterfaceInfoDTO getByComCodeAndProvinceAndSystemCode(String comCode, Long provinceId, String systemCode) {
        return service.getByComCodeAndProvinceAndSystemCode(comCode, provinceId, systemCode);
    }

    @Override
    public PlatformInterfaceInfoDTO getByComCodeAndSystemCode(String comCode, String systemCode) {
        return service.getByComCodeAndSystemCode(comCode, systemCode);
    }

    @Override
    public List<PlatformInterfaceInfoDTO> getByIspAndProvince(String comCode, Long provinceId) {
        return service.getByIspAndProvince(comCode, provinceId);
    }

    @Override
    public Map<String, Object> startProvinceMap() {
        Map<String, Object> map = new HashMap<>(2);
        AntiVpnProperties.startProvinceSearch startProvinceSearch = antiVpnProperties.getStartProvinceSearch();
        PlatformInterfaceInfoDTO currentPlatform = getCurrentPlatform();
        startProvinceSearch.setProvinceId(ObjectUtil.isNotEmpty(currentPlatform)?currentPlatform.getProvinceId():null);
        map = JSONUtil.parseObj(startProvinceSearch);
        return map;
    }

    @Operation(summary = "根据id查询")
    @GetMapping("/getById/{id}")
    public PlatformInterfaceInfoDTO getById(@PathVariable("id") Long id) {
        return platformInterfaceInfoFactory.entityToDto(service.getById(id));
    }


    @Operation(summary = "查询-所有")
    @GetMapping("/listAll")
    public List<PlatformInterfaceInfoDTO> listAll() {
        LambdaQueryWrapper<PlatformInterfaceInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.orderByAsc(PlatformInterfaceInfo::getConfigType);
        wrapper.orderByDesc(PlatformInterfaceInfo::getEnabled);
        wrapper.orderByAsc(PlatformInterfaceInfo::getComCode, PlatformInterfaceInfo::getProvinceId, PlatformInterfaceInfo::getSystemCode);
        List<PlatformInterfaceInfo> list = service.list(wrapper);
        return ListBuilder.convertList(list, platformInterfaceInfoFactory::entityToDto);
    }

    @Operation(summary = "新增")
    @PostMapping("/save")
    public void save(@Valid @RequestBody PlatformInterfaceInfoDTO paramDto) {
        service.save(paramDto);

    }

    @Operation(summary = "根据id修改")
    @PutMapping("/updateById")
    public void updateById(@Valid @RequestBody PlatformInterfaceInfoDTO paramDto) {
        service.updateById(paramDto);

    }

    @Operation(summary = "根据id批量删除，逗号分割")
    @DeleteMapping("/deleteByIds/{ids}")
    public void deleteByIds(@PathVariable("ids") List<Long> ids) {
        service.removeByIds(ids);

    }

}
