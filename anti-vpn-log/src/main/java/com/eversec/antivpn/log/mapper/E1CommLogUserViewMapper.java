package com.eversec.antivpn.log.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.eversec.antivpn.emnus.ScreenTypeEnum;
import com.eversec.antivpn.log.entity.E1CommLogUserView;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.eversec.antivpn.log.mapper.vo.*;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-18
 */
@Mapper
@DS("clickhouse")
public interface E1CommLogUserViewMapper extends BaseMapper<E1CommLogUserView> {

     Long getVpnAirportCodeCount(BaseSearchVO baseSearchVO);

     Long getDistinctVpnIdNum(BaseSearchVO baseSearchVO);

     List<VpnResourceNodeAggVO> getResourceNodeUserNum(BaseSearchVO baseSearchVO);

     List<VpnHistogramAggVO> getVpnServiceAndResourceNodeDateHistogram(BaseSearchVO baseSearchVO);

    Long countNum(BaseSearchVO baseSearchVO);

    /**
     * 根据时间分区查询所有数据
     * @param baseSearchVO
     * @return
     */
    List<E1CommLogUserView> selectListByPartition(BaseSearchVO baseSearchVO);

    /**
     * 根据时间分区查询VPN服务商-软件信息
     * @param baseSearchVO
     * @return
     */
    List<String> selectSoftwareCodeByPartition(BaseSearchVO baseSearchVO);

    /**
     * 软件分组用户数量TOPO10
     * @param baseSearchVO
     * @return
     */
    List<E1CommLogUserView> getLogNumByUserTop10(BaseSearchVO baseSearchVO);

    /**
     * 用户量TOP10的软件提供服务次数趋势图
     * @param baseSearchVO
     * @return
     */
    List<E1CommLogUserView> getLogNumBySofaWareAndDayTop10(BaseSearchVO baseSearchVO);

    /**
     * 软件分组日志数量TOPO10
     * @param baseSearchVO
     * @return
     */
    List<E1CommLogUserView> getLogNumBySofaWareTop10(BaseSearchVO baseSearchVO);


    List<TypeCountVO> getVpnServicePersonNumTop10(BaseSearchVO baseSearchVO);

    Long getUserCount(BaseSearchVO baseSearchVO);

    List<TypeCountVO> getVpnServicePageInfo(List<String> vpnAirportCodes);

    Long getVpnUserNum(BaseSearchVO baseSearchVO);

    Long getUserHaveTelPhoneNum(BaseSearchVO baseSearchVO);


    List<String> getDestCountry(BaseSearchVO baseSearchVO);

    List<Long> getUserLocation(BaseSearchVO baseSearchVO);


    List<TypeCountVO> getUserDest(BaseSearchVO baseSearchVO);

    List<TypeCountVO> getUserOverAllMap(BaseSearchVO baseSearchVO);

    List<TypeCountVO>  getUserDateHistogram(BaseSearchVO baseSearchVO);

    List<TypeCountVO> getLocationAggregate(BaseSearchVO baseSearchVO);

    List<TypeCountVO> userCountLogNum(BaseSearchVO baseSearchVO);

    List<TypeCountVO> getUserCrossBorderCommResourceNodeTop(BaseSearchVO baseSearchVO);

    List<TypeCountVO> getNetworkBusinessTypeAggregate(BaseSearchVO baseSearchVO);

    List<UserViewVO> getUserAndSrcIp(BaseSearchVO baseSearchVO);
}
