package com.eversec.antivpn.log.controller;

import com.eversec.antivpn.emnus.ScreenTypeEnum;
import com.eversec.antivpn.log.api.E4DnsLogApi;
import com.eversec.antivpn.log.api.dto.E4DnsLogDTO;
import com.eversec.antivpn.log.api.dto.base.AggCountDTO;
import com.eversec.antivpn.log.service.IE4DnsLogService;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import lombok.AllArgsConstructor;
import java.util.List;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-09
 */
@RestController
@RequestMapping(E4DnsLogApi.PATH)
@AllArgsConstructor
@Slf4j
public class E4DnsLogController implements E4DnsLogApi {

    private final IE4DnsLogService service;


    @Override
    public E4DnsLogDTO getById(Long id) {
        // TODO
        return null;
    }


    @Override
    public List<E4DnsLogDTO> listAll() {
        // TODO
        return null;
    }

    @Override
    public Page<E4DnsLogDTO> page(E4DnsLogDTO paramDto, Page<E4DnsLogDTO> pageInfo) {
        // TODO
        return null;
    }

    @Override
    public void save(E4DnsLogDTO paramDto) {
        // TODO

    }

    @Override
    public void updateById(E4DnsLogDTO paramDto) {
        // TODO

    }

    @Override
    public void deleteByIds(List<Long> ids) {
        // TODO

    }

    @Override
    public AggCountDTO timeRangeAndTodayCount(ScreenTypeEnum screenTypeEnum) {
        return service.countNum(screenTypeEnum);
    }

}
