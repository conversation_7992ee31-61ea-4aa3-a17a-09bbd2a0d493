package com.eversec.antivpn.code.generator.generator;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.generator.FastAutoGenerator;
import com.baomidou.mybatisplus.generator.config.DataSourceConfig;
import com.baomidou.mybatisplus.generator.config.TemplateType;
import com.baomidou.mybatisplus.generator.config.builder.CustomFile;
import com.baomidou.mybatisplus.generator.config.rules.DbColumnType;
import com.baomidou.mybatisplus.generator.engine.FreemarkerTemplateEngine;
import com.eversec.antivpn.code.generator.generator.template.EnhanceFreemarkerTemplateEngine;
import com.eversec.stark.generic.common.infra.mybatis.plus.BaseDO;

import java.sql.SQLException;
import java.sql.Types;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * anti-vpn 代码生成器
 *
 * 代码生成在项目的generated目录下，复制生成的代码到相应的工程下
 *
 */
public class Generator {

    static  String DATASOURCE;
    static  String PACKAGE_NAME;
    static  String MODULE_NAME;
    // 多个表逗号分割，不能能有空格
    static  String TABLE_NAMES;

    static {
        initMysql();
//        initCK();
    }
    static  void initMysql(){
        DATASOURCE = "AntiVpnDev";
        PACKAGE_NAME = "com.eversec.antivpn";
        MODULE_NAME = "log";
        TABLE_NAMES = "province_platform_status";
    }
    static void  initCK(){
        DATASOURCE = "ClickhouseDev";
        PACKAGE_NAME = "com.eversec.antivpn";
        MODULE_NAME = "log";
        TABLE_NAMES = "e2_machine_learning_log_view";
    }


    // 数据源
    private static final Map<String, DataSourceConfig.Builder> dataSourceConfigMap = new HashMap<>();

    static {
        dataSourceConfigMap.put("AntiVpnDev", new DataSourceConfig.Builder(
                "*****************************************************************************************************************************************",
                "root",
                "netDc@#su"));

        dataSourceConfigMap.put("ClickhouseDev", new DataSourceConfig.Builder(
                "**************************************************************",
                "default",
                ""));
    }


    /**
     * 执行 run
     */
    public static void main(String[] args) throws SQLException {

        FastAutoGenerator.create(dataSourceConfigMap.get(DATASOURCE)).globalConfig(builder -> {
                    builder.author("baomidou") // 设置作者
//                            .enableSwagger() // 开启 swagger 模式
                            .enableSpringdoc()
                            .fileOverride()
                            .outputDir(System.getProperty("user.dir") + "/generated/"); // 指定输出目录
                }).dataSourceConfig(builder -> builder.typeConvertHandler((globalConfig, typeRegistry, metaInfo) -> {
                    int typeCode = metaInfo.getJdbcType().TYPE_CODE;
                    if (typeCode == Types.SMALLINT) {
                        // 自定义类型转换
                        return DbColumnType.INTEGER;
                    }
                    return typeRegistry.getColumnType(metaInfo);
                })).packageConfig(builder -> {
                    builder.parent(PACKAGE_NAME).moduleName(MODULE_NAME);  // 设置父包名
                }).strategyConfig(builder -> {
                    builder.addInclude(TABLE_NAMES) // 设置需要生成的表名
                            .addTablePrefix("t_", "c_"); // 设置过滤表前缀
                }).templateEngine(new EnhanceFreemarkerTemplateEngine()) // 使用Freemarker引擎模板，默认的是Velocity引擎模板
                .templateConfig(builder -> {
                    builder.entity("/templates/entity.java");
                    builder.disable(TemplateType.ENTITY);
                })
                .strategyConfig(builder -> {

                    builder.entityBuilder()
                            .enableLombok()
                            .enableFileOverride()
                            .addIgnoreColumns("id", "create_user", "update_user", "create_datetime", "update_datetime", "deleted")
                            .superClass(BaseDO.class);
                    builder.controllerBuilder().enableRestStyle().enableFileOverride();
                    builder.mapperBuilder().enableMapperAnnotation();

                })
                .injectionConfig(builder -> {
                    // 模板用到的扩展参数
                    Map<String, Object> customMap = new HashMap<>();
                    builder.customMap(customMap);
                    List<CustomFile> customFiles = new ArrayList<>();
                    // 此处可以添加其他文件
                    customFiles.add(new CustomFile.Builder()
                            .templatePath("/templates/po.java.ftl")
                            .fileName("PO.java")
                            .enableFileOverride()
                            .packageName("entity.po")
                            .build());
                    customFiles.add(new CustomFile.Builder()
                            .templatePath("/templates/entity.java.ftl")
                            .fileName(".java")
                            .packageName("entity")
                            .build());
                    customFiles.add(new CustomFile.Builder()
                            .templatePath("/templates/api.java.ftl")
                            .fileName("Api.java")
                            .packageName("api")
                            .enableFileOverride()
                            .build());
                    customFiles.add(new CustomFile.Builder()
                            .templatePath("/templates/apiDto.java.ftl")
                            .fileName("DTO.java")
                            .packageName("api.dto")
                            .enableFileOverride()
                            .build());
                    builder.customFile(customFiles);
                }).execute();
    }

}
