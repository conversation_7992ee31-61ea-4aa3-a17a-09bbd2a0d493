// import { formatTime } from '@/utils/time';
import echarts from 'echarts'
const baseOpt_yAxis = (rotate = 0) => {
  return {
    axisLabel: { // 坐标轴的标签
      rotate, // 旋转角度
      show: true, // 是否显示
      inside: false, // 是否朝内
      margin: 8, // 刻度标签与轴线之间的距离
      color: '#808191' // 默认取轴线的颜色 808191
    },
    axisLine: { // 坐标轴 轴线
      show: true, // 是否显示
      //  -----   箭头 -----
      // symbol: ['none', 'arrow'],  // 是否显示轴线箭头
      // symbolSize: [8, 8], // 箭头大小
      // symbolOffset: [0, 7], // 箭头位置
      // ----- 线 -------
      lineStyle: {
        color: '#474b57', // E1E3ED
        width: 1,
        type: 'solid'
      }
    },
    splitLine: { // gird 区域中的分割线
      show: true, // 是否显示
      lineStyle: {
        color: '#262c3a',
        width: 1,
        type: 'solid'
      }
    },
  }
}

export const barBase = (data, color, isXAxisValue, legendName = '') => {
  if (!data.length) return;
  if (!color || !(Array.isArray(color) && color.length)) {
    color = ["#436bd9"]
  }
  let x = '';
  let y = '';
  if (isXAxisValue) {
    // console.log('bar纵向', isXAxisValue)
    y = 'xAxis';
    x = 'yAxis';
  } else {
    // console.log('bar横向', isXAxisValue)
    x = 'xAxis';
    y = 'yAxis';
  }
  let len = color.length;
  let xData = [];
  let yData = [];
  data.forEach((el, i) => {
    xData.push(el.name);
    yData.push({
      value: el.value,
      itemStyle: {
        color: color[i % len]
      }
    });
  })
  let name = legendName ? {
    name: legendName
  } : {};
  let yD1 = {
    ...name,
    type: 'bar',
    data: yData,
    barWidth: 10,
    barMinHeight: 10,
    itemStyle: {
      normal: {
        color: '#436bd9',
        borderRadius: 5
      }
    }
  }

  let isShowDataZoom = false;
  let end = Math.floor((10 / xData.length) * 100);
  if (xData.length > 10) {
    isShowDataZoom = true;
  }
  return {
    // title: {
    //   text: title,
    //   textStyle: {
    //     color: '#3F405D', //设置主标题字体颜色
    //     fontSize: 16,
    //   }
    // },
    legend: {
      data: [legendName],
      top: '0px',
      right: '3%',
      itemWidth: 20,
      itemHeight: 10,
      itemGap: 15,
      textStyle: {
        //图例文字的样式
        fontSize: 14
      }
    },
    grid: {
      top: 20,
      left: '3%',
      right: '4%',
      bottom: isShowDataZoom ? '5%' : '5%',
      containLabel: true
    },
    dataZoom: [{
      show: isShowDataZoom,
      height: 8,
      xAxisIndex: 0,
      bottom: 6,
      start: 0,
      end: end,
      fillerColor: "rgba(107, 152, 255, .8)",
      handleIcon: "path://M306.1,413c0,2.2-1.8,4-4,4h-59.8c-2.2,0-4-1.8-4-4V200.8c0-2.2,1.8-4,4-4h59.8c2.2,0,4,1.8,4,4V413z",
      handleSize: "100%",
      handleStyle: {
        color: "transparent",
        borderColor: "transparent"
      },
      textStyle: {
        color: "#666",
        fontSize: 14
      },
      borderColor: "transparent",
      backgroundColor: 'rgba(107, 152, 255, 0.2)'
    }],
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
        shadowStyle: {
          color: 'rgba(98,148,249,0.2)'
        }
      },
      // formatter(param) {
      //   // console.log(param, '当前线')
      //   const pam1 = param[0];
      //   const pam2 = param[1];
      //   const pam3 = param[2];
      //   const list = [];
      //   list.push(pam1.name, `${pam1.marker}门户网站：${pam1.value}`, `${pam2.marker}移动应用：${pam2.value}`, `${pam3.marker}业务系统：${pam3.value}`);
      //   return list.join('<br>');
      // }
    },
    [x]: {
      type: 'category',
      data: xData,
      splitLine: {
        show: false,
        lineStyle: {
          color: '#e2e2e2'
        }
      },
      axisLine: {
        //坐标轴轴线相关设置。就是数学上的y轴
        show: true,
        lineStyle: {
          color: '#eee'
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        textStyle: {
          color: '#999',
          fontSize: 12
        },
        rotate: 30,
        interval: 0,
        // rotate: 20,
        formatter: item => {
          return item.length > 10 ? item.substring(0, 10) + '...' : item;
        }
      }
    },
    [y]: {
      type: 'value',
      splitLine: {
        show: true,
        lineStyle: {
          color: '#dadada',
          type: 'dashed'
        }
      },
      axisLine: {
        //坐标轴轴线相关设置。就是数学上的y轴
        show: true,
        lineStyle: {
          color: '#eee'
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        textStyle: {
          color: '#999',
          fontSize: 12
        }
      }
    },
    series: [
      yD1
    ]
  };
};

export const barFn = (data = [], color, isXAxisValue = false) => {
  if (!data.length) return;
  // color = color || ["#6456D1", "#436BD9", "#348FEA", "#0BC4B6", "#F59E3B"].reverse();
  if (!color || !(Array.isArray(color) && color.length)) {
    color = ["#6456D1", "#436BD9", "#348FEA", "#0BC4B6", "#F59E3B"].reverse();
  }
  let x = '';
  let y = '';
  if (isXAxisValue) {
    // console.log('bar纵向', isXAxisValue)
    y = 'xAxis';
    x = 'yAxis';
  } else {
    // console.log('bar横向', isXAxisValue)
    x = 'xAxis';
    y = 'yAxis';
  }
  let len = color.length;
  let xData = [];
  let yData = [];
  let total = 0;
  data.forEach((el, i) => {
    total += el.value;
    xData.push(el.name);
    yData.push({
      value: el.value,
      itemStyle: {
        color: color[i % len]
      }
    });
  })

  return {
    color,
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '2%',
      top: '7%',
      containLabel: true
    },
    [x]: [{
      type: 'category',
      // data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
      data: xData,
      axisTick: {
        alignWithLabel: true
      },
      ...baseOpt_yAxis(30),
      axisLabel: {
        textStyle: {
          color: '#999',
          fontSize: 12
        },
        interval: 0,
        rotate: 20,
        formatter: item => {
          return item.length > 10 ? item.substring(0, 10) + '...' : item;
        }
      },
      splitLine: {
        show: false,
      }
    }],
    [y]: [{
      type: 'value',
      ...baseOpt_yAxis(), // 轴线的颜色设置
      axisLabel: {
        formatter: '{value}',
        show: true, // 是否显示
        inside: false, // 是否朝内
        margin: 8, // 刻度标签与轴线之间的距离
        color: '#808191' // 默认取轴线的颜色
      },
      axisLine: {
        show: false
      }
    }],
    series: [{
      name: '',
      type: 'bar',
      // barWidth: '60%',
      barWidth: 12,
      itemStyle: { // 图形的形状
        barBorderRadius: [8, 8, 8, 8]
      },
      // data: [10, 52, 200, 334, 390, 330, 220]
      data: yData,
    }]
  };
};

// 竖行的 -- 可用barFn 设置 isXAxisValue = true
export const barFn2 = (data = [], color) => {
  if (!data.length) return;
  // color = color || ["#6456D1", "#436BD9", "#348FEA", "#0BC4B6", "#F59E3B", '#F59E3B'].reverse();
  color = color || ["#EB5F5E", "#FC7F54", "#F59E3B", "#436BD9", "#348FEA"]
  let len = color.length;
  let xData = [];
  let yData = [];
  let total = 0;
  data.forEach((el, i) => {
    total += el.value;
    xData.push(el.name);
    yData.push({
      value: el.value,
      itemStyle: {
        color: color[i % len]
      }
    });
  })

  return {
    color,
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '0%',
      right: '5%',
      bottom: '2%',
      top: '5%',
      containLabel: true
    },
    yAxis: [{
      type: 'category',
      // data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
      data: xData,
      axisTick: {
        alignWithLabel: true
      },
      ...baseOpt_yAxis(),
    }],
    xAxis: [{
      type: 'value',
      ...baseOpt_yAxis(), // 轴线的颜色设置
      axisLine: {
        show: false,
      }
    }],
    series: [{
      name: '',
      type: 'bar',
      // barWidth: '60%',
      barWidth: 12,
      itemStyle: { // 图形的形状
        barBorderRadius: [8, 8, 8, 8]
      },
      // data: [10, 52, 200, 334, 390, 330, 220]
      data: yData,
    }]
  };
};
export const publicPieFn = (data = [], color) => {
  let total = data.reduce((t, cur) => t += cur.value, 0);
  // console.log('total-color:', total, color)
  color = color || ["#6456D1", "#436BD9", "#348FEA", "#f59e3b", "#eb5f5e"]
  if (data.length < 1) {
    return
  }
  return {
    backgroundColor: 'transparent',
    title: {
      zlevel: 0,
      text: [
        '{value|' + total + '}',
        '{name|总数}',
      ].join('\n'),
      top: 'center',
      left: '29%',
      width: '100%',
      textAlign: 'center',
      // borderWidth: 1,
      // borderColor: 'red',
      textStyle: {
        rich: {
          value: {
            color: '#3f405d',
            fontSize: 18,
            fontWeight: 'bold',
            lineHeight: 40,
          },
          name: {
            color: '#808191',
            lineHeight: 16
          },
        },
      },
    },
    legend: {
      right: '2%',
      top: 'center',
      orient: 'vertical',

      itemWidth: 16,
      itemHeight: 8,
      itemGap: 10,
      textStyle: {
        color: '#3F405D',
        // borderColor: 'red',
        // borderWidth: 1,
        rich: {
          value: {
            color: '#3f405d',
            fontSize: 14,
            fontWeight: 'bold',
            lineHeight: 14,
            padding: [-16, 0, 0, 0],
          },
          name: {
            color: '#808191',
            lineHeight: 14,
            padding: [-16, 0, 0, 0],
          },
        },
      },
      formatter: function (name) {
        let find = data.find(el => el.name == name);
        return ['{name|' + name + '}',
          '{value|' + find.value + '}',
        ].join('\n');
        // return `${name} ${find.value}`;
      },
    },
    color,
    tooltip: {
      show: true
    },
    series: [{
      type: "pie",
      minAngle: 8,
      radius: ["47%", "68%"],
      center: ["30%", "50%"],
      hoverAnimation: true,
      z: 10,
      itemStyle: {
        normal: {
          borderWidth: data.length > 1 ? 2 : 0,
          borderColor: "#fff"
        }
      },
      label: {
        show: false
      },
      data,
      // data: [
      //   { name: 'IPv4', value: 10 },
      //   { name: 'IPv6', value: 20 }
      // ],
      labelLine: {
        show: false
      }
    }]
  }
};

export const pieBase = (data = [], color, titleShow = true) => {
  let total = data.reduce((t, cur) => t += cur.value, 0);
  console.log('total-color:', total)
  color = color || ["#6456D1", "#436BD9", "#348FEA", "#f59e3b", "#eb5f5e", "#a13de8", "#0bc4b6", "#61d8fe"]
  if (data.length < 1) return
  return {
    backgroundColor: 'transparent',
    title: {
      show: titleShow,
      zlevel: 0,
      text: [
        '{value|' + total + '}',
        '{name|总数}',
      ].join('\n'),
      top: 'center',
      left: '24%',
      width: '100%',
      textAlign: 'center',
      // borderWidth: 1,
      // borderColor: 'red',
      textStyle: {
        rich: {
          value: {
            color: '#3f405d',
            fontSize: 18,
            fontWeight: 'bold',
            lineHeight: 40,
          },
          name: {
            color: '#808191',
            lineHeight: 16
          },
        },
      },
    },
    legend: {
      type: 'scroll',
      right: '5px',
      top: 'center',
      orient: 'vertical',

      itemWidth: 14,
      itemHeight: 8,
      itemGap: 10,
      textStyle: {
        color: '#3F405D',
        // borderColor: 'red',
        // borderWidth: 1,
        rich: {
          value: {
            color: '#3f405d',
            fontSize: 14,
            fontWeight: 'bold',
            lineHeight: 14,
            padding: [-16, 0, 0, 0],
          },
          name: {
            color: '#808191',
            lineHeight: 14,
            padding: [-16, 0, 0, 0],
          },
          "n": {
            "color": "#808191", // 70cbff
            "width": 70,
            "padding": [0, 7],
            "fontSize": 14
          },
          "v": {
            "align": "right",
            "color": "#3f405d",
            "width": 70,
            "fontSize": 16,
            "fontFamily": "Aldrich-Regular"
          }
        },
      },
      formatter: function (name) {
        let find = data.find(el => el.name == name);
        // return ['{name|' + name + '}',
        //   '{value|' + find.value + '}',
        // ].join('\n');
        // return `${name} ${find.value}`;
        return '{n|' + name + '}' + '{v|' + find.value + '}';
      },
    },
    color,
    tooltip: {
      show: true,
      formatter: function (params) {
        console.log(params, '处理悬浮')
        return params.data.name + '：' + params.data.value + ' 占比：' + (params.data.value / total.toFixed(2) * 100 + '%');
        // let tip = '';
        // if (params.seriesIndex === 1) {
        //   tip = params.percent + '%'
        // } else if (params.seriesIndex === 0) {
        //   tip = ''
        // }
        // return tip;
      }
    },
    series: [{
      type: "pie",
      minAngle: 8,
      radius: ["42%", "68%"],
      center: ["25%", "50%"],
      hoverAnimation: true,
      z: 10,
      itemStyle: {
        normal: {
          borderWidth: data.length > 1 ? 2 : 0,
          borderColor: "#fff"
        }
      },
      label: {
        show: false
      },
      data,
      // data: [
      //   { name: 'IPv4', value: 10 },
      //   { name: 'IPv6', value: 20 }
      // ],
      labelLine: {
        show: false
      }
    }]
  }
};

// 单条线
export const lineBase = (data = [], color, name = '') => {
  if (!color || !(Array.isArray(color) && color.length)) {
    color = ['254,79,110', '141, 169, 241', '#F59E3B', '#FC7F54', '#EB5F5E']
  }
  let len = data.length;
  if (len == 0) return;
  let xData = [];
  let yData = [];
  data.forEach((el, i) => {
    xData.push(el.name);
    yData.push(el.value);
  })
  // let yData = [320, 332, 301, 334, 390, 330]
  const option = {
    color,
    tooltip: {
      trigger: 'axis',
      // backgroundColor: 'transparent',
      axisPointer: {
        lineStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [{
              offset: 0,
              color: 'rgba(126,199,255,0)' // 0% 处的颜色
            }, {
              offset: 0.5,
              color: 'rgba(126,199,255,1)' // 100% 处的颜色
            }, {
              offset: 1,
              color: 'rgba(126,199,255,0)' // 100% 处的颜色
            }],
            global: false // 缺省为 false
          }
        },
      },
    },
    legend: {
      show: false,
      // data: ['低危', '中危', '高危','紧急'],
      itemWidth: 10,
      itemHeight: 10,
      itemGap: 15,
      icon: "rect", // pin rect
      top: 0,
      right: 0,
      textStyle: {
        fontSize: 12,
        color: "#666",
      },
    },
    grid: {
      left: '6%',
      right: '2%',
      bottom: '2%',
      top: '12%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      // data: ['2021-10-21', '2021-10-22', '2021-10-23', '2021-10-24', '2021-10-25', '2021-10-26'],
      data: xData,
      ...baseOpt_yAxis(30),
      splitLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
    },
    yAxis: {
      type: 'value',
      ...baseOpt_yAxis(),
      axisLabel: {
        formatter: '{value}',
        show: true, // 是否显示
        inside: false, // 是否朝内
        margin: 10, // 刻度标签与轴线之间的距离
        color: '#808191' // 默认取轴线的颜色
      },
      // 网格线的样式
      splitLine: {
        show: true,
        lineStyle: {
          color: ["rgba(255,255,255,0.05)"],
        },
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        show: false,
      },
    },
    series: [{
      data: yData,
      name: name || '数量',
      type: 'line',
      // symbol: 'circle', // 默认是空心圆（中间是白色的），改成实心圆
      showAllSymbol: true,
      symbolSize: 0,
      smooth: true,
      lineStyle: {
        normal: {
          width: 2,
          color: `rgba(${color[0]},1)`, // 线条颜色
        },
        borderColor: 'rgba(0,0,0,.4)',
      },
      itemStyle: {
        color: `rgba(${color[0]},1)`,
        borderColor: "#646ace",
        borderWidth: 2
      },
      areaStyle: { //区域填充样式
        normal: {
          //线性渐变，前4个参数分别是x0,y0,x2,y2(范围0~1);相当于图形包围盒中的百分比。如果最后一个参数是‘true’，则该四个值是绝对像素位置。
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
              offset: 0,
              color: `rgba(${color[0]},.3)`
            },
            {
              offset: 1,
              color: `rgba(${color[0]}, 0)`
            }
          ], false),
          shadowColor: `rgba(${color[0]}, 0.5)`, //阴影颜色
          shadowBlur: 20 //shadowBlur设图形阴影的模糊大小。配合shadowColor,shadowOffsetX/Y, 设置图形的阴影效果。
        }
      },
      // data: [320, 332, 301, 334, 390, 330]
    }, ]
  };
  return option
}

/** 多线条 -- 最多5条线
  let obj = {
    name: ["类型1", "类型2", "类型3", "类型4", "类型5"],
    xData: xData: ["08-13", "08-14", "08-15", "08-16", "08-17", "08-18"],
    lineData: [
      [10, 15, 17, 20, 30, 25],
      [20, 25, 27, 20, 40, 35],
      [30, 35, 37, 40, 50, 45],
    ],
  };
*/
export const linesFn = (data = { xData: [], name: [], lineData: []}, color, legendShow = true) => {
  color = color || ['41,162,255', '255,113,112', '218,249,59', '37,237,255', '220,60,88', '255,150,56']
  if (data.xData.length == 0) return;
  return {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        lineStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [{
              offset: 0,
              color: 'rgba(126,199,255,0)' // 0% 处的颜色
            }, {
              offset: 0.5,
              color: 'rgba(126,199,255,1)' // 100% 处的颜色
            }, {
              offset: 1,
              color: 'rgba(126,199,255,0)' // 100% 处的颜色
            }],
            global: false // 缺省为 false
          }
        },
      },
    },
    grid: {
      left: "1%",
      right: "3%",
      bottom: "1%",
      top: "18%",
      containLabel: true,
    },
    xAxis: {
      type: "category",
      data: data.xData || [],
      ...baseOpt_yAxis(),
      splitLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
    },
    yAxis: {
      type: "value",
      ...baseOpt_yAxis(),
      // 网格线的样式
      splitLine: {
        show: true,
        lineStyle: {
          color: ["rgba(255,255,255,0.05)"],
        },
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        show: false,
      },
      // boundaryGap: [0, "100%"],
    },
    legend: {
      show: legendShow,
      data: data.name,
      itemWidth: 12,
      itemHeight: 4,
      itemGap: 15,
      itemStyle: {
        borderColor: "#EFF0F2",
        borderWidth: 0,
      },
      icon: "rect", // pin rect
      top: "4%",
      right: "4%",
      textStyle: {
        fontSize: 14,
        color: "#fff",
      },
    },
    series: data.lineData.map((el, index) => {
      return {
        smooth: true,
        name: data.name[index],
        type: "line",
        symbol: "none",
        sampling: "lttb",
        itemStyle: {
          normal: {
            color: 'rgba(' + color[index] + ',1)',
            borderColor: 'rgba(' + color[index] + ',1)'
          }
        },
        lineStyle: {
          width: 2,
          color: 'rgba(' + color[index] + ',1)',
          shadowColor: 'rgba(' + color[index] + ',0.3)',
          shadowBlur: 10,
          shadowOffsetY: 20

        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
              offset: 0,
              color: 'rgba(' + color[index] + ',0.3)',
            },
            {
              offset: 1,
              color: 'rgba(' + color[index] + ',0)',
            },
          ]),
        },
        data: el,
      }
    })
  };
};

/**
 * 双轴，bar，line混合，自定义，传数组series
 * @param { series } d 
 * @param {*} color 
 * @param {[ { name: '10-21', value: 100, value2: 100, total: 200 } ]}  data
 * @returns 
 */
export const hunheFn = (series, data, color) => {
  if (!color || !(Array.isArray(color) && color.length)) {
    color = ['#50c7ff', '#ce9253', '#43c5c5', '#FC7F54', '#EB5F5E']
  }
  if (data.length == 0) return;
  // data = [
  //   { name: '10-21', value: 100, value2: 200, value3: 200, total: 300 },
  //   { name: '10-22', value: 200, value2: 700, value3: 100, total: 900 },
  //   { name: '10-23', value: 300, value2: 400, value3: 200, total: 700 },
  //   { name: '10-24', value: 100, value2: 200, value3: 400, total: 500 },
  //   { name: '10-25', value: 500, value2: 300, value3: 700, total: 800 },
  // ]

  // xData = ['10-21', '10-22', '10-23', '10-24', '10-25', '10-26'];
  let yData1 = [];
  let yData2 = [];
  let yData3 = [];
  let xData = data.map(el => {
    yData1.push(el.value);
    yData2.push(el.value2);
    yData3.push(el.value3);
    return el.name;
  });
  series = series || [{
      name: '情报日志',
      type: 'bar',
      barWidth: 12,
      stack: "总量",
      data: yData1,
      // data: [10, 52, 200, 334, 100, 200],
      // yAxisIndex: 0, // 使用两个坐标轴关键 yAxis为数组
      itemStyle: {
        // barBorderRadius: [8, 8, 8, 8], // 图形的形状
      }
    },
    {
      name: '机器学习日志',
      type: 'bar',
      barWidth: 12,
      itemStyle: { // 图形的形状
        // barBorderRadius: [8, 8, 8, 8], // 图形的形状
        normal: {
          label: {
            show: true,
            position: "top",
            color: '#fff',
            formatter: function (p) {
              let f = data.find(el => el.name == p.name);
              return f.total || p.value;
              // return p.value > 0 ? (p.value) : '';
            }
          }
        }
      },
      stack: "总量",
      data: yData2,
      // data: [10, 52, 200, 334, 100, 200],
      // yAxisIndex: 0, // 使用两个坐标轴关键 yAxis为数组
    },
    {
      name: '上报',
      type: 'line',
      // stack: 'Total',
      // data: d.yData2,
      data: yData3,
      // data: [320, 232, 201, 154, 190, 330],
      // yAxisIndex: 1, // 使用两个坐标轴关键 yAxis为数组
    },
  ]
  let legendD = series.map(el => el.name);
  const option = {
    color,
    tooltip: {
      trigger: 'axis',
      // formatter: function (params) {
      //   console.log(params, '处理悬浮')
      // }
    },
    
    legend: {
      // data: ['接收', '上报'],
      data: legendD,
      itemWidth: 10,
      itemHeight: 10,
      borderRadius: 5,
      itemGap: 20,
      // itemStyle: {
      //   borderColor: "#848a98",
      //   borderWidth: 1,
      // },
      icon: "rect", // pin rect roundRect
      top: 0,
      left: 0,
      textStyle: {
        fontSize: 12,
        color: "#fefefe",
      },
    },
    grid: {
      left: '4%',
      right: '4%',
      bottom: '2%',
      top: '14%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      // data: ['10-21', '10-22', '10-23', '10-24', '10-25', '10-26'],
      data: xData || [],
      ...baseOpt_yAxis(30),
      splitLine: {
        show: false,
      },
      axisLabel: {
        color: '#808191'
      }
    },
    yAxis: [{
        type: 'value',
        name: legendD[0],
        ...baseOpt_yAxis(),
        axisLabel: {
          formatter: '{value}',
          show: true, // 是否显示
          inside: false, // 是否朝内
          margin: 20, // 刻度标签与轴线之间的距离
          color: '#808191' // 默认取轴线的颜色
        },
        axisLine: {
          show: false
        },

      },
      // {
      //   type: 'value',
      //   name: legendD[1],
      //   ...baseOpt_yAxis(),
      //   axisLabel: {
      //     formatter: '{value}',
      //     show: true, // 是否显示
      //     inside: false, // 是否朝内
      //     margin: 8, // 刻度标签与轴线之间的距离
      //     color: '#808191' // 默认取轴线的颜色
      //   },
      //   axisLine: {
      //     show: false
      //   }
      // }
    ],
    series,
    // series: [{
    //     name: '低危',
    //     type: 'bar',
    //     // stack: 'Total',
    //     // data: d.yData1,
    //     data: [320, 332, 301, 334, 390, 330]
    //   },
    //   {
    //     name: '中危',
    //     type: 'line',
    //     // stack: 'Total',
    //     // data: d.yData2,
    //     data: [320, 232, 201, 154, 190, 330]
    //   }
    // ]
  };
  return option
}
