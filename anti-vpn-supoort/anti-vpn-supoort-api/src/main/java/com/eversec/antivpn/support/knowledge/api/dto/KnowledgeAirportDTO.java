package com.eversec.antivpn.support.knowledge.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 机场信息DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-12
 */
@Schema(name = "KnowledgeAirportDTO", description = "机场信息")
@EqualsAndHashCode(callSuper=false)
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class KnowledgeAirportDTO extends KnowledgeBaseDTO {


    @NotNull(message = "官网不能为空")
    @Schema(description = "官网")
    private String officialWebsite;

    @NotNull(message = "注册地不能为空")
    @Schema(description = "注册地")
    private String registered;

    @NotNull(message = "简介不能为空")
    @Schema(description = "简介")
    private String introduction;

    @NotNull(message = "价格不能为空")
    @Schema(description = "价格")
    private String price;

    @NotNull(message = "订阅渠道不能为空")
    @Schema(description = "订阅渠道")
    private String subscriptionChannel;

    @NotNull(message = "付款方式不能为空")
    @Schema(description = "付款方式")
    private String paymentMethod;

    @NotNull(message = "发现时间不能为空")
    @Schema(description = "发现时间")
    private String discoveryTime;

//    @NotNull(message = "入库时间不能为空")
//    @Schema(description = "入库时间")
//    private String insertTime;



}