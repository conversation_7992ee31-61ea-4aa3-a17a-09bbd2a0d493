<template>
  <div class="mapbox">
    <div class="mapbg"></div>
    <e-charts
      map="china"
      key="china"
      type="map"
      class="chinamap"
      empty-type="2"
      empty-color="#e4fdff"
      :options="options"
    ></e-charts>
  </div>
</template>

<script>
import { cloneDeep } from "lodash";
import { worldSecurity } from "@/assets/mapStatic.js";
import echarts from "echarts";
export default {
  name: "MapWorldNote",
  props: {},
  data() {
    return {
      lists: [],
      options: {},
      fromImg: require("./img/flag.png"),
      symbolImg: require("./img/icon_addr2.png"),
    };
  },
  mounted() {
    // this.drawMap();
  },
  methods: {
    drawMap(data) {
      console.log(data);
      let _this = this;
      let geoCoordMap = worldSecurity;
      data = data.sort((a, b) => {
        return b.value - a.value;
      });
      let max = Math.max.apply(
        null,
        data.map((v) => v.value)
      );
      let maxH = 8; // [0,8]
      data = data.map((el, i) => {
        return {
          ...el,
          height: (el.value / max) * maxH, // 画柱状图用
          idx: i + 1,
        };
      });

      let topThree = data.filter((item) => {
        return item.idx < 4;
      });
      let otherData = data.filter((item) => {
        return item.idx > 3;
      });
      var scatterData = topThree.map((item) => {
        return [
          geoCoordMap[item.name][0],
          geoCoordMap[item.name][1] + item.height,
        ];
      });
      var scatterData2 = topThree.map((item) => {
        return geoCoordMap[item.name];
      });
      var scatterData3 = otherData.map((item) => {
        return geoCoordMap[item.name].concat(item.name);
      });
      console.log(scatterData3);
      var lineData = topThree.map((item) => {
        return {
          coords: [
            geoCoordMap[item.name],
            [
              geoCoordMap[item.name][0],
              geoCoordMap[item.name][1] + item.height,
            ],
          ],
        };
      });
      console.log(scatterData2, lineData);
      let series_new = [
        {
          // 地图和颜色映射
          type: "map",
          map: "中国",
          geoIndex: 0,
          zlevel: 2,
          label: {
            normal: {
              show: true,
              position: "right",
              formatter: "{b}",
            },
          },
          data: data,
        },
        // 画柱状图
        {
          type: "lines",
          zlevel: 5,
          effect: {
            show: false,
            period: 4, //箭头指向速度，值越小速度越快
            trailLength: 0.02, //特效尾迹长度[0,1]值越大，尾迹越长重
            symbol: "arrow", //箭头图标
            symbolSize: 5, //图标大小
          },
          lineStyle: {
            width: 13, //尾迹线条宽度
            color: {
              type: "linear",
              x: 0,
              y: 0,
              x2: 1,
              y2: 0,
              colorStops: [
                {
                  offset: 0,
                  color: "#F09B0A", // 0% 处的颜色
                },
                {
                  offset: 0.2,
                  color: "#F09B0A", // 0% 处的颜色
                },
                {
                  offset: 0.5,
                  color: "#FEF03B", // 0% 处的颜色
                },
                {
                  offset: 0.7,
                  color: "#FEF03B", // 0% 处的颜色
                },
                {
                  offset: 1,
                  color: "#F09B0A", // 100% 处的颜色
                },
              ],
              global: false, // 缺省为 false
            },
            opacity: 1, //尾迹线条透明度
            curveness: 0, //尾迹线条曲直度
          },
          label: {
            show: 0,
            position: "end",
            formatter: "245",
          },
          silent: true,
          data: lineData,
        },
        // 柱形数量显示(圆柱顶)
        {
          type: "scatter",
          coordinateSystem: "geo",
          geoIndex: 0,
          zlevel: 5,
          label: {
            show: !0,
            position: "top",
            formatter: (params) => data[params.dataIndex].totalFt,
            padding: [4, 8],
            backgroundColor: "#003F5E",
            borderRadius: 5,
            borderColor: "#67F0EF",
            borderWidth: 1,
            color: "#67F0EF",
          },
          symbol: "circle",
          symbolSize: [13, 7],
          itemStyle: {
            color: "#FEF03B",
            opacity: 1,
          },
          silent: true,
          data: scatterData,
        },
        // 图中圆点设置(底)
        {
          type: "scatter",
          coordinateSystem: "geo",
          geoIndex: 0,
          zlevel: 5,
          symbol: "circle",
          symbolSize: [13, 7],
          itemStyle: {
            color: "#F09B0A",
            opacity: 1,
            shadowColor: "#000",
            shadowBlur: 5,
            shadowOffsetY: 2,
          },
          silent: true,
          data: scatterData2,
        },
        {
          type: "scatter",
          coordinateSystem: "geo",
          geoIndex: 0,
          symbol: "image://" + _this.symbolImg,
          symbolSize: 50,

          itemStyle: {
            color: "#FEF134",
          },
          zlevel: 6,
          data: scatterData3,
        },
        {
          type: "scatter",
          coordinateSystem: "geo",
          geoIndex: 0,
          zlevel: 5,
          label: {
            show: !0,
            position: "top",
            formatter: (params) => data[params.dataIndex + 3].name,
            padding: [4, 8],
            backgroundColor: "#003F5E",
            borderRadius: 5,
            borderColor: "#67F0EF",
            borderWidth: 1,
            color: "#67F0EF",
          },
          symbol: "circle",
          symbolSize: [13, 7],
          itemStyle: {
            color: "transparent",
            opacity: 1,
          },
          silent: true,
          data: scatterData3,
        },
      ];

      var option01 = {
        backgroundColor: "transparent",
        // visualMap: {
        //   min: 10000,
        //   max: 100000,
        //   left: "0%",
        //   bottom:'30',
        //   calculable: true,
        //   orient:'horizontal',
        //   color:['#cd6d69','#b3c53b','#5abf7c','#236dd3'],
        //   textStyle:{
        //     color:'#fff'
        //   }
        // },
        tooltip: {
          show: true,
          trigger: "item",
          backgroundColor: "none",
          extraCssText: "border:0; box-shadow: none;",
          formatter: function (params) {
            let d = params.data;
            console.log(params);
            if (d && d.name) {
              return `
                  <div class="tooltipbox">
                    <div class="provincename">${d.name} </div>
                    <div class="tooltip-item">
                      <div class="item-name">排名：</div>
                      <div class="item-value">${d.idx}</div>
                    </div>
                    <div class="tooltip-item">
                      <div class="item-name">今日上报：</div>
                      <div class="item-value">${d.todayNumFt}</div>
                    </div>
                    <div class="tooltip-item">
                      <div class="item-name">总上报：</div>
                      <div class="item-value"> ${d.totalFt}</div>
                    </div>
                  </div>
                `;
            } else {
              return `
                  <div class="tooltipbox">
                    <div class="provincename">${
                      data[params.dataIndex + 3].name
                    } </div>
                    <div class="tooltip-item">
                      <div class="item-name">排名：</div>
                      <div class="item-value">${
                        data[params.dataIndex + 3].idx
                      }</div>
                    </div>
                    <div class="tooltip-item">
                      <div class="item-name">今日上报：</div>
                      <div class="item-value">${
                        data[params.dataIndex + 3].todayNum
                      }</div>
                    </div>
                    <div class="tooltip-item">
                      <div class="item-name">总上报：</div>
                      <div class="item-value"> ${
                        data[params.dataIndex + 3].value > 10000
                          ? Math.round(
                              data[params.dataIndex + 3].value / 10000
                            ) + "万"
                          : data[params.dataIndex + 3].value
                      }</div>
                    </div>
                  </div>
                `;
            }
          },
        },
        geo: [
          {
            // 坐标层
            map: "china",
            zoom: 1.36,
            aspectScale: 1.02,
            roam: false,
            layoutCenter: ["50%", "49%"],
            layoutSize: 620,
            zlevel: 1,
            label: {
              normal: {
                show: false,
                color: "#fff",
              },
              emphasis: {
                show: false,
              },
            },
            itemStyle: {
              normal: {
                // areaColor: "#12235c",
                borderColor: "#7cb7d5",
                areaColor: "transparent",
                // borderColor: "#36bcc6",
                // opacity: 0.4
              },
              emphasis: {
                // areaColor: "#8cb2ff"
                areaColor: "rgba(31,96,190, .75)",
              },
            },
            regions: [
              {
                name: "南海诸岛",
                value: 0,
                itemStyle: {
                  normal: {
                    opacity: 0,
                    label: {
                      show: false,
                    },
                  },
                },
              },
            ],
          },
        ],
        series: series_new,
      };
      console.log("---options-series", series_new);
      this.options = option01;
      // myChart01.setOption(option01);
    },
  },
};
</script>

<style lang="scss">
.mapbox {
  height: 580px;
  position: relative;
  margin-top: -30px;
  .echarts-panel {
    position: absolute;
    z-index: 2;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
  }
  .mapbg {
    background: url(./img/map_bg.png) no-repeat -8px 18px;
    width: 878px;
    height: 580px;
    position: relative;
    z-index: 2;
    margin: 0 auto;
  }
}

.tooltipbox {
  background: url(./img/pop_bg.png) no-repeat;
  height: 117px;
  width: 154px;
  font-size: 12px;
  padding: 8px 14px;
  .provincename {
    font-weight: bold;
  }
  .tooltip-item {
    display: flex;
    line-height: 26px;
    position: relative;

    .item-name {
      color: #fff;
      width: 60px;
    }
    .item-value {
      color: #41c6fe;
    }
  }
}
</style>
