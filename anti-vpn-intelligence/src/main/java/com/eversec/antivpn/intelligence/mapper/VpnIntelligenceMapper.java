package com.eversec.antivpn.intelligence.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.eversec.antivpn.intelligence.entity.VpnIntelligence;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * vpn情报 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-10
 */
@Mapper
public interface VpnIntelligenceMapper extends BaseMapper<VpnIntelligence> {

    /**
     * 根据id批量删除
     * @param ids
     */
    void deleteByIds(List<Long> ids);

    /**
     * 批量保存
     * @param vpnIntelligenceList
     */
    void saveBatchIntelligence(List<VpnIntelligence> vpnIntelligenceList);

}
