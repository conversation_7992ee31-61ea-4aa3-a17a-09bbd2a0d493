import api from '@/api/rm/dic'
import cloneDeep from 'lodash/cloneDeep'
import storeSet from '@/store'

const getSysDictAllList = e => {
  if (JSON.stringify(e) !== '{}') return e
  else {
    api
      .getDictAllListTree()
      .then(res => {
        let temp = {}
        res.map(e => {
          temp[e.dictType.typeKey] = e.dictList
        })
        storeSet.commit('sys-dict/GET_SYS_DICTIONARY_ALL_LIST', cloneDeep(temp))
      })
      .catch(e => {})
  }
}

const state = {
  dictionaryData: {} // 全部字典数据
}

const getters = {
  dictionaryData: state => getSysDictAllList(state.dictionaryData)
}
const mutations = {
  // 请求字典全部数据
  GET_SYS_DICTIONARY_ALL_LIST(state, res) {
    let dataList = {}
    for (let name in res) {
      // 过滤字典中无效数据
      if (res[name]) dataList[name] = res[name].filter(e => e.valid)
      else {
        dataList[name] = []
      }
    }
    state.dictionaryData = dataList
  }
}

const actions = {
  // 更新字典
  updateDictionary() {
    getSysDictAllList({})
  }
}

export default {
  namespaced: true,
  state,
  getters,
  actions,
  mutations
}
