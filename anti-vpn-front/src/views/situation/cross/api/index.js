import httpRequest from '@/utils/httpRequest'
import serverProxy from '@/config/serverProxy'
let antiVpnService = serverProxy.antiVpnService
export default {
  /*
   * 获取跨境软件使用情况
   * @param {string} dateType - 日期类型 TODAY, THISWEEK, THISWEEK_BEFORE, THISMONTH, SOFAR
   */
  getCrossBoardAppCount(dateType = 'TODAY') {
    return httpRequest({
      url: `${antiVpnService}/log/softwareScreen/dateSoftWareSum`,
      params: {
        screenTypeEnum: dateType
      }
    })
  },

  // 近7日跨境用户通信使用的软件数量
  getCrossBoardAppCountLast7d(dateType = 'TODAY') {
    return httpRequest({
      url: `${antiVpnService}/log/softwareScreen/last7Days`,
      params: {
        screenTypeEnum: dateType
      }
    })
  },

  // 软件用户量TOP10
  getAppUserCountTop10(dateType = 'TODAY') {
    return httpRequest({
      url: `${antiVpnService}/log/softwareScreen/softwareUserPie`,
      params: {
        screenTypeEnum: dateType
      }
    })
  },

  // 软件日志访问量TOP10
  getAppLogAccessTop10(dateType = 'TODAY') {
    return httpRequest({
      url: `${antiVpnService}/log/softwareScreen/softwareAccessLog`,
      params: {
        screenTypeEnum: dateType
      }
    })
  },

  // 用户量TOP10的软件提供服务次数
  getUserAppServiceCountTop10(dateType = 'TODAY') {
    return httpRequest({
      url: `${antiVpnService}/log/softwareScreen/userNumChart`,
      params: {
        screenTypeEnum: dateType
      }
    })
  },

  // 软件对系统的适配度
  getAppSystemAdaptability(dateType = 'TODAY') {
    return httpRequest({
      url: `${antiVpnService}/log/softwareScreen/softWareSystemTopo10`,
      params: {
        screenTypeEnum: dateType
      }
    })
  },

  // 軟件授权类型占比
  getAppAuthTypePercentage(dateType = 'TODAY') {
    return httpRequest({
      url: `${antiVpnService}/log/softwareScreen/softwareLicence`,
      params: {
        screenTypeEnum: dateType
      }
    })
  },

  // 用户量TOP10的软件开发者信息
  getTop10UserAppDeveloperInfo(dateType = 'TODAY') {
    return httpRequest({
      url: `${antiVpnService}/log/softwareScreen/userNumDevelop`,
      params: {
        screenTypeEnum: dateType
      }
    })
  },

  // 软件对协议的适配度 Top10
  getAppProtocolAdaptabilityTop10(dateType = 'TODAY') {
    return httpRequest({
      url: `${antiVpnService}/log/softwareScreen/softWareProtocolTopo10`,
      params: {
        screenTypeEnum: dateType
      }
    })
  }
}
