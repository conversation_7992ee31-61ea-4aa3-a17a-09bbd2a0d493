package com.eversec.antivpn.log.service.impl;

import com.alibaba.fastjson2.JSONArray;
import com.eversec.antivpn.config.enums.AntiVpnDataDictTypeEnum;
import com.eversec.antivpn.emnus.ScreenTypeEnum;
import com.eversec.antivpn.log.api.dto.MonitorDataDTO;
import com.eversec.antivpn.log.entity.ProvincePlatformStatus;
import com.eversec.antivpn.log.entity.po.GenericSysDataDict;
import com.eversec.antivpn.log.mapper.*;
import com.eversec.antivpn.log.mapper.vo.*;
import com.eversec.antivpn.log.service.GenericSysDataDictService;
import com.eversec.antivpn.log.service.LogService;
import com.eversec.antivpn.log.util.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;

import com.eversec.antivpn.log.mapper.E1CommLogViewMapper;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/7/24 14:33
 **/
@Service
public class LogServiceImpl implements LogService {

    @Resource
    private MapDataCleanUtil mapDataCleanUtil;


    @Resource
    private E1CommLogViewMapper e1CommLogViewMapper;

    @Resource
    private E2MachineLearningLogViewMapper e2MachineLearningLogViewMapper;

    @Resource
    private E1CommLogUserViewMapper e1CommLogUserViewMapper;

    @Resource
    private E4DnsLogMapper e4DnsLogMapper;
    @Resource
    private SearchParamUtil searchParamUtil;
    @Resource
    private GenericSysDataDictService genericSysDataDictService;
    @Resource
    private ProvincePlatformStatusMapperExt provincePlatformStatusMapperExt;

    @Value("${app.anti-vpn.yun-xin-an:false}")
    private Boolean yun_xin_nan;


    @Override
    public List<TypeCountVO> getNetworkBusinessTypeAggregate(ScreenTypeEnum screenTypeEnum) {
        if(yun_xin_nan) return yunXinAn(screenTypeEnum);
        BaseSearchVO baseSearchVO = searchParamUtil.getBaseSearchVOWithTimePartition(screenTypeEnum);
        List<TypeCountVO> e1DataList = e1CommLogUserViewMapper.getNetworkBusinessTypeAggregate(baseSearchVO);
        List<TypeCountVO> e2DataList = e2MachineLearningLogViewMapper.getNetworkBusinessTypeAggregate(baseSearchVO);
        e1DataList.addAll(e2DataList);
        List<TypeCountVO> result = AggregateDataUtil.mergeTypeCountVO(e1DataList);
        SortUtil.sortTypeCountVODesc(result);
        return result;
    }

    private List<TypeCountVO> yunXinAn(ScreenTypeEnum screenTypeEnum){
        List<TypeCountVO> result = new ArrayList<>();
        BaseSearchVO baseSearchVO = new BaseSearchVO();
        baseSearchVO.setStartTimePartition(ScreenTypeEnum.getTimeCKPartition(screenTypeEnum));
        Long e1DateRangeCount = e1CommLogViewMapper.countNum(baseSearchVO);
        Long e2DateRangeCount =  e2MachineLearningLogViewMapper.countNum(baseSearchVO);
        TypeCountVO typeCountVO = new TypeCountVO();
        typeCountVO.setNum(e1DateRangeCount+e2DateRangeCount);
        typeCountVO.setKey("1000");
        result.add(typeCountVO);
        return result;
    }
    @Override
    public List<TypeCountVO> getProvinceAggregate(ScreenTypeEnum screenTypeEnum) {
        BaseSearchVO baseSearchVO = searchParamUtil.getBaseSearchVOWithTimePartition(screenTypeEnum);
        List<TypeCountVO> e1DataList = e1CommLogViewMapper.getProvinceAggregate(baseSearchVO);
        List<TypeCountVO> e2DataList = e2MachineLearningLogViewMapper.getProvinceAggregate(baseSearchVO);
        e1DataList.addAll(e2DataList);
        List<TypeCountVO> resultList = AggregateDataUtil.mergeTypeCountVO(e1DataList);
        //增加今天数据处理
        if (screenTypeEnum != ScreenTypeEnum.TODAY) {
            addTodayNum(resultList);
        } else {
            resultList.forEach(typeCountVO -> {
                typeCountVO.setTodayNum(typeCountVO.getNum());
            });
        }
        SortUtil.sortTypeCountVODesc(resultList);
        removeUselessData(resultList);
        mapDataCleanUtil.cleanCityOutOfTheProvince(resultList);
        cpuTop10Percent(resultList);
        return resultList;
    }

    private void removeUselessData(List<TypeCountVO> data){
        if(data == null || data.isEmpty()) return;
        Iterator<TypeCountVO> iterator = data.iterator();
        while (iterator.hasNext()){
            TypeCountVO vo = iterator.next();
            if(vo.getKey()==null || "".equals(vo.getKey()) || "null".equals(vo.getKey())) iterator.remove();
        }
    }

    private void cpuTop10Percent(List<TypeCountVO> list) {
        Long sum = 0l;
        for (TypeCountVO typeCountVO : list) {
            sum = sum + typeCountVO.getNum();
        }
        for (int i = 0; i < list.size(); i++) {
            double percentage = ((double) list.get(i).getNum() / sum) * 100;
            list.get(i).setPercent(percentage);
        }

    }

    private void addTodayNum(List<TypeCountVO> list) {
        ;
        BaseSearchVO baseSearchVO = searchParamUtil.getBaseSearchVOWithTimePartition(ScreenTypeEnum.TODAY);
        List<TypeCountVO> e1DataList = e1CommLogViewMapper.getProvinceAggregate(baseSearchVO);
        List<TypeCountVO> e2DataList = e2MachineLearningLogViewMapper.getProvinceAggregate(baseSearchVO);
        e1DataList.addAll(e2DataList);
        List<TypeCountVO> todayDataList = AggregateDataUtil.mergeTypeCountVO(e1DataList);
        AggregateDataUtil.mergeAllAndTodayTypeCountVO(list, todayDataList);
    }


    @Override
    public List<TypeCountVO> getProtocolTypeAggregate(ScreenTypeEnum screenTypeEnum) {
        BaseSearchVO baseSearchVO = new BaseSearchVO();
        baseSearchVO.setStartTimePartition(ScreenTypeEnum.getTimeCKPartition(screenTypeEnum));
        List<TypeCountVO> e1DataList = e1CommLogViewMapper.getProtocolTypeAggregate(baseSearchVO);
        List<TypeCountVO> e2DataList = e2MachineLearningLogViewMapper.getProtocolTypeAggregate(baseSearchVO);
        e1DataList.addAll(e2DataList);
        return AggregateDataUtil.mergeTypeCountVO(e1DataList);
    }

    @Override
    public DateHistogramCombinationVO getDateHistogram(ScreenTypeEnum screenTypeEnum) {
        DateHistogramCombinationVO result = new DateHistogramCombinationVO();
        BaseSearchVO baseSearchVO = new BaseSearchVO();
        Long startTimePartition = ScreenTypeEnum.getTimeCKPartition(screenTypeEnum);
        baseSearchVO.setStartTimePartition(startTimePartition);
        baseSearchVO.setScreenTypeEnumCode(screenTypeEnum.getCode());
        baseSearchVO.setEndTimePartition(ScreenTypeEnum.getTimeCKPartition(ScreenTypeEnum.TODAY));
        List<TypeCountVO> e1List = e1CommLogViewMapper.getDateHistogram(baseSearchVO);
        List<TypeCountVO> e2List = e2MachineLearningLogViewMapper.getDateHistogram(baseSearchVO);
        baseSearchVO.clear();
        baseSearchVO.setTodayTimePartition(ScreenTypeEnum.getTimeCKPartition(ScreenTypeEnum.TODAY));
        Long e1TodayCount = e1CommLogViewMapper.countNum(baseSearchVO);
        Long e2TodayCount = e2MachineLearningLogViewMapper.countNum(baseSearchVO);
        List<DateHistogramVO> dataList = AggregateDataUtil.mergeE1AndE2TypeCountVO(e1List, e2List);
        AtomicReference<Long> total = new AtomicReference<>(0l);
        dataList.forEach(dateHistogramVO -> {
            dateHistogramVO.setReport(dateHistogramVO.getE1Num() + dateHistogramVO.getE2Num());
            total.set(total.get() + dateHistogramVO.getE1Num() + dateHistogramVO.getE2Num());
        });
        result.setTotalReport(total.get());
        result.setDateHistogramDTOList(dataList);
        result.setTodayReport(e1TodayCount + e2TodayCount);
        return result;
    }

    @Override
    public List<TypeCountVO> getVpnTypeAggregate(ScreenTypeEnum screenTypeEnum) {
        BaseSearchVO baseSearchVO = searchParamUtil.getBaseSearchVOWithTimePartition(screenTypeEnum);
        List<TypeCountVO> e1List = e1CommLogViewMapper.getVpnTypeAggregate(baseSearchVO);
        cleanUselessData(e1List);
        Long e2Count = e2MachineLearningLogViewMapper.countNum(baseSearchVO);
        if (e2Count > 0l) {
            TypeCountVO typeCountVO = new TypeCountVO();
            typeCountVO.setKey("1000");
            typeCountVO.setNum(e2Count);
            e1List.add(typeCountVO);
        }
        return e1List;
    }
    private void cleanUselessData(List<TypeCountVO> e1List){
        if(e1List==null || e1List.isEmpty()) return;
        List<GenericSysDataDict> dictList = genericSysDataDictService.getDictList(AntiVpnDataDictTypeEnum.INTELLIGENCE_CONTENT_TYPE);
        if(dictList.isEmpty()) return;
        Map<Long,GenericSysDataDict> map = new HashMap<>();
        dictList.forEach(dict -> {
            map.put(Long.valueOf(dict.getEnumKey()),dict);
        });
        Iterator<TypeCountVO> iterator = e1List.iterator();
        while (iterator.hasNext()){
            TypeCountVO vo = iterator.next();
            if(vo.getKey() == null || "".equals(vo.getKey())) {
                iterator.remove();
                continue;
            }
            Long longKey = Long.valueOf(vo.getKey());
            if(!map.containsKey(longKey)) iterator.remove();
        }
    }

    @Override
    public List<TypeCountVO> getSystemLogAggCount(ScreenTypeEnum screenTypeEnum) {
        BaseSearchVO baseSearchVO = searchParamUtil.getBaseSearchVOWithTimePartition(screenTypeEnum);
        List<TypeCountVO> e1List = e1CommLogViewMapper.getSystemLogAggCount(baseSearchVO);
        List<TypeCountVO> e2List = e2MachineLearningLogViewMapper.getSystemLogAggCount(baseSearchVO);
        e1List.addAll(e2List);
        return AggregateDataUtil.mergeSameKeyTypeCountVO(e1List);
    }

    @Override
    public Map<String, List<String>> getMonitorDataMap() {
        Map<String, List<String>> map = new HashMap<>();
        Long today = ScreenTypeEnum.getTimeCKPartition(ScreenTypeEnum.TODAY);
        Integer hour = DateUtil.getNowHour();
        List<MonitorDataVO> e1MonitorDataVOList = e1CommLogViewMapper.getSystemProvinceCount(today,hour);
        List<String> e1MonitorDataVOListString = new ArrayList<>();
        e1MonitorDataVOList.forEach(vo -> e1MonitorDataVOListString.add(vo.toString()));
        List<MonitorDataVO> e2MonitorDataVOList = e2MachineLearningLogViewMapper.getSystemProvinceCount(today,hour);
        List<String> e2MonitorDataVOListString = new ArrayList<>();
        e2MonitorDataVOList.forEach(vo -> e2MonitorDataVOListString.add(vo.toString()));
        List<MonitorDataVO> e4MonitorDataVOList = e4DnsLogMapper.getSystemProvinceCount(today,hour);
        List<String> e4MonitorDataVOListString = new ArrayList<>();
        e4MonitorDataVOList.forEach(vo -> {
            e4MonitorDataVOListString.add(vo.toString());
        });
        List<ProvincePlatformStatus> provincePlatformStatusList = provincePlatformStatusMapperExt.monitorList();
        List<String> provincePlatformStatusListString = new ArrayList<>();
        provincePlatformStatusList.forEach(vo -> provincePlatformStatusListString.add(vo.toString()));
        map.put("E1话单", e1MonitorDataVOListString);
        map.put("E2话单", e2MonitorDataVOListString);
        map.put("E4话单", e4MonitorDataVOListString);
        map.put("E6话单", provincePlatformStatusListString);
        return map;
    }




}
