package com.eversec.antivpn.log.api.dto.base;

import java.io.Serializable;
import lombok.Data;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/8/16 14:15
 **/
@Data
public class VpnAggDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String vpnServiceName;
    private String resourceNodeName;
    private Integer personNum = 0;
    private Double percent;
    //用户数量
    private Integer customerNum = 0;
    //服务次数
    private Long serviceTimes = 0l;
    //资源节点数量
    private Integer resourceNodeNum = 0;
}
