package com.eversec.antivpn.log.service;

import com.eversec.antivpn.emnus.ScreenTypeEnum;
import com.eversec.antivpn.log.api.dto.base.AggCountWeekDTO;
import com.eversec.antivpn.log.mapper.vo.*;

import java.util.List;

public interface VPNServiceOverallService {
    Long getVPNServiceCount();
    Long getResourceNodeCount();
    //近7日活跃的VPN服务商数量
    AggCountWeekDTO getLastSevenDayActiveVPNServiceNum();
    //近7日活跃的资源节点数量
    AggCountWeekDTO getLastSevenDayActiveResourceNodeNum();
    List<VpnAggVO> getUsedResourceNodeTop10(ScreenTypeEnum screenTypeEnum);
    List<VpnHistogramAggVO> getVpnServiceAndResourceNodeDateHistogram(ScreenTypeEnum screenTypeEnum);
    List<VpnMapAggVO> map(ScreenTypeEnum screenTypeEnum);

    List<TypeCountVO> getCountryResourceNodeNumTop10(ScreenTypeEnum screenTypeEnum);

    List<TypeCountVO> getVpnServicePersonNumTop10(ScreenTypeEnum screenTypeEnum);

    List<VpnServicePageVO> getTheNewestVpnService(Integer num);
}
