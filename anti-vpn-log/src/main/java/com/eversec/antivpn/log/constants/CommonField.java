package com.eversec.antivpn.log.constants;

/**
 * 公共字段数据状态
 */
public class CommonField {


	/**
	 * E2 话单字段
	 */
	/** 源IP
	 */
	public static final String SRC_IP = "srcIp";

	/** 目的IP
	 */
	public static final String DEST_IP = "destIp";

	/** 传输层协议类型	1：TCP
	 */
	public static final Long  PROTOCOL_TYPE_TCP = 1L;

	public static final String PROTOCOL_TYPE_TCP_STR = "TCP";
	/** 输层协议类型	2：UDP
	 */
	public static final Long PROTOCOL_TYPE_UDP = 2L;

	public static final String PROTOCOL_TYPE_UDP_STR = "UDP";


	/** 流量类型1：入向；2：出向。。
	 */
	public static final Integer TRAFFIC_TYPE = 1;

}
