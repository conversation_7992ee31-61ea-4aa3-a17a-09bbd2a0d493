package com.eversec.antivpn.log.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.eversec.antivpn.log.entity.E4DnsLog;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.eversec.antivpn.log.mapper.vo.MonitorDataVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-08
 */
@Mapper
@DS("clickhouse")
public interface E4DnsLogMapper extends BaseMapper<E4DnsLog> {

    List<MonitorDataVO> getSystemProvinceCount(@Param("day") Long day, @Param("hour") Integer hour);
}
