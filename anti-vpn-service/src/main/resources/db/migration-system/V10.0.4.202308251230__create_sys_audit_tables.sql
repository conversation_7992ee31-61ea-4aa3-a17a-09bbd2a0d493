-- 由于epb-system在启动时没有创建下面两个表，初始化下面两个表解决初始部署后登录报错问题
CREATE TABLE IF NOT EXISTS `sys_audit_login_log` (
   `login_app_key` varchar(32) DEFAULT NULL,
   `login_app_name` varchar(512) DEFAULT NULL,
   `login_ip` varchar(32) DEFAULT NULL COMMENT '登录的IP',
   `login_time` bigint(20) DEFAULT NULL COMMENT '登录的时间',
   `logout_app_key` varchar(32) DEFAULT NULL,
   `logout_app_name` varchar(512) DEFAULT NULL,
   `logout_ip` varchar(32) DEFAULT NULL COMMENT '退出时的ip',
   `logout_time` bigint(20) DEFAULT NULL COMMENT '退出时间',
   `user_id` bigint(20) DEFAULT NULL COMMENT '用户ID',
   `user_name` varchar(32) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '用户账号（用户名）',
   `session_id` varchar(64) DEFAULT NULL COMMENT '登录的会话ID',
   `real_name` varchar(64) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '登录人的真实名称',
   `login_status` varchar(64) DEFAULT NULL,
   `login_type` varchar(64) DEFAULT NULL,
   `ua` varchar(1024) DEFAULT NULL,
   `login_desc` varchar(1024) DEFAULT NULL,
   `role_type` varchar(64) DEFAULT NULL,
   `role_name` varchar(256) DEFAULT NULL,
   `login_location` varchar(256) DEFAULT NULL,
   `auth` varchar(2048) DEFAULT NULL,
   KEY `idx_sys_audit_login_log_login_time` (`login_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='登录日志表';

CREATE TABLE IF NOT EXISTS `sys_audit_opt_log` (
     `user_id` bigint(20) DEFAULT NULL COMMENT '用户ID',
     `user_name` varchar(32) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '用户名',
     `app_key` varchar(32) DEFAULT NULL COMMENT '操作的应用',
     `app_name` varchar(128) DEFAULT NULL COMMENT '操作的应用',
     `opt_ip` varchar(32) DEFAULT NULL COMMENT '操作时的IP',
     `opt_time` bigint(20) NOT NULL COMMENT '操作时的时间',
     `url` varchar(255) DEFAULT NULL COMMENT '访问的后台地址',
     `request` text COMMENT '请求参数',
     `response` longtext,
     `remark` varchar(2048) DEFAULT NULL COMMENT '操作描述',
     `exec_time` int(8) DEFAULT NULL COMMENT '操作的执行时长',
     `req_scheme` varchar(16) DEFAULT NULL COMMENT '请求协议类型',
     `req_protocol` varchar(16) DEFAULT NULL COMMENT '请求协议',
     `req_method` varchar(8) DEFAULT NULL COMMENT '请求方式（get/post/put/delete）',
     `rsp_msg` varchar(2048) DEFAULT NULL COMMENT '响应描述',
     `rsp_status` varchar(8) DEFAULT NULL COMMENT '响应的状态码',
     `modules` varchar(128) DEFAULT NULL COMMENT '操作的模块',
     `aaaa_log` varchar(128) DEFAULT NULL,
     `note_1` varchar(128) DEFAULT NULL COMMENT '扩展字段',
     `note_2` varchar(128) DEFAULT NULL COMMENT '扩展字段',
     `note_3` varchar(128) DEFAULT NULL COMMENT '扩展字段',
     `session_id` varchar(64) DEFAULT NULL COMMENT '会话ID',
     `permission` varchar(1024) DEFAULT NULL COMMENT '操作时的权限',
     `real_name` varchar(64) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '用户的真实名称',
     `role_type` varchar(64) DEFAULT NULL,
     `audit_status` int(2) DEFAULT '0',
     `auditor` varchar(64) DEFAULT NULL,
     `audit_time` bigint(20) DEFAULT NULL,
     `audit_comment` varchar(1024) DEFAULT NULL,
     `role_name` varchar(256) DEFAULT NULL,
     KEY `idx_sys_audit_opt_log_opt_time` (`opt_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='操作日志表';
