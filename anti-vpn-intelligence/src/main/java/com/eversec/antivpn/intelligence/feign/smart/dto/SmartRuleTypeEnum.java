package com.eversec.antivpn.intelligence.feign.smart.dto;

/**
 * smart 规则类型
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023-08-09 18:02
 */
public enum SmartRuleTypeEnum {

	URL(1, "URL"),
	DOMAIN(2, "泛域名匹配"),
	IP(3, "IP（支持v4、v6地址）"),
	IP_PORT(7, "ipV4-port(规则格式ipv4xxx.xxx.xxx.xxx-xx)"),
	VPN_PROTOCOL(102, "VPN协议"),

	;


	SmartRuleTypeEnum(int code, String name){
		this.code = code;
		this.name = name;
	}

	private final int code;
	private final String name;

	public int getCode() {
		return code;
	}

}
