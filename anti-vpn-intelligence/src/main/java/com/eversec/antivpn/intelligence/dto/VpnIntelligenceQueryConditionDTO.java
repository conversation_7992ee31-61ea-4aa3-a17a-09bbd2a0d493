package com.eversec.antivpn.intelligence.dto;

import com.baomidou.mybatisplus.core.enums.SqlLike;
import com.eversec.antivpn.intelligence.entity.VpnIntelligence;
import com.eversec.stark.common.component.criteria.annotaion.Eq;
import com.eversec.stark.common.component.criteria.annotaion.Ge;
import com.eversec.stark.common.component.criteria.annotaion.Le;
import com.eversec.stark.common.component.criteria.annotaion.Like;
import com.eversec.stark.common.component.criteria.query.AbstractQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 情报库分页查询参数
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023-08-08 10:21
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class VpnIntelligenceQueryConditionDTO extends AbstractQuery<VpnIntelligence> {

	@Eq(alias = "command_id")
	@Schema(description = "跨境通信情报库下发执行指令ID。当部侧下发时由部侧系统控制惟一；当企业侧系统上报时企业侧内惟一。")
	private Long commandId;

	@Eq(alias = "version")
	@Schema(description = "跨境通信情报库版本格式：主版本号.副版本号，本规范主版本号为1，副版本号为0，即1.0。")
	private String version;

	@Eq(alias = "com_code")
	@Schema(description = "运营商编码，见附录F.2")
	private String comCode;

	@Eq(alias = "province_id")
	@Schema(description = "省级区域编号，见附录F.4")
	private Long provinceId;

	@Eq(alias = "system_code")
	@Schema(description = "省平台业务系统标识。省平台部署：当前省平台业务系统标识；集团部署：省平台业务系统标识")
	private String systemCode;

	@Eq(alias = "network_business_id")
	@Schema(description = "监测网络类型code，见附录F.3。企业侧上报时必填。部侧下发时可选填，当部侧下发为空时表示所有网络类型生效")
	private Integer networkBusinessId;

	@Like(alias = "rule_id", like = SqlLike.RIGHT)
	@Schema(description = "规则id，14位")
	private Long ruleId;

	@Eq(alias = "source")
	@Schema(description = "情报来源，FULL：全量下发。INCREASE：增量下发。REPORT：省端上报。INPUT：页面录入")
	private String source;

	@Eq(alias = "type_id")
	@Schema(description = "情报类型code： 1-VPN服务地址,例:提供VPN下载、注册、登录、购买、登出等各类访问地址和链接； 2-VPN 跨境服务地址,例：海外域名通道或海外IP通道地址； 3-模型研判； 4-样本库； 999-其他。 当操作类型为0、1、2时必填，当操作类型为3、4、5时选填。 ")
	private Integer typeId;

	@Like(alias = "vpn_id", like = SqlLike.RIGHT)
	@Schema(description = "情报库ID，当部侧系统下发时必填；企业侧系统上报、全量库下发时可不填写")
	private Long vpnId;

	@Like(alias = "vpn_name")
	@Schema(description = "Vpn名称或文件名称，当操作类型为0、1、2时填写VPN名称，当操作类型为3时填写的是全量跨境通信情报库文件名；当操作类型为4、5时填写的是模型或样本文件名，多个用|线分隔；当填写Vpn名称时，优先使用中文名称，没有中文名称时使用英文名称。")
	private String vpnName;

	@Like(alias = "vpn_domain")
	@Schema(description = "域名。VPN活动所使用的域名地址。")
	private String vpnDomain;

	@Eq(alias = "vpn_ip")
	@Schema(description = "IP地址,VPN活动所使用的IP地址；IPv4使用点分表示法，IPv6使用冒分小写0位压缩表示法。")
	private String vpnIp;

	@Like(alias = "vpn_url")
	@Schema(description = "URL地址,VPN活动所使用的URL地址，采用base64编码后的URL。")
	private String vpnUrl;

	@Eq(alias = "vpn_message")
	@Schema(description = "报文（最少支持snort规则，版本语法为2.9)")
	private String vpnMessage;

	@Eq(alias = "vpn_link")
	@Schema(description = "取证链接,包含VMess、VLESS、Shadowsocks、Socks、Trojan等链接地址。企业侧系统上报时根据实际情况填写。")
	private String vpnLink;

	@Eq(alias = "content_type")
	@Schema(description = "匹配内容类型。1-域名；2-IP；3-URL；4-报文（最少支持snort规则，版本语法为2.9)；5-VPN协议（具体参见F.5节）；999-其他。")
	private Integer contentType;

	@Schema(description = "匹配内容。当类型为3和4时采用base64编码后结果。当类型为5时填写的是VPN协议编号。")
	private String vpnContent;

	@Eq(alias = "vpn_port")
	@Schema(description = "端口")
	private String vpnPort;

	@Eq(alias = "vpn_airport_code")
	@Schema(description = "vpn机场编码")
	private String vpnAirportCode;

	@Like(alias = "vpn_software_codes")
	@Schema(description = "vpn软件编码")
	private String vpnSoftwareCode;

	@Eq(alias = "vpn_protocol_code")
	@Schema(description = "vpn协议编码")
	private String vpnProtocolCode;

	@Eq(alias = "vpn_protocol_eversec_code")
	@Schema(description = "vpn协议编码（eversec）")
	private String vpnProtocolEversecCode;

	@Eq(alias = "report_status")
	@Schema(description = "上报状态，REPORTED：未上报，TO_BE_REPORTED：待上报")
	private String reportStatus;

	@Eq(alias = "distribute_status")
	@Schema(description = "下发状态，DISTRIBUTED：已下发，TO_BE_DISTRIBUTED：待下发")
	private String distributeStatus;

	@Ge(alias = "create_datetime")
	@Schema(description = "创建时间-start")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date createDatetimeStart;

	@Le(alias = "create_datetime")
	@Schema(description = "创建时间-end")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date createDatetimeEnd;

	@Ge(alias = "report_datetime")
	@Schema(description = "上报时间-start")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date reportDatetimeStart;

	@Le(alias = "report_datetime")
	@Schema(description = "上报时间-end")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date reportDatetimeEnd;

	@Ge(alias = "time_stamp")
	@Schema(description = "发现时间-start")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date timeStampStart;

	@Le(alias = "time_stamp")
	@Schema(description = "发现时间-end")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date timeStampEnd;



}
