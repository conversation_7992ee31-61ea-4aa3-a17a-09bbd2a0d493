package com.eversec.antivpn.support.knowledge.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <p>
 * 软件信息DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-12
 */
@Schema(name = "KnowledgeSoftwareDTO", description = "软件信息")
@EqualsAndHashCode(callSuper=false)
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class KnowledgeSoftwareDTO extends KnowledgeBaseDTO {

    @NotNull(message = "logo不能为空")
    @Schema(description = "logo")
    private String logo;

    @NotNull(message = "支持系统不能为空")
    @Schema(description = "支持系统")
    private String supportSystem;

    @NotNull(message = "支持协议不能为空")
    @Schema(description = "支持协议")
    private String supportProtocol;

    @NotNull(message = "开发者信息不能为空")
    @Schema(description = "开发者信息")
    private String issuer;

    @NotNull(message = "授权类型不能为空")
    @Schema(description = "授权类型")
    private String authType;

    @NotNull(message = "下载站点不能为空")
    @Schema(description = "下载站点")
    private String downloadWebsite;

    @NotNull(message = "简介不能为空")
    @Schema(description = "简介")
    private String introduction;

    @NotNull(message = "发现时间不能为空")
    @Schema(description = "发现时间")
    private String discoveryTime;

//    @NotNull(message = "入库时间不能为空")
//    @Schema(description = "入库时间")
//    private String insertTime;

}