import httpRequest from "@/utils/httpRequest";

const prefix = window.globalConfig.bi_query_useGateway ? "/everbi" : ""
const url = prefix + "/OlapQueryController/V2";

function encryptParams(o) {
  if (o.advancedFilter) o.advancedFilter = encryptAES(o.advancedFilter);
  o.advancedFilters = o.advancedFilters.map(item => encryptAES(item));
}

export default {
  getList(o) {
    encryptParams(o);

    return httpRequest({
      url: url + "/sec/query",
      method: "post",
      data: o
    });
  },
  getListByPage(o) {
    encryptParams(o);

    return httpRequest({
      url: url + "/query/sec/page",
      method: "post",
      data: o
    });
  }
};
