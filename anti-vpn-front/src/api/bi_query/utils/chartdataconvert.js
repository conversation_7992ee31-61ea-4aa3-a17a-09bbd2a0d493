/**
 * 获取过滤条件
 * @param {*} filters
 */
export function buildFilter(filters) {
  function buildColumn(data) {
    function checkedDate(data) {
      if (data.data instanceof Array) {
        data.minData = data.data[0];
        data.maxData = data.data[1];
        delete data.data;
      }
    }
    function checkedText(data) {
      if (data.expression == "like") data.data = "%" + data.data + "%";
      else if (data.expression == "llike") {
        data.data = "%" + data.data;
        data.expression = "like";
      } else if (data.expression == "rlike") {
        data.data = data.data + "%";
        data.expression = "like";
      }
    }
    function checkedNumber(data) {}

    if (data.column.dataType == 0) checkedText(data.condition);
    else if (data.column.dataType == 1) checkedNumber(data.condition);
    else checkedDate(data.condition);

    data.column.value = data.column.value || data.column.uuid;
    data.alias = data.column.value;   // 用于高级筛选栏
  }

  function buildChildren(parent) {
    if (parent.children) {
      for (let index = 0; index < parent.children.length; index++) {
        const child = parent.children[index];
        buildChildren(child);
      }
    } else {
      buildColumn(parent);
    }
  }

  // 单条件
  if (filters.children == null) {
    buildColumn(filters);
    return {
      relationType: 1,
      children: [filters]
    };
  }

  // 多条件
  buildChildren(filters);
  return filters;
}
