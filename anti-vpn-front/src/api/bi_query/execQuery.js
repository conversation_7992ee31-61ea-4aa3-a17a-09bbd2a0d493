// 针对某一数据源直接执行sql语句
import httpRequest from "@/utils/httpRequest";
import { encryptAES } from "./utils/des";

const prefix = window.globalConfig.bi_query_useGateway ? "/everbi" : ""
const url = prefix + "/exec/" + window.globalConfig.bi_query_dsId + "/sec/query";

export default {
  execQuerySql(o, cancel) {
    if (o.strSql) o.strSql = encryptAES(o.strSql);

    const data = {
      dsId: window.globalConfig.bi_query_dsId,
      strSql: o.strSql,
      paging: o.paging || 1,
      limit: o.limit || 10
    };
    return httpRequest({
      url,
      method: "get",
      params: data,
      cancel
    });
  }
};
