// 全局配置
window.globalConfig = {
  /**
   * 环境配置
   */
  DOMAIN: "", // 域配置，配合 evercloud 使用，将域名中需要变动的部分放在这里，例如：everbi-develop.wh.everark.com.cn，然后在配置地址时使用类似 http://everbi-front.${DOMAIN} 以便切换环境。当值含有 ip 地址时，将替换整个域名

  /**
   * 免密配置（详见 /guest/index.html，注意后台不能开启验证码）
   */
  guestUser: "", // 游客身份的用户名
  guestPwd: "", // 游客身份的密码

  /**
   * 基础配置
   */
  defaultProxyPath: "./service", // 默认代理路径，开发环境下需要与 vue.config.js 中的代理配置对应，生产环境下需要与 nginx 中的代理配置对应
  requestTimeout: undefined, // 请求超时时间（undefined 表示不做控制），单位毫秒，直接填入数字即可，示例：1000 * 30，表示 30 秒
  showRecommendedBrowser: true, // 显示推荐使用 Chrome 浏览器的提示
  forcePasswordChange: true, // 密码过期时，强制修改密码（无法跳过）
  lockScreenTime: undefined, // 长时间无交互自动锁屏（undefined 或 0 表示不自动锁屏），单位毫秒，直接填入数字即可，示例：1000 * 60 * 10，表示 10 分钟
  iframePreload: true, // 对 iframe 嵌入的第三方应用进行预载（IE除外），当 useTabs 为 false 时，还会进行预渲染
  iframePreloadDelay: 5000, // iframe 页面预载的延迟时间，单位毫秒（因预载开始时可能出现卡顿，请选择合适的时机）
  triggerParentLogout: true, // session 失效时，触发父系统（需基于 evervue）登出
  allowLogoutByIframe: false, // 允许 iframe 嵌入的第三方应用触发当前系统登出

  /**
   * 菜单与权限
   */
  prdKey: "", // 产线关键字，不为空时仅获取该产线下的应用和菜单（可选）
  appKey: "anti-vpn", // 应用关键字，登录时所需参数，将作为默认应用，以及应用切换时的原始应用（必填）
  showAppSelect: false, // 显示应用切换，开启后总是只显示当前应用下的菜单（无视 showFullMenu 和 showSystemMenu），呈现在引导页的应用才会显示
  appSelectStyle: 1, // 应用切换样式，0 下拉框 | 1 顶部平铺
  showFullMenu: false, // 显示完整菜单，true 将显示该用户在各app（包括系统管理）下所有菜单的组合，false 仅显示当前appKey对应的app下的菜单
  showSystemMenu: true, // 显示系统管理菜单，前提是用户拥有系统管理的菜单权限
  menuStyle: 1, // 菜单样式，0 经典 | 1 右侧展开
  homePageUrl: "", // 默认首页地址（留空时将自动采用第一个菜单页面），支持第三方页面、菜单页面、未配置成菜单的页面，支持 ${ip} 和 ${port} 的转义
  homePageIsGlobal: false, // 默认首页是否全局页面（即没有顶部栏、左侧菜单栏、底部栏），仅当 homePageUrl 非空时生效
  verifyAuth: false, // 是否开启 license 授权校验

  /**
   * 显示与交互
   */
  useTabs: false, // 使用标签形式打开页面
  pageAdaptToWindow: true, // 页面适应窗口，true 页面高度与窗口一致，侧边栏和内容在div内滚动，false 页面自然增长高度
  headerHeight: 50, // 顶部栏高度，单位 px
  mainPadding: 20, // 正文内容内边距（iframe除外），单位 px
  showFooter: false, // 显示底部栏
  asideMenuWidth: 200, // 左侧菜单栏宽度，单位 px
  asideMenuUniqueOpened: true, // 左侧菜单只保持一个子菜单的展开
  asideMenuCollapse: false, // 左侧菜单默认折叠
  themes: [
    { name: "default", label: "默认主题" },
    { name: "orange", label: "醒目橙色" },
    { name: "blue", label: "蓝色海洋" },
    { name: "dark", label: "浩瀚星空" },
    { name: "dark-blue", label: "深蓝波纹" },
    { name: "light", label: "清新淡雅" },
    { name: "blue-nssa", label: "金御蓝色" }
  ], // 平台主题列表，对应 index.html 目录下的 themes 文件夹
  theme: "blue-nssa", // 初始平台主题（需从主题列表中选择），仅在 localStorage 中没有该值或不显示平台主题切换控件时生效
  siteMenuMode: "vertical", // 初始菜单显示位置 vertical（左侧纵向） / horizontal（顶部横向），仅在 localStorage 中没有该值时生效
  showErrorLog: false, // 显示顶部栏异常日志控件
  showChangeTheme: true, // 显示顶部栏平台主题切换控件
  showUserOpt: true, // 显示顶部栏用户操作控件
  showChangePassword: true, // 显示修改密码按钮（用户操作下拉列表中）
  showChangeMenuMode: true, // 显示菜单位置切换按钮（用户操作下拉列表中）
  showLockScreen: true, // 显示锁屏按钮（用户操作下拉列表中）
  showVersionInfo: true, // 显示版本信息（用户操作下拉列表中及登录页中）
  pageTitleBreadcrumb: true, // 页面标题栏显示面包屑
  useMainPageTitle: true, // 使用主框架的统一页面标题（此时各页面内的标题不会显示）
  showContentOnlyInIframe: true, // 被 iframe 嵌入时，仅显示内容（被嵌方）
  showContentPageTitleInIframe: true, // 被 iframe 嵌入时，显示内容中的标题（被嵌方）
  showPageTitleInIframe: false, // iframe 页面中显示页面标题（嵌别人）
  useIconfont: true, // 使用 iconfont 图标，需在 index.html 同目录下存在相应的 iconfont 文件夹（从官网下载资源包后解压得到）
  uiSize: "small", // Element UI 组件的默认尺寸 large / medium / small / mini

  /**
   * logo、标题和文字
   * 登录页主标题和副标题都为空时，显示默认图片标题
   */
  loginTitle: "", // 登录页主标题
  loginSubTitle: "", // 登录页副标题
  loginPageUrl: "", // 自定义登录页地址
  headerLogo: "", // 顶部栏 logo（相对于 index.html 的路径，示例：logo.png，没有 logo 时留空，注意不要用中文）
  headerTitle: "EVERSEC", // 顶部栏标题（同时会替换网页 title）
  footerInfo: "恒安嘉新（北京）科技股份公司-技术支持", // 底部栏文字

  /**
   * 使用 BI 增强
   */
  useBiDic: true, // 使用 bi 增强数据字典

  useBiCompass: false, // 是否使用应用导航功能（需要bi后台接口支持）
  compassBtnName: "应用导航", // 导航按钮的名字
  compassMode: 1, // 0 仅显示叶子节点菜单 1 仅显示第一级菜单
  compassStyle: 1, // 0 原版样式 1 everone样式
  // 仅当 compassStyle 为 0 时，以下设置才能看到效果
  showCompassPageAfterLogin: true, // 登录成功后进入默认页时，是否自动弹出旧版导航页
  // 仅当 compassStyle 为 1 时，以下设置才能看到效果
  isEverOne: false, // 是否产品能力展示中心
  showCustomApp: false, // 应用导航中显示自定义应用
  compassSubBtn: {
    // 应用导航副按钮
    name: "", // 按钮名称
    url: "", // 跳转地址，例如：/#/sys/app?_app_=system
    style: { color: "orange", fontSize: "16px" } // 按钮 css 样式
  },
  deployedAlias: "已部署", // 应用导航中“已部署”标签的别名
  undeployedAlias: "未部署", // 应用导航中“未部署”标签的别名

  /**
   * 应用级别整体个性化设置，其中 xxx 为 appkey，而页面级别的设置可通过 this.$emit("onUpdateMain", {}) 单独抛出，详见 main.vue
   */
  appSettings: {
    // xxx: {
    //   siteMenu: {
    //     hidden: true,
    //   },
    //   pageTitle: {
    //     hidden: true,
    //   },
    //   pageContent: {
    //     padding: 0,
    //     background: "transparent",
    //   },
    // }
  },
  situation: {
    bigScreenCurI: 2
  },
  noLogin: {
    userName: "vpn", //账号
    password: "vR^V-HO(Dh*ip2GA" //密码
  }

  /**************************************************************************************
   * Docker 用来设置环境变量的埋点（为注释状态），必须在结尾，不要删
   **************************************************************************************/
  // ${GLOBAL_CONFIG_EXPAND}
};
