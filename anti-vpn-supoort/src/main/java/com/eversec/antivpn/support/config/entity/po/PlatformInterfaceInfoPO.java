package com.eversec.antivpn.support.config.entity.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eversec.stark.generic.common.infra.mybatis.plus.BaseDO;
import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 平台接口信息
 * </p>
 * <p>
 * 持久化对象，此对象与数据库属性一一对应，被Entity对象继承，扩展属性写在Entity对象中
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-18
 */
@Getter
@Setter
@TableName("platform_interface_info")
@Schema(name = "PlatformInterfaceInfoPO", description = "平台接口信息")
public class PlatformInterfaceInfoPO extends BaseDO {

    private static final long serialVersionUID = 1L;

    @Schema(description = "配置类型。CURRENT:当前系统; PROVINCE: 省平台。平台部署时，存在一条当前系统配置记录。")
    private String configType;

    @Schema(description = "是否生效,false不生效，true生效。是否生效只针对PROVINCE类型记录起作用")
    private Boolean enabled;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "运营商编码。省平台部署：当前平台运营商编码；集团部署：运营商编码")
    private String comCode;

    @Schema(description = "省级区域编号。省平台部署：当前平台省级区域编号；集团部署：省级区域编号")
    private Long provinceId;

    @Schema(description = "省平台业务系统标识。省平台部署：当前省平台业务系统标识；集团部署：省平台业务系统标识")
    private String systemCode;

    @Schema(description = "DC消费的目录。省平台部署：DC消费的目录；集团部署：DC消费的目录，每个省平台配置的目录不同，可通过该目录查询省平台信息")
    private String southReceiveDir;

    @Schema(description = "上报ftp地址。例：ftp://localhost:21/report-log/01-11")
    private String northReportFtpAddress;

    @Schema(description = "上报ftp username")
    private String northReportFtpUsername;

    @Schema(description = "上报ftp password")
    private String northReportFtpPassword;

    @Schema(description = "上报加密密钥")
    private String northReportSecretKey;

    @Schema(description = "情报库ftp地址。例：ftp://localhost:21/download-intelligence/01-11")
    private String northIntelligenceFtpAddress;

    @Schema(description = "情报库ftp username")
    private String northIntelligenceFtpUsername;

    @Schema(description = "情报库ftp password")
    private String northIntelligenceFtpPassword;

    @Schema(description = "情报库解密密钥")
    private String northIntelligenceSecretKey;

    @Schema(description = "上报企业状态-网络类型id，多类型|分割")
    private String northCurrentNetworkBusinessIds;

    @Schema(description = "情报库目录。省平台部署：不填写；集团部署：情报库目录，透传部侧情报库文件")
    private String southIntelligenceDir;

    @Schema(description = "集团调用省平台webservice地址，省平台调用CU地址，例如：http://localhost:8080/services")
    private String southWsAddress;

    @Schema(description = "接收解密密钥")
    private String southReceiveSecretKey;

    @Schema(description = "smart 服务地址。")
    private String southSmartAddress;

    @Schema(description = "smart 用户名。")
    private String southSmartUsername;

    @Schema(description = "smart 密码。")
    private String southSmartPassword;


}