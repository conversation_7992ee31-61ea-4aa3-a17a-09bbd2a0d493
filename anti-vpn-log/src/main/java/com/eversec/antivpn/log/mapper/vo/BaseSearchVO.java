package com.eversec.antivpn.log.mapper.vo;

import com.eversec.antivpn.emnus.ScreenTypeEnum;
import lombok.Data;

import java.util.List;


/**
 * @Description
 * <AUTHOR>
 * @Date 2023/8/18 17:30
 **/
@Data
public class BaseSearchVO {

    private Long todayTimePartition;
    private Long startTimePartition;
    private Long endTimePartition;
    private Long nowTimePartition;
    private String screenTypeEnumCode;
    private Integer vpnType;

    private String columnSql;
    private String groupBySql;
    private String whereSql;
    private String mapLocationSearchDimension;

    private List<String> strIds;

    public void clear() {
        this.todayTimePartition = null;
        this.startTimePartition = null;
        this.endTimePartition = null;
        this.screenTypeEnumCode = null;
        this.columnSql = null;
        this.groupBySql = null;
        this.whereSql = null;
    }

    public BaseSearchVO(){
        nowTimePartition = ScreenTypeEnum.getTimeCKPartition(ScreenTypeEnum.TODAY);
    }
}
