package com.eversec.antivpn.intelligence.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eversec.antivpn.intelligence.api.dto.VpnIntelligenceCommandDistributeDTO;
import com.eversec.antivpn.intelligence.api.dto.VpnIntelligenceCommandDistributeRequestDTO;
import com.eversec.antivpn.intelligence.api.dto.VpnIntelligenceCommandDistributeResponseDTO;
import com.eversec.antivpn.intelligence.api.dto.VpnIntelligenceIrcsCommandDistributeRequestDTO;
import com.eversec.antivpn.intelligence.api.enums.IntelligenceCommandOperationTypeEnum;
import com.eversec.antivpn.intelligence.dto.VpnIntelligenceCommandQueryConditionDTO;
import com.eversec.antivpn.intelligence.entity.VpnIntelligence;
import com.eversec.antivpn.intelligence.entity.VpnIntelligenceCommand;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.File;
import java.util.List;

/**
 * <p>
 * vpn情报指令 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-10
 */
@Validated
public interface IVpnIntelligenceCommandService extends IService<VpnIntelligenceCommand> {

    /**
     * 接收到指令就保存，用于回放，开启新事物，不回滚
     * @param requestDto
     * @param systemCode
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    Long saveDistributeCommand(VpnIntelligenceCommandDistributeRequestDTO requestDto, String systemCode);


    /**
     * 接收到指令就保存，用于回放，开启新事物，不回滚
     * @param requestDto
     * @param systemCode
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    Long saveIrcsDistributeCommand(VpnIntelligenceIrcsCommandDistributeRequestDTO requestDto, String comCode);




    @Transactional(propagation = Propagation.REQUIRES_NEW)
    Long saveImportCommand(VpnIntelligenceCommand entity);

    /**
     * 跨境通信情报库指令下发
     * @param paramDto
     */
    List<VpnIntelligence> commandDistribute(Long commandEntityId, @RequestBody @Valid final VpnIntelligenceCommandDistributeDTO paramDto, String systemCode, Long reDistributeByCommandId);

    /**
     * 保存数据到数据库，开启新事物，不回滚
     * @param intelligenceCommandOperationTypeEnum
     * @param vpnIntelligenceCommand
     * @param vpnIntelligenceList
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    void saveCommandToDb(IntelligenceCommandOperationTypeEnum intelligenceCommandOperationTypeEnum, VpnIntelligenceCommand vpnIntelligenceCommand, List<VpnIntelligence> vpnIntelligenceList);


    /**
     * 更新解析状态
     * @param vpnIntelligenceCommand
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    void updateCommandParseStatus(VpnIntelligenceCommand vpnIntelligenceCommand);


    /**
     * 更新指令处理状态
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    void updateCommandProcessStatus(Long commandEntityId, VpnIntelligenceCommandDistributeResponseDTO resultDto, Exception responseException);

    /**
     * 根据commandId查询
     * @param commandId
     * @return
     */
    VpnIntelligenceCommand findByCommandId(@NotNull Long commandId);

    Page<VpnIntelligenceCommand> page(VpnIntelligenceCommandQueryConditionDTO param, Page<VpnIntelligenceCommand> pageInfo);

    /**
     * 调用南向接口
     *
     */
    VpnIntelligenceCommandDistributeResponseDTO callSouth(VpnIntelligenceCommandDistributeRequestDTO requestDto, String systemCode);
    VpnIntelligenceCommandDistributeResponseDTO ircsCallSouth(VpnIntelligenceIrcsCommandDistributeRequestDTO requestDto, Long provinceId, String comCode);


    /**
     * 调用南向webservice接口
     * @param requestDto
     * @param systemCode
     * @return
     */
    VpnIntelligenceCommandDistributeResponseDTO callSouthWs(VpnIntelligenceCommandDistributeRequestDTO requestDto, String systemCode);

    /**
     * 调用南向webservice接口
     * @param requestDto
     * @param systemCode
     * @return
     */
    VpnIntelligenceCommandDistributeResponseDTO ircsCallSouthWs(VpnIntelligenceIrcsCommandDistributeRequestDTO requestDto, Long provinceId, String comCode);

    /**
     * 获取指令下发对应的文件
     * 当指令目录下存在多个文件时，将多个文件打包成一个文件进行下载
     * 当目录下存在一个文件，直接下载
     *
     * @param id
     * @return
     */
    File getCommandDistributeFile(Long id);

    /**
     * 指令重新下发
     * @param id
     */
    void reDistributeCommand(Long id);

}
