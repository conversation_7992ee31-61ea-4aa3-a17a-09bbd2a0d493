<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eversec.antivpn.log.mapper.E1CommLogViewMapper">
    <resultMap id="TypeCount" type="com.eversec.antivpn.log.mapper.vo.TypeCountVO">
        <result column="num" jdbcType="BIGINT" property="num"/>
        <result column="key_type" jdbcType="INTEGER" property="key"/>
    </resultMap>

    <sql id="viewBaseSql">
        (
            SELECT
                day,
                hour,
                anyMerge(month) AS month,
                anyMerge(week) AS week,
                province_id,
                vpn_type,
                vpn_name,
                vpn_content,
                protocol_type,
                application_protocol,
                countMerge(log_count) AS log_count,
                system_code,
                log_city_id
        FROM e1_comm_log_view
        <where>
            <if test="vpnType!=null and vpnType != '' ">
                AND vpn_type = #{vpnType}
            </if>
            <if test="todayTimePartition!=null and todayTimePartition != '' ">
                AND day = #{todayTimePartition}
            </if>
            <if test="startTimePartition!=null and startTimePartition != ''">
                AND day >= #{startTimePartition}
            </if>
            <if test="endTimePartition!=null and endTimePartition != ''">
                AND day &lt;= #{endTimePartition}
            </if>
        </where>
            GROUP BY
                day,
                hour,
                province_id,
                protocol_type,
                application_protocol,
                vpn_type,
                vpn_name,
                vpn_content,
                system_code,
                log_city_id
        ) AS base_view
    </sql>
    <sql id="groupByTimeBaseSql">
        <if test="screenTypeEnumCode=='day'"> group by hour </if>
        <if test="screenTypeEnumCode=='week'"> group by day </if>
    </sql>
    <sql id="keyBaseSql">
        <if test="screenTypeEnumCode=='day'"> hour as key_type </if>
        <if test="screenTypeEnumCode=='week'"> day  as key_type </if>
        <if test="screenTypeEnumCode=='month'"> anyMerge(week) as key_type </if>
        <if test="screenTypeEnumCode=='sofar'"> anyMerge(month)  as key_type </if>
    </sql>

    <sql id="groupByMapLocation">
        group by
        <if test="mapLocationSearchDimension=='province'"> province_id </if>
        <if test="mapLocationSearchDimension=='city'"> log_city_id </if>
    </sql>
    <sql id="keyMapLocation">
        <if test="mapLocationSearchDimension=='province'"> province_id as key_type </if>
        <if test="mapLocationSearchDimension=='city'"> log_city_id  as key_type </if>
    </sql>
    <sql id="sumLogCountSql">
        sum(log_count) as num
    </sql>

    <sql id="whereBase">
        where 1=1
            <if test="vpnType!=null and vpnType != '' ">
                AND vpn_type = #{vpnType}
            </if>
            <if test="todayTimePartition!=null and todayTimePartition != '' ">
                AND day = #{todayTimePartition}
            </if>
            <if test="startTimePartition!=null and startTimePartition != ''">
                AND day >= #{startTimePartition}
            </if>
            <if test="endTimePartition!=null and endTimePartition != ''">
                AND day &lt;= #{endTimePartition}
            </if>
            <if test="nowTimePartition!=null">
                AND day &lt;= #{nowTimePartition}
            </if>
    </sql>

    <select id="countNum" resultType="java.lang.Long" parameterType="com.eversec.antivpn.log.mapper.vo.BaseSearchVO">
    select
        countMerge(log_count) as num
    from
        e1_comm_log_view
        <include refid="whereBase"></include>
    </select>


    <select id="getVpnTypeAggregate" resultMap="TypeCount" parameterType="com.eversec.antivpn.log.mapper.vo.BaseSearchVO" >
        SELECT
            countMerge(log_count) AS num,
            vpn_type as key_type
        FROM e1_comm_log_view
        <include refid="whereBase"></include>
        and vpn_type is not null
        GROUP BY
            vpn_type
    </select>

    <select id="getVpnType5ContentTypeAggregate" resultMap="TypeCount" parameterType="com.eversec.antivpn.log.mapper.vo.BaseSearchVO" >
        SELECT
        vpn_content as key_type,
        <include refid="sumLogCountSql"></include>
        FROM  <include refid="viewBaseSql"></include>
        GROUP BY
        vpn_content
    </select>
    <select id="getVpnNameAggregate" resultMap="TypeCount" parameterType="com.eversec.antivpn.log.mapper.vo.BaseSearchVO" >
        SELECT
            vpn_name as key_type,
            countMerge(log_count) AS num
        FROM e1_comm_log_view
        <include refid="whereBase"></include>
        and  vpn_name is not null and vpn_name != ''
        GROUP BY
            vpn_name order by num desc
            limit 10
    </select>
    <select id="getDateHistogram" resultMap="TypeCount" parameterType="com.eversec.antivpn.log.mapper.vo.BaseSearchVO">
        SELECT
        <include refid="keyBaseSql"></include>,
        countMerge(log_count) as num
        FROM  e1_comm_log_view
        <include refid="whereBase"></include>
        <include refid="groupByTimeBaseSql"></include>
        order by key_type
    </select>


    <select id="getProvinceAggregate" resultMap="TypeCount" parameterType="com.eversec.antivpn.log.mapper.vo.BaseSearchVO">
        SELECT
        <include refid="keyMapLocation"></include>
        ,
        countMerge(log_count) as num
        FROM  e1_comm_log_view
        <include refid="whereBase"></include>
        and key_type is not null
        <include refid="groupByMapLocation"></include>
    </select>
    <select id="getApplicationProtocolTypeAggregate" resultMap="TypeCount" parameterType="com.eversec.antivpn.log.mapper.vo.BaseSearchVO">
        SELECT
        application_protocol key_type,
        countMerge(log_count) AS num
        FROM  e1_comm_log_view
        <include refid="whereBase"></include>
        GROUP BY
        application_protocol
        order by num desc limit  10
    </select>


    <select id="getProtocolTypeAggregate" resultMap="TypeCount" parameterType="com.eversec.antivpn.log.mapper.vo.BaseSearchVO">
        SELECT
        protocol_type key_type,
        <include refid="sumLogCountSql"></include>
        FROM  <include refid="viewBaseSql"></include>
        GROUP BY
        protocol_type
    </select>


    <select id="getLocationAggregate" resultMap="TypeCount" parameterType="com.eversec.antivpn.log.mapper.vo.BaseSearchVO">
        SELECT
        province_id key_type,
        count(distinct (user)) num
        FROM  <include refid="viewBaseSql"></include>
        GROUP BY
        province_id
    </select>


    <select id="userCountLogNum" resultMap="TypeCount" parameterType="com.eversec.antivpn.log.mapper.vo.BaseSearchVO">
        SELECT
        user key_type,
        <include refid="sumLogCountSql"></include>
        FROM  <include refid="viewBaseSql"></include>
        GROUP BY
        user
    </select>


    <select id="getProtocolTypeAggregate" resultMap="TypeCount" parameterType="com.eversec.antivpn.log.mapper.vo.BaseSearchVO">
        SELECT
        protocol_type key_type,
        countMerge(log_count) AS num
        FROM  e1_comm_log_view
        <include refid="whereBase"></include>
        GROUP BY
        protocol_type
    </select>
    <select id="getSystemLogAggCount" resultMap="TypeCount" parameterType="com.eversec.antivpn.log.mapper.vo.BaseSearchVO">
        SELECT
        system_code key_type,
        countMerge(log_count)  as num
        FROM  e1_comm_log_view
        <include refid="whereBase"></include>
        GROUP BY system_code
    </select>

    <resultMap id="MonitorDataVO" type="com.eversec.antivpn.log.mapper.vo.MonitorDataVO">
        <result column="province_id" jdbcType="INTEGER" property="provinceId"/>
        <result column="day" jdbcType="INTEGER" property="day"/>
        <result column="hour" jdbcType="INTEGER" property="hour"/>
        <result column="system_code" jdbcType="VARCHAR" property="systemCode"/>
        <result column="num" jdbcType="BIGINT" property="num"/>
    </resultMap>
    <select id="getSystemProvinceCount" resultMap="MonitorDataVO">
        SELECT
        province_id,
        day,hour,
        system_code,
        countMerge(log_count)  as num
        FROM  e1_comm_log_view
        where day=#{day} and hour in (#{hour},#{hour}-1)
        GROUP BY system_code,province_id,day,hour
        ORDER BY province_id,system_code,day,hour
    </select>
</mapper>
