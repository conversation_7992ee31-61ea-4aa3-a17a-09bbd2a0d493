<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eversec.antivpn.log.mapper.E1CommLogMapper">

    <resultMap id="TypeCount" type="com.eversec.antivpn.log.mapper.vo.TypeCountVO">
        <result column="num" jdbcType="BIGINT" property="num"/>
        <result column="key_type" jdbcType="VARCHAR" property="key"/>
    </resultMap>

    <select id="getDateHistogram" resultMap="TypeCount">
        SELECT
        `day` key_type,
        count(1) AS num
        FROM e1_comm_log
        <where>
            <if test="startTimePartition!=null">
                AND day >= #{startTimePartition}
            </if>
            <if test="endTimePartition!=null">
                AND day &lt;= #{endTimePartition}
            </if>
        </where>
        GROUP BY
        day
        ORDER BY day ASC
    </select>
    <select id="getVpnTypeAggregate" resultMap="TypeCount">
        SELECT
        count(1) num,
        vpn_type key_type
        FROM e1_comm_log
        <where>
            <if test="startTimePartition!=null">
                AND day >= #{startTimePartition}
            </if>
            <if test="endTimePartition!=null">
                AND day &lt;= #{endTimePartition}
            </if>
        </where>
        GROUP BY
        vpn_type
    </select>

    <select id="getVpnType5ContentTypeAggregate" resultMap="TypeCount">
        SELECT
        count(1) num,
        vpn_content key_type
        FROM e1_comm_log
        <where>
            vpn_type = 5
            <if test="startTimePartition!=null">
                AND day >= #{startTimePartition}
            </if>
            <if test="endTimePartition!=null">
                AND day &lt;= #{endTimePartition}
            </if>
        </where>
        GROUP BY
        vpn_content
    </select>


    <select id="getApplicationProtocolTypeAggregate" resultMap="TypeCount">
        SELECT
        count(1) num,
        application_protocol key_type
        FROM e1_comm_log
        <where>
            <if test="startTimePartition!=null">
                day >= #{startTimePartition}
            </if>
            <if test="endTimePartition!=null">
                AND day &lt;= #{endTimePartition}
            </if>
        </where>
        GROUP BY
        application_protocol
    </select>

    <select id="getProtocolTypeAggregate" resultMap="TypeCount">
        SELECT
        count(1) num,
        protocol_type key_type
        FROM e1_comm_log
        <where>
            <if test="startTimePartition!=null">
                day >= #{startTimePartition}
            </if>
            <if test="endTimePartition!=null">
                AND day &lt;= #{endTimePartition}
            </if>
        </where>
        GROUP BY
        protocol_type
    </select>

    <select id="getNetworkBusinessTypeAggregate" resultMap="TypeCount">
        SELECT
        count(1) num,
        network_business_id key_type
        FROM e1_comm_log
        <where>
            <if test="startTimePartition!=null">
                day >= #{startTimePartition}
            </if>
            <if test="endTimePartition!=null">
                AND day &lt;= #{endTimePartition}
            </if>
        </where>
        GROUP BY
        network_business_id
    </select>


    <select id="getProvinceAggregate" resultMap="TypeCount">
        SELECT
        log_province_id key_type,
        count(1) AS num
        FROM e1_comm_log
        <where>
            <if test="startTimePartition!=null">
                day >= #{startTimePartition}
            </if>
            <if test="endTimePartition!=null">
                AND day &lt;= #{endTimePartition}
            </if>
        </where>
        GROUP BY
        log_province_id
    </select>

    <select id="getTodayProvinceAggregate" resultMap="TypeCount">
        SELECT
        log_province_id key_type,
        count(1) AS num
        FROM e1_comm_log
        <where>
            <if test="timePartition!=null">
                day = #{timePartition}
            </if>
        </where>
        GROUP BY
        log_province_id
    </select>
    <select id="getAllLogProvinceAggregate" resultMap="TypeCount">
        SELECT
        log_province_id key_type,
        count(1) AS num
        FROM e1_comm_log
        <where>
            <if test="startTime!=null">
                create_time >= #{startTime}
            </if>
            <if test="endTime!=null">
                AND create_time &lt;= #{endTime}
            </if>
        </where>
        GROUP BY
        log_province_id
    </select>

    <select id="getLogStatusPage" resultType="com.eversec.antivpn.log.entity.E1CommLogPOExt">
        select cross_border_comm_log.*,
               log_report_status.status_code,
               log_report_status.end_time
        from log_report_status
                 right join cross_border_comm_log
                            on log_report_status.log_id = cross_border_comm_log.log_id
        where log_report_status.data_type = 1
    </select>

    <select id="getCommLogStatusPage" resultType="com.eversec.antivpn.log.entity.E1CommLogPOExt"
            parameterType="com.eversec.antivpn.log.api.dto.CrossBorderCommLogRequest">
        select
        version ,
        com_code ,
        network_business_id ,
        log_id ,
        house_id ,
        house_name ,
        log_province_id ,
        log_city_id ,
        log_county_id ,
        src_ip ,
        src_port ,
        src_country ,
        src_province_id ,
        src_info ,
        dest_ip ,
        dest_port ,
        dst_country ,
        dst_province_id ,
        dest_info ,
        traffic_type ,
        protocol_type ,
        application_protocol ,
        url ,
        content_type ,
        vpn_id ,
        vpn_name ,
        type_id ,
        vpn_content_type ,
        vpn_content ,
        user_agent ,
        msisdn_md5 ,
        mask_msisdn ,
        phone_province_id ,
        phone_city_id ,
        imsi ,
        imei ,
        apn ,
        lac ,
        ci ,
        cert_info_version ,
        cert_info_serial_number ,
        cert_info_algorithm ,
        cert_info_issuer ,
        cert_info_alidity ,
        cert_info_subject ,
        access_time ,
        create_time ,
        `timestamp` ,
        `day`,
        `hour`
        from cross_border_comm_log
        <where>
            <if test=" version != null and  version != '' ">and version = #{ version}</if>
            <if test="comCode != null and comCode != '' ">and com_code = #{comCode}</if>
            <if test="networkBusinessId != null and networkBusinessId != '' ">and network_business_id =
                #{networkBusinessId}
            </if>
            <if test="logId != null and logId != '' ">and log_id = #{logId}</if>
            <if test="houseId != null and houseId != '' ">and house_id = #{houseId}</if>
            <if test="houseName != null and houseName != '' ">and house_name = #{houseName}</if>
            <if test="logProvinceId != null and logProvinceId != '' ">and log_province_id = #{logProvinceId}</if>
            <if test="logCityId != null and logCityId != '' ">and log_city_id = #{logCityId}</if>
            <if test="logCountyId != null and logCountyId != '' ">and log_county_id = #{logCountyId}</if>
            <if test="srcIp != null and srcIp != '' ">and src_ip = #{srcIp}</if>
            <if test="srcPort != null and srcPort != '' ">and src_port = #{srcPort}</if>
            <if test="srcCountry != null and srcCountry != '' ">and src_country = #{srcCountry}</if>
            <if test="srcProvinceId != null and srcProvinceId != '' ">and src_province_id = #{srcProvinceId}</if>
            <if test="srcInfo != null and srcInfo != '' ">and src_info = #{srcInfo}</if>
            <if test="destIp != null and destIp != '' ">and dest_ip = #{destIp}</if>
            <if test="destPort != null and destPort != '' ">and dest_port = #{destPort}</if>
            <if test="dstCountry != null and dstCountry != '' ">and dst_country = #{dstCountry}</if>
            <if test="dstProvinceId != null and dstProvinceId != '' ">and dst_province_id = #{dstProvinceId}</if>
            <if test="destInfo != null and destInfo != '' ">and dest_info = #{destInfo}</if>
            <if test="trafficType != null and trafficType != '' ">and traffic_type = #{trafficType}</if>
            <if test="protocolType != null and protocolType != '' ">and protocol_type = #{protocolType}</if>
            <if test="applicationProtocol != null and applicationProtocol != '' ">and application_protocol =
                #{applicationProtocol}
            </if>
            <if test="url != null and url != '' ">and url = #{url}</if>
            <if test="contentType != null and contentType != '' ">and content_type = #{contentType}</if>
            <if test="vpnId != null and vpnId != '' ">and vpn_id = #{vpnId}</if>
            <if test="vpnName != null and vpnName != '' ">and vpn_name = #{vpnName}</if>
            <if test="typeId != null and typeId != '' ">and type_id = #{typeId}</if>
            <if test="vpnContentType != null and vpnContentType != '' ">and vpn_content_type = #{vpnContentType}</if>
            <if test="vpnContent != null and vpnContent != '' ">and vpn_content = #{vpnContent}</if>
            <if test="userAgent != null and userAgent != '' ">and user_agent = #{userAgent}</if>
            <if test="msisdnMd5 != null and msisdnMd5 != '' ">and msisdn_md5 = #{msisdnMd5}</if>
            <if test="maskMsisdn != null and maskMsisdn != '' ">and mask_msisdn = #{maskMsisdn}</if>
            <if test="phoneProvinceId != null and phoneProvinceId != '' ">and phone_province_id = #{phoneProvinceId}
            </if>
            <if test="phoneCityId != null and phoneCityId != '' ">and phone_city_id = #{phoneCityId}</if>
            <if test="imsi != null and imsi != '' ">and imsi = #{imsi}</if>
            <if test="imei != null and imei != '' ">and imei = #{imei}</if>
            <if test="apn != null and apn != '' ">and apn = #{apn}</if>
            <if test="lac != null and lac != '' ">and lac = #{lac}</if>
            <if test="ci != null and ci != '' ">and ci = #{ci}</if>
            <if test="certInfoVersion != null and certInfoVersion != '' ">and cert_info_version = #{certInfoVersion}
            </if>
            <if test="certInfoSerialNumber != null and certInfoSerialNumber != '' ">and cert_info_serial_number =
                #{certInfoSerialNumber}
            </if>
            <if test="certInfoAlgorithm != null and certInfoAlgorithm != '' ">and cert_info_algorithm =
                #{certInfoAlgorithm}
            </if>
            <if test="certInfoIssuer != null and certInfoIssuer != '' ">and cert_info_issuer = #{certInfoIssuer}</if>
            <if test="certInfoAlidity != null and certInfoAlidity != '' ">and cert_info_alidity = #{certInfoAlidity}
            </if>
            <if test="certInfoSubject != null and certInfoSubject != '' ">and cert_info_subject = #{certInfoSubject}
            </if>
            <if test="accessTime != null and accessTime != '' ">and access_time = #{accessTime}</if>
            <if test="createTime != null and createTime != '' ">and create_time = #{createTime}</if>
            <if test="timestamp != null and timestamp != '' ">and `timestamp` = #{timestamp}</if>
            <if test="day != null and day != '' ">and `day` = #{day}</if>
            <if test="hour != null and hour != '' ">and `hour` = #{hour}</if>
            <if test="startAccessTime != null  ">and access_time>= #{startAccessTime}</if>
            <if test="endAccessTime != null ">and access_time &lt;= #{endAccessTime}</if>
            <if test="startTimestamp != null ">and timestamp >= #{startTimestamp}</if>
            <if test="endTimestamp != null  ">and timestamp &lt;= #{endTimestamp}</if>
        </where>
        order by `timestamp`
        LIMIT #{size}
        OFFSET #{current}
    </select>
    <delete id="deleteByLogId">
        alter table cross_border_comm_log delete where log_id = #{logId}
    </delete>

    <select id="getDistinctVpnId">
        select distinct vpn_id from  e1_comm_log
        <where>
            <if test="startTime!=null">
                create_time >= #{startTime}
            </if>
            <if test="endTime!=null">
                AND create_time &lt;= #{endTime}
            </if>
        </where>
    </select>

    <resultMap id="VpnAggVO" type="com.eversec.antivpn.log.mapper.vo.VpnAggVO">
        <result column="vpn_service_name" jdbcType="VARCHAR" property="vpnServiceName"/>
        <result column="resource_name" jdbcType="VARCHAR" property="resourceName"/>
        <result column="person_num" jdbcType="BIGINT" property="personNum"/>
        <result column="customer_num" jdbcType="INTEGER" property="customerNum"/>
        <result column="service_times" jdbcType="BIGINT" property="serviceTimes"/>
        <result column="resource_node_num" jdbcType="INTEGER" property="resourceNodeNum"/>
    </resultMap>

    <select id="getUsedResourceNodeAggregate" resultMap="VpnAggVO">
        SELECT
        vpn_id vpn_service_name,
        count(1) AS person_num
        FROM e1_comm_log
        <where>
            <if test="startTimePartition!=null">
                day >= #{startTimePartition}
            </if>
            <if test="endTimePartition!=null">
                AND day &lt;= #{endTimePartition}
            </if>
        </where>
        GROUP BY
        vpn_id
    </select>


    <select id="getUserLogPageList" resultType="com.eversec.antivpn.log.mapper.vo.UserLogPageVO">
        SELECT
        src_ip as srcIp,
        vpn_airport_code as vpnAirportCode,
        vpn_software_code as vpnSoftwareCode,
        province_id as provinceId,
        dest_country as destCountry,
        access_time as accessDateTime,
        vpn_id as vpnId
        FROM e1_comm_log
        where dest_country not in ('中国','NA')
        and  day >= #{startTimePartition}
        order by access_time desc
        limit 20
    </select>
</mapper>
