plugins {
    id 'java'
    id 'com.google.cloud.tools.jib'
    id 'com.gorylenko.gradle-git-properties'
}
//// 构建时生成 git.properties
gitProperties {
    // 日期格式
    dateFormat = "yyyy-MM-dd' 'HH:mm:ss"
    // 时区
    dateFormatTimeZone = "Asia/Shanghai"

    keys = ['git.branch',
            'git.commit.id',
            'git.commit.id.abbrev',
            'git.commit.time',
            'git.build.version']
    extProperty = "gitProps"
}

//// 构建镜像
jib {
    from {
        image = "${DOCKER_REGISTRY}${jibFromImage}${PLATFORM_SUFFIX}"
    }
    to {
        image = "${DOCKER_REGISTRY}/${jibToDockerProject}/${project.name}"
        tags = [
                "${project.version}-dev${PLATFORM_SUFFIX}",
        ]
    }
    allowInsecureRegistries true
    container {
        jvmFlags = ["-server"]
        creationTime = "USE_CURRENT_TIMESTAMP"
        mainClass = "com.eversec.antivpn.AntiVpnServiceApplication"
        environment = [
                "TZ": "Asia/Shanghai",
                "NACOS_CONFIG_ENABLED": "false",
                "SPRING_PROFILES_ACTIVE": "docker"
        ]
    }
}

dependencies {
    implementation "com.eversec.framework:eversec-webboot:${eversecWebbootVersion}"

    // 内部依赖
    implementation project(":anti-vpn-supoort")
    implementation project(":anti-vpn-supoort:anti-vpn-supoort-api")

    // 内部依赖
    implementation project(":anti-vpn-intelligence")
    implementation project(":anti-vpn-intelligence:anti-vpn-intelligence-api")

    implementation project(":anti-vpn-log")
    implementation project(":anti-vpn-log:anti-vpn-log-api")

    implementation "com.eversec.stark.generic:generic-common:${genericVersion}"

    // 动态flyway
    implementation "com.eversec.framework:eversec-dynamic-flyway:${eversecDynamicFlyway}"
    implementation "cn.hutool:hutool-all:${hutoolVersion}"
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation "org.springframework.cloud:spring-cloud-starter-bootstrap"
    implementation "org.springframework.cloud:spring-cloud-starter-netflix-eureka-client"
    implementation "com.baomidou:mybatis-plus-boot-starter:${mybatisPlusVersion}"
    implementation "com.baomidou:dynamic-datasource-spring-boot-starter:${mybatisPlusDatasourceVersion}"
    implementation "p6spy:p6spy:${p6spyVersion}"
    implementation "mysql:mysql-connector-java:${mysqlConnectorVersion}"
    implementation "org.springdoc:springdoc-openapi-ui:${springdocOpenapiVersion}"
    implementation "com.github.xiaoymin:knife4j-springdoc-ui:${knife4jSpringdocVersion}"

    implementation 'org.flywaydb:flyway-core'

    test {
        useTestNG()
    }
}
