package com.eversec.antivpn.intelligence.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.text.StrSplitter;
import cn.hutool.core.text.csv.CsvData;
import cn.hutool.core.text.csv.CsvReader;
import cn.hutool.core.text.csv.CsvRow;
import cn.hutool.core.text.csv.CsvUtil;
import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.XmlUtil;
import cn.hutool.core.util.ZipUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.extra.ssh.Sftp;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eversec.antivpn.config.AntiVpnProperties;
import com.eversec.antivpn.config.enums.AntiVpnBusinessExceptionEnum;
import com.eversec.antivpn.config.enums.AntiVpnDeployPlaceEnum;
import com.eversec.antivpn.config.enums.CollectTypeEnum;
import com.eversec.antivpn.config.enums.ResponseCodeEnum;
import com.eversec.antivpn.intelligence.api.VpnIntelligenceCommandWebService;
import com.eversec.antivpn.intelligence.api.VpnIntelligenceIrcsCommandWebService;
import com.eversec.antivpn.intelligence.api.dto.*;
import com.eversec.antivpn.intelligence.api.enums.IntelligenceCommandOperationTypeEnum;
import com.eversec.antivpn.intelligence.api.enums.IntelligenceCommandProcessStatusEnum;
import com.eversec.antivpn.intelligence.api.enums.IntelligenceSourceEnum;
import com.eversec.antivpn.intelligence.dto.VpnIntelligenceCommandQueryConditionDTO;
import com.eversec.antivpn.intelligence.entity.VpnIntelligence;
import com.eversec.antivpn.intelligence.entity.VpnIntelligenceCommand;
import com.eversec.antivpn.intelligence.factory.VpnIntelligenceCommandFactory;
import com.eversec.antivpn.intelligence.factory.VpnIntelligenceFactory;
import com.eversec.antivpn.intelligence.mapper.VpnIntelligenceCommandMapper;
import com.eversec.antivpn.intelligence.service.IVpnIntelligenceCommandService;
import com.eversec.antivpn.intelligence.service.IVpnIntelligenceService;
import com.eversec.antivpn.support.config.api.PlatformInterfaceInfoApi;
import com.eversec.antivpn.support.config.api.dto.PlatformInterfaceInfoDTO;
import com.eversec.antivpn.util.FtpUtil;
import com.eversec.framework.webboot.common.exception.BusinessException;
import com.eversec.framework.webboot.common.exception.ExceptionUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.cxf.jaxws.JaxWsProxyFactoryBean;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.io.File;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.nio.charset.StandardCharsets;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * vpn情报指令 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-10
 */
@Validated
@Service
@AllArgsConstructor
@Slf4j
public class VpnIntelligenceCommandServiceImpl extends ServiceImpl<VpnIntelligenceCommandMapper, VpnIntelligenceCommand>
		implements IVpnIntelligenceCommandService {

	@Lazy
	private final IVpnIntelligenceService iVpnIntelligenceService;

//	private final ProvincePlatformInfoApi provincePlatformInfoApi;

	private final PlatformInterfaceInfoApi platformInterfaceInfoApi;

	private final AntiVpnProperties antiVpnProperties;

	private final VpnIntelligenceFactory vpnIntelligenceFactory;

	private final VpnIntelligenceCommandFactory vpnIntelligenceCommandFactory;

	private IVpnIntelligenceCommandService getVpnIntelligenceCommandService() {
		// 解决循环依赖
		return SpringUtil.getBean(IVpnIntelligenceCommandService.class);
	}


	@Transactional(propagation = Propagation.REQUIRES_NEW)
	@Override
	public Long saveDistributeCommand(VpnIntelligenceCommandDistributeRequestDTO requestDto, String systemCode) {
		VpnIntelligenceCommand command = vpnIntelligenceCommandFactory.distributeRequestDtoToIntelligenceCommandEntity(requestDto, systemCode);
		this.save(command);
		return command.getId();
	}

	@Override
	public Long saveIrcsDistributeCommand(VpnIntelligenceIrcsCommandDistributeRequestDTO requestDto, String comCode) {
		VpnIntelligenceCommand command = vpnIntelligenceCommandFactory.distributeRequestDtoToIntelligenceCommandEntity(requestDto, comCode);
		this.save(command);
		return command.getId();
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public Long saveImportCommand(VpnIntelligenceCommand entity) {
		this.save(entity);
		return entity.getId();
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public List<VpnIntelligence> commandDistribute(Long commandEntityId, @Valid VpnIntelligenceCommandDistributeDTO paramDto, String systemCode, Long reDistributeByCommandId) {
		log.info("指令（情报）解析、复制全量情报库文件、入库");

		// 判断是全量下发还是增量下发
		IntelligenceCommandOperationTypeEnum intelligenceCommandOperationTypeEnum = EnumUtil.getBy(IntelligenceCommandOperationTypeEnum::getCode,
				paramDto.getOperationType());
		Path commandDir = null;
		switch (intelligenceCommandOperationTypeEnum) {
			case FULL:
			case MODEL:
			case SAMPLE:
				// ftp://localhost:21/cross_info_home/01-11
				// 1. 下载情报文件
				commandDir = downloadCommandDistributeFile(paramDto, systemCode, reDistributeByCommandId);
				// 2. 复制文件到省平台情报目录
				copyToProvinceIntelligenceFtpDir(paramDto, commandDir, systemCode);
				break;
			default:
				break;
		}

		VpnIntelligenceCommand dbVpnIntelligenceCommand = this.getById(commandEntityId);
		// 全量下发解析xml文件
		VpnIntelligenceCommand vpnIntelligenceCommand = generateDistributeIntelligenceCommand(dbVpnIntelligenceCommand, paramDto, systemCode);
		getVpnIntelligenceCommandService().updateCommandParseStatus(vpnIntelligenceCommand);


		if (reDistributeByCommandId != null) {
			// 重新下发创建时间改为上次下发的时间
			VpnIntelligenceCommand reDistributeByCommand = this.getById(reDistributeByCommandId);
			vpnIntelligenceCommand.setDistributeDatetime(reDistributeByCommand.getDistributeDatetime());
		}

		List<VpnIntelligence> vpnIntelligenceList = generateDistributeIntelligenceList(paramDto, commandDir, systemCode);


		getVpnIntelligenceCommandService().saveCommandToDb(intelligenceCommandOperationTypeEnum, vpnIntelligenceCommand, vpnIntelligenceList);

		return vpnIntelligenceList;
	}

	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void saveCommandToDb(IntelligenceCommandOperationTypeEnum intelligenceCommandOperationTypeEnum, VpnIntelligenceCommand vpnIntelligenceCommand, List<VpnIntelligence> vpnIntelligenceList) {

		// 入库，1、判断command是否存在。2、全量先插入新数据再清除老数据
		log.info("保存/更新情报记录:{}条", vpnIntelligenceList.size());
		iVpnIntelligenceService.saveOrUpdateEntity(vpnIntelligenceList);
		getVpnIntelligenceCommandService().updateById(vpnIntelligenceCommand);

		if (intelligenceCommandOperationTypeEnum == IntelligenceCommandOperationTypeEnum.FULL) {
			log.info("全量下发，删除原下发的情报库文件");
			iVpnIntelligenceService.remove(
					new LambdaQueryWrapper<VpnIntelligence>().in(VpnIntelligence::getSource, IntelligenceSourceEnum.FULL, IntelligenceSourceEnum.INCREASE)
							.ne(VpnIntelligence::getCommandId, vpnIntelligenceCommand.getCommandId()).eq(VpnIntelligence::getComCode, vpnIntelligenceCommand.getComCode())
							.eq(VpnIntelligence::getProvinceId, vpnIntelligenceCommand.getProvinceId()));
		}
	}

	/**
	 * 更新指令解析状态
	 */
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void updateCommandParseStatus(VpnIntelligenceCommand vpnIntelligenceCommand) {
		vpnIntelligenceCommand.setCommandProcessStatus(IntelligenceCommandProcessStatusEnum.PARSE.getDesc());
		this.updateById(vpnIntelligenceCommand);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void updateCommandProcessStatus(Long commandEntityId, VpnIntelligenceCommandDistributeResponseDTO resultDto, Exception responseException) {
		if (commandEntityId == null) {
			return;
		}
		Map<String, Object> stringObjectMap = BeanUtil.beanToMap(resultDto);
		if (stringObjectMap == null) {
			stringObjectMap = new HashMap<>();
		}
		if (responseException != null) {
			StringWriter stringWriter = new StringWriter();
			responseException.printStackTrace(new PrintWriter(stringWriter, true));
			String exceptionStr = stringWriter.getBuffer().toString();
			stringObjectMap.put("stackTrace", exceptionStr);
		}
		VpnIntelligenceCommand byId = this.getById(commandEntityId);
		if (resultDto.getResultCode().equals(ResponseCodeEnum.处理完成.getCode())) {
			byId.setCommandProcessStatus(IntelligenceCommandProcessStatusEnum.SUCCESS.getDesc());
		} else {
			byId.setCommandProcessStatus(IntelligenceCommandProcessStatusEnum.FAIL.getDesc());
		}
		byId.setDistributeResponseParams(JSONUtil.toJsonPrettyStr(stringObjectMap));
		this.updateById(byId);
	}


	@Transactional(propagation = Propagation.REQUIRES_NEW)
	@Override
	public boolean save(VpnIntelligenceCommand entity) {
		return super.save(entity);
	}

	/**
	 * 校验数据
	 *
	 * @param paramDto
	 */
	private void validateCommandDistributeDto(VpnIntelligenceCommandDistributeDTO paramDto) {

		VpnIntelligenceCommand existsCommand = findByCommandId(paramDto.getCommandId());
		if (existsCommand != null) {
			throw new BusinessException(AntiVpnBusinessExceptionEnum.CODE_10003);
		}
	}

	public VpnIntelligenceCommand findByCommandId(Long commandId) {
		VpnIntelligenceCommand command = this.getOne(new LambdaQueryWrapper<VpnIntelligenceCommand>().eq(VpnIntelligenceCommand::getCommandId, commandId));
		return command;
	}

	@Override
	public Page<VpnIntelligenceCommand> page(VpnIntelligenceCommandQueryConditionDTO param, Page<VpnIntelligenceCommand> pageInfo) {
		QueryWrapper<VpnIntelligenceCommand> vpnIntelligenceCommandQueryWrapper = param.autoWrapper();
		LambdaQueryWrapper<VpnIntelligenceCommand> vpnIntelligenceCommandLambdaQueryWrapper = vpnIntelligenceCommandQueryWrapper.lambda().orderByDesc(VpnIntelligenceCommand::getId);
		Page<VpnIntelligenceCommand> page = this.page(pageInfo, vpnIntelligenceCommandLambdaQueryWrapper);
		return page;
	}


	/**
	 * 下发生成情报指令
	 *
	 * @return
	 */
	private VpnIntelligenceCommand generateDistributeIntelligenceCommand(VpnIntelligenceCommand dbVpnIntelligenceCommand, VpnIntelligenceCommandDistributeDTO paramDto, String systemCode) {
		VpnIntelligenceCommand vpnIntelligenceCommand = vpnIntelligenceCommandFactory.distributeDtoToIntelligenceCommandEntity(dbVpnIntelligenceCommand, paramDto, systemCode);

		// 判断是全量下发还是增量下发
		IntelligenceCommandOperationTypeEnum intelligenceCommandOperationTypeEnum = EnumUtil.getBy(IntelligenceCommandOperationTypeEnum::getCode,
				paramDto.getOperationType());
		// 默认为全量下发，有文件情况
		switch (intelligenceCommandOperationTypeEnum) {
			case FULL:
			case MODEL:
			case SAMPLE:
			case VPN_TOOL:
				vpnIntelligenceCommand.setSource(IntelligenceSourceEnum.FULL.name());
				break;
			case DELETE:
			case INCREASE:
				vpnIntelligenceCommand.setSource(IntelligenceSourceEnum.INCREASE.name());
				break;
			case REPORT:
				vpnIntelligenceCommand.setSource(IntelligenceSourceEnum.REPORT.name());
				break;
		}
		return vpnIntelligenceCommand;
	}

	/**
	 * 下发生成情报
	 *
	 * @return
	 */
	private List<VpnIntelligence> generateDistributeIntelligenceList(VpnIntelligenceCommandDistributeDTO paramDto, Path commandDir, String systemCode) {
		List<VpnIntelligence> vpnIntelligenceList = new ArrayList<>();

		// 判断是全量下发还是增量下发
		IntelligenceCommandOperationTypeEnum intelligenceCommandOperationTypeEnum = EnumUtil.getBy(IntelligenceCommandOperationTypeEnum::getCode,
				paramDto.getOperationType());
		log.info("情报下发类型: {}", intelligenceCommandOperationTypeEnum);
		switch (intelligenceCommandOperationTypeEnum) {
			case FULL:
				vpnIntelligenceList = parseVpnIntelligenceByZip(paramDto, commandDir.toString(), systemCode);
				break;
			case INCREASE:
				vpnIntelligenceList = vpnIntelligenceFactory.increaseDistributeDtoToEntity(paramDto, systemCode);
				break;
			default:

		}
		return vpnIntelligenceList;
	}

	private Path getCommandDir(int distributeCode, String distributeDate, Long commandId) {
		Path commandDir = Paths.get(antiVpnProperties.getFileDataStorageDir(), antiVpnProperties.getBusinessDir().getCommandDistributeDir(), StrUtil.format("{}/{}/{}", distributeCode, distributeDate,
				commandId));
		return commandDir;
	}

	/**
	 * 下载指令下发文件
	 * @param paramDto
	 * @param systemCode
	 * @return 返回指令文件目录，根据需要进行解析里面的文件内容
	 */
	private Path downloadCommandDistributeFile(VpnIntelligenceCommandDistributeDTO paramDto, String systemCode, Long reDistributeByCommandId) {
		PlatformInterfaceInfoDTO platformInterfaceInfoDTO = getPlatformInterfaceInfoDTO(paramDto.getComCode(), paramDto.getProvinceId(), systemCode);
		log.info("下载指令文件，平台接口配置信息为: {}", JSONUtil.toJsonPrettyStr(platformInterfaceInfoDTO));

		// 1. ftp下载全量包
		// 7.3.1
		// 部侧系统下发全量跨境通信情报库文件时，部侧系统为每个企业侧系统创建一个根目录，为便于描述，下文用cross_info_home表示该目录，部侧系统负责维护该目录。
		// 跨境通信情报库文件数据存放路径为/cross_info_home/下发类型代码/下发日期/指令编号，下发类型代码见如表3所示，指令编码即为跨境通信情报库、监测模型文件下发指令的指令id。日期采用yyyy-MM-dd的格式编写。
		// 跨境通信情报库文件压缩为一个zip格式文件。跨境通信情报库文件的文件命名为省级区域编号(2位)，见附录F.4 REF _Ref73644258 \h  \* MERGEFORMAT 节+运营商标识(2位)+生成时间（用1970年1月1日到文件生成时的微秒数表示）+随机数(4位)命名，其中：运营商标识可以用10-电信，11-移动，12-联通，13-广电，99-其他。文件压缩后文件名为文件名部分+“.zip”

		int distributeCode = EnumUtil.getBy(IntelligenceCommandOperationTypeEnum::getCode, paramDto.getOperationType()).getDistributeCode();
		// 下发日期
		String distributeDate = DateUtil.format(LocalDateTime.now(), "yyyy-MM-dd");
		if (reDistributeByCommandId != null) {
			// 重新下发，去第一次下发目录下载文件
			VpnIntelligenceCommand reDistributeByCommand = this.getById(reDistributeByCommandId);
			LocalDateTime distributeDatetime;
			if (reDistributeByCommand.getDistributeDatetime() != null) {
				distributeDatetime = reDistributeByCommand.getDistributeDatetime();
			} else {
				distributeDatetime = reDistributeByCommand.getReportDatetime();
			}
			distributeDate = DateUtil.format(distributeDatetime, "yyyy-MM-dd");
		}

		// ftp://localhost:21/cross_info_home/01-11/1/2023-07-31/1000001
		String downloadIntelligenceFtpDir = StrUtil.format("{}/{}/{}/{}", platformInterfaceInfoDTO.getNorthIntelligenceFtpAddress(), distributeCode, distributeDate,
				paramDto.getCommandId());
		log.info("指令文件下载地址：downloadIntelligenceFtpDir: {}", downloadIntelligenceFtpDir);
		// /cross_info_home/01-11/1/2023-07-31/1000001
		String ftpPath = FtpUtil.getFtpPath(downloadIntelligenceFtpDir);

		// /app/data/storage/CommandDistribute_cross_info_home/01-11/1/2023-07-31/1000001
		Path commandDir = getCommandDir(distributeCode, distributeDate, paramDto.getCommandId());

		FileUtil.mkParentDirs(commandDir);
		FileUtil.mkdir(commandDir);
		log.info("指令文件存储的临时目录：{}", commandDir);
		// 下载文件
		try (Sftp ftp = FtpUtil.buildSftpClient(downloadIntelligenceFtpDir, platformInterfaceInfoDTO.getNorthIntelligenceFtpUsername(), platformInterfaceInfoDTO.getNorthIntelligenceFtpPassword())) {
			log.info("通过ftp进行下载操作");
			ftp.recursiveDownloadFolder(ftpPath, commandDir.toFile());
		} catch (Exception e) {
			throw new BusinessException(AntiVpnBusinessExceptionEnum.CODE_10004, e);
		}

		return commandDir;
	}


	private PlatformInterfaceInfoDTO getPlatformInterfaceInfoDTO(String comCode, Long provinceId, String systemCode) {
		PlatformInterfaceInfoDTO platformInterfaceInfoDTO;
		if (antiVpnProperties.getDeployPlace() == AntiVpnDeployPlaceEnum.GROUP) {
			// 集团模式部署，根据运营商、省、系统标识查询配置信息
			platformInterfaceInfoDTO = platformInterfaceInfoApi.getByComCodeAndProvinceAndSystemCode(comCode, provinceId, systemCode);
		} else {
			platformInterfaceInfoDTO = platformInterfaceInfoApi.getCurrentPlatform();
		}
		return platformInterfaceInfoDTO;
	}

	private void copyToProvinceIntelligenceFtpDir(VpnIntelligenceCommandDistributeDTO paramDto, Path commandDir, String systemCode) {
		PlatformInterfaceInfoDTO platformInterfaceInfoDTO = getPlatformInterfaceInfoDTO(paramDto.getComCode(), paramDto.getProvinceId(), systemCode);
		log.info("复制文件到省平台情报目录");

		int distributeCode = EnumUtil.getBy(IntelligenceCommandOperationTypeEnum::getCode, paramDto.getOperationType()).getDistributeCode();
		String distributeDate = DateUtil.format(LocalDateTime.now(), "yyyy-MM-dd");

		if (antiVpnProperties.getDeployPlace() == AntiVpnDeployPlaceEnum.GROUP) {
			// 3. 集团测移动文件到省平台目录，目录不用再有commandId，因为commandDir已经包含commandId
			Path provinceIntelligenceFtpDir = Paths.get(platformInterfaceInfoDTO.getSouthIntelligenceDir(), String.valueOf(distributeCode), distributeDate);
			FileUtil.mkParentDirs(provinceIntelligenceFtpDir);
			FileUtil.mkdir(provinceIntelligenceFtpDir);
			log.info("集团模式部署，复制全量情报文件到省平台ftp目录（SouthIntelligenceDir），{}", provinceIntelligenceFtpDir);
			FileUtil.copy(commandDir.toFile(), provinceIntelligenceFtpDir.toFile(), true);
		} else if (antiVpnProperties.getDeployPlace() == AntiVpnDeployPlaceEnum.SYSTEM){
			// 3. 和业务系统部署在一起
			CollectTypeEnum collectTypeEnum = CollectTypeEnum.valueOf(platformInterfaceInfoDTO.getCollectType());
			// cu 模式部署
			if (CollectTypeEnum.CU == collectTypeEnum) {
				Path provinceIntelligenceFtpDir = Paths.get(platformInterfaceInfoDTO.getSouthIntelligenceDir(), String.valueOf(distributeCode), distributeDate);
				FileUtil.mkParentDirs(provinceIntelligenceFtpDir);
				FileUtil.mkdir(provinceIntelligenceFtpDir);
				log.info("CU模式部署，复制全量情报文件到CU ftp目录（SouthIntelligenceDir），{}", provinceIntelligenceFtpDir);
				FileUtil.copy(commandDir.toFile(), provinceIntelligenceFtpDir.toFile(), true);
			}

		} else if (antiVpnProperties.getDeployPlace() == AntiVpnDeployPlaceEnum.PROVINCE){
			List<PlatformInterfaceInfoDTO> byIspAndProvince = platformInterfaceInfoApi.getByIspAndProvince(paramDto.getComCode(), paramDto.getProvinceId());
			for (PlatformInterfaceInfoDTO interfaceInfoDTO : byIspAndProvince) {
				// 3. 省平台共用一个系统
				CollectTypeEnum collectTypeEnum = CollectTypeEnum.valueOf(interfaceInfoDTO.getCollectType());
				// cu 模式部署
				if (CollectTypeEnum.CU == collectTypeEnum) {
					Path provinceIntelligenceFtpDir = Paths.get(interfaceInfoDTO.getSouthIntelligenceDir(), String.valueOf(distributeCode), distributeDate);
					FileUtil.mkParentDirs(provinceIntelligenceFtpDir);
					FileUtil.mkdir(provinceIntelligenceFtpDir);
					log.info("省平台CU模式部署，复制全量情报文件到CU ftp目录（SouthIntelligenceDir），{}", provinceIntelligenceFtpDir);
					FileUtil.copy(commandDir.toFile(), provinceIntelligenceFtpDir.toFile(), true);
				}
			}
		}
	}


	/**
	 * zip解析为情报库
	 * @param paramDto
	 * @param commandDir /tmp/report-log/01-11/1/2023-07-31/1000001
	 * @return
	 */
	public List<VpnIntelligence> parseVpnIntelligenceByZip(VpnIntelligenceCommandDistributeDTO paramDto, String commandDir, String systemCode) {
		List<VpnIntelligenceFullDistributeDTO> fullDistributeDTOS = new ArrayList<>();
		List<String> commandZipFiles = FileUtil.listFileNames(commandDir);
		log.info("解析全量情报内容：{}", commandZipFiles);
		for (String commandZipFile : commandZipFiles) {
			boolean zipFile = StrUtil.endWithAnyIgnoreCase(commandZipFile, ".zip");
			if (!zipFile) {
				continue;
			}

			// /tmp/report-log/01-11/1/2023-07-31
			String parent = FileUtil.getParent(commandDir, 1);

			// /tmp/report-log/01-11/1/2023-07-31/tmp/1000001/111016444433333330010002.zip/
			File zipTargetDir = Paths.get(parent, "tmp", new File(commandDir).getName(), commandZipFile).toFile();
			log.info("unzip文件到：{}", zipTargetDir);
			ZipUtil.unzip(Paths.get(commandDir, commandZipFile).toFile(), zipTargetDir, StandardCharsets.UTF_8);

			// 一个zip下可能有多个xml文件
			List<String> fullFiles = FileUtil.listFileNames(zipTargetDir.getPath());
			log.info("解析全量情报库unzip后的文件: {}", fullFiles);
			//  文件内容为文本格式，竖线分割
			for (String csvFileName : fullFiles) {
				File csvFile = Paths.get(zipTargetDir.getPath(), csvFileName).toFile();
				List<String> vpnIntelligenceLines = FileUtil.readUtf8Lines(csvFile);
				for (String vpnIntelligenceLine : vpnIntelligenceLines) {
					VpnIntelligenceFullDistributeDTO vpnIntelligenceFullDistributeDTO = new VpnIntelligenceFullDistributeDTO(vpnIntelligenceLine);
					fullDistributeDTOS.add(vpnIntelligenceFullDistributeDTO);
				}
			}
		}
		List<VpnIntelligence> vpnIntelligenceList = vpnIntelligenceFactory.fullDistributeDtoToEntity(paramDto, fullDistributeDTOS, systemCode);
		log.info("解析全量情报库文件完成，共解析 {} 条情报记录", vpnIntelligenceList.size());
		return vpnIntelligenceList;
	}



	@Override
	public VpnIntelligenceCommandDistributeResponseDTO callSouthWs(VpnIntelligenceCommandDistributeRequestDTO requestDto, String systemCode) {

		PlatformInterfaceInfoDTO byAndProvinceAndSystemCode;
		List<PlatformInterfaceInfoDTO> platformInterfaceInfoDTOS = new ArrayList<>();
		if (antiVpnProperties.getDeployPlace() == AntiVpnDeployPlaceEnum.GROUP) {
			byAndProvinceAndSystemCode = platformInterfaceInfoApi.getByComCodeAndProvinceAndSystemCode(requestDto.getComCode(), requestDto.getProvinceId(), systemCode);
			platformInterfaceInfoDTOS.add(byAndProvinceAndSystemCode);
		} else if (antiVpnProperties.getDeployPlace() == AntiVpnDeployPlaceEnum.SYSTEM) {
			byAndProvinceAndSystemCode = platformInterfaceInfoApi.getCurrentPlatform();
			platformInterfaceInfoDTOS.add(byAndProvinceAndSystemCode);
		} else {
			platformInterfaceInfoDTOS = platformInterfaceInfoApi.getByIspAndProvince(requestDto.getComCode(), requestDto.getProvinceId());
		}

		for (PlatformInterfaceInfoDTO platformInterfaceInfoDTO : platformInterfaceInfoDTOS) {

			if (CollectTypeEnum.valueOf(platformInterfaceInfoDTO.getCollectType()) != CollectTypeEnum.CU){
				continue;
			}

			// 接口地址 "http://localhost:8080/services/IDCWebService/idcCommand?wsdl"
			String address = platformInterfaceInfoDTO.getSouthWsAddress();
			// 代理工厂
			JaxWsProxyFactoryBean jaxWsProxyFactoryBean = new JaxWsProxyFactoryBean();

			// 设置代理地址
			jaxWsProxyFactoryBean.setAddress(address);
			// 设置接口类型
			jaxWsProxyFactoryBean.setServiceClass(VpnIntelligenceCommandWebService.class);
			// 创建一个代理接口实现
			VpnIntelligenceCommandWebService webService = (VpnIntelligenceCommandWebService) jaxWsProxyFactoryBean.create();
			//测试数据
			// 调用代理接口的方法调用并返回结果
			try {
				String responseXml = webService.idcCommandProvince(requestDto.getComCode(), requestDto.getProvinceId(),
						requestDto.getRandVal(), requestDto.getPwdHash(), requestDto.getCommand(), requestDto.getCommandHash(), requestDto.getCommandType(),
						requestDto.getEncryptAlgorithm(), requestDto.getHashAlgorithm(), requestDto.getCompressionFormat(), requestDto.getCommandVersion());

				VpnIntelligenceCommandDistributeResponseDTO responseDTO =
						XmlUtil.xmlToBean(XmlUtil.readXML(responseXml).getDocumentElement(), VpnIntelligenceCommandDistributeResponseDTO.class);

				log.info("调用南向接口完成，返回内容如下\n {}", XmlUtil.format(responseXml));

				if (platformInterfaceInfoDTOS.size() == 1) {
					return responseDTO;
				} else {
					log.error("调用南向接口失败，返回结果如下: {}", responseXml);
				}
			} catch (Exception e) {
				log.error("调用南向接口失败", e);
				if (platformInterfaceInfoDTOS.size() == 1) {
					return VpnIntelligenceCommandDistributeResponseDTO.buildError(ResponseCodeEnum.其他异常.getCode(), "调用南向接口失败");
				}
			}
		}
		return VpnIntelligenceCommandDistributeResponseDTO.buildSuccess();
	}

	@Override
	public VpnIntelligenceCommandDistributeResponseDTO ircsCallSouthWs(VpnIntelligenceIrcsCommandDistributeRequestDTO requestDto, Long provinceId, String comCode) {

		PlatformInterfaceInfoDTO byAndProvinceAndSystemCode;
		List<PlatformInterfaceInfoDTO> platformInterfaceInfoDTOS = new ArrayList<>();
		if (antiVpnProperties.getDeployPlace() == AntiVpnDeployPlaceEnum.GROUP) {
			byAndProvinceAndSystemCode = platformInterfaceInfoApi.getByComCodeAndSystemCode(comCode, requestDto.getIrcsId());
			platformInterfaceInfoDTOS.add(byAndProvinceAndSystemCode);
		} else if (antiVpnProperties.getDeployPlace() == AntiVpnDeployPlaceEnum.SYSTEM) {
			byAndProvinceAndSystemCode = platformInterfaceInfoApi.getCurrentPlatform();
			platformInterfaceInfoDTOS.add(byAndProvinceAndSystemCode);
		} else {
			platformInterfaceInfoDTOS = platformInterfaceInfoApi.getByIspAndProvince(comCode, provinceId);
		}

		for (PlatformInterfaceInfoDTO platformInterfaceInfoDTO : platformInterfaceInfoDTOS) {

			if (CollectTypeEnum.valueOf(platformInterfaceInfoDTO.getCollectType()) != CollectTypeEnum.CU){
				continue;
			}

			// 接口地址 "http://localhost:8080/services/IDCWebService/idcCommand?wsdl"
			String address = platformInterfaceInfoDTO.getSouthWsAddress();
			// 代理工厂
			JaxWsProxyFactoryBean jaxWsProxyFactoryBean = new JaxWsProxyFactoryBean();

			// 设置代理地址
			jaxWsProxyFactoryBean.setAddress(address);
			// 设置接口类型
			jaxWsProxyFactoryBean.setServiceClass(VpnIntelligenceIrcsCommandWebService.class);
			// 创建一个代理接口实现
			VpnIntelligenceIrcsCommandWebService webService = (VpnIntelligenceIrcsCommandWebService) jaxWsProxyFactoryBean.create();
			//测试数据
			// 调用代理接口的方法调用并返回结果
			try {
				String responseXml = webService.ircs_command(requestDto.getIrcsId(),
						requestDto.getRandVal(), requestDto.getPwdHash(), requestDto.getCommand(),
						requestDto.getCommandHash(), requestDto.getCommandType(), requestDto.getCommandSequence(),
						requestDto.getEncryptAlgorithm(), requestDto.getHashAlgorithm(), requestDto.getCompressionFormat(), requestDto.getCommandVersion());

				VpnIntelligenceCommandDistributeResponseDTO responseDTO =
						XmlUtil.xmlToBean(XmlUtil.readXML(responseXml).getDocumentElement(), VpnIntelligenceCommandDistributeResponseDTO.class);

				log.info("调用南向接口完成，返回内容如下\n {}", XmlUtil.format(responseXml));

				if (platformInterfaceInfoDTOS.size() == 1) {
					return responseDTO;
				} else {
					log.error("调用南向接口失败，返回结果如下: {}", responseXml);
				}
			} catch (Exception e) {
				log.error("调用南向接口失败", e);
				if (platformInterfaceInfoDTOS.size() == 1) {
					return VpnIntelligenceCommandDistributeResponseDTO.buildError(ResponseCodeEnum.其他异常.getCode(), "调用南向接口失败");
				}
			}
		}
		return VpnIntelligenceCommandDistributeResponseDTO.buildSuccess();
	}

	public static void main(String[] args) {



		String xml = "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?><return><version>1.0</version><resultCode>0</resultCode><msg>处理完成</msg></return>";

		VpnIntelligenceCommandDistributeResponseDTO responseDTO =
				XmlUtil.xmlToBean(XmlUtil.readXML(xml).getDocumentElement(), VpnIntelligenceCommandDistributeResponseDTO.class);

		System.out.println(responseDTO);

		System.out.println(CollectTypeEnum.valueOf((null)));

	}

	@Override
	public File getCommandDistributeFile(Long id) {
		VpnIntelligenceCommand command = getById(id);
		int distributeCode = EnumUtil.getBy(IntelligenceCommandOperationTypeEnum::getCode, command.getOperationType()).getDistributeCode();
		// 下发日期
		LocalDateTime distributeDatetime;
		if (command.getDistributeDatetime() != null) {
			distributeDatetime = command.getDistributeDatetime();
		} else {
			distributeDatetime = command.getReportDatetime();
		}
		String distributeDate = DateUtil.format(distributeDatetime, "yyyy-MM-dd");

		Path commandDir = getCommandDir(distributeCode, distributeDate, command.getCommandId());

		List<String> commandZipFiles = FileUtil.listFileNames(commandDir.toString());

		if (CollectionUtil.isNotEmpty(commandZipFiles)) {
			if (commandZipFiles.size() == 1) {
				// 直接返回文件
				return Paths.get(commandDir.toString(), commandZipFiles.get(0)).toFile();
			} else {
				// 压缩成一个zip文件，并返回
				File zip = ZipUtil.zip(commandDir.toFile());
				return zip;
			}
		} else {
			throw new BusinessException(AntiVpnBusinessExceptionEnum.CODE_10012);
		}


	}

	@Override
	public void reDistributeCommand(Long id) {
		VpnIntelligenceCommand byId = this.getById(id);
		String distributeRequestParams = byId.getDistributeRequestParams();
		VpnIntelligenceCommandDistributeRequestDTO requestDto = JSONUtil.toBean(distributeRequestParams, VpnIntelligenceCommandDistributeRequestDTO.class);

		VpnIntelligenceCommandWebService vpnIntelligenceCommandWebService = SpringUtil.getBean(VpnIntelligenceCommandWebService.class);
		vpnIntelligenceCommandWebService.reDistributeIdcCommandProvince(requestDto.getComCode(), requestDto.getProvinceId(),
				requestDto.getRandVal(), requestDto.getPwdHash(), requestDto.getCommand(),
				requestDto.getCommandHash(), requestDto.getCommandType(), requestDto.getEncryptAlgorithm(),
				requestDto.getHashAlgorithm(), requestDto.getCompressionFormat(), requestDto.getCommandVersion(), byId.getSystemCode(), id);


	}

	/**
	 * 调用省平台或者CU
	 *
	 */
	@Override
	public VpnIntelligenceCommandDistributeResponseDTO callSouth(VpnIntelligenceCommandDistributeRequestDTO requestDto, String systemCode) {
		log.info("调用南向接口（smart、webservice）");

		// 调用smart
		iVpnIntelligenceService.callSmart();

		// 调用南向webservice
		return callSouthWs(requestDto, systemCode);
	}

	@Override
	public VpnIntelligenceCommandDistributeResponseDTO ircsCallSouth(VpnIntelligenceIrcsCommandDistributeRequestDTO requestDto, Long provinceId, String comCode) {
		log.info("调用南向接口（smart、webservice）");

		// 调用smart
		iVpnIntelligenceService.callSmart();

		// 调用南向webservice
		return ircsCallSouthWs(requestDto, provinceId, comCode);
	}


}
