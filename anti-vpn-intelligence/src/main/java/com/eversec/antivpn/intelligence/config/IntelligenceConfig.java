package com.eversec.antivpn.intelligence.config;

import com.eversec.antivpn.intelligence.feign.smart.SmartApi;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 情报模块spring配置
 * 1. 线程池
 *
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023-07-31 14:28
 */
@EnableAsync
@Configuration
@EnableFeignClients(clients = { SmartApi.class})
public class IntelligenceConfig {

	/**
	 * spring线程池，接收情报指令
	 * 情报转发到省平台；本地解析、入库、调用smart
	 * @return
	 */
	@Bean("vpnIntelligenceCommandDistribute")
	public Executor doSomethingExecutor() {
		ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
		// 核心线程数：线程池创建时候初始化的线程数
		executor.setCorePoolSize(10);
		// 最大线程数：线程池最大的线程数，只有在缓冲队列满了之后才会申请超过核心线程数的线程
		executor.setMaxPoolSize(20);
		// 缓冲队列：用来缓冲执行任务的队列
		executor.setQueueCapacity(500);
		// 允许线程的空闲时间60秒：当超过了核心线程之外的线程在空闲时间到达之后会被销毁
		executor.setKeepAliveSeconds(60);
		// 线程池名的前缀：设置好了之后可以方便我们定位处理任务所在的线程池
		executor.setThreadNamePrefix("do-something-");
		// 缓冲队列满了之后的拒绝策略：由调用线程处理（一般是主线程）
		executor.setRejectedExecutionHandler(new ThreadPoolExecutor.DiscardPolicy());
		executor.initialize();
		return executor;
	}

}
