<template>
  <el-main class="nacMain">
    <page-title />
    <dic-key v-show="!iskey" />
    <dic-value v-if="iskey" />
  </el-main>
</template>
<script>
import dic<PERSON>ey from './dic-key'
import dicValue from './dic-value'
export default {
  components: {
    dicKey,
    dicValue
  },
  data() {
    return {
      iskey: this.$route.query.enumItem
    }
  },
  watch: {
    $route() {
      this.iskey = this.$route.query.enumItem
    }
  }
}
</script>
