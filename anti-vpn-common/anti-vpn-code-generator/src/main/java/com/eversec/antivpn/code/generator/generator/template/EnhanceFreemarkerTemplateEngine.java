package com.eversec.antivpn.code.generator.generator.template;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.generator.config.OutputFile;
import com.baomidou.mybatisplus.generator.config.builder.CustomFile;
import com.baomidou.mybatisplus.generator.config.po.TableInfo;
import com.baomidou.mybatisplus.generator.engine.FreemarkerTemplateEngine;

import java.io.File;
import java.util.List;
import java.util.Map;

/**
 * 代码生成器支持自定义[DTO\VO等]模版
 */
public final class EnhanceFreemarkerTemplateEngine extends FreemarkerTemplateEngine {

    @Override
    protected void outputCustomFile( List<CustomFile> customFiles,  TableInfo tableInfo,
             Map<String, Object> objectMap) {
        // 自定义实现
        {
            Map<String, Object> aPackage = (Map<String, Object>)objectMap.get("package");
            // api
            objectMap.put("apiName", tableInfo.getEntityName() + "Api");
            objectMap.put("apiPackage", aPackage.get("Parent") + ".api");
            objectMap.put("apiDtoName", tableInfo.getEntityName() + "DTO");
            objectMap.put("apiDtoPackage", aPackage.get("Parent")+ ".api" + ".dto");



            // po
            objectMap.put("poName", tableInfo.getEntityName() + "PO");
            objectMap.put("poPackage", aPackage.get("Entity")+ ".po");



        }

        String entityName = tableInfo.getEntityName();
        String parentPath = getPathInfo(OutputFile.parent);
        customFiles.forEach(file -> {
            String filePath = StringUtils.isNotBlank(file.getFilePath()) ? file.getFilePath() : parentPath;
            if (StringUtils.isNotBlank(file.getPackageName())) {
                filePath = filePath + File.separator + file.getPackageName();
                filePath = filePath.replaceAll("\\.", StringPool.BACK_SLASH + File.separator);
            }
            String fileName = filePath + File.separator + entityName + file.getFileName();
            outputFile(new File(fileName), objectMap, file.getTemplatePath(), file.isFileOverride());
        });
    }

}