package com.eversec.antivpn.support.province.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eversec.antivpn.support.province.api.dto.ProvincePlatformStatusDTO;
import com.eversec.antivpn.support.province.dto.ProvincePlatformStatusQueryConditionDTO;
import com.eversec.antivpn.support.province.convertor.ProvincePlatformStatusFactory;
import com.eversec.antivpn.support.province.entity.ProvincePlatformStatus;
import com.eversec.antivpn.support.province.mapper.ProvincePlatformStatusMapper;
import com.eversec.antivpn.support.province.mapper.ProvincePlatformStatusExtMapper;
import com.eversec.antivpn.support.province.service.IProvincePlatformStatusService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eversec.stark.generic.common.util.ListBuilder;
import com.eversec.stark.generic.common.util.PageBuilder;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;

import java.text.SimpleDateFormat;
import java.util.List;

/**
 * <p>
 * 省平台信息（企业测） 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-12
 */
@Service
@Slf4j
@AllArgsConstructor
public class ProvincePlatformStatusServiceImpl extends ServiceImpl<ProvincePlatformStatusMapper, ProvincePlatformStatus> implements IProvincePlatformStatusService {

    private final ProvincePlatformStatusFactory provincePlatformStatusFactory;

    private final ProvincePlatformStatusExtMapper provincePlatformStatusMapperExt;

    @Override
    public List<ProvincePlatformStatusDTO> listAllLatestReportProvincePlatform(ProvincePlatformStatusQueryConditionDTO paramDto) {
//        QueryWrapper<ProvincePlatformStatus> provincePlatformStatusQueryWrapper = paramDto.autoWrapper();
//        provincePlatformStatusQueryWrapper.select("id,com_code,province_id,system_code,network_business_ids,current_state,time_stamp,create_user,update_user,update_datetime, max(create_datetime) as create_datetime");
//        LambdaQueryWrapper<ProvincePlatformStatus> wrapper = provincePlatformStatusQueryWrapper.lambda();
//        wrapper.groupBy(ProvincePlatformStatus::getComCode, ProvincePlatformStatus::getProvinceId, ProvincePlatformStatus::getSystemCode);
        List<ProvincePlatformStatus> list = provincePlatformStatusMapperExt.lisNewestProvincePlatformStatus();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        list.forEach(provincePlatformStatus -> provincePlatformStatus.setTimeStamp(sdf.format(provincePlatformStatus.getCreateDatetime())));
        List<ProvincePlatformStatusDTO> provincePlatformStatusDTOS = ListBuilder.convertList(list, provincePlatformStatusFactory::entityToDto);
        return provincePlatformStatusDTOS;
    }

    @Override
    public Page<ProvincePlatformStatusDTO> page(ProvincePlatformStatusQueryConditionDTO paramDto, Page<ProvincePlatformStatus> pageInfo) {
        Page<ProvincePlatformStatus> page = this.page(pageInfo, paramDto.autoWrapper());
        Page<ProvincePlatformStatusDTO> provincePlatformStatusDTOPage = PageBuilder.convertMybatisPlusPage(page, provincePlatformStatusFactory::entityToDto);
        return provincePlatformStatusDTOPage;
    }


}
