<template>
  <section class="title_box">
    <div class="box_tit">AI 模型识别通信日志置信度分布</div>
    <div class="box_con">
      <Empty class="empty" v-if="isEmpty" color="#fff" />
      <e-charts v-else type="bar" :options="options"></e-charts>
    </div>
  </section>
</template>

<script>
import Empty from "@/components/empty.vue";

export default {
  components: {
    Empty,
  },
  props: {
    rateData: {
      type: Object,
    },
  },
  watch: {
    rateData: {
      handler(val) {
        this.obj = val;
        this.init();
      },
      immediate: true,
      deep: true,
    },
  },
  data() {
    return {
      options: null,
      obj: {},
      isEmpty: false,
    };
  },
  mounted() {
    this.init();
  },
  methods: {
    init() {
      let total = this.obj.realData.reduce((n, cur) => n + cur, 0);

      let max = Math.max(...this.obj.realData);
      if (max > 10000) {
        for (let index = 0; index < this.obj.realData.length; index++) {
          this.obj.realData[index]=this.obj.realData[index]/10000
        }
      }
      total == 0 ? (this.isEmpty = true) : (this.isEmpty = false);
      this.options = {
        grid: {
          left: "10",
          right: "20",
          bottom: "10",
          top: "40",
          containLabel: true,
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            lineStyle: {
              color: "#ddd",
            },
          },
          backgroundColor: "rgba(74, 178, 230,0.2)",
          padding: [5, 10],
          textStyle: {
            color: "#fff",
          },
          extraCssText: "box-shadow: 0 0 5px rgba(0,0,0,0.3)",
        },
        xAxis: {
          type: "category",
          data: this.obj.xAxisData,
          boundaryGap: false,

          splitLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          axisLine: {
            lineStyle: {
              color: "#444956",
            },
          },
          axisLabel: {
            margin: 10,
            textStyle: {
              fontSize: 12,
              color: "#fff",
            },
          },
        },
        yAxis: [
          {
            name: max>10000?'日志量(万)':"日志量",
            type: "value",
            splitLine: {
              lineStyle: {
                color: ["#1a2234"],
              },
            },
            axisTick: {
              show: false,
            },
            axisLine: {
              show: false,
              lineStyle: {
                color: "#8e9397",
              },
            },
            axisLabel: {
              textStyle: {
                fontSize: 12,
                color: "#fff",
              },
            },
          },
          // {
          //   name: "频率（%）",
          //   splitLine: {
          //     lineStyle: {
          //       color: ["#1a2234"],
          //     },
          //   },
          //   axisTick: {
          //     show: false,
          //   },
          //   axisLine: {
          //     show: false,
          //     lineStyle: {
          //       color: "#fff",
          //     },
          //   },
          //   axisLabel: {
          //     textStyle: {
          //       fontSize: 12,
          //     },
          //   },
          // },
        ],
        series: [
          {
            name: "实际出现次数",
            type: "bar",
            showSymbol: false,
            symbol: "circle",
            symbolSize: 6,
            smooth: true,
            barWidth: "1px",
            data: this.obj.realData,
            itemStyle: {
              normal: {
                color: "#48aadc",
              },
            },
            lineStyle: {
              normal: {
                width: 1,
              },
            },
          },
          // {
          //   name: "正态分布",
          //   yAxisIndex: 1,
          //   data: this.obj.stdplotData,
          //   type: "line",
          //   smooth: true,
          //   itemStyle: {
          //     normal: {
          //       color: "#c04060",
          //     },
          //   },
          //   lineStyle: {
          //     normal: {
          //       width: 1,
          //     },
          //   },
          // },
        ],
      };
    },
    getChartData(baseData, cityName) {
      var data = {
        baseData: [],
        xAxisData: [], //x轴
        realData: [], //实际数值
        stdplotData: [], //正态分布数值
      };
      return data;
    },
  },
};
</script>

<style scoped lang="scss">
.empty {
  margin: 0 !important;
  height: 100%;
  display: flex;
  justify-content: center;
  flex-direction: column;
}
.title_box {
  .box_tit {
    color: #a5acb4;
    font-size: 16px;
    padding: 0 39px;
    line-height: 38px;
    height: 37px;
    background: url(../overview/img/tit_bg.png) no-repeat;
    color: #fff;
  }
  .box_con {
    height: 300px;
  }
}
</style>
