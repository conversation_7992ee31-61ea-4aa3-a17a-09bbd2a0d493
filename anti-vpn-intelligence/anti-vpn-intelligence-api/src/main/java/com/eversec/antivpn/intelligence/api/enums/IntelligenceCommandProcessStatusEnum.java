package com.eversec.antivpn.intelligence.api.enums;

/**
 * 指令处理状态
 * 接收：RECEIVE；成功：SUCCESS；失败：FAIL
 *
 */
public enum IntelligenceCommandProcessStatusEnum {

    RECEIVE("接收"),
    PARSE("解析"),
    SUCCESS("成功"),
    FAIL("失败"),

    ;


    IntelligenceCommandProcessStatusEnum(String desc){
        this.desc = desc;
    }

    private String desc;

    public String getDesc() {
        return desc;
    }
}
