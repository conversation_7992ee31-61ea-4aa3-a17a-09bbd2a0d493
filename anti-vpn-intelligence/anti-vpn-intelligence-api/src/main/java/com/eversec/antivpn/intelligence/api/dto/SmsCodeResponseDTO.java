package com.eversec.antivpn.intelligence.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 短信验证码响应DTO
 * 
 * <AUTHOR>
 * @since 2025-01-12
 */
@Data
@Schema(name = "SmsCodeResponseDTO", description = "短信验证码响应")
public class SmsCodeResponseDTO {

    @Schema(description = "是否成功", example = "true")
    private Boolean success;

    @Schema(description = "响应消息", example = "验证码发送成功")
    private String message;

    @Schema(description = "验证码有效期（分钟）", example = "5")
    private Integer expireMinutes;

    @Schema(description = "下次可发送时间（秒）", example = "60")
    private Integer nextSendSeconds;

    public static SmsCodeResponseDTO success(String message, Integer expireMinutes) {
        SmsCodeResponseDTO response = new SmsCodeResponseDTO();
        response.setSuccess(true);
        response.setMessage(message);
        response.setExpireMinutes(expireMinutes);
        return response;
    }

    public static SmsCodeResponseDTO success(String message, Integer expireMinutes, Integer nextSendSeconds) {
        SmsCodeResponseDTO response = success(message, expireMinutes);
        response.setNextSendSeconds(nextSendSeconds);
        return response;
    }

    public static SmsCodeResponseDTO failure(String message) {
        SmsCodeResponseDTO response = new SmsCodeResponseDTO();
        response.setSuccess(false);
        response.setMessage(message);
        return response;
    }

    public static SmsCodeResponseDTO failure(String message, Integer nextSendSeconds) {
        SmsCodeResponseDTO response = failure(message);
        response.setNextSendSeconds(nextSendSeconds);
        return response;
    }

}
