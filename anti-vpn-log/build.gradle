
dependencies {

    implementation project(':anti-vpn-log:anti-vpn-log-api')
    implementation project(':anti-vpn-common:anti-vpn-config')
    implementation project(':anti-vpn-common:anti-vpn-util')
    implementation project(":anti-vpn-supoort:anti-vpn-supoort-api")
    // mybatis-plus工具扩展等
    implementation "com.eversec.stark:stark-common:${starkCommonVersion}"
    implementation "com.eversec.stark.generic:generic-common:${genericVersion}"
    // 数据字典、系统配置api
    implementation "com.eversec.stark.generic:generic-sys-api:${genericVersion}"

    implementation "cn.hutool:hutool-all:${hutoolVersion}"
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation "org.springframework.cloud:spring-cloud-starter-bootstrap"
    implementation "org.springframework.cloud:spring-cloud-starter-netflix-eureka-client"
    implementation "com.baomidou:mybatis-plus-boot-starter:${mybatisPlusVersion}"
    implementation "com.baomidou:dynamic-datasource-spring-boot-starter:${mybatisPlusDatasourceVersion}"
    implementation "org.springdoc:springdoc-openapi-ui:${springdocOpenapiVersion}"
    implementation "com.github.xiaoymin:knife4j-springdoc-ui:${knife4jSpringdocVersion}"
    implementation "com.clickhouse:clickhouse-jdbc:${clickhouseConnectorVersion}"
    implementation group: 'io.swagger', name: 'swagger-annotations', version: '1.5.22'
    implementation "com.alibaba.fastjson2:fastjson2:2.0.26"
    implementation "com.alibaba.fastjson2:fastjson2:2.0.26"
    implementation "io.minio:minio:8.3.1"
    implementation "dom4j:dom4j:1.6.1"
    // leader判断
    implementation "org.springframework.integration:spring-integration-jdbc"
    implementation "com.baomidou:mybatis-plus-boot-starter:${mybatisPlusVersion}"
    implementation "org.springdoc:springdoc-openapi-ui:${springdocOpenapiVersion}"
    implementation "com.github.xiaoymin:knife4j-springdoc-ui:${knife4jSpringdocVersion}"
    test {
        useTestNG()
    }
}
