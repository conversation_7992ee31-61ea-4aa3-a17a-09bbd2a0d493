<template>
  <el-dialog
    title="自定义搜索字段"
    :visible.sync="dialogVisible"
    width="800px"
    :before-close="handleClose"
    class="dialogCustomBox"
  >
    <el-checkbox
      :indeterminate="isIndeterminate"
      v-model="checkAll"
      @change="handleCheckAllChange"
      >全选</el-checkbox
    >
    <div style="margin: 15px 0;"></div>
    <el-checkbox-group
      v-model="checkedD"
      @change="handleCheckedCitiesChange"
    >
      <el-checkbox v-for="item in searchD" :label="item.prop" :key="item.prop">
        {{ item.label }}
      </el-checkbox>
    </el-checkbox-group>

    <span slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="confirmFn">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import searchD from './searchData.js'
export default {
  data() {
    return {
      dialogVisible: false,
      checkAll: false,
      isIndeterminate: true,
      searchD,
      checkedD: [] // ['vpnName', 'logId', 'srcPort']
    }
  },
  computed: {
    allKeys() {
      return searchD.map(el => el.prop)
    }
  },
  methods: {
    init(nowChecked) {
      console.log('init', nowChecked)
      this.checkedD = nowChecked.map(el => el.prop);
      this.dialogVisible = true;
    },
    handleClose() {
      this.dialogVisible = false;
    },
    handleCheckAllChange(val) {
      console.log('全选', val)
      this.checkedD = val ? this.allKeys : [];
      this.isIndeterminate = false;
    },
    handleCheckedCitiesChange(value) {
      console.log('选择', value);
      let checkedCount = value.length;
      this.checkAll = checkedCount === this.searchD.length;
      this.isIndeterminate =
        checkedCount > 0 && checkedCount < this.searchD.length;
    },
    confirmFn() {
      this.$emit('updateSearchD', this.checkedD)
      this.dialogVisible = false;
    }
  }
};
</script>

<style scoped lang="scss">
.dialogCustomBox {
  .el-checkbox-group {
    line-height: 40px;
  }
  
  .el-checkbox {
    min-width: 130px;
  }
}
</style>
