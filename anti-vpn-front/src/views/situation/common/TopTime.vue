<template>
  <section
    class="time_top_box"
    :style="{ borderColor: borderColor || 'transparent' }"
  >
    <div class="top_time">
      <slot name="topTimeL"></slot>
      <span
        v-for="(item, i) in timeD"
        :class="i == curI ? 'active' : ''"
        :key="item.label"
        @click="timeChange(i, item)"
      >
        {{ item.label }}
      </span>

      <el-date-picker
        class="mr10"
        v-model="timeAuto"
        type="datetimerange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        value-format="yyyy-MM-dd HH:mm:ss"
        @change="timeAutoChange"
        v-if="isShow_dataPicker"
      >
      </el-date-picker>
    </div>
    <div class="top_time_r">
      <slot name="topTimeR"></slot>
    </div>
  </section>
</template>

<script>
import { formatTime } from "@/utils/time.js";
export default {
  props: {
    timeOpt: {
      type: Array,
      default: () => [
        { label: "今日", day: 1, type: "day", screenType: "TODAY" },
        { label: "近一周", day: 7, type: "week", screenType: "THISWEEK" },
        { label: "近一月", day: 30, type: "month", screenType: "THISMONTH" },
        { label: "至今", day: null, type: "all", screenType: "SOFAR" }
        // { label: "近一年", day: 365, type: "year", screenType: 'TODAY' },
        // { label: "自定义", day: "", type: "auto", screenType: '' } // day空时，为自定义
      ]
    },
    // 下划线-颜色
    borderColor: {
      type: String,
      default: "#e1e3ed"
    },
    initCurI: {
      // 初始化选中
      type: Number,
      default: 0
    },
    isShow_dataPicker: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      curI: this.initCurI,
      timeD: [],
      timeAuto: "",
      timeTitle: "选中时间段",
      timeRange: ["", ""]
    };
  },
  watch: {
    timeOpt: {
      handler(v) {
        this.timeD = v;
        this.timeChange(this.curI);
      },
      deep: true,
      // immediate: true
    },
    initCurI(v) {
      this.curI = v;
        this.timeChange(this.curI);
    },
  },
  mounted() {
    // this.timeChange(this.curI, this.timeD[this.curI]);
  },
  methods: {
    getTime(day = 7, fmt = "yyyy-MM-dd hh:mm:ss") {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * day);
      // console.log('时间段：', formatTime(start, fmt), formatTime(end, fmt));
      return [formatTime(start, fmt), formatTime(end, fmt)];
    },
    timeChange(i) {
      this.curI = i;
      let ft = this.timeD[i] && this.timeD[i].type;
      let screenType = this.timeD[i] && this.timeD[i].screenType;

      if(screenType) this.$emit("timeChange", screenType, ft);
    },
    timeAutoChange() {
      this.curI = this.timeD.findIndex(el => !el.day);
      console.log("time-change:", this.timeAuto, this.curI);
      if (!this.timeAuto) {
        return;
      }
      if (this.timeAuto[0] == this.timeAuto[1]) {
        this.timeAuto[1] = this.timeAuto[1].slice(0, 11) + "23:59:59";
      }
      let ft = this.timeAuto && `${this.timeAuto[0]}-${this.timeAuto[1]}`;
      this.timeRange = this.timeAuto;
      this.$emit("timeChange", this.timeAuto, ft);
    }
  }
};
</script>

<style scoped lang="scss">
// 头部时间
.time_top_box {
  display: flex;
  // justify-content: space-between;
  // padding: 15px 20px 8px;
  // border-bottom: 1px solid #e1e3ed;
  font-size: 14px;
  .top_time {
    line-height: 40px;
    display: flex;
    border: 1px solid #028fc7;
    > span {
      // margin-right: 48px;
      color: #84909c;
      cursor: pointer;
      background: #071f39;
      width: 60px;
      height: 26px;
      line-height: 26px;
      display: block;
      text-align: center;
      font-weight: 400;
      &:not(:nth-last-child(1)) {
        border-right: 1px solid #028fc7;
      }
    }
    .active {
      // color: var(--primary-bg);
      color: #0496bf;
    }
  }
}

.time-line {
  min-width: 430px;
  font-size: 14px;
  color: #3f405d;
  background: #f7f8fc;
  border-radius: 5px;
  height: 40px;
  display: flex;
  align-items: center;
  padding: 0 10px;
  i {
    display: inline-block;
    width: 18px;
    height: 18px;
    // background: url("../img/time-icon.png");
    margin-right: 10px;
  }
}
</style>
