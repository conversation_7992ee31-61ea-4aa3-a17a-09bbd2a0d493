package com.eversec.antivpn.log.schedule;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.text.csv.CsvUtil;
import cn.hutool.core.text.csv.CsvWriter;
import com.eversec.antivpn.emnus.AntiVpnReportCodeEnum;
import com.eversec.antivpn.emnus.ScreenTypeEnum;
import com.eversec.antivpn.log.entity.ProvincePlatformStatus;
import com.eversec.antivpn.log.mapper.E1CommLogViewMapper;
import com.eversec.antivpn.log.mapper.E2MachineLearningLogViewMapper;
import com.eversec.antivpn.log.mapper.LogVpnIntelligenceMapper;
import com.eversec.antivpn.log.mapper.ProvincePlatformStatusMapperExt;
import com.eversec.antivpn.log.mapper.vo.BaseSearchVO;
import com.eversec.antivpn.log.mapper.vo.PlatformInterfaceVO;
import com.eversec.antivpn.log.mapper.vo.TypeCountVO;
import com.eversec.antivpn.log.service.LogService;
import com.eversec.antivpn.log.util.SearchParamUtil;
import com.eversec.antivpn.support.province.api.dto.ProvinceStatusDTO;
import com.eversec.antivpn.util.ReportUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.integration.support.leader.LockRegistryLeaderInitiator;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.*;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/11/16 16:03
 **/
@Slf4j
@Component
public class MonitorDataReport {

    @Resource
    private E1CommLogViewMapper e1CommLogViewMapper;
    @Resource
    private E2MachineLearningLogViewMapper e2MachineLearningLogViewMapper;

    @Value("${app.anti-vpn.deploy-place}")
    private String deployPlace;
    @Resource
    private SearchParamUtil searchParamUtil;
    @Resource
    private LogVpnIntelligenceMapper logVpnIntelligenceMapper;
    @Resource
    private ProvincePlatformStatusMapperExt provincePlatformStatusMapperExt;
    private Map<String, TypeCountVO> networkBusinessTypeMap = new HashMap<>();
    private final Map<String, TypeCountVO> e1SystemCodeTypeMap = new HashMap<>();
    private final Map<String, TypeCountVO> e2SystemCodeTypeMap = new HashMap<>();
    private Map<String, Boolean> systemCodeTypeStatusMap = new HashMap<>();
    private Long e1Count = 0l;
    private Long e2Count = 0l;
    @Resource
    private LockRegistryLeaderInitiator leaderElectionLockRegistryLeaderInitiator;
    @Resource
    private com.eversec.antivpn.config.AntiVpnProperties antiVpnProperties;


    public Map<String, Boolean> getSystemCodeTypeStatus() {
        return systemCodeTypeStatusMap;
    }

    @Scheduled(fixedDelayString = "${app.anti-vpn.monitor:600000}")
    public void monitorSystemReport() {
        if(!leaderElectionLockRegistryLeaderInitiator.getContext().isLeader()){
            return;
        }
        log.info("当前节点为leaser节点，执行上报企业状态操作");
        log.info("当前平台类型为：" + deployPlace);
        if ("system".equals(deployPlace)) {
            log.info("执行system类型监控");
            systemTypeMonitor();
        }
        if ("province".equals(deployPlace)) {
            log.info("执行" + deployPlace + "类型监控");
            provinceTypeMonitor();
        }
    }

    private void systemTypeMonitor() {
        BaseSearchVO baseSearchVO = searchParamUtil.getBaseSearchVOWithTimePartition(ScreenTypeEnum.TODAY);
        Long e1TodayCount = e1CommLogViewMapper.countNum(baseSearchVO);
        Long e2TodayCount = e2MachineLearningLogViewMapper.countNum(baseSearchVO);
        PlatformInterfaceVO platformInterfaceVO = logVpnIntelligenceMapper.getCurrentPlatformInterfaceInfo();
        boolean currentStatus = true;
        if (e1Count.equals(e1TodayCount) || e1TodayCount==0L) currentStatus = false;
        if (e2Count.equals(e2TodayCount) || e2TodayCount==0L) currentStatus = false;
        if (currentStatus) {
            platformInterfaceVO.setCurrentState(0);
        } else {
            platformInterfaceVO.setCurrentState(1);
        }
        List<PlatformInterfaceVO> dataList = new ArrayList<>();
        dataList.add(platformInterfaceVO);
        saveProvincePlatformStatus(dataList);
        doReport(dataList);
    }

    private void provinceTypeMonitor() {
        BaseSearchVO baseSearchVO = searchParamUtil.getBaseSearchVOWithTimePartition(ScreenTypeEnum.TODAY);
        List<TypeCountVO> e1VoList = e1CommLogViewMapper.getSystemLogAggCount(baseSearchVO);
        Map<String, Boolean> e1SystemCodeTypeStatusMap = reportStatus(e1SystemCodeTypeMap, listToMap(e1VoList));
        List<TypeCountVO> e2VoList = e2MachineLearningLogViewMapper.getSystemLogAggCount(baseSearchVO);
        Map<String, Boolean> e2SystemCodeTypeStatusMap = reportStatus(e2SystemCodeTypeMap, listToMap(e2VoList));
        List<PlatformInterfaceVO> platformInterfaceVO = logVpnIntelligenceMapper.getPlatformInterfaceInfo();
        Iterator<PlatformInterfaceVO> iterator = platformInterfaceVO.iterator();
        while (iterator.hasNext()) {
            if (iterator.next().getConfigType().equals("CURRENT")) iterator.remove();
        }
        for (int i = 0; i < platformInterfaceVO.size(); i++) {
            String systemCode = platformInterfaceVO.get(i).getSystemCode();
            int trueStatusTime = 0;
            if (e1SystemCodeTypeStatusMap.containsKey(systemCode) && e1SystemCodeTypeStatusMap.get(systemCode))
                trueStatusTime++;
            if (e2SystemCodeTypeStatusMap.containsKey(systemCode) && e2SystemCodeTypeStatusMap.get(systemCode))
                trueStatusTime++;
            if (trueStatusTime == 2) {
                platformInterfaceVO.get(i).setCurrentState(0);
            } else {
                platformInterfaceVO.get(i).setCurrentState(1);
            }
        }
        saveProvincePlatformStatus(platformInterfaceVO);
        doReport(platformInterfaceVO);
    }


    public void saveProvincePlatformStatus(List<PlatformInterfaceVO> platformInterfaceVOList) {
        if (platformInterfaceVOList == null || platformInterfaceVOList.size() == 0) return;
        for (int i = 0; i < platformInterfaceVOList.size(); i++) {
            PlatformInterfaceVO platformInterfaceVO = platformInterfaceVOList.get(i);
            ProvincePlatformStatus provincePlatformStatus = new ProvincePlatformStatus();
            provincePlatformStatus.setProvinceId(platformInterfaceVO.getProvinceId());
            provincePlatformStatus.setComCode(platformInterfaceVO.getComCode());
            provincePlatformStatus.setSystemCode(platformInterfaceVO.getSystemCode());
            provincePlatformStatus.setCurrentState(platformInterfaceVO.getCurrentState());
            provincePlatformStatus.setNetworkBusinessIds(dealNetworkBusinessIds(platformInterfaceVO.getNetworkBusinessIds()));
            provincePlatformStatus.setTimeStamp(LocalDateTimeUtil.format(LocalDateTime.now(), "yyyy-MM-dd HH:mm:ss"));
            provincePlatformStatusMapperExt.insert(provincePlatformStatus);
        }
    }

    private String dealNetworkBusinessIds(String networkBusinessIds) {
        if (networkBusinessIds == null || "".equals(networkBusinessIds)) return null;
        String arrayStart = "[";
        String separator = ",";
        String arrayEnd = "]";
        String[] strs = networkBusinessIds.split("|");
        String str = arrayStart + strs[0];
        for (int i = 1; i < strs.length; i++) {
            if ("|".equals(strs[i])) continue;
            str = str + separator + strs[i];
        }
        str = str + arrayEnd;
        return str;
    }

    private Map<String, TypeCountVO> listToMap(List<TypeCountVO> voList) {
        Map<String, TypeCountVO> map = new HashMap<>();
        if (voList == null || voList.isEmpty()) return map;
        voList.forEach(vo -> map.put(vo.getKey(), vo));
        return map;
    }

    private Map<String, Boolean> reportStatus(Map<String, TypeCountVO> historyData, Map<String, TypeCountVO> nowData) {
        Map<String, Boolean> result = new HashMap<>();
        for (Map.Entry<String, TypeCountVO> entry : historyData.entrySet()) {
            TypeCountVO vo = nowData.get(entry.getKey());
            if (vo == null) {
                //新的1天，未上报
                result.put(entry.getKey(), false);
                continue;
            }
            if (vo.getNum().equals(entry.getValue().getNum())) {
                //数量相同，未上报
                result.put(entry.getKey(), false);
            } else {
                //数量不同，说明已上报，刷新
                historyData.put(entry.getKey(), entry.getValue());
                result.put(entry.getKey(), true);
            }
        }
        //add new today
        for (Map.Entry<String, TypeCountVO> entry : nowData.entrySet()) {
            if (!historyData.containsKey(entry.getKey())) {
                historyData.put(entry.getKey(), entry.getValue());
                result.put(entry.getKey(), true);
            }
        }
        return result;
    }


    private void doReport(List<PlatformInterfaceVO> dataList) {
        if (dataList.isEmpty()) return;
        List<ProvinceStatusDTO> provinceStatusDTOList = new ArrayList<>();
        dataList.forEach(vo -> {
            ProvinceStatusDTO provinceStatusDTO = new ProvinceStatusDTO();
            provinceStatusDTO.setVersion("1.0");
            provinceStatusDTO.setComCode(vo.getComCode());
            provinceStatusDTO.setProvinceId(vo.getProvinceId());
            provinceStatusDTO.setNetworkBusinessId(vo.getNetworkBusinessIds());
            provinceStatusDTO.setProvinceId(vo.getProvinceId());
            provinceStatusDTO.setCurrentState(vo.getCurrentState());
            provinceStatusDTO.setTimeStamp(LocalDateTimeUtil.format(LocalDateTime.now(), "yyyy-MM-dd HH:mm:ss"));
            provinceStatusDTOList.add(provinceStatusDTO);
        });
        // 上报路径
        File reportFile = ReportUtil.getReportFile(antiVpnProperties.getReportDataDir(), AntiVpnReportCodeEnum.PROVINCE_PLATFORM_STATUS, dataList.get(0).getProvinceId(),  dataList.get(0).getComCode(), null);
        File reportTempFile = ReportUtil.getTempFile(reportFile);
        log.info("生成并上报企业状态文件:{}", reportFile);
        try (CsvWriter writer = CsvUtil.getWriter(reportTempFile, StandardCharsets.UTF_8)) {
            for (int i = 0; i < provinceStatusDTOList.size(); i++) {
                writer.writeLine(provinceStatusDTOList.get(i).toCvsFields());
                writer.flush();
            }
            FileUtil.move(reportTempFile, reportFile, true);
        }

    }
}