<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eversec.antivpn.log.mapper.E2MachineLearningLogMapper">

    <resultMap id="TypeCount" type="com.eversec.antivpn.log.mapper.vo.TypeCountVO">
        <result column="num" jdbcType="BIGINT" property="num"/>
        <result column="key_type" jdbcType="INTEGER" property="key"/>
    </resultMap>

    <resultMap id="VpnAggVO" type="com.eversec.antivpn.log.mapper.vo.VpnAggVO">
        <result column="vpn_service_name" jdbcType="VARCHAR" property="vpnServiceName"/>
        <result column="resource_name" jdbcType="VARCHAR" property="resourceName"/>
        <result column="person_num" jdbcType="BIGINT" property="personNum"/>
        <result column="customer_num" jdbcType="INTEGER" property="customerNum"/>
        <result column="service_times" jdbcType="BIGINT" property="serviceTimes"/>
        <result column="resource_node_num" jdbcType="INTEGER" property="resourceNodeNum"/>
    </resultMap>
    <select id="getProvinceAggregate" resultMap="TypeCount">
        SELECT
        log_province_id key_type,
        count(1) AS num
        FROM e2_machine_learning_log
        <where>
            <if test="startTimePartition!=null">
                day >= #{startTimePartition}
            </if>
            <if test="endTimePartition!=null">
                AND day &lt;= #{endTimePartition}
            </if>
        </where>
        GROUP BY
        log_province_id
    </select>


    <select id="getProtocolTypeAggregate" resultMap="TypeCount">
        SELECT
        count(1) num,
        protocol_type key_type
        FROM e2_machine_learning_log
        <where>
            <if test="startTimePartition!=null">
                day >= #{startTimePartition}
            </if>
            <if test="endTimePartition!=null">
                AND day &lt;= #{endTimePartition}
            </if>
        </where>
        GROUP BY
        protocol_type
    </select>


    <select id="getNetworkBusinessTypeAggregate" resultMap="TypeCount">
        SELECT
        count(1) num,
        network_business_id key_type
        FROM e2_machine_learning_log
        <where>
            <if test="startTimePartition!=null">
                day >= #{startTimePartition}
            </if>
            <if test="endTimePartition!=null">
                AND day &lt;= #{endTimePartition}
            </if>
        </where>
        GROUP BY
        network_business_id
    </select>


    <select id="getTodayProvinceAggregate" resultMap="TypeCount">
        SELECT
        log_province_id key_type,
        count(1) AS num
        FROM e2_machine_learning_log
        <where>
            <if test="timePartition!=null">
                day = #{timePartition}
            </if>
        </where>
        GROUP BY
        log_province_id
    </select>


    <select id="getDateHistogram" resultMap="TypeCount">
        SELECT
        `day` key_type,
        count(1) AS num
        FROM e2_machine_learning_log
        <where>
            <if test="startTimePartition!=null">
                AND day >= #{startTimePartition}
            </if>
            <if test="endTimePartition!=null">
                AND day &lt;= #{endTimePartition}
            </if>
        </where>
        GROUP BY
        day
        ORDER BY day ASC
    </select>

    <select id="getResourceNodeCount" resultType="Long">
        select count(dest_ip) from (select distinct dest_ip from e2_machine_learning_log where traffic_type='2'
                <where>
                    <if test="startTimePartition!=null">
                        AND day >= #{startTimePartition}
                    </if>
                    <if test="endTimePartition!=null">
                        AND day &lt;= #{endTimePartition}
                    </if>
                </where>
            )
    </select>

    <select id="getUsedResourceNodeAggregate" resultMap="VpnAggVO">
        SELECT
        vpn_id vpn_service_name,
        count(1) AS person_num
        FROM e1_comm_log
        <where>
            <if test="startTimePartition!=null">
                day >= #{startTimePartition}
            </if>
            <if test="endTimePartition!=null">
                AND day &lt;= #{endTimePartition}
            </if>
        </where>
        GROUP BY
        vpn_id
    </select>
</mapper>
