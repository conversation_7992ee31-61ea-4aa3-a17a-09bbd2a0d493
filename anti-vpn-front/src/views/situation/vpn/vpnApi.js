import httpRequest from '@/utils/httpRequest.js';
const BaseServer = '/anti-vpn-service';

// VPN
export default {
  // num 1---VPN服务商总量
  vpnServiceCount(o) {
    return httpRequest({
      url: BaseServer + `/log/vpnserviceoverall/vpnServiceCount`,
      method: "get",
      params: o,
    });
  },

  // num 2---资源节点总量
  resourceNodeCount(o) {
    return httpRequest({
      url: BaseServer + `/log/vpnserviceoverall/resourceNodeCount`,
      method: "get",
      params: o,
    });
  },
  // num 3---近7日活跃的VPN服务商数量
  day7_service(o) {
    return httpRequest({
      url: BaseServer + `/log/vpnserviceoverall/lastSevenDayActiveVPNServiceNum`,
      method: "get",
      params: o,
    });
  },
  // num 4---近7日活跃的资源节点数量
  day7_node(o) {
    return httpRequest({
      url: BaseServer + `/log/vpnserviceoverall/lastSevenDayActiveResourceNodeNum`,
      method: "get",
      params: o,
    });
  },

  // left2 -- 用户使用资源节点热度 TOP10
  usedResourceNodeTop10(o) {
    return httpRequest({
      url: BaseServer + `/log/vpnserviceoverall/usedResourceNodeTop10`,
      method: "get",
      params: o,
    });
  },
  // left3 ---近7日服务商及资源节点活跃量趋势
  vpnServiceAndResourceNodeDateHistogram(o) {
    return httpRequest({
      url: BaseServer + `/log/vpnserviceoverall/vpnServiceAndResourceNodeDateHistogram`,
      method: "get",
      params: o,
    });
  },

  // mid -- 地图
  map(o) {
    return httpRequest({
      url: BaseServer + `/log/vpnserviceoverall/map`,
      method: "get",
      params: o
    });
  },
  
  // mid2 -- VPN服务商服务用户量 TOP10
  theNewestVpnService(o) {
    return httpRequest({
      url: BaseServer + `/log/vpnserviceoverall/theNewestVpnService`,
      method: "get",
      params: o,
    });
  },


  // rig1 -- 所在地区VPN服务商数量 TOP10
  countryVpnServiceNumTop10(o) {
    return httpRequest({
      url: BaseServer + `/log/vpnserviceoverall/countryVpnServiceNumTop10`,
      method: "get",
      params: o
    });
  },

  // rig2 -- 所在地区VPN服务资源节点数量 TOP10
  countryResourceNodeNumTop10(o) {
    return httpRequest({
      url: BaseServer + `/log/vpnserviceoverall/countryResourceNodeNumTop10`,
      method: "get",
      params: o
    });
  },
  // rig3 -- VPN服务商用户量TOP10
  vpnVpnServicePersonNumTop10(o) {
    return httpRequest({
      url: BaseServer + `/log/vpnserviceoverall/vpnVpnServicePersonNumTop10`,
      method: "get",
      params: o
    });
  },
};
