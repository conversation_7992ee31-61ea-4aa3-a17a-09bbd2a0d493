const searchD = [
  {
    prop: "vpnName",
    label: "VPN名称"
  },
  {
    prop: "typeId",
    label: "VPN类型",
    type: "select",
    options: [],
    enumKey: "VPN_TYPE",
    labelKey: "enumVal",
    valueKey: "enumKey"
  },
  {
    prop: "comCode",
    label: "运营商", // 编码
    type: "select",
    options: [],
    enumKey: "OPERATOR",
    labelKey: "enumVal",
    valueKey: "enumKey"
  },
  {
    prop: "networkBusinessId",
    label: "网络类型",
    type: "select",
    options: [],
    enumKey: "NETWORK_TYPE",
    labelKey: "enumVal",
    valueKey: "enumKey"
  },
  
  {
    prop: "protocolType",
    label: "传输层协议",
    type: "select",
    options: [],
    enumKey: "TRANSPORT_PROTOCOL",
    labelKey: "enumVal",
    valueKey: "enumKey"
  },

  {
    prop: "logId",
    label: "日志记录ID",
  },
  {
    prop: "houseId",
    label: "机房ID",
  },
  {
    prop: "houseName",
    label: "机房名称",
  },
  {
    prop: "logProvinceId",
    label: "日志监测所在省/直辖市",
    type: "select",
    options: [],
    enumKey: "PROVINCE_CODE",
    labelKey: "enumVal",
    valueKey: "enumKey"
  },
  {
    prop: "srcIp",
    label: "源IP",
  },
  {
    prop: "srcPort",
    label: "源端口",
  },
  {
    prop: "srcCountry",
    label: "源IP所属国家",
  },
  {
    prop: "srcInfo",
    label: "源IP主体信息",
  },
  {
    prop: "destIp",
    label: "目的IP",
  },
  {
    prop: "destPort",
    label: "目的端口",
  },
  {
    prop: "dstCountry",
    label: "目的IP所属国家",
  },
  {
    prop: "destInfo",
    label: "目的IP主体信息",
  },


  {
    prop: "trafficType",
    label: "流量类型",
    type: "select",
    options: [
      { label: '流入', value: 1 },
      { label: '流出', value: 2 },
    ],
  },
  
  {
    prop: "modelName",
    label: "监测模型名称",
  },
  {
    prop: "modelVersion",
    label: "监测模型版本",
  },
  {
    prop: "rate",
    label: "结果置信度",
  },
  {
    prop: "vpnDomain",
    label: "域名",
  },
  {
    prop: "vpnIp",
    label: "IP地址",
  },
  {
    prop: "vpnUrl",
    label: "URL地址",
  },
  {
    prop: "accessTime",
    label: "访问时间",
    type: 'time'
  },
  {
    prop: "payload",
    label: "载荷片段",
  },
  {
    prop: "isUploadFile",
    label: "是否上报取证文件",
    type: "select",
    options: [
      { label: '流入', value: 1 },
      { label: '流出', value: 2 },
    ],
  },

  {
    prop: "attachment",
    label: "取证文件名称",
  },
  {
    prop: "timeStamp",
    label: "生成时间",
    type: 'time'
  },
]

export default searchD;