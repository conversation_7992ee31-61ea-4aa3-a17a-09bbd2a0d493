package com.eversec.antivpn;

import com.eversec.antivpn.intelligence.api.dto.VpnIntelligenceDTO;
import com.eversec.antivpn.intelligence.service.IVpnIntelligenceService;
import com.eversec.antivpn.support.config.api.PlatformInterfaceInfoApi;
import com.eversec.antivpn.support.province.job.ReportStatusJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.testng.AbstractTestNGSpringContextTests;
import org.testng.annotations.Test;

/**
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023-08-01 13:40
 */
@SpringBootTest
public class VpnIntelligenceServiceImplTest extends AbstractTestNGSpringContextTests {

	@Autowired
	private IVpnIntelligenceService iVpnIntelligenceService;

	@Autowired
	private PlatformInterfaceInfoApi platformInterfaceInfoApi;

	@MockBean
	private ReportStatusJob reportStatusJob;

	@Test
	public void testSave() {
		VpnIntelligenceDTO paramDto = new VpnIntelligenceDTO();
		paramDto.setCommandId(null);
		paramDto.setRuleId(null);
		paramDto.setComCode("01");
		paramDto.setProvinceId(11L);
		paramDto.setTypeId(2);
		paramDto.setVpnId(null);
		paramDto.setVpnName("HK-综合（VIP5）-1");
		paramDto.setVpnDomain("www.baidu.com");
		paramDto.setTimeStamp("2023-08-07 18:24:11");
		iVpnIntelligenceService.save(paramDto);
	}


	@Test
	public void testCallSmartCU() {

		iVpnIntelligenceService.callSmartCU("01", 11L,  "1",null, null);
	}




}
