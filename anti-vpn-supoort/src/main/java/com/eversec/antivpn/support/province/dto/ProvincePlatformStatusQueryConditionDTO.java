package com.eversec.antivpn.support.province.dto;

import java.io.Serializable;
import java.util.Date;

import com.eversec.antivpn.support.province.entity.ProvincePlatformStatus;
import com.eversec.stark.common.component.criteria.annotaion.Eq;
import com.eversec.stark.common.component.criteria.annotaion.Ge;
import com.eversec.stark.common.component.criteria.annotaion.In;
import com.eversec.stark.common.component.criteria.annotaion.Le;
import com.eversec.stark.common.component.criteria.annotaion.Like;
import com.eversec.stark.common.component.criteria.query.AbstractQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <p>
 * 省平台信息（企业测）DTO
 * </p>
 * <p>
 * api接口数据传输对象（用于save、update接收参数，list、page、find返回对象等）
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-12
 */
@Getter
@Setter
@Schema(name = "ProvincePlatformStatusDTO", description = "省平台信息（企业测）")
public class ProvincePlatformStatusQueryConditionDTO extends AbstractQuery<ProvincePlatformStatus> {

    private static final long serialVersionUID = 1L;

    @Eq(alias = "com_code")
    @Schema(description = "运营商编码")
    private String comCode;

    @Eq(alias = "province_id")
    @Schema(description = "省级区域编号")
    private Long provinceId;

    @Eq(alias = "system_code")
    @Schema(description = "省平台业务系统标识")
    private String systemCode;

    // bug，查询会不正确，如数据中存在11，查询条件是1，会查出
    @Like(alias = "network_business_ids")
    @Schema(description = "网络类型编码，数组存储 ")
    private Integer networkBusinessId;

    @Eq(alias = "current_state")
    @Schema(description = "当前状态。监测网络类型的系统状态：0：正常；1：异常；2：未覆盖；9：其他。")
    private Integer currentState;

    @Ge(alias = "create_datetime")
    @Schema(description = "创建时间-start")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDatetimeStart;

    @Le(alias = "create_datetime")
    @Schema(description = "创建时间-end")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDatetimeEnd;


    @Ge(alias = "time_stamp")
    @Schema(description = "上报时间-start")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String timeStampStart;

    @Le(alias = "time_stamp")
    @Schema(description = "上报时间-end")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String timeStampEnd;

}