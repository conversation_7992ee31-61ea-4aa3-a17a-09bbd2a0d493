package com.eversec.antivpn.intelligence.controller;

import cn.hutool.core.io.FileUtil;
import com.eversec.antivpn.intelligence.api.VpnIntelligenceCommandApi;
import com.eversec.antivpn.intelligence.dto.VpnIntelligenceCommandQueryConditionDTO;
import com.eversec.antivpn.intelligence.entity.VpnIntelligenceCommand;
import com.eversec.antivpn.intelligence.service.IVpnIntelligenceCommandService;

import com.eversec.stark.generic.common.types.ContentDispositionTypeEnum;
import com.eversec.stark.generic.common.types.auth.Login;
import com.eversec.stark.generic.common.util.DownloadUtil;
import io.swagger.v3.oas.annotations.Operation;
import lombok.SneakyThrows;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import lombok.AllArgsConstructor;

import java.io.File;
import java.util.List;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.http.HttpServletResponse;

/**
 * <p>
 * vpn情报指令 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-10
 */
@RestController
@RequestMapping(VpnIntelligenceCommandApi.PATH)
@AllArgsConstructor
@Slf4j
@Login
public class VpnIntelligenceCommandController  implements VpnIntelligenceCommandApi {

    private final IVpnIntelligenceCommandService service;

    private final HttpServletResponse response;
    @Operation(summary = "查询-分页")
    @GetMapping("/page")
    public Page<VpnIntelligenceCommand> page(@ModelAttribute @ParameterObject VpnIntelligenceCommandQueryConditionDTO param,
            @ModelAttribute @ParameterObject Page<VpnIntelligenceCommand> pageInfo) {
        return service.page(param, pageInfo);
    }

    @Operation(summary = "根据id批量删除，逗号分割")
    @DeleteMapping("/deleteByIds/{ids}")
    public void deleteByIds(@PathVariable List<Long> ids) {
        service.removeByIds(ids);
    }

    @SneakyThrows
    @Operation(summary = "下载下发指令文件")
    @GetMapping("/downloadCommandDistributeFile/{id}")
    @Login(false)
    public void downloadCommandDistributeFile(@PathVariable(value = "id") Long id) {
        File file = service.getCommandDistributeFile(id);
        DownloadUtil.download(response, FileUtil.getInputStream(file), ContentDispositionTypeEnum.attachment, file.getName(), file.length());
    }

    @Operation(summary = "指令重新下发")
    @GetMapping("/reDistributeCommand/{id}")
    @Login(false)
    public void reDistributeCommand(@PathVariable(value = "id") Long id) {
        service.reDistributeCommand(id);
    }


}