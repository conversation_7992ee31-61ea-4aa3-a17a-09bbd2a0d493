<template>
  <div class="chartbox">
    <e-charts
      v-if="!isEmpty"
      class="piebg"
      type="pie"
      :options="pieOpt"
      emptyColor="#fff"
    ></e-charts>
    <ul v-if="!isEmpty" class="chartUl">
      <li v-for="(item, i) in pieD" :key="item.name">
        <span class="dian" :style="{ background: color[i] }"> </span>
        {{ item.name }}
        <span class="fr">{{ item.percent }}%</span>
      </li>
    </ul>
    <Empty class="empty" v-if="isEmpty" color="#fff" />
  </div>
</template>

<script>
import Empty from "@/components/empty.vue";

export default {
  components: {
    Empty,
  },
  props: {
    title: {
      type: String,
      default: "",
    },
    color: {
      type: Array,
      default: () => [
        "#fe7b69",
        "#ffd459",
        "#fff33a",
        "#ccf176",
        "#7ae15f",
        "#69edca",
        "#1ca4ff",
        "#2a84ff",
        "#77a4ff",
        "#a477ff",
      ], // 从红开始
    },
    pieData: {
      type: Array,
    },
  },
  data() {
    return {
      pieOpt: null,
      // color: ["#2884ff", "#1ba5fe", "#68eec9", "#7ae15e", "#fcf94c","#fdd35a","#fe7b69", "#b52be2", "#6646ee", "#9442f1"],
      pieD: [],
      isEmpty: false,
    };
  },
  watch: {
    pieData: {
      handler(val) {
        this.pieD = val;
        this.init();
      },
      immediate: true,
      deep: true,
    },
  },
  mounted() {
    // this.init();
  },
  methods: {
    init() {
      let color = this.color;
      let total = this.pieD.reduce((n, cur) => n + cur.value, 0);
      if (total == 0) {
        this.pieD = [];
        this.isEmpty = true;
      } else {
        this.isEmpty = false;
      }
      this.pieD = this.pieD.map((el, i) => {
        return {
          ...el,
          percent: ((el.value * 100) / total).toFixed(0),
        };
      });
      if (this.pieD.length == 0) {
        this.pieOpt = null;
        return;
      }
      this.pieOpt = {
        color,
        tooltip: {
          show: true,
          trigger: "item",
          // backgroundColor: "none",
          // extraCssText: "border:0; box-shadow: none;",
          formatter: function (p) {
            let d = p.data;
            // console.log('aa', p, p.color)
            //  style="background:#082243; border: 1px solid #3b4356; padding: 0 10px; min-width: 90px;"
            return `
              <div><span style="background:${p.color}; display:inline-block; width:7px;height:7px;border-radius:50%;margin-right:6px"> </span>
                <span style="color:${p.color}">${d.name}</span>
                ${d.value}
                <div>${d.percent}%</div>
              </div>`;
          },
        },
        legend: {
          show: false,
          bottom: "5%",
          left: "center",
          icon: "circle",
          itemWidth: 7,
          itemHeight: 7,
          textStyle: {
            color: "#fff",
          },
        },
        series: [
          {
            name: "",
            type: "pie",
            minAngle: 6,
            radius: ["45%", "78%"],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: "center",
            },
            emphasis: {
              label: {
                show: false,
                fontSize: 30,
                fontWeight: "bold",
              },
            },
            labelLine: {
              show: false,
            },
            itemStyle: {
              borderRadius: 0,
              borderColor: "transprent",
              borderWidth: 1,
            },
            data: this.pieD,
            // [
            //   { value: 1048, name: "Search Engine" },
            //   { value: 735, name: "Direct" },
            //   { value: 580, name: "Email" },
            //   { value: 484, name: "Union Ads" },
            //   { value: 300, name: "Video Ads" }
            // ]
          },
        ],
      };
    },
  },
};
</script>

<style scoped lang="scss">
.chartbox {
  // height: 500px;
  display: flex;
  .empty {
    height: 300px;
    display: flex;
    justify-content: center;
    flex-direction: column;
    margin: 0;
    flex: 1;
  }
  .piebg {
    width: 50%;
    background: url(./img/pie_bg.png) no-repeat center;
    height: 300px;
    background-size: 85%;
  }
  .chartUl {
    display: flex;
    flex: 1;
    flex-direction: column;
    justify-content: center;
    li {
      color: #fff;
      font-size: 12px;
      background: #1e2432;
      height: 24px;
      line-height: 24px;
      clear: both;
      padding: 0 10px;
      margin-bottom: 5px;

      .dian {
        display: inline-block;
        width: 7px;
        height: 7px;
        border-radius: 50%;
        margin-right: 6px;
      }
      .percent {
        color: #a7e7ff;
      }
    }
  }
}
</style>
