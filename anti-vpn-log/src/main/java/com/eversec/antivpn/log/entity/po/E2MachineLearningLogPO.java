package com.eversec.antivpn.log.entity.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.eversec.stark.generic.common.infra.mybatis.plus.BaseDO;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 * <p>
 * 持久化对象，此对象与数据库属性一一对应，被Entity对象继承，扩展属性写在Entity对象中
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-08
 */
@Getter
@Setter
@TableName("e2_machine_learning_log")
@Schema(name = "E2MachineLearningLogPO", description = "")
public class E2MachineLearningLogPO  {

    private static final long serialVersionUID = 1L;

    private String version;

    private String comCode;

    private Integer networkBusinessId;

    private Long logId;

    private Long houseId;

    private String houseName;

    private Long logProvinceId;

    private Long provinceId;

    private Long logCityId;

    private Long logCountyId;

    private String srcIp;

    private Long srcPort;

    private String srcCountry;

    private String srcProvinceId;

    private String srcInfo;

    private String destIp;

    private Long destPort;

    private String dstCountry;

    private String dstProvinceId;

    private String destInfo;

    private Long trafficType;

    private Long protocolType;

    private String modelCode;

    private String modelName;

    private String modelVersion;

    private String vpnName;

    private Integer rate;

    private String vpnDomain;

    private String vpnIp;

    private String vpnUrl;

    private String payLoad;

    private Integer isUploadFile;

    private String attachment;

    private LocalDateTime accessTime;

    private LocalDateTime timestamp;

    private Long day;

    private Integer hour;
}