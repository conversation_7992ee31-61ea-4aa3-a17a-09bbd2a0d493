import httpRequest from '@/utils/httpRequest.js';
const BaseServer = '/anti-vpn-service';

// 监测总览
export default {
  // left1 -- 匹配监测方式
  vpnTypeAggregate(o) {
    return httpRequest({
      url: BaseServer + `/log/overall/vpnTypeAggregate`,
      method: "get",
      params: o,
    });
  },
  // left2 -- 匹配VPN协议
  vpnType5ContentTypeAggregate(o) {
    return httpRequest({
      url: BaseServer + `/log/e1CommLogApi/vpnType5ContentTypeAggregate`,
      method: "get",
      params: o,
    });
  },

  // mid -- 地图统计
  provinceAggregate(o) {
    return httpRequest({
      url: BaseServer + `/log/overall/provinceAggregate`,
      method: "get",
      params: o
    });
  },
  // mid2 -- 接收各省企业监测日志与上报折线图
  dateHistogram(o) {
    return httpRequest({
      url: BaseServer + `/log/overall/dateHistogram`,
      method: "get",
      params: o
    });
  },

  // rig1 -- 传输层协议占比
  protocolTypeAggregate(o) {
    return httpRequest({
      url: BaseServer + `/log/overall/protocolTypeAggregate`,
      method: "get",
      params: o
    });
  },
  // rig1 -- 应用层协议占比
  applicationProtocolTypeCount(o) {
    return httpRequest({
      url: BaseServer + `/log/e1CommLogApi/applicationProtocolTypeCount`,
      method: "get",
      params: o
    });
  },

  // rig2 -- 用户使用的网络类型占比
  networkBusinessTypeAggregate(o) {
    return httpRequest({
      url: BaseServer + `/log/overall/networkBusinessTypeAggregate`,
      method: "get",
      params: o
    });
  },

  // rig3 -- 集团端 -- 省区域上报监测日志 TOP10  取地图接口的前10条
  // rig3 -- 省端
  systemAggCount(o) {
    return httpRequest({
      url: BaseServer + `/log/overall/systemAggCount`,
      method: "get",
      params: o
    });
  },

  // topNum -- 上报情报监测日志
  timeRangeAndTodayCount(o) {
    return httpRequest({
      url: BaseServer + `/log/e1CommLogApi/timeRangeAndTodayCount`,
      method: "get",
      params: o
    });
  },
  // topNum -- 上报AI模型
  aiCount(o) {
    return httpRequest({
      url: BaseServer + `/log/e2MachineLearningLog/timeRangeAndTodayCount`,
      method: "get",
      params: o
    });
  },
  // topNum -- 3 DNS日志
  dnsCount(o) {
    return httpRequest({
      url: BaseServer + `/log/e4DnsLog/timeRangeAndTodayCount`,
      method: "get",
      params: o
    });
  },
  // topNum -- 4 上报情报
  reportVpnIntelligence(o) {
    return httpRequest({
      url: BaseServer + `/intelligence/vpnIntelligence/reportVpnIntelligence`,
      method: "get",
      params: o
    });
  },
  // topNum -- 5 情报总量
  totalCount(o) {
    return httpRequest({
      url: BaseServer + `/intelligence/vpnIntelligence/totalCount`,
      method: "get",
      params: o
    });
  },
};
