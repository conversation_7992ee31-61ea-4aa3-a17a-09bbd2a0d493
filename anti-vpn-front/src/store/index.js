import Vue from 'vue'
import Vuex from 'vuex'
import cloneDeep from 'lodash/cloneDeep'

const path = require('path')
const files = require.context('./modules', false, /\.js$/)
const modules = {}
files.keys().forEach(key => {
  const name = path.basename(key, '.js')
  modules[name] = files(key).default || files(key)
})
Vue.use(Vuex)

export default new Vuex.Store({
  strict: process.env.NODE_ENV !== 'production',
  modules: { ...modules },
  mutations: {
    // 重置vuex本地储存状态
    resetStore(state) {
      Object.keys(state).forEach(key => {
        if (key === 'theme') return // 主题设置不重置
        state[key] = cloneDeep(window.initialStoreState[key])
      })
    }
  },
  actions: {
    backMenu(context) {
      context.commit('mainTabs/backActiveName')
    }
  }
})
