package com.eversec.antivpn.support.config.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eversec.antivpn.config.enums.AntiVpnBusinessExceptionEnum;
import com.eversec.antivpn.support.config.api.dto.PlatformInterfaceInfoDTO;
import com.eversec.antivpn.support.config.api.dto.PlatformInterfaceInfoTypeEnum;
import com.eversec.antivpn.support.config.convertor.PlatformInterfaceInfoFactory;
import com.eversec.antivpn.support.config.entity.PlatformInterfaceInfo;
import com.eversec.antivpn.support.config.mapper.PlatformInterfaceInfoMapper;
import com.eversec.antivpn.support.config.service.IPlatformInterfaceInfoService;
import com.eversec.framework.webboot.common.exception.BusinessException;
import com.eversec.stark.generic.common.util.ListBuilder;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 平台接口信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-17
 */
@Service
@Slf4j
@AllArgsConstructor
public class PlatformInterfaceInfoServiceImpl extends ServiceImpl<PlatformInterfaceInfoMapper, PlatformInterfaceInfo> implements IPlatformInterfaceInfoService {

    private final PlatformInterfaceInfoFactory platformInterfaceInfoFactory;

    @Override
    public PlatformInterfaceInfoDTO getCurrentPlatform() {
        LambdaQueryWrapper<PlatformInterfaceInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PlatformInterfaceInfo::getConfigType, PlatformInterfaceInfoTypeEnum.CURRENT.name());
        List<PlatformInterfaceInfo> list = this.list(wrapper);
        if (CollectionUtil.isEmpty(list)) {
            throw new BusinessException(AntiVpnBusinessExceptionEnum.CODE_10007);
        }
        if (list.size() > 1) {
            log.warn("当前数据库存在多条当前系统配置，取第一条记录。");
        }
        PlatformInterfaceInfo platformInterfaceInfo = list.get(0);
        PlatformInterfaceInfoDTO platformInterfaceInfoDTO = platformInterfaceInfoFactory.entityToDto(platformInterfaceInfo);
        setSecretKeyAndPlatformInfo(platformInterfaceInfoDTO);
        return platformInterfaceInfoDTO;
    }

    public void setSecretKeyAndPlatformInfo(PlatformInterfaceInfoDTO dto) {

        if (StrUtil.isBlank(dto.getNorthIntelligenceSecretKey())) {
            dto.setNorthIntelligenceSecretKey(dto.getNorthReportSecretKey());
        }

        if (StrUtil.isBlank(dto.getNorthIntelligenceFtpUsername())) {
            dto.setNorthIntelligenceFtpUsername(dto.getNorthReportFtpUsername());
        }

        if (StrUtil.isBlank(dto.getNorthIntelligenceFtpPassword())) {
            dto.setNorthIntelligenceFtpPassword(dto.getNorthReportFtpPassword());
        }

    }

    @Override
    public PlatformInterfaceInfoDTO getByComCodeAndProvinceAndSystemCode(String comCode, Long provinceId, String systemCode) {
        LambdaQueryWrapper<PlatformInterfaceInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PlatformInterfaceInfo::getConfigType, PlatformInterfaceInfoTypeEnum.PROVINCE.name());
        wrapper.eq(PlatformInterfaceInfo::getComCode, comCode);
        wrapper.eq(PlatformInterfaceInfo::getProvinceId, provinceId);
        if (StrUtil.isNotBlank(systemCode)) {
            wrapper.eq(PlatformInterfaceInfo::getSystemCode, systemCode);
        } else {
            wrapper.and(true, (i ->
                    i.eq(PlatformInterfaceInfo::getSystemCode, "")
                            .or()
                    .isNull(PlatformInterfaceInfo::getSystemCode)));
        }
        wrapper.eq(PlatformInterfaceInfo::getEnabled, true);
        PlatformInterfaceInfo platformInterfaceInfo = this.getOne(wrapper);
        PlatformInterfaceInfoDTO platformInterfaceInfoDTO = platformInterfaceInfoFactory.entityToDto(platformInterfaceInfo);
        // 北向接口配置不存在时通过current进行替换
        PlatformInterfaceInfoDTO currentPlatform = this.getCurrentPlatform();
        setCurrentPlatformInfo(platformInterfaceInfoDTO, currentPlatform);
        return platformInterfaceInfoDTO;
    }


    @Override
    public PlatformInterfaceInfoDTO getByComCodeAndSystemCode(String comCode, String systemCode) {
        LambdaQueryWrapper<PlatformInterfaceInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PlatformInterfaceInfo::getConfigType, PlatformInterfaceInfoTypeEnum.PROVINCE.name());
        wrapper.eq(PlatformInterfaceInfo::getComCode, comCode);
        if (StrUtil.isNotBlank(systemCode)) {
            wrapper.eq(PlatformInterfaceInfo::getSystemCode, systemCode);
        } else {
            wrapper.and(true, (i ->
                    i.eq(PlatformInterfaceInfo::getSystemCode, "")
                            .or()
                            .isNull(PlatformInterfaceInfo::getSystemCode)));
        }
        wrapper.eq(PlatformInterfaceInfo::getEnabled, true);
        PlatformInterfaceInfo platformInterfaceInfo = this.getOne(wrapper);
        PlatformInterfaceInfoDTO platformInterfaceInfoDTO = platformInterfaceInfoFactory.entityToDto(platformInterfaceInfo);
        // 北向接口配置不存在时通过current进行替换
        PlatformInterfaceInfoDTO currentPlatform = this.getCurrentPlatform();
        setCurrentPlatformInfo(platformInterfaceInfoDTO, currentPlatform);
        return platformInterfaceInfoDTO;
    }


    private void setCurrentPlatformInfo(PlatformInterfaceInfoDTO platformInterfaceInfoDTO, PlatformInterfaceInfoDTO currentPlatform) {
        if (StrUtil.isBlank(platformInterfaceInfoDTO.getNorthReportFtpAddress())) {

            if (StrUtil.isBlank(platformInterfaceInfoDTO.getNorthReportFtpAddress())) {
                platformInterfaceInfoDTO.setNorthReportFtpAddress(currentPlatform.getNorthReportFtpAddress());
            }

            if (StrUtil.isBlank(platformInterfaceInfoDTO.getNorthReportFtpPassword())) {
                platformInterfaceInfoDTO.setNorthReportFtpPassword(currentPlatform.getNorthReportFtpPassword());
            }

            if (StrUtil.isBlank(platformInterfaceInfoDTO.getNorthReportFtpUsername())) {
                platformInterfaceInfoDTO.setNorthReportFtpUsername(currentPlatform.getNorthReportFtpUsername());
            }

            if (StrUtil.isBlank(platformInterfaceInfoDTO.getNorthReportSecretKey())) {
                platformInterfaceInfoDTO.setNorthReportSecretKey(currentPlatform.getNorthReportSecretKey());
            }

        }
        if (StrUtil.isBlank(platformInterfaceInfoDTO.getNorthIntelligenceFtpAddress())) {
            if (StrUtil.isBlank(platformInterfaceInfoDTO.getNorthIntelligenceFtpAddress())) {
                platformInterfaceInfoDTO.setNorthIntelligenceFtpAddress(currentPlatform.getNorthIntelligenceFtpAddress());
            }
            if (StrUtil.isBlank(platformInterfaceInfoDTO.getNorthIntelligenceFtpPassword())) {
                platformInterfaceInfoDTO.setNorthIntelligenceFtpPassword(currentPlatform.getNorthIntelligenceFtpPassword());
            }
            if (StrUtil.isBlank(platformInterfaceInfoDTO.getNorthIntelligenceFtpUsername())) {
                platformInterfaceInfoDTO.setNorthIntelligenceFtpUsername(currentPlatform.getNorthIntelligenceFtpUsername());
            }
            if (StrUtil.isBlank(platformInterfaceInfoDTO.getNorthIntelligenceSecretKey())) {
                platformInterfaceInfoDTO.setNorthIntelligenceSecretKey(currentPlatform.getNorthIntelligenceSecretKey());
            }
        }
        setSecretKeyAndPlatformInfo(platformInterfaceInfoDTO);
    }

    @Override
    public List<PlatformInterfaceInfoDTO> getByIspAndProvince(String comCode, Long provinceId) {
        LambdaQueryWrapper<PlatformInterfaceInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PlatformInterfaceInfo::getConfigType, PlatformInterfaceInfoTypeEnum.PROVINCE.name());
        wrapper.eq(PlatformInterfaceInfo::getComCode, comCode);
        wrapper.eq(PlatformInterfaceInfo::getProvinceId, provinceId);
        wrapper.eq(PlatformInterfaceInfo::getEnabled, true);
        List<PlatformInterfaceInfo> platformInterfaceInfos = this.list(wrapper);
        PlatformInterfaceInfoDTO currentPlatform = this.getCurrentPlatform();
        List<PlatformInterfaceInfoDTO> platformInterfaceInfoDTOS = ListBuilder.convertList(platformInterfaceInfos, platformInterfaceInfoFactory::entityToDto);
        for (PlatformInterfaceInfoDTO platformInterfaceInfoDTO : platformInterfaceInfoDTOS) {
            setCurrentPlatformInfo(platformInterfaceInfoDTO, currentPlatform);
        }
        return platformInterfaceInfoDTOS;
    }


    @Override
    public void save(PlatformInterfaceInfoDTO paramDto) {
        PlatformInterfaceInfo platformInterfaceInfo = platformInterfaceInfoFactory.dtoToEntity(paramDto);
        // 判断current是否已经存在
        if(PlatformInterfaceInfoTypeEnum.CURRENT.name().equals(platformInterfaceInfo.getConfigType())) {
            LambdaQueryWrapper<PlatformInterfaceInfo> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(PlatformInterfaceInfo::getConfigType, PlatformInterfaceInfoTypeEnum.CURRENT.name());
            List<PlatformInterfaceInfo> list = this.list(wrapper);
            if (CollectionUtil.isNotEmpty(list)) {
                throw new BusinessException(AntiVpnBusinessExceptionEnum.CODE_10008);
            }
        }
        this.save(platformInterfaceInfo);
    }

    @Override
    public void updateById(PlatformInterfaceInfoDTO paramDto) {
        PlatformInterfaceInfo platformInterfaceInfo = platformInterfaceInfoFactory.dtoToEntity(paramDto);
        this.updateById(platformInterfaceInfo);
    }


}
