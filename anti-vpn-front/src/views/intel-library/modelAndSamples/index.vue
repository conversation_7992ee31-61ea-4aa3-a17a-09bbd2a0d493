<template>
  <div class="modelAndSamples" v-loading="loading">
    <!-- form -->
    <publicForm
      ref="publicForm"
      :formDataLabel="formDataLabel"
      :labelWidth="'0'"
      :form-data="formData"
      @throwEvent="changeItem"
    />
    <div class="is-overflow-hidden is-mb-10">
      <el-button
        class="is-float-right is-ml-10"
        type="danger"
        :disabled="!(activeList && activeList.length > 0)"
        @click="batchDelete"
      >
        批量删除
      </el-button>
      <el-button
        class="is-float-right"
        type="primary"
        :disabled="!(activeList && activeList.length > 0)"
        @click="escalationFun(null)"
      >
        批量上报
      </el-button>
      <el-button
        class="is-float-right"
        type="primary"
        :disabled="!(activeList && activeList.length > 0)"
        @click="escalationFun(null, 1)"
      >
        批量上报模型
      </el-button>
      <el-button
        class="is-float-right"
        type="primary"
        :disabled="!(activeList && activeList.length > 0)"
        @click="escalationFun(null, 2)"
      >
        批量上报样本
      </el-button>
      <el-button class="is-float-right" type="primary" @click="importFun">
        导入
      </el-button>
    </div>
    <!-- table -->
    <publicTable
      :tableLabel="tableLabel"
      :tableData="dataList"
      ref="table"
      height="100%"
      :options="{
        highlightCurrentRow: false,
        isString: true,
        checkbox: true,
        border: false
      }"
      @handleSelectionChange="handleSelectionChange"
    >
      <template v-slot:download="{ data }">
        <el-link
          v-if="data.row.modelFileId"
          class="is-ml-10 is-float-left"
          type="primary"
          @click="downloadFun(data.row.modelFileId)"
        >
          下载文件
        </el-link>
        <el-link
          v-if="!data.row.modelFileId && data.row.contentFileName"
          class="is-ml-10 is-float-left"
          type="primary"
          @click="downloadModuleFileByNameFun(data.row.contentFileName)"
        >
          下载文件
        </el-link>
      </template>
      <template v-slot:handle="{ data }">
        <el-link
          class="is-ml-10 is-float-left"
          :underline="false"
          type="primary"
          @click="escalationFun(data.row.id)"
        >
          上报
        </el-link>
        <el-link
          class="is-ml-10 is-float-left"
          :underline="false"
          type="primary"
          @click="escalationFun(data.row.id, 1)"
        >
          上报模型
        </el-link>
        <el-link
          class="is-ml-10 is-float-left"
          :underline="false"
          type="primary"
          @click="escalationFun(data.row.id, 2)"
        >
          上报样本
        </el-link>
        <el-link
          class="is-ml-10 is-float-left"
          :underline="false"
          type="danger"
          @click="deleteBtn(data.row.id)"
        >
          删除
        </el-link>
      </template>
    </publicTable>
    <!-- 分页 -->
    <el-pagination
      class="is-pt-10 is-float-right"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="pagingInfo.current"
      :page-sizes="pagingInfo.pageSizes"
      :page-size="pagingInfo.size"
      :layout="pagingInfo.layout"
      :total="pagingInfo.total"
      :key="pagingInfo.total"
    />
    <importDialog ref="importDialog" @submitFun="getDataList" />
  </div>
</template>

<script>
import cloneDeep from 'lodash/cloneDeep'
import { pagingInfo } from '@/config/base'
import debounce from 'lodash/debounce'
import antiVpnService from '@/api/antiVpnService'
import sysConf from '@/api/rm/sysConf'

export default {
  name: 'modelAndSamples',
  components: {
    importDialog: () => import('./import-dialog')
  },
  data() {
    return {
      loading: false,
      formDataLabel: [
        [
          {
            val: 'comCode',
            type: 'dic-select',
            enumKey: 'ISP_CODE',
            filterable: true,
            showAll: true,
            funName: 'changeItem',
            className: 'is-one-fifth is-pr-10',
            placeholder: '请选择运营商编码'
          },
          {
            val: 'provinceId',
            type: 'dic-select',
            enumKey: 'PROVINCE_CODE',
            filterable: true,
            showAll: true,
            funName: 'changeItem',
            className: 'is-one-fifth is-pr-10',
            placeholder: '请选择省级区域编号'
          },
          {
            val: 'systemCode',
            type: 'dic-select',
            enumKey: 'SYSTEM_CODE',
            filterable: true,
            showAll: true,
            funName: 'changeItem',
            className: 'is-one-fifth is-pr-10',
            placeholder: '请选择省平台业务系统表示'
          },
          {
            val: 'networkBusinessId',
            type: 'dic-select',
            enumKey: 'NETWORK_TYPE',
            filterable: true,
            showAll: true,
            funName: 'changeItem',
            className: 'is-one-fifth is-pr-10',
            placeholder: '请选择监测网络类型'
          },
          {
            type: 'btn',
            btnTxt: '刷新',
            className: 'is-one-fifth is-pr-10',
            funName: 'refresh',
            btnType: ''
          }
        ],
        [
          {
            val: 'modelCode',
            type: 'input',
            funName: 'changeItem',
            className: 'is-one-fifth is-pr-10',
            placeholder: '请输入模型编码'
          },
          {
            val: 'modelChName',
            type: 'input',
            funName: 'changeItem',
            className: 'is-one-fifth is-pr-10',
            placeholder: '请输入模型中文名称'
          },
          {
            val: 'vpnName',
            type: 'input',
            funName: 'changeItem',
            className: 'is-one-fifth is-pr-10',
            placeholder: '请输入关联vpn名称'
          }
        ]
      ],
      queryKey: [
        'comCode',
        'provinceId',
        'systemCode',
        'networkBusinessId',
        'modelCode',
        'modelChName',
        'vpnName'
      ],
      formData: {},
      pagingInfo: cloneDeep(pagingInfo()), // 分页信息
      tableLabel: [
        {
          label: '模型编码',
          val: 'modelCode',
          width: 120
        },
        {
          label: '模型协议',
          val: 'protocolType',
          width: 120
        },
        {
          label: '模型中文名称',
          val: 'modelChName',
          width: 120
        },
        {
          label: '模型英文名称',
          val: 'modelName',
          width: 120
        },
        {
          label: '模型版本',
          val: 'modelVersion',
          width: 120
        },
        {
          label: '关联vpn名称',
          val: 'vpnName',
          width: 120
        },
        {
          label: '运营商编码',
          val: 'comCode',
          type: 'dic-val',
          enumKey: 'ISP_CODE',
          width: 100
        },
        {
          label: '省级区域编号',
          val: 'provinceId',
          type: 'dic-val',
          enumKey: 'PROVINCE_CODE',
          width: 120
        },
        {
          label: '省平台业务系统标识',
          val: 'systemCode',
          type: 'dic-val',
          enumKey: 'SYSTEM_CODE',
          width: 160
        },
        {
          label: '网络类型',
          val: 'networkBusinessId',
          type: 'dic-val',
          enumKey: 'NETWORK_TYPE',
          width: 100
        },
        {
          label: '文件类型',
          val: 'fileType',
          type: 'dic-val',
          enumKey: 'MACHINE_LEARNING_FILE_TYPE',
          width: 100
        },
        {
          label: '文件格式',
          val: 'contentFilesuffix',
          width: 100
        },
        {
          label: '模型/样本文件',
          val: 'modelFileId',
          type: 'slot',
          slotName: 'download',
          width: 120
        },
        {
          label: '操作',
          type: 'slot',
          slotName: 'handle',
          fixed: 'right',
          headerAlign: 'center',
          width: 240
        }
      ],
      dataList: [],
      activeList: []
    }
  },
  computed: {},
  watch: {},
  mounted() {
    this.formData = {}
    for (let name in this.$route.query) {
      if (name === 'size') {
        this.pagingInfo.size = Number(this.$route.query[name])
      } else if (name === 'current') {
        this.pagingInfo.current = Number(this.$route.query[name])
      } else {
        this.$set(this.formData, name, this.$route.query[name])
        this.formData[name] = this.$route.query[name]
      }
    }
    this.getDataList()
  },
  methods: {
    // 表单监听回调
    changeItem(val) {
      if (val && val.funName === 'refresh') {
        this.getDataList()
        return
      }
      if (val) {
        this.pagingInfo.current = 1
      }
      this.debounce(this)
    },
    // 防抖查询
    debounce: debounce(vm => {
      let tmpData = {
        size: vm.pagingInfo.size,
        current: vm.pagingInfo.current
      }
      vm.queryKey.map(e => {
        if (vm.formData[e]) vm.$set(tmpData, e, vm.formData[e])
      })
      vm.$router.push({
        path: vm.$route.path,
        query: tmpData
      })
      vm.getDataList()
    }, 300),
    // 选中框
    handleSelectionChange(val) {
      this.activeList = val
    },
    // 列表查询
    getDataList() {
      this.loading = true
      let param = {
        current: this.pagingInfo.current, //当前页
        size: this.pagingInfo.size //每页展示数量
      }
      this.queryKey.map(e => {
        if (this.formData[e]) this.$set(param, e, this.formData[e])
      })
      antiVpnService
        .getVpnMachineLearningCodeDict(param)
        .then(res => {
          this.dataList = res.records || []
          this.pagingInfo.total = res.total || 0
          this.loading = false
        })
        .catch(() => {
          this.loading = false
        })
    },
    // 每页数
    handleSizeChange(val) {
      this.pagingInfo.size = val
      this.pagingInfo.current = 1
      this.changeItem()
    },
    // 当前页
    handleCurrentChange(val) {
      this.pagingInfo.current = val
      this.changeItem()
    },
    // 下载
    downloadFun(id) {
      sysConf.downloadById(id)
    },
    // 根据文件名下载
    downloadModuleFileByNameFun(fileName) {
      antiVpnService.downloadModuleFileByName(fileName)
    },
    // 删除
    deleteBtn(id) {
      this.$confirm('此操作将删除该数据, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.loading = true
          antiVpnService
            .vpnMachineLearningCodeDictDeleteByIds(id)
            .then(res => {
              this.loading = false
              this.$message({
                type: 'success',
                message: '删除成功!'
              })
              this.getDataList()
            })
            .catch(() => {
              this.loading = false
            })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    // 批量删除
    batchDelete() {
      this.$confirm('此操作将删除已选数据, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.loading = true
          antiVpnService
            .vpnMachineLearningCodeDictDeleteByIds(
              this.activeList.map(e => e.id)
            )
            .then(res => {
              this.loading = false
              this.$message({
                type: 'success',
                message: '删除成功!'
              })
              this.getDataList()
            })
            .catch(() => {
              this.loading = false
            })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    // 导入
    importFun() {
      this.$refs.importDialog.openDialog()
    },
    // 上报
    escalationFun(id, fileType) {
      this.$confirm('是否上报已选数据？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          let param = id ? id : this.activeList.map(e => e.id).join(',')
          this.loading = true
          antiVpnService
            .vpnMachineLearningCodeDictReport(param, fileType)
            .then(res => {
              this.$message({
                message: `上报成功`,
                type: 'success'
              })
              this.loading = false
              this.getDataList()
            })
            .catch(() => {
              this.loading = false
            })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消上报操作'
          })
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.modelAndSamples {
  height: 100%;
  ::v-deep .public-table {
    height: calc(100% - 140px - 40px);
  }
}
</style>
