package com.eversec.antivpn.intelligence.api.enums;

import lombok.Getter;

/**
 * 情报内容类型
 *
 * 和字典的 INTELLIGENCE_CONTENT_TYPE 对应
 */
public enum IntelligenceContentTypeEnum {

    DOMAIN(1, "域名"),
    IP(2, "IP"),
    URL(3, "URL"),
    MESSAGE(4, "报文"),
    VPN_PROTOCOL(5, "VPN协议"),
    OTHER(999, "其他"),

    ;


    IntelligenceContentTypeEnum(int code, String name){
        this.code = code;
        this.name = name;
    }

    private final int code;
    private final String name;

    public int getCode() {
        return code;
    }
}
