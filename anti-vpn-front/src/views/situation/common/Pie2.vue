<template>
  <div class="chartbox">
    <e-charts :class="pieD.length?'piebg':''" class="chartH" type="pie" :options="pieOpt" emptyColor="#fff"></e-charts>
    <ul class="chartUl">
      <li v-for="(item, i) in pieD" :key="item.name">
        <span class="dian" :style="{ background: color[i] }"> </span>
        {{ item.name }}
        <span class="fr">{{ item.percent }}</span>
      </li>
    </ul>
  </div>
</template>

<script>
// import { barBase, barFn } from "../echartOption";
export default {
  props: {
    // title: {
    //   type: String,
    //   default: ""
    // }
  },
  data() {
    return {
      title: "",
      pieOpt: null,
      // color: ["#2884ff", "#1ba5fe", "#68eec9", "#7ae15e", "#fcf94c","#fdd35a","#fe7b69", "#b52be2", "#6646ee", "#9442f1"],
      color: [
        "#fe7b69",
        "#ffd459",
        "#fff33a",
        "#ccf176",
        "#7ae15f",
        "#69edca",
        "#1ca4ff",
        "#2a84ff",
        "#77a4ff",
        "#a477ff"
      ], // 从红开始
      pieD: [
        // { name: "域名", value: 1500 },
        // { name: "URL", value: 1000 },
        // { name: "IP", value: 1000 },
        // { name: "AI模型", value: 800 },
        // { name: "VPN协议", value: 500 },
        // { name: "PPTP VPN", value: 300 },
        // { name: "L2TP VPN", value: 200 },
        // { name: "SOCKS5", value: 100 },
        // { name: "DNS", value: 80 },
        // { name: "SSH", value: 70 }
      ]
    };
  },
  mounted() {
    // this.init();
  },
  methods: {
    init(title = "", subtit = "占比", d = []) {
      this.pieD = d;
      this.title = title;
      let color = this.color;
      let total = this.pieD.reduce((n, cur) => n + cur.value, 0);
      this.pieD = this.pieD.map(el => {
        let percent = ((el.value * 100) / total).toFixed(2) + "%";
        if (total == 0) percent = '0%';
        return {
          ...el,
          percent
        };
      });

      if(this.pieD.length==0) {
        this.pieOpt = null; // 显示暂无
        return;
      }

      this.pieOpt = {
        color,
        title: {
          zlevel: 0,
          text: [`{name|${this.title}}`, `{name|${subtit}}`].join("\n"),
          // text: ["{value|" + total + "}", "{name|总数}"].join("\n"),
          x: "center",
          y: "center",
          with: "60px",
          textStyle: {
            lineHeight: 30,
            rich: {
              // value: {
              //   color: "#3f405d",
              //   fontSize: 18,
              //   fontWeight: "bold",
              //   lineHeight: 40
              // },
              name: {
                lineHeight: 12,
                color: "#eeeff3"
              }
            }
          }
        },
        tooltip: {
          show: true,
          trigger: "item",
          // backgroundColor: "none",
          // extraCssText: "border:0; box-shadow: none;",
          formatter: function(p) {
            let d = p.data;
            // console.log('aa', p, p.color)
            //  style="background:#082243; border: 1px solid #3b4356; padding: 0 10px; min-width: 90px;"
            return `
              <div><span style="background:${p.color}; display:inline-block; width:7px;height:7px;border-radius:50%;margin-right:6px"> </span>
                <span style="color:${p.color}">${d.name}</span>
                ${d.value}
                <div>${d.percent}</div>
              </div>`;
          }
        },
        legend: {
          show: false,
          bottom: "5%",
          left: "center",
          icon: "circle",
          itemWidth: 7,
          itemHeight: 7,
          textStyle: {
            color: "#fff"
          }
        },
        series: [
          {
            name: "",
            type: "pie",
            minAngle: 6,
            radius: ["44%", "74%"],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: "center"
            },
            emphasis: {
              label: {
                show: false,
                fontSize: 30,
                fontWeight: "bold"
              }
            },
            labelLine: {
              show: false
            },
            itemStyle: {
              borderRadius: 0,
              borderColor: "transprent",
              borderWidth: 1
            },
            data: this.pieD
            // [
            //   { value: 1048, name: "Search Engine" },
            //   { value: 735, name: "Direct" },
            //   { value: 580, name: "Email" },
            //   { value: 484, name: "Union Ads" },
            //   { value: 300, name: "Video Ads" }
            // ]
          }
        ]
      };

      // this.pieOpt = barFn(this.pieD, color)
    }
  }
};
</script>

<style scoped lang="scss">
.chartbox {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .piebg {
    background: url(./img/pie_bg.png) no-repeat center 34px;
  }
  .chartH {
    height: 270px;
    width: 250px;
  }
  .chartUl {
    width: 210px;
    max-height: 230px;
    overflow-y: auto;
    li {
      color: #fff;
      font-size: 12px;
      background: #1e2432;
      height: 24px;
      line-height: 24px;
      clear: both;
      padding: 0 10px;
      margin-bottom: 5px;
      .dian {
        display: inline-block;
        width: 7px;
        height: 7px;
        border-radius: 50%;
        margin-right: 6px;
      }
      .percent {
        color: #a7e7ff;
      }
    }
  }
}
</style>
