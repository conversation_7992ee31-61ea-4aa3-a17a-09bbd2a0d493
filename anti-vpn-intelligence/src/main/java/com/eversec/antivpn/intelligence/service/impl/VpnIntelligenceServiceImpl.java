package com.eversec.antivpn.intelligence.service.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.hutool.core.codec.Base64;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.lang.Tuple;
import cn.hutool.core.text.csv.CsvData;
import cn.hutool.core.text.csv.CsvRow;
import cn.hutool.core.text.csv.CsvUtil;
import cn.hutool.core.text.csv.CsvWriter;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ZipUtil;
import cn.hutool.crypto.digest.MD5;
import cn.hutool.extra.compress.CompressUtil;
import cn.hutool.extra.compress.archiver.Archiver;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eversec.antivpn.config.AntiVpnProperties;
import com.eversec.antivpn.config.enums.*;
import com.eversec.antivpn.emnus.AntiVpnReportCodeEnum;
import com.eversec.antivpn.emnus.ScreenTypeEnum;
import com.eversec.antivpn.intelligence.api.dto.*;
import com.eversec.antivpn.intelligence.api.enums.IntelligenceCommandDistributeStatusEnum;
import com.eversec.antivpn.intelligence.api.enums.IntelligenceCommandOperationTypeEnum;
import com.eversec.antivpn.intelligence.api.enums.IntelligenceCommandReportStatusEnum;
import com.eversec.antivpn.intelligence.api.enums.IntelligenceContentTypeEnum;
import com.eversec.antivpn.intelligence.api.enums.IntelligenceSourceEnum;
import com.eversec.antivpn.intelligence.dto.AggCountDTO;
import com.eversec.antivpn.intelligence.dto.VpnIntelligenceImportDTO;
import com.eversec.antivpn.intelligence.dto.VpnIntelligenceQueryConditionDTO;
import com.eversec.antivpn.intelligence.entity.VpnIntelligence;
import com.eversec.antivpn.intelligence.entity.VpnIntelligenceCommand;
import com.eversec.antivpn.intelligence.entity.po.VpnIntelligencePO;
import com.eversec.antivpn.intelligence.factory.VpnIntelligenceCommandFactory;
import com.eversec.antivpn.intelligence.factory.VpnIntelligenceFactory;
import com.eversec.antivpn.intelligence.feign.smart.SmartApi;
import com.eversec.antivpn.intelligence.feign.smart.dto.SmartFileTypeEnum;
import com.eversec.antivpn.intelligence.feign.smart.dto.SmartUpgradeRequestDTO;
import com.eversec.antivpn.intelligence.feign.smart.dto.SmartUploadResponseDTO;
import com.eversec.antivpn.intelligence.mapper.VpnIntelligenceMapper;
import com.eversec.antivpn.intelligence.service.IVpnIntelligenceCommandService;
import com.eversec.antivpn.intelligence.service.IVpnIntelligenceService;
import com.eversec.antivpn.intelligence.service.impl.encrypt.CompressionFormatEnum;
import com.eversec.antivpn.intelligence.service.impl.encrypt.EncryptAlgorithmEnum;
import com.eversec.antivpn.intelligence.service.impl.encrypt.HashAlgorithmEnum;
import com.eversec.antivpn.intelligence.util.EncryptUtil;
import com.eversec.antivpn.support.config.api.PlatformInterfaceInfoApi;
import com.eversec.antivpn.support.config.api.dto.PlatformInterfaceInfoDTO;
import com.eversec.antivpn.util.ReportUtil;
import com.eversec.framework.webboot.common.exception.BusinessException;
import com.eversec.stark.common.utils.generator.IdUtil;
import com.eversec.stark.generic.common.util.ListBuilder;
import com.eversec.stark.generic.sys.api.SysConfItemApi;
import com.eversec.stark.generic.sys.dto.SysConfItemDto;
import feign.codec.DecodeException;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileOutputStream;
import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <p>
 * vpn情报 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-10
 */
@Service
@Slf4j
@AllArgsConstructor
public class VpnIntelligenceServiceImpl extends ServiceImpl<VpnIntelligenceMapper, VpnIntelligence> implements IVpnIntelligenceService {

	private final PlatformInterfaceInfoApi platformInterfaceInfoApi;

	private final VpnIntelligenceFactory vpnIntelligenceFactory;

	private final SmartApi smartApi;

	private final AntiVpnProperties antiVpnProperties;

	private final VpnIntelligenceCommandFactory vpnIntelligenceCommandFactory;

	private final SysConfItemApi sysConfItemApi;

	private IVpnIntelligenceCommandService getVpnIntelligenceCommandService() {
		// 解决循环依赖
		return SpringUtil.getBean(IVpnIntelligenceCommandService.class);
	}

	private IVpnIntelligenceService getVpnIntelligenceService() {
		// 解决循环依赖
		return SpringUtil.getBean(IVpnIntelligenceService.class);
	}

	@Override
	public Page<VpnIntelligence> page(VpnIntelligenceQueryConditionDTO paramDto, Page<VpnIntelligence> pageInfo) {
		QueryWrapper<VpnIntelligence> vpnIntelligenceQueryWrapper = paramDto.autoWrapper();
		LambdaQueryWrapper<VpnIntelligence> vpnIntelligenceLambdaQueryWrapper = vpnIntelligenceQueryWrapper.lambda().orderByDesc(VpnIntelligence::getId);
		Page<VpnIntelligence> page = this.page(pageInfo, vpnIntelligenceLambdaQueryWrapper);
		return page;
	}

	@Override
	public void save(VpnIntelligenceDTO paramDto) {
		paramDto.setSource(IntelligenceSourceEnum.INPUT.name());
		VpnIntelligence vpnIntelligence = vpnIntelligenceFactory.dtoToEntity(paramDto);
		vpnIntelligence.setReportStatus(IntelligenceCommandReportStatusEnum.TO_BE_REPORTED.name());
		vpnIntelligence.setDistributeStatus(IntelligenceCommandDistributeStatusEnum.TO_BE_DISTRIBUTED.name());
		getVpnIntelligenceService().saveOrUpdateEntity(ListUtil.toList(vpnIntelligence));

		callSmartCU(paramDto.getComCode(), paramDto.getProvinceId(), paramDto.getSystemCode(), ListUtil.toList(vpnIntelligence), IntelligenceCommandOperationTypeEnum.INCREASE);
	}

	/**
	 * 新开启一个事务，避免调用smart影响导入
	 * @param vpnIntelligenceList
	 * @return
	 */
	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public String saveOrUpdateEntity(List<VpnIntelligence> vpnIntelligenceList) {

		if (CollectionUtil.isEmpty(vpnIntelligenceList)) {
			return "";
		}

		switch (antiVpnProperties.getDeployPlace()) {
			case GROUP:
				break;
			case PROVINCE:
			case SYSTEM:
				// 验证数据是否和当前系统配置一致，不一致不可保存
				PlatformInterfaceInfoDTO currentPlatform = platformInterfaceInfoApi.getCurrentPlatform();
				for (VpnIntelligence vpnIntelligence : vpnIntelligenceList) {
					if (!vpnIntelligence.getComCode().equals(currentPlatform.getComCode())
							|| !vpnIntelligence.getProvinceId().equals(currentPlatform.getProvinceId())
							|| !StrUtil.nullToEmpty(vpnIntelligence.getSystemCode()).equals(StrUtil.nullToEmpty(currentPlatform.getSystemCode()))) {
						throw new BusinessException(AntiVpnBusinessExceptionEnum.CODE_10010);
					}
				}
				break;
		}
		VpnIntelligence vpnIntelligence = vpnIntelligenceList.get(0);

		// 判断是否存在重复数据，重复数据进行覆盖操作
		List<Long> newRuleIdList = vpnIntelligenceList.stream().map(VpnIntelligencePO::getRuleId).collect(Collectors.toList());
		LambdaQueryWrapper<VpnIntelligence> vpnIntelligenceLambdaQueryWrapper = new LambdaQueryWrapper<>();
		vpnIntelligenceLambdaQueryWrapper.eq(VpnIntelligence::getComCode, vpnIntelligence.getComCode());
		vpnIntelligenceLambdaQueryWrapper.eq(VpnIntelligence::getProvinceId, vpnIntelligence.getProvinceId());

		if (StrUtil.isNotBlank(vpnIntelligence.getSystemCode())) {
			vpnIntelligenceLambdaQueryWrapper.eq(VpnIntelligence::getSystemCode, vpnIntelligence.getSystemCode());
		} else {
			vpnIntelligenceLambdaQueryWrapper.and(true, (i ->
					i.eq(VpnIntelligence::getSystemCode, "")
							.or()
							.isNull(VpnIntelligence::getSystemCode)));
		}

		vpnIntelligenceLambdaQueryWrapper.in(VpnIntelligence::getRuleId, newRuleIdList);
		vpnIntelligenceLambdaQueryWrapper.select(VpnIntelligence::getRuleId, VpnIntelligence::getId);
		// 先删除历史重复数据
		this.remove(vpnIntelligenceLambdaQueryWrapper);

		// 保存新数据
		List<List<VpnIntelligence>> partition = ListUtil.partition(vpnIntelligenceList, 1000);
		for (List<VpnIntelligence> vpnIntelligences : partition) {
			if (CollectionUtil.isNotEmpty(vpnIntelligences)) {
				getBaseMapper().saveBatchIntelligence(vpnIntelligences);
			}
		}
		return "操作成功";
	}

	@Override
	public void updateById(VpnIntelligenceDTO paramDto) {
		VpnIntelligence vpnIntelligence = vpnIntelligenceFactory.dtoToEntity(paramDto);
		getVpnIntelligenceService().saveOrUpdateEntity(ListUtil.toList(vpnIntelligence));
		callSmartCU(paramDto.getComCode(), paramDto.getProvinceId(), paramDto.getSystemCode(), ListUtil.toList(vpnIntelligence), IntelligenceCommandOperationTypeEnum.INCREASE);

	}

	@SneakyThrows
	@Override
	public void importVpnIntelligence(String comCode, Long provinceId, String systemCode, MultipartFile file) {
		log.info("开始执行导入，comCode: {}, provinceId: {}, systemCode: {}", comCode, provinceId, systemCode);

		// 生成指令数据
		VpnIntelligenceCommand command = vpnIntelligenceCommandFactory.importIntelligence(comCode, provinceId, systemCode);
		getVpnIntelligenceCommandService().saveImportCommand(command);

		try {
			String originalFilename = file.getOriginalFilename().toLowerCase();
			List<VpnIntelligenceImportDTO> vpnIntelligenceImportDTOList;
			if (originalFilename.endsWith(".csv")) {
				vpnIntelligenceImportDTOList = genFromCsv(file);
			} else if (originalFilename.endsWith(".xlsx") || originalFilename.endsWith(".xls")) {
				vpnIntelligenceImportDTOList = genFromExcel(file);
			} else {
				// zip
				vpnIntelligenceImportDTOList = genFromZip(file);
			}

			List<VpnIntelligence> vpnIntelligenceList = new ArrayList<>();
			vpnIntelligenceFactory.refDataDict();
			for (VpnIntelligenceImportDTO vpnIntelligenceImportDTO : vpnIntelligenceImportDTOList) {
				vpnIntelligenceList.add(vpnIntelligenceFactory.importDtoToEntity(vpnIntelligenceImportDTO, comCode, provinceId, systemCode, command.getCommandId()));
			}
			getVpnIntelligenceService().saveOrUpdateEntity(vpnIntelligenceList);

			callSmartCU(comCode, provinceId, systemCode, vpnIntelligenceList, IntelligenceCommandOperationTypeEnum.INCREASE);

		} catch (Exception e) {
			VpnIntelligenceCommandDistributeResponseDTO responseDTO = new VpnIntelligenceCommandDistributeResponseDTO();
			responseDTO.setResultCode(ResponseCodeEnum.其他异常.getCode());
			getVpnIntelligenceCommandService().updateCommandProcessStatus(command.getId(),responseDTO,  e);
			throw e;
		}


		VpnIntelligenceCommandDistributeResponseDTO responseDTO = new VpnIntelligenceCommandDistributeResponseDTO();
		responseDTO.setResultCode(ResponseCodeEnum.处理完成.getCode());
		getVpnIntelligenceCommandService().updateCommandProcessStatus(command.getId(),responseDTO,  null);


	}

	@SneakyThrows
	private List<VpnIntelligenceImportDTO> genFromZip(MultipartFile file) {
		String zipFilePath = DateUtil.format(new Date(), "yyyy-MM-dd.HHmmss")  + "." + RandomUtil.randomString(10);

		File tempFile = Paths.get(FileUtil.getTmpDirPath(), "intelligence-import-zip", zipFilePath + ".zip").toFile();
		FileUtil.mkParentDirs(tempFile);
		file.transferTo(tempFile);

		Path tmpPath = Paths.get(FileUtil.getTmpDirPath(), "intelligence-import-zip", zipFilePath);
		FileUtil.mkdir(tmpPath);
		// 文件解压到 /tmp/intelligence-import-zip/20231214.111111.asdfasdfas/
		ZipUtil.unzip(tempFile, tmpPath.toFile());
		// 查找目录下菜单csv文件，其余zip文件解压到 /app/data/storage/IntelligenceAttachMent/ ，覆盖现有文件
		List<String> files = FileUtil.listFileNames(tmpPath.toString());
		List<VpnIntelligenceImportDTO> importDTOS = null;
		for (String childFile : files) {
			String extName = FileUtil.extName(childFile);
			if ("csv".equalsIgnoreCase(extName) ) {
				// 解析csv
				importDTOS = genFromCsv(Paths.get(tmpPath.toString(), childFile).toFile());
			}if ("xlsx".equalsIgnoreCase(extName) || "xls".equalsIgnoreCase(extName)) {
				// 解析excel
				importDTOS = genFromExcel(Paths.get(tmpPath.toString(), childFile).toFile());
			} else if ("zip".equalsIgnoreCase(extName) ){
				// 解压其他zip文件到 /app/data/storage/IntelligenceAttachMent/ ，覆盖现有文件
				Path attachmentZipFilePath = Paths.get(tmpPath.toString(), childFile);
				Path attachMentDirPath = Paths.get(antiVpnProperties.getFileDataStorageDir(), antiVpnProperties.getBusinessDir().getIntelligenceAttachMentDir());
				ZipUtil.unzip(attachmentZipFilePath.toFile(), attachMentDirPath.toFile());
			}
		}
		return importDTOS;
	}

	@SneakyThrows
	private List<VpnIntelligenceImportDTO> genFromExcel(MultipartFile file) {
		log.info("开始执行excel导入");

		List<VpnIntelligenceImportDTO> vpnIntelligenceImportDTOList = ExcelImportUtil.importExcel(file.getInputStream(), VpnIntelligenceImportDTO.class, new ImportParams() {{
			this.setHeadRows(1);
			// 设置验证支持
			this.setNeedVerfiy(false);
			this.setImportFields(VpnIntelligenceImportDTO.MUST_FILL);

		}});
		log.info("执行excel导入，共解析{}条记录", vpnIntelligenceImportDTOList.size());
		return vpnIntelligenceImportDTOList;
	}

	@SneakyThrows
	private List<VpnIntelligenceImportDTO> genFromExcel(File file) {
		log.info("开始执行excel导入");

		List<VpnIntelligenceImportDTO> vpnIntelligenceImportDTOList = ExcelImportUtil.importExcel(file, VpnIntelligenceImportDTO.class, new ImportParams() {{
			this.setHeadRows(1);
			// 设置验证支持
			this.setNeedVerfiy(false);
			this.setImportFields(VpnIntelligenceImportDTO.MUST_FILL);

		}});
		log.info("执行excel导入，共解析{}条记录", vpnIntelligenceImportDTOList.size());
		return vpnIntelligenceImportDTOList;
	}

	@SneakyThrows
	private List<VpnIntelligenceImportDTO> genFromCsv(MultipartFile file) {
		File tempFile = Paths.get(FileUtil.getTmpDirPath(),
				"intelligence-import-csv",
				DateUtil.format(new Date(), "yyyy-MM-dd.HHmmss")  + "." + RandomUtil.randomString(10)).toFile();
		FileUtil.mkParentDirs(tempFile);
		file.transferTo(tempFile);

		return genFromCsv(tempFile);
	}

	private List<VpnIntelligenceImportDTO> genFromCsv(File file) {

		CsvData csvData = CsvUtil.getReader().read(file);
		List<String> header = csvData.getHeader();
		List<CsvRow> rows = csvData.getRows();
		List<VpnIntelligenceImportDTO> vpnIntelligenceImportDTOList = new ArrayList<>();

		for (int i = 1; i < csvData.getRowCount(); i++) {
			CsvRow row = csvData.getRow(i);
			vpnIntelligenceImportDTOList.add(vpnIntelligenceFactory.csvToEntity(row.getRawList()));

		}
		log.info("执行csv导入，共解析{}条记录", vpnIntelligenceImportDTOList.size());
		return vpnIntelligenceImportDTOList;
	}

	@SneakyThrows
	public static void main(String[] args) {
		List<VpnIntelligenceImportDTO> vpnIntelligenceList = new ArrayList<>();

		for (int i = 0; i < 10; i++) {
			VpnIntelligenceImportDTO vpnIntelligence = new VpnIntelligenceImportDTO();
			vpnIntelligence.setVpnId(1L);
			vpnIntelligenceList.add(vpnIntelligence);
		}



	}

	/**
	 * 全量下发到态势感知
	 * 1. 根据运营商和省查出所有情报规则进行下发
	 * 2. 生成excel文件
	 * 3. 存放到指定目录
	 * @return 生成文件的路径
	 */
	@SneakyThrows
	@Override
	public Path redistributeToNssa() {
		// 查询记录
		Pair<List<VpnIntelligence>, List<PlatformInterfaceInfoDTO>> allVpnIntelligencesAndPlatformInterfaceInfos = getAllVpnIntelligencesAndPlatformInterfaceInfos();
		List<PlatformInterfaceInfoDTO> platformInterfaceInfoDTOS = allVpnIntelligencesAndPlatformInterfaceInfos.getValue();
		List<VpnIntelligence> vpnIntelligenceList =  allVpnIntelligencesAndPlatformInterfaceInfos.getKey();

		// 生成excel
		List<VpnIntelligenceImportDTO> importDTOS = ListBuilder.convertList(vpnIntelligenceList, vpnIntelligenceFactory::entityToImportDto);

		String fileName = "rule-" + LocalDateTimeUtil.format(LocalDateTime.now(), "yyyyMMddHHmmss") + ".csv";
		// excel 规则文件
		Path filePath = Paths.get(antiVpnProperties.getFileDataStorageDir(), antiVpnProperties.getBusinessDir().getNssaRule(), fileName);
		FileUtil.mkParentDirs(filePath);

		genCsv(importDTOS, filePath);
//		genExcel(importDTOS, filePath);

		// 存放到态势感知目录
		for (PlatformInterfaceInfoDTO platformInterfaceInfoDTO : platformInterfaceInfoDTOS) {
			//
			if (!platformInterfaceInfoDTO.getCollectType().equals(CollectTypeEnum.NSSA.name())) {
				continue;
			}
			String southIntelligenceDir = platformInterfaceInfoDTO.getSouthIntelligenceDir();

			Path distributeFilePath = Paths.get(southIntelligenceDir, fileName);
			FileUtil.copy(filePath.toFile(), distributeFilePath.toFile(), true);
		}
		return filePath;
	}


	private void genCsv(List<VpnIntelligenceImportDTO> importDTOS, Path filePath) {
		List<String> csvLineList = new ArrayList<>();
		for (VpnIntelligenceImportDTO importDTO : importDTOS) {
			csvLineList.add(importDTO.toCsvLine());
		}

		try (CsvWriter writer = CsvUtil.getWriter(filePath.toFile(), StandardCharsets.UTF_8)) {
            writer.writeHeaderLine(VpnIntelligenceImportDTO.MUST_FILL);
			writer.write(csvLineList);
			writer.flush();
		}
	}


	/**
	 * 生成情报库excel
	 * @param importDTOS
	 * @param filePath
	 */
	@SneakyThrows
	private void genExcel(List<VpnIntelligenceImportDTO> importDTOS, Path filePath) {
		Workbook sheets = ExcelExportUtil.exportExcel(new ExportParams() {{
			this.setCreateHeadRows(true);
		}}, VpnIntelligenceImportDTO.class, importDTOS);
		try (FileOutputStream fileOutputStream = new FileOutputStream(filePath.toFile())){
			sheets.write(fileOutputStream);
		}
		sheets.close();
		log.info("生成下发态势感知规则文件成功：{}", filePath);
	}

	@Override
	public void callSmartCU(String comCode, Long provinceId, String systemCode, List<VpnIntelligence> vpnIntelligenceList,
			IntelligenceCommandOperationTypeEnum operationTypeEnum) {
		callSmart();
		callCU(comCode, provinceId, systemCode, vpnIntelligenceList, operationTypeEnum);
		redistributeToNssa();
	}

	/**
	 * 下发时规则id可能没有，进行填充并更新到数据库
	 */
	private void fillAndUpdateField(List<VpnIntelligence> vpnIntelligenceList) {
		if (CollectionUtil.isEmpty(vpnIntelligenceList)) {
			return;
		}
		List<VpnIntelligence> toBeFillAndUpdateRuleIdList = vpnIntelligenceFactory.fillAndUpdateField(vpnIntelligenceList);
		// TODO: 2023-11-22 更新数据库 暂时先不做了

	}


	private Pair<List<VpnIntelligence>, List<PlatformInterfaceInfoDTO>> getAllVpnIntelligencesAndPlatformInterfaceInfos() {
		PlatformInterfaceInfoDTO currentPlatform = platformInterfaceInfoApi.getCurrentPlatform();
		List<PlatformInterfaceInfoDTO> platformInterfaceInfoDTOS;
		List<VpnIntelligence> vpnIntelligenceList;
		if (antiVpnProperties.getDeployPlace() == AntiVpnDeployPlaceEnum.PROVINCE) {
			platformInterfaceInfoDTOS = platformInterfaceInfoApi.getByIspAndProvince(currentPlatform.getComCode(),
					currentPlatform.getProvinceId());
			vpnIntelligenceList = this.list(currentPlatform.getComCode(), currentPlatform.getProvinceId());

		} else if (antiVpnProperties.getDeployPlace() == AntiVpnDeployPlaceEnum.SYSTEM) {
			vpnIntelligenceList = this.list(currentPlatform.getComCode(), currentPlatform.getProvinceId(), currentPlatform.getSystemCode());
			platformInterfaceInfoDTOS = new ArrayList<>();
			platformInterfaceInfoDTOS.add(currentPlatform);
		} else {
			vpnIntelligenceList = new ArrayList<>();
			platformInterfaceInfoDTOS = new ArrayList<>();
		}
		fillAndUpdateField(vpnIntelligenceList);
		Pair<List<VpnIntelligence>, List<PlatformInterfaceInfoDTO>> result = new Pair<>(vpnIntelligenceList, platformInterfaceInfoDTOS);
		return result;
	}

	/**
	 * 全量下发到smart
	 * 根据运营商和省查出所有情报规则进行下发
	 * 1. 分离全网统一特征库和SNORT特征库
	 * 2. 构造全网统一特征库文件
	 * 3. 构造SNORT特征库文件
	 * 4. 调用上传接口
	 * 5.调用通知接口
	 *
	 */
	@SneakyThrows
	@Override
	public Path callSmart() {
		log.info("调用smart");
		try {
			Path tzPath = null;
			Path snortPath = null;
			Pair<List<VpnIntelligence>, List<PlatformInterfaceInfoDTO>> allVpnIntelligencesAndPlatformInterfaceInfos = getAllVpnIntelligencesAndPlatformInterfaceInfos();
			List<PlatformInterfaceInfoDTO> platformInterfaceInfoDTOS = allVpnIntelligencesAndPlatformInterfaceInfos.getValue();
			List<VpnIntelligence> vpnIntelligenceList =  allVpnIntelligencesAndPlatformInterfaceInfos.getKey();
			SysConfItemDto ignoreToSmartVpnIdsConf = sysConfItemApi.getByKey(AntiVpnDataDictTypeEnum.IGNORE_TO_SMART_VPN_IDS.name());
			Set<Long> ignoreToSmartVpnIds = new HashSet<>();
			if (ignoreToSmartVpnIdsConf != null) {
				String ignoreToSmartVpnIdsStr = ignoreToSmartVpnIdsConf.getItemValue();
				if (ignoreToSmartVpnIdsStr!=null) {
					JSONArray objects = JSONUtil.parseArray(ignoreToSmartVpnIdsStr);
					for (Object object : objects) {
						ignoreToSmartVpnIds.add((Long)object);
					}
				}
			}

			// 全网统一特征库：tz.rule
			List<VpnIntelligence> tzVpnIntelligenceList = new ArrayList<>();
			// SNORT特征库：snort.evt
			List<VpnIntelligence> snortVpnIntelligenceList = new ArrayList<>();
			for (VpnIntelligence vpnIntelligence : vpnIntelligenceList) {
				if (vpnIntelligence.getContentType() != null && vpnIntelligence.getContentType() == IntelligenceContentTypeEnum.MESSAGE.getCode()) {
					snortVpnIntelligenceList.add(vpnIntelligence);
				} else {
					tzVpnIntelligenceList.add(vpnIntelligence);
				}
			}
			for (PlatformInterfaceInfoDTO platformInterfaceInfoDTO : platformInterfaceInfoDTOS) {

				tzPath = callSmart(tzVpnIntelligenceList, ignoreToSmartVpnIds, SmartFileTypeEnum.TZ_RULE, platformInterfaceInfoDTO);
				snortPath = callSmart(snortVpnIntelligenceList, ignoreToSmartVpnIds, SmartFileTypeEnum.SNORT_EVT, platformInterfaceInfoDTO);
			}

			if (tzPath != null || snortPath != null) {
				// 生成压缩文件并返回
				String fileName = "smart-rule-" + LocalDateTimeUtil.format(LocalDateTime.now(), "yyyyMMddHHmmss") + ".zip";
				Path filePath = Paths.get(antiVpnProperties.getFileDataStorageDir(), antiVpnProperties.getBusinessDir().getSmartRule(), fileName);
				FileUtil.mkParentDirs(filePath);
				List<File> files = new ArrayList<>();
				if (tzPath != null) {
					files.add(tzPath.toFile());
				}
				if (snortPath != null) {
					files.add(snortPath.toFile());
				}
				ZipUtil.zip(filePath.toFile(), false, files.toArray(new File[0]));
				return filePath;
			} else {
				return null;
			}

		} catch (BusinessException | DecodeException e) {
			// feign异常直接返回
			throw e;
		} catch (Exception e) {
			throw new BusinessException(AntiVpnBusinessExceptionEnum.CODE_10009, e);
		}

	}


	@Override
	public void callCU(String comCode, Long provinceId, String systemCode, List<VpnIntelligence> vpnIntelligenceList, IntelligenceCommandOperationTypeEnum operationTypeEnum) {
		fillAndUpdateField(vpnIntelligenceList);
		PlatformInterfaceInfoDTO currentPlatform = platformInterfaceInfoApi.getCurrentPlatform();
		List<PlatformInterfaceInfoDTO> platformInterfaceInfoDTOS;
		if (antiVpnProperties.getDeployPlace() == AntiVpnDeployPlaceEnum.PROVINCE) {
			platformInterfaceInfoDTOS = platformInterfaceInfoApi.getByIspAndProvince(currentPlatform.getComCode(),
					currentPlatform.getProvinceId());

		} else if (antiVpnProperties.getDeployPlace() == AntiVpnDeployPlaceEnum.SYSTEM) {
			platformInterfaceInfoDTOS = new ArrayList<>();
			platformInterfaceInfoDTOS.add(currentPlatform);
		} else {
			platformInterfaceInfoDTOS = new ArrayList<>();
		}

		for (PlatformInterfaceInfoDTO platformInterfaceInfoDTO : platformInterfaceInfoDTOS) {
			if (CollectTypeEnum.valueOf(platformInterfaceInfoDTO.getCollectType()) != CollectTypeEnum.CU) {
				continue;
			}
			PlatformInterfaceInfoDTO.SecretKeyDTO secretKeyDTO = platformInterfaceInfoDTO.toSecretKeyDTO(platformInterfaceInfoDTO.getNorthIntelligenceSecretKey());

			CompressionFormatEnum compressionFormatEnum = CompressionFormatEnum.NO_COMPRESSION;
			HashAlgorithmEnum hashAlgorithmEnum = HashAlgorithmEnum.NO_HASH;
			EncryptAlgorithmEnum encryptAlgorithmEnum = EncryptAlgorithmEnum.CLEAR_TEXT;

			// 生成指令
			VpnIntelligenceCommandDistributeDTO distributeDto = vpnIntelligenceCommandFactory.createDistributeDto();

			String randVla = RandomUtil.randomString(16);

			String pwd = secretKeyDTO.getMessageKey();

			// 生成 E3 xml
			String distributedXml = distributeDto.toDistributeXml();

			// 压缩xml
			byte[] compressedCommand = EncryptUtil.compressCommand(compressionFormatEnum, distributedXml);

			// 生成hash
			String commandHash = EncryptUtil.commandHash(hashAlgorithmEnum, compressedCommand);
			// 加密
			byte[] encryptCommand = EncryptUtil.encryptCommand(encryptAlgorithmEnum, compressedCommand, secretKeyDTO.getAesKey(), secretKeyDTO.getAesOffsets());

			// base64
			String base64Command = Base64.encode(encryptCommand);
			String pwdHash = EncryptUtil.makePwdHash(pwd, randVla, hashAlgorithmEnum);

			VpnIntelligenceCommandDistributeRequestDTO requestDto = VpnIntelligenceCommandDistributeRequestDTO.builder()
					.comCode(comCode)
					.provinceId(provinceId)
					.randVal(randVla)
					.pwdHash(pwdHash)
					.command(base64Command)
					.commandHash(commandHash)
					.commandType(1)
					.encryptAlgorithm(encryptAlgorithmEnum.getCode())
					.hashAlgorithm(hashAlgorithmEnum.getCode())
					.compressionFormat(compressionFormatEnum.getCode())
					.commandVersion("1.0")
					.build();
			// 调用CU，没有文件，增量下发
			getVpnIntelligenceCommandService().callSouthWs(requestDto, platformInterfaceInfoDTO.getSystemCode());

		}
	}

	/**
	 * 下发到smart，并返回生成的规则文件
	 * @param vpnIntelligenceList
	 * @param smartFileType
	 * @param platformInterfaceInfoDTO
	 * @return 生成的规则文件
	 */
	@SneakyThrows
	private Path callSmart(List<VpnIntelligence> vpnIntelligenceList, Set<Long> ignoreToSmartVpnIds, SmartFileTypeEnum smartFileType, PlatformInterfaceInfoDTO platformInterfaceInfoDTO) {
		String randomKey = RandomUtil.randomString(16);

		if (vpnIntelligenceList.size() == 0) {
			log.info("无情报记录，不调用smart.平台配置：{}", JSONUtil.toJsonPrettyStr(platformInterfaceInfoDTO));
			return null;
		}

		if (CollectTypeEnum.valueOf(platformInterfaceInfoDTO.getCollectType()) != CollectTypeEnum.SMART) {
			log.info("当前非smart对接，不调用smart.平台配置：{}", JSONUtil.toJsonPrettyStr(platformInterfaceInfoDTO));
			return null;
		}

		String fileType;
		String versionPrefix;
		if (smartFileType == SmartFileTypeEnum.TZ_RULE) {
			fileType = "tz.rule";
			versionPrefix = "tz_rule_";
		} else {
			fileType = "snort.evt";
			versionPrefix = "snort_evt_";
		}

		String timeFormat = LocalDateTimeUtil.format(LocalDateTime.now(), "yyyyMMddHHmmss");
		String fileName = versionPrefix + timeFormat;
		String gzFileName = fileName + ".tar.gz";
		// 2. 构造全网统一特征库文件
		Path filePath = Paths.get(antiVpnProperties.getFileDataStorageDir(), antiVpnProperties.getBusinessDir().getSmartRule(), fileName);
		Path gzFilePath = Paths.get(antiVpnProperties.getFileDataStorageDir(), antiVpnProperties.getBusinessDir().getSmartRule(), gzFileName);
		File ruleFile = filePath.toFile();
		File gzUploadFile = gzFilePath.toFile();
		FileUtil.mkParentDirs(gzUploadFile);
		gzUploadFile.createNewFile();


		List<String> lines = new ArrayList<>();
		for (VpnIntelligence vpnIntelligence : vpnIntelligenceList) {
			if (ignoreToSmartVpnIds.contains(vpnIntelligence.getVpnId())) {
				continue;
			}
			String ruleLine;
			if (smartFileType == SmartFileTypeEnum.TZ_RULE) {
				ruleLine = vpnIntelligence.tzRuleLine();
			} else {
				ruleLine = vpnIntelligence.snortEvtLine();
			}
			if (StrUtil.isNotBlank(ruleLine)) {
				lines.add(ruleLine);
			}
		}
		if (lines.size() == 0) {
			log.info("情报记录为空，不调用smart");
			return null;
		}
		FileUtil.appendUtf8Lines(lines, ruleFile);
		try (Archiver archiver = CompressUtil.createArchiver(StandardCharsets.UTF_8, "tar.gz", gzUploadFile)) {
			archiver.add(ruleFile);
			archiver.finish();
		}
		log.info("上传smart文件: {}", gzFilePath);

		// 4.调用上传接口
		String southSmartAddress = platformInterfaceInfoDTO.getSouthSmartAddress();
		URI uri = null;
		if (southSmartAddress != null) {
			uri = URI.create(southSmartAddress);
		}
		String authorizationHeaderValue = "Basic " + cn.hutool.core.codec.Base64.encode(((platformInterfaceInfoDTO.getSouthSmartUsername() + ":" + platformInterfaceInfoDTO.getSouthSmartPassword()).getBytes(
				StandardCharsets.UTF_8)));
		SmartUploadResponseDTO upload = smartApi.upload(uri, authorizationHeaderValue, gzUploadFile, MD5.create().digestHex(gzUploadFile), fileType, fileName, lines.size(), 0, "1", "");
		SmartUpgradeRequestDTO smartUpgradeRequestDTO = new SmartUpgradeRequestDTO();
		smartUpgradeRequestDTO.setFileId(upload.getFileId());
		smartUpgradeRequestDTO.setDevIds(null);
		smartUpgradeRequestDTO.setDevType("6,8");
		smartUpgradeRequestDTO.setCpuArchitecture(null);
		smartUpgradeRequestDTO.setProvinceIds(null);
		smartUpgradeRequestDTO.setOpFlag(null);
		smartUpgradeRequestDTO.setBathNo(randomKey);
		smartUpgradeRequestDTO.setRemarks("");
		smartUpgradeRequestDTO.setSycType("1");
		Boolean upgrade = smartApi.upgrade(uri, authorizationHeaderValue, smartUpgradeRequestDTO);
		if (!upgrade) {
			throw new BusinessException(AntiVpnBusinessExceptionEnum.CODE_10005);
		}
		return filePath;
	}

	private List<VpnIntelligence> list(String comCode, Long provinceId, String systemCode) {
		return this.list(new LambdaQueryWrapper<VpnIntelligence>().eq(VpnIntelligence::getComCode, comCode)
				.eq(VpnIntelligence::getSystemCode, systemCode)
				.eq(VpnIntelligence::getProvinceId, provinceId));
	}


	private List<VpnIntelligence> list(String comCode, Long provinceId) {
		return this.list(new LambdaQueryWrapper<VpnIntelligence>().eq(VpnIntelligence::getComCode, comCode)
				.eq(VpnIntelligence::getProvinceId, provinceId));
	}


	@Override
	public void increaseCallSmartCU(List<VpnIntelligence> vpnIntelligenceList, IntelligenceCommandOperationTypeEnum operationTypeEnum) {
		throw new UnsupportedOperationException("smart暂不支持增量下发，请使用全量下发方式进行下发");
	}

	@Override
	public AggCountDTO reportVpnIntelligence(ScreenTypeEnum screenTypeEnum) {
		AggCountDTO aggCountDTO = new AggCountDTO();
		QueryWrapper<VpnIntelligence> wrapper = new QueryWrapper<>();
		wrapper.select("distinct(rule_id)");
		wrapper.isNotNull("report_datetime");
		Long totalCount = this.count(wrapper);
		aggCountDTO.setDateRangeCount(totalCount);
		wrapper.ge("report_datetime",getDateStr(0));
		Long todayCount = this.count(wrapper);
		aggCountDTO.setTodayCount(todayCount);
		return aggCountDTO;
	}

	private String getDateStr(Integer beforeDays){
		SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
		Calendar calendar = Calendar.getInstance();
		calendar.add(Calendar.DATE, beforeDays*-1);
		Date today = calendar.getTime();
		return simpleDateFormat.format(today)+ " 00:00:00";
	}

	@Override
	public AggCountDTO totalCount(ScreenTypeEnum screenTypeEnum) {
		AggCountDTO aggCountDTO = new AggCountDTO();
		QueryWrapper<VpnIntelligence> wrapper = new QueryWrapper<>();
		wrapper.eq("deleted",0);
		long totalCount = this.count(wrapper);
		aggCountDTO.setDateRangeCount(totalCount);
		wrapper.ge("create_datetime",getDateStr(0));;
		long todayCount = this.count(wrapper);
		aggCountDTO.setTodayCount(todayCount);
		return aggCountDTO;
	}


	/**
	 * 文件名转为md5，文件存储在磁盘上，情报中记录文件名，如 E807F1FCF82D132F9BB018CA6738A19F.zip
	 * 文件存储在fileDataStorageDir + intelligenceAttachMentDir
	 * 在上报时将md5值转为部侧需要的名称，如 121016444433333330010001.zip
	 * @param id
	 * @param file
	 */
	@SneakyThrows
	@Override
	public void uploadAttachMent(Long id, MultipartFile file) {

		String originalFilename = file.getOriginalFilename();
		File tempFile = FileUtil.createTempFile();
		file.transferTo(tempFile);
		String fileMd5 = MD5.create().digestHex(tempFile).toUpperCase();
		String extName = FileUtil.extName(originalFilename);
		String md5FileName = fileMd5 + "." + extName;
		Path filePath = Paths.get(antiVpnProperties.getFileDataStorageDir(), antiVpnProperties.getBusinessDir().getIntelligenceAttachMentDir(), md5FileName);
		FileUtil.mkParentDirs(filePath);
		log.info("写入情报取证文件, id：{}，filePath：{}", id, filePath);
		FileUtil.copy(tempFile, filePath.toFile(), false);

		VpnIntelligence vpnIntelligence = this.getById(id);
		if (vpnIntelligence == null) {
			throw new BusinessException(AntiVpnBusinessExceptionEnum.CODE_10001);
		}
		vpnIntelligence.setAttachMent(md5FileName);
		this.updateById(vpnIntelligence);

	}

	@Override
	public File getAttachMent(String fileName) {

		Path moduleFilePath = Paths.get(antiVpnProperties.getFileDataStorageDir(), antiVpnProperties.getBusinessDir().getIntelligenceAttachMentDir(), fileName);
		return moduleFilePath.toFile();

	}

	@Override
	public void deleteByCondition(VpnIntelligenceQueryConditionDTO paramDto) {
		QueryWrapper<VpnIntelligence> vpnIntelligenceQueryWrapper = paramDto.autoWrapper();
		this.remove(vpnIntelligenceQueryWrapper);
	}


	/**
	 * 取证文件复制到 reportDataDir
	 * @param vpnIntelligenceList
	 * @return
	 */
	private Map<String, String> copyAttachMentFile(List<VpnIntelligence> vpnIntelligenceList) {
		if (CollectionUtil.isEmpty(vpnIntelligenceList)) {
			return new HashMap<>();
		}
		Map<String, String> result = new HashMap<>();
		Set<String> attachMentSet = vpnIntelligenceList.stream().map(VpnIntelligencePO::getAttachMent).filter(StrUtil::isNotBlank).collect(Collectors.toSet());


		/*
		文件以省级区域编号(2位)，见附录F.4节+运营商标识(2位)+生成时间（用1970年1月1日到文件生成时的微秒数表示）+随机数(4位).zip命名，
		其中：运营商标识可以用10-电信，11-移动，12-联通，13-广电，99-其他，例如：121016444433333330010001.zip。
		企业侧先以“xxx.zip.tmp”形式命名，表示文件在上传中，如“121016444433333330010001.zip.tmp”。
		数据上报完成后去除.tmp后缀，如：“121016444433333330010001.zip”。

		 */

		VpnIntelligence vpnIntelligence = vpnIntelligenceList.get(0);
		int i = 0;
		for (String attachMent : attachMentSet) {
			i ++;
			Path sourceFilePath = Paths.get(antiVpnProperties.getFileDataStorageDir(), antiVpnProperties.getBusinessDir().getIntelligenceAttachMentDir(), attachMent);
			File reportZipFile = ReportUtil.getReportZipFile(antiVpnProperties.getReportDataDir(), AntiVpnReportCodeEnum.VPN_INTELLIGENCE,
					vpnIntelligence.getProvinceId(), vpnIntelligence.getComCode(), i);
			if (sourceFilePath.toFile().exists()) {
				log.info("开始复制取证文件到上报目录，source：{}，target：{}", sourceFilePath, reportZipFile.getPath());
				// 先复制为临时文件，避免被newiup读取不完整的文件
				File reportZipFileTmp = new File(Paths.get(reportZipFile.getAbsolutePath()) + ".tmp");
				FileUtil.copy(sourceFilePath.toFile(), reportZipFileTmp, false);
				FileUtil.move(reportZipFileTmp, reportZipFile, false);
				result.put(attachMent, reportZipFile.getName());
			} else {
				log.warn("取证文件不存在，请检查：{}", sourceFilePath);
			}
		}

		return result;

	}

	/**
	 * 0. 生成指令信息
	 * 1. 生成上报文件
	 * 2. 上传到指定ftp地址
	 * 3. 保存指令
	 * 4. 修改情报状态
	 *
	 * @param idList
	 */
	@Override
	public void reportVpnIntelligence(ArrayList<Long> idList, Integer networkBusinessId) {
		// 1. 生成指令
		List<VpnIntelligence> vpnIntelligenceList = listByIds(idList);
		List<VpnIntelligenceCommand> vpnIntelligenceCommandList = new ArrayList<>();
		List<VpnIntelligenceCommandReportDTO> reportCommandDtoList = new ArrayList<>();


		Map<String, String> attachMentFileNameMap = copyAttachMentFile(vpnIntelligenceList);

		List<String> csvLineList = new ArrayList<>();
		for (VpnIntelligence vpnIntelligence : vpnIntelligenceList) {
			// 可能存在重复情况
			long commandId = IdUtil.nextId() % 100000000L;
			vpnIntelligence.setCommandId(commandId);
			vpnIntelligence.setReportStatus(IntelligenceCommandReportStatusEnum.REPORTED.name());
			vpnIntelligence.setReportDatetime(LocalDateTime.now());
			VpnIntelligenceCommand vpnIntelligenceCommand = vpnIntelligenceCommandFactory.createVpnIntelligenceCommand(vpnIntelligence.getComCode(),
					vpnIntelligence.getProvinceId(), vpnIntelligence.getSystemCode(), commandId, networkBusinessId);
			vpnIntelligenceCommandList.add(vpnIntelligenceCommand);

			VpnIntelligenceCommandReportDTO reportDTO = vpnIntelligenceCommandFactory.createReportDto(vpnIntelligenceCommand, vpnIntelligence,
					attachMentFileNameMap.get(vpnIntelligence.getAttachMent()));
			reportCommandDtoList.add(reportDTO);
			csvLineList.add(reportDTO.toCsvLine());
		}

		// 2.1 生成csv
		PlatformInterfaceInfoDTO currentPlatform = platformInterfaceInfoApi.getCurrentPlatform();
		File reportFile = ReportUtil.getReportFile(antiVpnProperties.getReportDataDir(), AntiVpnReportCodeEnum.VPN_INTELLIGENCE,
				currentPlatform.getProvinceId(), currentPlatform.getComCode(), null);
		File reportTempFile = ReportUtil.getTempFile(reportFile);
		try (CsvWriter writer = CsvUtil.getWriter(reportTempFile, StandardCharsets.UTF_8)) {
			//            writer.writeHeaderLine(AntiVpnReportCodeEnum.VPN_INTELLIGENCE.getHeader());
			writer.write(csvLineList);
			writer.flush();
		}
		// 3. 保存指令
		getVpnIntelligenceCommandService().saveBatch(vpnIntelligenceCommandList);

		log.info("移动生成上报的情报文件: {}; {}", reportTempFile, reportFile);
		FileUtil.move(reportTempFile, reportFile, true);

		// 4. 修改情报状态，循环修改，开发方便
		this.updateBatchById(vpnIntelligenceList);

	}

}
