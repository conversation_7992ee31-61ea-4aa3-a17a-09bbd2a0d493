package com.eversec.antivpn.intelligence.api;

import com.eversec.antivpn.intelligence.api.dto.VpnIntelligenceCommandDistributeResponseDTO;
import org.springframework.stereotype.Service;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import java.util.concurrent.ExecutionException;

/**
 * 需求参考8.1
 *
 * 集团测接收部侧下发情报指令
 * 处理流程：
 * 一：解析入库（多线程）
 * 1、接收、反解码、解密、哈希校验、解压，得到指令
 * 2、下载全量情报库文件
 * 3、解析情报库文件
 * 4、数据存入mysql
 * 二：并行（主线程）
 * 1、复制全量情报库文件到省平台共享目录
 * 2、调用省平台webservice接口
 * <p>
 * 省平台接收集团测下发情报指令
 * 处理流程：
 * 一：解析入库（多线程）
 * 1、接收、反解码、解密、哈希校验、解压，得到指令
 * 2、下载全量情报库文件
 * 3、解析情报库文件
 * 4、数据存入mysql
 * 二：并行（主线程）
 * 1、转换为smart可识别的指令
 * 2、上传到smart，调用smart接口
 *
 */
@WebService(name = "VpnIntelligenceCommandWebService", targetNamespace = "http://impl.service.intelligence.antivpn.eversec.com/")
public interface VpnIntelligenceCommandWebService {

	/*
	请求参数中可以指定systemCode参数或者url IDCWebService/idcCommand 后面加一级，用作systemCode
	"http://localhost:8080/services/IDCWebService/idcCommand?wsdl&systemCode=xxx";
	"http://localhost:8080/services/IDCWebService/idcCommand/xxx2?wsdl";
	 */
	/*

	<?xml version="1.0" encoding="UTF-8"?>
	<return>
		<version>1.0</version>
		<resultCode>0</resultCode>
		<msg>处理完成</msg>
	</return>

	 */
	@WebMethod(operationName = "idc_command_province")
	@WebResult
	String idcCommandProvince(@WebParam(name = "comCode") String comCode, @WebParam(name = "provinceId") Long provinceId,
							  @WebParam(name = "randVal") String randVal, @WebParam(name = "pwdHash") String pwdHash, @WebParam(name = "command") String command,
							  @WebParam(name = "commandHash") String commandHash, @WebParam(name = "commandType") Integer commandType,
							  @WebParam(name = "encryptAlgorithm") Integer encryptAlgorithm, @WebParam(name = "hashAlgorithm") Integer hashAlgorithm,
							  @WebParam(name = "compressionFormat") Integer compressionFormat, @WebParam(name = "commandVersion") String commandVersion);

	String reDistributeIdcCommandProvince(@WebParam(name = "comCode") String comCode, @WebParam(name = "provinceId") Long provinceId,
							  @WebParam(name = "randVal") String randVal, @WebParam(name = "pwdHash") String pwdHash, @WebParam(name = "command") String command,
							  @WebParam(name = "commandHash") String commandHash, @WebParam(name = "commandType") Integer commandType,
							  @WebParam(name = "encryptAlgorithm") Integer encryptAlgorithm, @WebParam(name = "hashAlgorithm") Integer hashAlgorithm,
							  @WebParam(name = "compressionFormat") Integer compressionFormat, @WebParam(name = "commandVersion") String commandVersion, String systemCode, Long reDistributeByCommandId);




}
