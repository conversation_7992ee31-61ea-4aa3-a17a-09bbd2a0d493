package com.eversec.antivpn.log.controller;

import cn.hutool.core.util.ObjectUtil;
import com.eversec.antivpn.emnus.ScreenTypeEnum;
import com.eversec.antivpn.log.aop.RedisCache;
import com.eversec.antivpn.log.aop.RedisCacheOnlyMethodKey;
import com.eversec.antivpn.log.api.dto.TypeCountDTO;
import com.eversec.antivpn.log.api.dto.VpnServicePageDTO;
import com.eversec.antivpn.log.api.dto.base.AggCountWeekDTO;
import com.eversec.antivpn.log.api.dto.base.VpnAggDTO;
import com.eversec.antivpn.log.api.dto.base.VpnHistogramAggDTO;
import com.eversec.antivpn.log.api.dto.base.VpnMapAggDTO;
import com.eversec.antivpn.log.mapper.vo.*;
import com.eversec.antivpn.log.service.ScreenRedisService;
import com.eversec.antivpn.log.service.VPNServiceOverallService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import cn.hutool.core.bean.BeanUtil;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/8/15 16:52
 **/
@Slf4j
@Tag(name = "vpn服务商态势")
@RestController
@RequestMapping("/log/vpnserviceoverall")
public class VPNServiceOverallController {

    @Resource
    private VPNServiceOverallService vpnServiceOverallService;

    @RedisCacheOnlyMethodKey
    @Operation(summary = "vpn服务商态势-VPN服务商总量")
    @GetMapping("/vpnServiceCount")
    public Long vpnServiceCount() {
        Long result = vpnServiceOverallService.getVPNServiceCount();
        return result;
    }


    @RedisCacheOnlyMethodKey
    @Operation(summary = "vpn服务商态势-资源节点总量")
    @GetMapping("/resourceNodeCount")
    public Long resourceNodeCount() {
        Long result = vpnServiceOverallService.getResourceNodeCount();;
        return result;
    }

    @RedisCacheOnlyMethodKey
    @Operation(summary = "vpn服务商态势-近7日活跃的VPN服务商数量")
    @GetMapping("/lastSevenDayActiveVPNServiceNum")
    public AggCountWeekDTO lastSevenDayActiveVPNServiceNum(@RequestParam(required = true) ScreenTypeEnum screenTypeEnum) {
        AggCountWeekDTO result = vpnServiceOverallService.getLastSevenDayActiveVPNServiceNum();
        return result;
    }


    @RedisCacheOnlyMethodKey
    @Operation(summary = "vpn服务商态势-近7日活跃的资源节点数量")
    @GetMapping("/lastSevenDayActiveResourceNodeNum")
    public AggCountWeekDTO lastSevenDayActiveResourceNodeNum(@RequestParam(required = true) ScreenTypeEnum screenTypeEnum) {
        AggCountWeekDTO result = vpnServiceOverallService.getLastSevenDayActiveResourceNodeNum();
        return result;
    }

    @RedisCache
    @Operation(summary = "vpn服务商态势-用户使用资源节点热度 TOP10")
    @GetMapping("/usedResourceNodeTop10")
    public List<VpnAggDTO> usedResourceNodeTop10(@RequestParam ScreenTypeEnum screenTypeEnum) {
        List<VpnAggDTO> result = new ArrayList<>();
        List<VpnAggVO> dataList = vpnServiceOverallService.getUsedResourceNodeTop10(screenTypeEnum);
        dataList.forEach(vpnAggVO -> result.add(BeanUtil.toBean(vpnAggVO, VpnAggDTO.class)));
        return result;
    }


    @RedisCacheOnlyMethodKey
    @Operation(summary = "vpn服务商态势-近7日服务商及资源节点活跃量趋势")
    @GetMapping("/vpnServiceAndResourceNodeDateHistogram")
    public List<VpnHistogramAggDTO> vpnServiceAndResourceNodeDateHistogram(@RequestParam ScreenTypeEnum screenTypeEnum) {
        List<VpnHistogramAggDTO> result = new ArrayList<>();
        List<VpnHistogramAggVO> dataList = vpnServiceOverallService.getVpnServiceAndResourceNodeDateHistogram(screenTypeEnum);
        dataList.forEach(vpnHistogramAggVO -> result.add(BeanUtil.toBean(vpnHistogramAggVO, VpnHistogramAggDTO.class)));
        return result;
    }


    @RedisCacheOnlyMethodKey
    @Operation(summary = "vpn服务商态势-地图")
    @GetMapping("/map")
    public List<VpnMapAggDTO> map(ScreenTypeEnum screenTypeEnum) {
        List<VpnMapAggDTO> result = new ArrayList<>();
        List<VpnMapAggVO> dataList = vpnServiceOverallService.map(screenTypeEnum);
        dataList.forEach(vpnMapAggVO -> result.add(BeanUtil.toBean(vpnMapAggVO, VpnMapAggDTO.class)));
        dataList.sort((a, b) -> {
            long v = a.getVpnServiceNum() - b.getVpnServiceNum();
            if(v>0l) {
                return -1;
            }else if(v==0l){
                return 0;
            }else {
                return 1;
            }
        });
        return result;
    }

    @RedisCache
    @Operation(summary = "vpn服务商态势-VPN服务商数量所在地区排行 TOP10")
    @GetMapping("/countryVpnServiceNumTop10")
    public List<VpnMapAggDTO> countryVpnServiceNumTop10(@RequestParam ScreenTypeEnum screenTypeEnum) {
        List<VpnMapAggDTO> result = new ArrayList<>();
        List<VpnMapAggVO> dataList = vpnServiceOverallService.map(screenTypeEnum);
        dataList.sort((a, b) -> {
            long v = a.getVpnServiceNum() - b.getVpnServiceNum();
            return v > 0l ? -1 : 1;
        });
        for(int i=0;i<10&&i<dataList.size();i++){
            if(dataList.get(i).getVpnServiceNum() == 0l) break;
            result.add(BeanUtil.toBean(dataList.get(i),VpnMapAggDTO.class));
        }
        return result;
    }


    @RedisCache
    @Operation(summary = "vpn服务商态势-VPN服务资源节点数量所在地区排行 TOP10")
    @GetMapping("/countryResourceNodeNumTop10")
    public List<TypeCountDTO> countryResourceNodeNumTop10(@RequestParam ScreenTypeEnum screenTypeEnum) {
        List<TypeCountDTO> result = new ArrayList<>();
        List<TypeCountVO> dataList = vpnServiceOverallService.getCountryResourceNodeNumTop10(screenTypeEnum);
        dataList.forEach(vo -> result.add(BeanUtil.toBean(vo, TypeCountDTO.class)));
        return result;
    }


    @RedisCache
    @Operation(summary = "vpn服务商态势-VPN服务商用户量TOP10")
    @GetMapping("/vpnVpnServicePersonNumTop10")
    public List<TypeCountDTO> vpnVpnServicePersonNumTop10(@RequestParam ScreenTypeEnum screenTypeEnum) {
        List<TypeCountDTO> result = new ArrayList<>();
        List<TypeCountVO> dataList = vpnServiceOverallService.getVpnServicePersonNumTop10(screenTypeEnum);
        dataList.forEach(vo -> result.add(BeanUtil.toBean(vo, TypeCountDTO.class)));
        return result;
    }

    @Operation(summary = "vpn服务商态势-最新发现的VPN服务商信息")
    @GetMapping("/theNewestVpnService")
    public List<VpnServicePageDTO> theNewestVpnService() {
        List<VpnServicePageDTO> result = new ArrayList<>();
        List<VpnServicePageVO> dataList = vpnServiceOverallService.getTheNewestVpnService(20);
        dataList.forEach(vpnServicePageVO -> result.add(BeanUtil.toBean(vpnServicePageVO, VpnServicePageDTO.class)));
        return result;
    }
}
