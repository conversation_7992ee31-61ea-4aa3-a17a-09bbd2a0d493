package com.eversec.antivpn.intelligence.service.impl.encrypt;

import lombok.Getter;

/**
 * 哈希算法如下。
 * 0：无hash；
 * 1：MD5；
 * 2：SHA-1；
 * 企业侧系统应根据部侧系统的要求完成哈希算法的具体实现。
 *
 */
@Getter
public enum HashAlgorithmEnum {
	NO_HASH(0, "无hash"),
	MD5(1, "MD5"),
	SHA_1(1, "SHA-1"),
	;

	HashAlgorithmEnum(int code, String name) {
		this.code = code;
		this.name = name;
	}

	private int code;
	private String name;

}
