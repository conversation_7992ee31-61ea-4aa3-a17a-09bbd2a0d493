package com.eversec.antivpn.intelligence.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eversec.antivpn.intelligence.api.dto.VpnMachineLearningCodeDictDTO;
import com.eversec.antivpn.intelligence.dto.VpnIntelligenceQueryConditionDTO;
import com.eversec.antivpn.intelligence.dto.VpnMachineLearningCodeDictQueryConditionDTO;
import com.eversec.antivpn.intelligence.entity.VpnIntelligence;
import com.eversec.antivpn.intelligence.entity.VpnMachineLearningCodeDict;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-21
 */
public interface IVpnMachineLearningCodeDictService extends IService<VpnMachineLearningCodeDict> {

	/**
	 * 页面分页查询
	 * @param paramDto
	 * @param pageInfo
	 * @return
	 */
	Page<VpnMachineLearningCodeDict> page(VpnMachineLearningCodeDictQueryConditionDTO paramDto, Page<VpnMachineLearningCodeDict> pageInfo);


	void importVpnMachineLearningCodeDict(String comCode, Long provinceId, String systemCode, Integer networkBusinessId, MultipartFile file);

	void report(List<Long> ids, Integer fileType);


	String saveOrUpdateEntity(List<VpnMachineLearningCodeDict> entityList);

	/**
	 * 获取模型文件
	 * @param fileName
	 * @return
	 */
    File getModuleFile(String fileName);

}
