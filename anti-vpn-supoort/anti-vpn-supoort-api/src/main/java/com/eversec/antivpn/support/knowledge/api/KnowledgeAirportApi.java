package com.eversec.antivpn.support.knowledge.api;

import com.eversec.antivpn.support.knowledge.api.dto.ImportDownloadRequest;
import com.eversec.antivpn.support.knowledge.api.dto.ImportResult;
import com.eversec.antivpn.support.knowledge.api.dto.KnowledgeAirportDTO;
import com.eversec.antivpn.support.knowledge.api.dto.KnowledgeBaseQueryDto;
import com.eversec.cloud.core.api.data.PageResult;
import com.eversec.framework.feignboot.conf.EversecFeignDecoderConfiguration;
import com.eversec.framework.feignboot.conf.EversecFeignLogConfiguration;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.io.IOException;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

/**
 * 知识库 Api接口
 *
 * 模块对外暴露的api
 *
 * @FeignClient 注解参数解释
 * value: 服务提供者提供的服务名
 * url: 开发、测试环境调试无法通过服务名调用，填写服务对外暴露的地址。该参数有值 value则不生效，该参数为空时使用value值
 * path: 服务前缀，实现类 @RequestMapping 也使用该值
 * contextId: 全局唯一，默认为包名加类名
 * configuration:
 *          EversecFeignDecoderConfiguration: 自动解包，异常传递。
 *          EversecFeignLogConfiguration: 记录请求日志。默认开启日志，可通过如下参数控制是否记录日志
 *                  eversec.feign.request-log=true
 *                  eversec.feign.result-log=true
 *
 */

@Validated
@Tag(name = "知识库-机场信息Api接口")
@FeignClient(value = "anti-vpn-service",
    path = KnowledgeAirportApi.PATH,
    url = "${app.anti-vpn.service-url:}",
    contextId = "com.eversec.antivpn.support.knowledge.api.KnowledgeAirportApi",
    configuration = { EversecFeignDecoderConfiguration.class, EversecFeignLogConfiguration.class})
public interface KnowledgeAirportApi {

    String PATH = "/knowledge/airport";

    @Operation(summary = "新增（机场信息）")
    @PostMapping("/saveAirport")
    void save(@RequestBody @Valid final KnowledgeAirportDTO airportDTO);

    @Operation(summary = "根据id修改（机场信息）")
    @PostMapping("/updateAirportById")
    void updateById(@RequestBody @Valid final KnowledgeAirportDTO airportDTO);

    @Operation(summary = "根据id批量删除（机场信息），逗号分割")
    @DeleteMapping("/deleteDictByIds/{ids}")
    void deleteByIds(@PathVariable(value = "ids") List<Long> ids);

    @Operation(summary = "查询数据-分页")
    @GetMapping("/page")
    PageResult<KnowledgeAirportDTO> page(@ModelAttribute @ParameterObject KnowledgeBaseQueryDto queryDto);


    @Operation(summary = "模板下载")
    @GetMapping("/template")
    void downloadTemplate(HttpServletResponse response) throws IOException;

    @Operation(summary = "模板导入")
    @RequestMapping(value = "/import/template", method = RequestMethod.POST , consumes = "multipart/form-data")
    ImportResult importTemplate(@RequestHeader Long userId, @RequestHeader String userName, @RequestParam(value = "file") MultipartFile file);

    @Operation(summary = "导出错误数据")
    @PostMapping("/download/importerror")
    Object importDownload(@Validated @RequestBody ImportDownloadRequest req) throws IOException ;
}





