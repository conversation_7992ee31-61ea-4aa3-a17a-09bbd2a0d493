INSERT IGNORE INTO `vpn_machine_learning_code_dict`(`id`, `protocol_type`, `model_ch_name`, `model_code`, `model_name`, `model_version`, `vpn_name`, `create_time`, `update_time`) VALUES (1, 'tls', 'UpnetVPN-Tor和非Tor识别模型', '0100001', 'upnetvpn-tor', 'v1.0.0.0', 'UpnetVPN Tor', '2023-08-23 11:02:48', '2023-08-23 11:02:48');
INSERT IGNORE INTO `vpn_machine_learning_code_dict`(`id`, `protocol_type`, `model_ch_name`, `model_code`, `model_name`, `model_version`, `vpn_name`, `create_time`, `update_time`) VALUES (2, 'tls', 'vpn_tls识别模型', '0105001', 'shadowsocksr-tls-d', 'v1.0.0.0', 'shadowsocksr-tls', '2023-08-23 11:06:12', '2023-08-23 11:06:12');
INSERT IGNORE INTO `vpn_machine_learning_code_dict`(`id`, `protocol_type`, `model_ch_name`, `model_code`, `model_name`, `model_version`, `vpn_name`, `create_time`, `update_time`) VALUES (3, 'tls', 'vpn_tls识别模型', '0105002', 'vmess-tls-d', 'v1.0.0.0', 'vmess-tls', '2023-08-23 11:07:58', '2023-08-23 11:07:58');
INSERT IGNORE INTO `vpn_machine_learning_code_dict`(`id`, `protocol_type`, `model_ch_name`, `model_code`, `model_name`, `model_version`, `vpn_name`, `create_time`, `update_time`) VALUES (4, 'tls', 'vpn_tls识别模型', '0105003', 'vless-tls-d', 'v1.0.0.0', 'vless-tls', '2023-08-23 11:08:07', '2023-08-23 11:08:07');
INSERT IGNORE INTO `vpn_machine_learning_code_dict`(`id`, `protocol_type`, `model_ch_name`, `model_code`, `model_name`, `model_version`, `vpn_name`, `create_time`, `update_time`) VALUES (5, 'tls', 'vpn_tls识别模型', '0105004', 'trojan-tls-d', 'v1.0.0.0', 'trojan-tls', '2023-08-23 11:08:29', '2023-08-23 11:08:29');
INSERT IGNORE INTO `vpn_machine_learning_code_dict`(`id`, `protocol_type`, `model_ch_name`, `model_code`, `model_name`, `model_version`, `vpn_name`, `create_time`, `update_time`) VALUES (6, 'tls', 'vpn_tls识别模型', '0105005', 'shadowtls-tls-d', 'v1.0.0.0', 'shadowtls-tls', '2023-08-23 11:08:43', '2023-09-15 15:59:15');
INSERT IGNORE INTO `vpn_machine_learning_code_dict`(`id`, `protocol_type`, `model_ch_name`, `model_code`, `model_name`, `model_version`, `vpn_name`, `create_time`, `update_time`) VALUES (7, 'tls', 'vpn_tls识别模型', '0105006', 'naiveproxy-tls-d', 'v1.0.0.0', 'naiveproxy-tls', '2023-08-23 11:08:50', '2023-09-15 15:59:31');
INSERT IGNORE INTO `vpn_machine_learning_code_dict`(`id`, `protocol_type`, `model_ch_name`, `model_code`, `model_name`, `model_version`, `vpn_name`, `create_time`, `update_time`) VALUES (8, 'tls', 'UpnetVPN-Telegram和非Telegram识别模型', '0108001', 'upnetvpn-telegram', 'v1.0.0.0', 'UpnetVPN Telegram', '2023-08-23 11:08:56', '2023-09-15 16:00:04');
INSERT IGNORE INTO `vpn_machine_learning_code_dict`(`id`, `protocol_type`, `model_ch_name`, `model_code`, `model_name`, `model_version`, `vpn_name`, `create_time`, `update_time`) VALUES (9, 'tls', 'UpnetVPN-Chatgpt和非Chatgpt识别模型', '0109001', 'upnetvpn-chatgpt', 'v1.0.0.0', 'UpnetVPN Chatgpt', '2023-08-23 11:09:04', '2023-09-15 16:00:10');
INSERT IGNORE INTO `vpn_machine_learning_code_dict`(`id`, `protocol_type`, `model_ch_name`, `model_code`, `model_name`, `model_version`, `vpn_name`, `create_time`, `update_time`) VALUES (10, 'tls', 'KuailianVPN-Chatgpt和非Chatgpt识别模型', '0110001', 'kuailianvpn-chatgpt', 'v1.0.0.0', 'KuailianVPN Chatgpt', '2023-08-23 11:09:10', '2023-09-15 16:00:13');
INSERT IGNORE INTO `vpn_machine_learning_code_dict`(`id`, `protocol_type`, `model_ch_name`, `model_code`, `model_name`, `model_version`, `vpn_name`, `create_time`, `update_time`) VALUES (11, 'tcp', 'Vmess节点识别模型', '0300001', 'vmess-tcp', 'v1.0.0.0', 'vmess-tcp', '2023-08-23 11:09:16', '2023-09-15 16:00:22');
INSERT IGNORE INTO `vpn_machine_learning_code_dict`(`id`, `protocol_type`, `model_ch_name`, `model_code`, `model_name`, `model_version`, `vpn_name`, `create_time`, `update_time`) VALUES (12, 'tcp', 'shadowsocks节点识别模型', '0301001', 'shadowsocks-tcp', 'v1.0.0.0', 'shadowsocks-tcp', '2023-08-23 11:09:22', '2023-09-15 16:00:32');
INSERT IGNORE INTO `vpn_machine_learning_code_dict`(`id`, `protocol_type`, `model_ch_name`, `model_code`, `model_name`, `model_version`, `vpn_name`, `create_time`, `update_time`) VALUES (13, 'tcp', 'shadowsocksr节点识别模型', '0302001', 'shadowsocksr-tcp', 'v1.0.0.0', 'shadowsocksr-tcp', '2023-08-23 11:09:29', '2023-09-15 16:00:40');
INSERT IGNORE INTO `vpn_machine_learning_code_dict`(`id`, `protocol_type`, `model_ch_name`, `model_code`, `model_name`, `model_version`, `vpn_name`, `create_time`, `update_time`) VALUES (14, 'udp', 'shadowsocks节点识别模型', '0401001', 'shadowsocks-udp', 'v1.0.0.0', 'shadowsocks-tcp', '2023-08-23 11:09:35', '2023-09-15 16:00:52');
INSERT IGNORE INTO `vpn_machine_learning_code_dict`(`id`, `protocol_type`, `model_ch_name`, `model_code`, `model_name`, `model_version`, `vpn_name`, `create_time`, `update_time`) VALUES (15, 'udp', 'naiveproxy_udp节点识别模型', '0402001', 'naiveproxy-udp', 'v1.0.0.0', 'naiveproxy-udp', '2023-08-23 11:09:41', '2023-08-23 11:09:41');
INSERT IGNORE INTO `vpn_machine_learning_code_dict`(`id`, `protocol_type`, `model_ch_name`, `model_code`, `model_name`, `model_version`, `vpn_name`, `create_time`, `update_time`) VALUES (16, 'tls/http/tcp/udp', '行为检测模型', '0500001', 'behavior-detection', 'v1.0.0.0', '其他VPN', '2023-08-23 11:09:47', '2023-08-23 11:09:47');
INSERT IGNORE INTO `vpn_machine_learning_code_dict`(`id`, `protocol_type`, `model_ch_name`, `model_code`, `model_name`, `model_version`, `vpn_name`, `create_time`, `update_time`) VALUES (17, 'tls/http/tcp/udp', '行为检测模型', '0500002', 'behavior-detection', 'v1.0.0.0', '其他VPN', '2023-08-23 11:09:53', '2023-08-23 11:09:53');
INSERT IGNORE INTO `vpn_machine_learning_code_dict`(`id`, `protocol_type`, `model_ch_name`, `model_code`, `model_name`, `model_version`, `vpn_name`, `create_time`, `update_time`) VALUES (18, 'tls/http/tcp/udp', '行为检测模型', '0500003', 'behavior-detection', 'v1.0.0.0', '其他VPN', '2023-08-23 11:09:58', '2023-08-23 11:09:58');
INSERT IGNORE INTO `vpn_machine_learning_code_dict`(`id`, `protocol_type`, `model_ch_name`, `model_code`, `model_name`, `model_version`, `vpn_name`, `create_time`, `update_time`) VALUES (19, 'tls/http/tcp/udp', '行为检测模型', '0500004', 'behavior-detection', 'v1.0.0.0', '其他VPN', '2023-08-23 11:10:04', '2023-08-23 11:10:04');
